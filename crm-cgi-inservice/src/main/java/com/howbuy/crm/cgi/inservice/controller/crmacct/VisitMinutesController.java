package com.howbuy.crm.cgi.inservice.controller.crmacct;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.inservice.request.customer.*;
import com.howbuy.crm.cgi.inservice.service.crmacct.VisitMinutesService;
import com.howbuy.crm.cgi.inservice.vo.customer.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import com.howbuy.crm.cgi.inservice.vo.common.ExportToFileVO;

/**
 * @description: 拜访纪要控制器
 * @author: jianjian.yang
 * @date: 2025-04-09 16:04:00
 */
@Slf4j
@RestController
@RequestMapping("/customer/visitMinutes")
public class VisitMinutesController {

    @Autowired
    private VisitMinutesService visitMinutesService;

    /**
     * @api {POST} /inner/customer/visitMinutes/visitMinutesList visitMinutesList
     * @apiVersion 1.0.0
     * @apiGroup VisitMinutesController
     * @apiName visitMinutesList
     * @apiDescription 查询客户拜访纪要列表
     * @apiParam (请求体) {String} orgCode 机构编码
     * @apiParam (请求体) {String} consCode 投顾Code
     * @apiParam (请求体) {String} visitDateStart 拜访日期开始，格式YYYYMMDD
     * @apiParam (请求体) {String} visitDateEnd 拜访日期结束，格式YYYYMMDD
     * @apiParam (请求体) {String} createDateStart 创建日期开始，格式YYYYMMDD
     * @apiParam (请求体) {String} createDateEnd 创建日期结束，格式YYYYMMDD
     * @apiParam (请求体) {Array} visitPurpose 拜访目的列表，多选
     * @apiParam (请求体) {String} custName 客户姓名，精确匹配
     * @apiParam (请求体) {String} consCustNo 投顾客户号，精确匹配
     * @apiParam (请求体) {String} accompanyingUser 陪访人，精确匹配
     * @apiParam (请求体) {String} managerId 上级主管，精确匹配
     * @apiParam (请求体) {Array} feedbackStatus 反馈情况，可选：陪访人未填、上级主管未填、陪访人已填、上级主管已填
     * @apiParam (请求体) {Number} pageNo 分页页码，默认1
     * @apiParam (请求体) {Number} pageSize 分页大小，可选100/200/500/1000/2000
     * @apiParamExample 请求体示例
     * {"visitDateEnd":"bME","visitDateStart":"N","visitPurpose":["G2OTR2"],"pageSize":7357,"managerId":"3OYn2kRXz8","custName":"eBebgKXg","consCode":"UYO","accompanyingUser":"8607kqBCHJ","createDateStart":"Wtv6BSQcLv","orgCode":"Qo","pageNo":8224,"consCustNo":"LO2QTqHv","createDateEnd":"UU2","feedbackStatus":["g2fzGx"]}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Number} data.total 总记录数
     * @apiSuccess (响应结果) {Array} data.list 拜访纪要列表
     * @apiSuccess (响应结果) {String} data.list.createTime 创建日期，格式YYYY-MM-DD HH:MM:SS
     * @apiSuccess (响应结果) {String} data.list.visitDt 拜访日期，格式YYYY-MM-DD
     * @apiSuccess (响应结果) {String} data.list.visitPurpose 拜访目的，多个用逗号分隔
     * @apiSuccess (响应结果) {String} data.list.consCustNo 投顾客户号
     * @apiSuccess (响应结果) {String} data.list.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.list.creatorName 创建人姓名
     * @apiSuccess (响应结果) {String} data.list.centerName 所属中心
     * @apiSuccess (响应结果) {String} data.list.areaName 所属区域
     * @apiSuccess (响应结果) {String} data.list.branchName 所属分公司
     * @apiSuccess (响应结果) {String} data.list.visitType 沟通方式
     * @apiSuccess (响应结果) {String} data.list.marketVal 客户存量
     * @apiSuccess (响应结果) {String} data.list.healthAvgStar 客户综合健康度
     * @apiSuccess (响应结果) {String} data.list.giveInformation 提供资料
     * @apiSuccess (响应结果) {String} data.list.attendRole 参与人员及角色
     * @apiSuccess (响应结果) {String} data.list.productServiceFeedback 产品服务反馈
     * @apiSuccess (响应结果) {String} data.list.ipsFeedback IPS报告反馈
     * @apiSuccess (响应结果) {String} data.list.addAmount 币种+金额+万，多个逗号分隔
     * @apiSuccess (响应结果) {String} data.list.focusAsset 关注资产
     * @apiSuccess (响应结果) {String} data.list.estimateNeedBusiness 客户需求
     * @apiSuccess (响应结果) {String} data.list.nextPlan 工作计划
     * @apiSuccess (响应结果) {String} data.list.accompanyingType 陪访人类型
     * @apiSuccess (响应结果) {String} data.list.accompanyingUser 陪访人姓名，多个逗号分隔
     * @apiSuccess (响应结果) {String} data.list.accompanySummary 陪访人反馈-概要，格式：姓名:内容，多个分号分隔
     * @apiSuccess (响应结果) {String} data.list.accompanySuggestion 陪访人反馈-建议，格式：姓名:内容，多个分号分隔
     * @apiSuccess (响应结果) {String} data.list.managerName 上级主管姓名
     * @apiSuccess (响应结果) {String} data.list.managerSummary 主管反馈概要
     * @apiSuccess (响应结果) {String} data.list.managerSuggestion 主管反馈建议
     * @apiSuccess (响应结果) {Boolean} data.list.canAccompanyFeedback 是否可陪访人反馈
     * @apiSuccess (响应结果) {Boolean} data.list.canManagerFeedback 是否可主管反馈
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"pws","data":{"total":1841,"list":[{"accompanySummary":"Mf0bi","accompanyingType":"vKgG7WBzis","visitPurpose":"g","creatorName":"14","managerName":"24cNGyh","ipsFeedback":"KAN7EGoKN","marketVal":"gB4a","accompanyingUser":"xT3mo","areaName":"fjk3","managerSuggestion":"aFyG","estimateNeedBusiness":"SiXNXCc","nextPlan":"7bOVt3J","accompanySuggestion":"l","managerSummary":"saE0P","healthAvgStar":"oWDfffMukA","giveInformation":"FdKT","attendRole":"fMJ2Cg","canManagerFeedback":true,"addAmount":"UgsDu","visitDt":"wisTYT5","branchName":"hm4b","custName":"dv","visitType":"DTblLAsAuD","canAccompanyFeedback":true,"createTime":"sQs7EH","consCustNo":"bHXx7DY","productServiceFeedback":"M4D","focusAsset":"Oq2GTZZ","centerName":"6z3gfva0"}]},"description":"wAlpGUCnSn","timestampServer":"fov"}
     */
    @PostMapping("/visitMinutesList")
    public CgiResponse<VisitMinutesListVO> visitMinutesList(@RequestBody VisitMinutesListRequest request) {
        log.info("VisitMinutesController_visitMinutesList_request:{}", JSON.toJSONString(request));
        return visitMinutesService.visitMinutesList(request);
    }

    /**
     * @api {POST} /inner/customer/visitMinutes/visitFeedbackDetail visitFeedbackDetail
     * @apiVersion 1.0.0
     * @apiGroup VisitMinutesController
     * @apiName visitFeedbackDetail
     * @apiDescription 查询客户拜访纪要反馈明细
     * @apiParam (请求体) {String} visitMinutesId 拜访纪要ID
     * @apiParam (请求体) {String} feedbackType 反馈类型 1-陪访人 2-主管
     * @apiParamExample 请求体示例
     * {"visitMinutesId":"aDm2TaN","feedbackType":"CDU"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.consCustNo 投顾客户号
     * @apiSuccess (响应结果) {String} data.visitDt 拜访日期，格式YYYYMMDD
     * @apiSuccess (响应结果) {String} data.visitType 沟通方式
     * @apiSuccess (响应结果) {String} data.marketVal 客户存量
     * @apiSuccess (响应结果) {String} data.healthAvgStar 客户综合健康度
     * @apiSuccess (响应结果) {Array} data.visitPurpose 拜访目的列表
     * @apiSuccess (响应结果) {String} data.visitPurposeOther 拜访目的其他说明
     * @apiSuccess (响应结果) {Object} data.ipsReport 报告
     * @apiSuccess (响应结果) {String} data.ipsReport.reportId 报告ID
     * @apiSuccess (响应结果) {String} data.ipsReport.reportTitle 报告标题
     * @apiSuccess (响应结果) {String} data.giveInformation 提供资料
     * @apiSuccess (响应结果) {String} data.accompanyingUser 陪访人姓名，多个逗号分隔
     * @apiSuccess (响应结果) {String} data.attendRole 参与人员及角色
     * @apiSuccess (响应结果) {String} data.productServiceFeedback 产品服务反馈
     * @apiSuccess (响应结果) {String} data.ipsFeedback IPS报告反馈
     * @apiSuccess (响应结果) {String} data.addAmountRmb 人民币金额
     * @apiSuccess (响应结果) {String} data.addAmountForeign 外币金额
     * @apiSuccess (响应结果) {String} data.focusAsset 关注资产
     * @apiSuccess (响应结果) {String} data.estimateNeedBusiness 客户需求
     * @apiSuccess (响应结果) {String} data.nextPlan 工作计划
     * @apiSuccess (响应结果) {String} data.userName 陪访人姓名
     * @apiSuccess (响应结果) {String} data.accompanyingId 陪访人数据ID
     * @apiSuccess (响应结果) {String} data.managerName 主管姓名
     * @apiSuccess (响应结果) {String} data.summary 概要
     * @apiSuccess (响应结果) {String} data.suggestion 建议
     * @apiSuccess (响应结果) {Boolean} data.canNotEditSummary 是否不可编辑概要经验与教训
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"oeSEfvPsl","data":{"visitPurpose":["spgQf7JaKd"],"managerName":"Pi9CxeGJ","ipsFeedback":"UOr21rN74X","marketVal":"j0RL","visitPurposeOther":"SLmN","accompanyingUser":"nq","canNotEditSummary":true,"estimateNeedBusiness":"llEUeV","nextPlan":"er99u","healthAvgStar":"H0oqmXI","accompanyingId":"cF","summary":"27F","giveInformation":"K4MmSk","attendRole":"qxO9D","suggestion":"ljJ6itJI6","visitDt":"EKPzTMUA","custName":"DA","userName":"OmW0rE","visitType":"jCX","ipsReport":{"reportId":"ME","reportTitle":"qJs"},"addAmountForeign":"JI","consCustNo":"VcCBH3","productServiceFeedback":"TCpe5ic","addAmountRmb":"y","focusAsset":"ma9Sy"},"description":"co","timestampServer":"SFN4wiIW"}
     */
    @PostMapping("/visitFeedbackDetail")
    public CgiResponse<VisitFeedbackDetailVO> visitFeedbackDetail(@RequestBody VisitFeedbackDetailRequest request) {
        log.info("VisitMinutesController_visitFeedbackDetail_request:{}", JSON.toJSONString(request));
        return visitMinutesService.visitFeedbackDetail(request);
    }

    /**
     * @api {POST} /inner/customer/visitMinutes/exportVisitMinutes exportVisitMinutes()
     * @apiVersion 1.0.0
     * @apiGroup VisitMinutesController
     * @apiName exportVisitMinutes
     * @apiDescription 导出客户拜访纪要列表
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} [consId] 投顾ID
     * @apiParam (请求体) {String} [visitDateStart] 拜访日期开始，格式YYYYMMDD
     * @apiParam (请求体) {String} [visitDateEnd] 拜访日期结束，格式YYYYMMDD
     * @apiParam (请求体) {String} [createDateStart] 创建日期开始，格式YYYYMMDD
     * @apiParam (请求体) {String} [createDateEnd] 创建日期结束，格式YYYYMMDD
     * @apiParam (请求体) {Array} [visitPurpose] 拜访目的列表，多选
     * @apiParam (请求体) {String} [custName] 客户姓名，精确匹配
     * @apiParam (请求体) {String} [consCustNo] 投顾客户号，精确匹配
     * @apiParam (请求体) {String} [accompanyingUser] 陪访人，精确匹配
     * @apiParam (请求体) {String} [managerId] 上级主管，精确匹配
     * @apiParam (请求体) {Array} [feedbackStatus] 反馈情况，可选：陪访人未填、上级主管未填、陪访人已填、上级主管已填
     * @apiParamExample 请求体示例
     * {
     *   "consId": "123",
     *   "visitDateStart": "20240101",
     *   "visitDateEnd": "20240131",
     *   "createDateStart": "20240101",
     *   "createDateEnd": "20240131",
     *   "visitPurpose": ["目的1", "目的2"],
     *   "custName": "张三",
     *   "consCustNo": "C001",
     *   "accompanyingUser": "李四",
     *   "managerId": "M001",
     *   "feedbackStatus": ["陪访人已填", "上级主管已填"]
     * }
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.fileByte 文件字节数组的base64编码串
     * @apiSuccess (响应结果) {String} data.name 文件名
     * @apiSuccess (响应结果) {String} data.type 文件类型
     * @apiSuccess (响应结果) {String} data.errorMsg 错误信息
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "data": {
     *     "fileByte": "base64编码串",
     *     "name": "客户拜访纪要列表.xlsx",
     *     "type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
     *     "errorMsg": ""
     *   },
     *   "description": "成功",
     *   "timestampServer": "1677196800000"
     * }
     */
    @PostMapping("/exportVisitMinutes")
    public CgiResponse<ExportToFileVO> exportVisitMinutes(@RequestBody ExportVisitMinutesRequest request) {
        log.info("VisitMinutesController_exportVisitMinutes_request:{}", JSON.toJSONString(request));
        return visitMinutesService.exportVisitMinutes(request);
    }


    /**
     * @api {POST} /inner/customer/visitMinutes/visitMinutesDetail visitMinutesDetail
     * @apiVersion 1.0.0
     * @apiGroup VisitMinutesController
     * @apiName visitMinutesDetail
     * @apiDescription 查询客户拜访纪要明细
     * @apiParam (请求体) {String} visitMinutesId 拜访纪要ID
     * @apiParamExample 请求体示例
     * {"visitMinutesId":"qJ0fYFY"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.consCustNo 投顾客户号
     * @apiSuccess (响应结果) {String} data.visitDt 拜访日期，格式YYYYMMDD
     * @apiSuccess (响应结果) {String} data.visitType 沟通方式
     * @apiSuccess (响应结果) {String} data.marketVal 客户存量
     * @apiSuccess (响应结果) {String} data.healthAvgStar 客户综合健康度
     * @apiSuccess (响应结果) {Array} data.visitPurpose 拜访目的列表
     * @apiSuccess (响应结果) {String} data.visitPurposeOther 拜访目的其他说明
     * @apiSuccess (响应结果) {Object} data.ipsReport IPS报告信息
     * @apiSuccess (响应结果) {String} data.ipsReport.reportId 报告ID
     * @apiSuccess (响应结果) {String} data.ipsReport.reportTitle 报告标题
     * @apiSuccess (响应结果) {String} data.giveInformation 提供资料
     * @apiSuccess (响应结果) {String} data.accompanyingUser 陪访人姓名，多个逗号分隔
     * @apiSuccess (响应结果) {String} data.attendRole 参与人员及角色
     * @apiSuccess (响应结果) {String} data.productServiceFeedback 产品服务反馈
     * @apiSuccess (响应结果) {String} data.ipsFeedback IPS报告反馈
     * @apiSuccess (响应结果) {String} data.rmb 人民币金额
     * @apiSuccess (响应结果) {String} data.foreign 外币金额
     * @apiSuccess (响应结果) {String} data.focusAsset 关注资产
     * @apiSuccess (响应结果) {String} data.estimateNeedBusiness 客户需求
     * @apiSuccess (响应结果) {String} data.nextPlan 工作计划
     * @apiSuccess (响应结果) {Array} data.accompanyingList 陪访人列表
     * @apiSuccess (响应结果) {String} data.accompanyingList.userName 陪访人姓名
     * @apiSuccess (响应结果) {String} data.accompanyingList.managerName 主管姓名
     * @apiSuccess (响应结果) {String} data.accompanyingList.summary 主管概要
     * @apiSuccess (响应结果) {String} data.accompanyingList.suggestion 主管建议
     * @apiSuccess (响应结果) {Boolean} data.canEditData 是否可编辑
     * @apiSuccess (响应结果) {Array} data.manageUserList 主管列表
     * @apiSuccess (响应结果) {String} data.manageUserList.code 用户编码
     * @apiSuccess (响应结果) {String} data.manageUserList.name 用户名称
     * @apiSuccess (响应结果) {Array} data.supervisorUserList 总部业资列表
     * @apiSuccess (响应结果) {String} data.supervisorUserList.code 用户编码
     * @apiSuccess (响应结果) {String} data.supervisorUserList.name 用户名称
     * @apiSuccess (响应结果) {Array} data.otherUserList 其他列表
     * @apiSuccess (响应结果) {String} data.otherUserList.code 用户编码
     * @apiSuccess (响应结果) {String} data.otherUserList.name 用户名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"R1","data":{"giveInformation":"ltWOWqDdC","attendRole":"3gBmxtUy","rmb":"dL7fKsUcSy","accompanyingList":[{"summary":"RnOe786","suggestion":"XRZmo","userName":"fbAeo","managerName":"qyzeScRBJ"}],"visitPurpose":["6"],"visitDt":"Sy","custName":"p4vDHMeSlv","ipsFeedback":"sHzfml","visitType":"Q","foreign":"Oh1","marketVal":"8","visitPurposeOther":"brXY8x09K","ipsReport":{"reportId":"wj8F1rRn","reportTitle":"s19XbsC"},"accompanyingUser":"H","estimateNeedBusiness":"SaLny9b0FR","nextPlan":"1Ykv0","canEditData":true,"consCustNo":"pIGHLji","productServiceFeedback":"4JY5ab1","focusAsset":"C4hFkd","healthAvgStar":"fJg5D6xTzT"},"description":"s26","timestampServer":"ZtSGZLEH1"}
     */
    @PostMapping("/visitMinutesDetail")
    public CgiResponse<VisitMinutesDetailVO> visitMinutesDetail(@RequestBody VisitMinutesDetailRequest request) {
        log.info("VisitMinutesController_visitMinutesDetail_request:{}", JSON.toJSONString(request));
        return visitMinutesService.visitMinutesDetail(request);
    }

    /**
     * @api {POST} /inner/customer/visitMinutes/saveFeedback saveFeedback
     * @apiVersion 1.0.0
     * @apiGroup VisitMinutesController
     * @apiName saveFeedback
     * @apiDescription 保存陪访人/主管反馈
     * @apiParam (请求体) {String} visitMinutesId 拜访纪要ID
     * @apiParam (请求体) {String} accompanyingId 陪访人数据ID
     * @apiParam (请求体) {String} feedbackType 反馈类型 1:陪访人反馈 2:主管反馈
     * @apiParam (请求体) {String} summary 陪访概要
     * @apiParam (请求体) {String} suggestion 工作建议
     * @apiParamExample 请求体示例
     * {"summary":"gTcQJs","suggestion":"Q50","visitMinutesId":"bk","feedbackType":"A","accompanyingId":"gGv0fnS"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.verifyMsg 验证消息
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"ExO","data":{"verifyMsg":"yolgocgl"},"description":"CvCd","timestampServer":"Aw16X5vL00"}
     */
    @PostMapping("/saveFeedback")
    public CgiResponse<SaveVisitFeedbackVO> saveFeedback(@RequestBody SaveVisitFeedbackRequest request) {
        log.info("VisitMinutesController_saveFeedback_request:{}", JSON.toJSONString(request));
        return visitMinutesService.saveFeedback(request);
    }

    /**
     * @api {POST} /inner/customer/visitMinutes/saveCustFeedback saveCustFeedback
     * @apiVersion 1.0.0
     * @apiGroup VisitMinutesController
     * @apiName saveCustFeedback
     * @apiDescription 保存客户反馈
     * @apiParam (请求体) {String} visitMinutesId 拜访纪要ID
     * @apiParam (请求体) {Array} accompanyingList 陪访人列表
     * @apiParam (请求体) {String} accompanyingList.accompanyingType 陪访人类型
     * @apiParam (请求体) {String} accompanyingList.accompanyingUserId 陪访人Id
     * @apiParam (请求体) {String} attendRole 客户参与角色
     * @apiParam (请求体) {String} productServiceFeedback 对产品或服务的具体反馈
     * @apiParam (请求体) {String} ipsFeedback 对于IPS报告反馈
     * @apiParam (请求体) {String} addAmtRmb 人民币金额
     * @apiParam (请求体) {String} addAmtForeign 外币金额
     * @apiParam (请求体) {String} focusAsset 近期关注的资产类别或具体产品
     * @apiParam (请求体) {String} estimateNeedBusiness 评估客户需求
     * @apiParam (请求体) {String} nextPlan 下一步工作计划
     * @apiParamExample 请求体示例
     * {"attendRole":"ra7UG","addAmtRmb":"E","estimateNeedBusiness":"PAi","accompanyingList":[{"accompanyingType":"FzGd4ocm","accompanyingUserId":"2e9EnqXkw"}],"addAmtForeign":"4u4","nextPlan":"dlfD","productServiceFeedback":"eRfNyMyG","focusAsset":"g6iG8Es","visitMinutesId":"meSK4p5mu","ipsFeedback":"fW8"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.verifyMsg 成功标识 true:成功 false:失败
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Bk0xZAR","data":{"verifyMsg":"HdDCCxfoBL"},"description":"3OaL","timestampServer":"cnesLkNxM"}
     */
    @PostMapping("/saveCustFeedback")
    public CgiResponse<SaveCustFeedbackVO> saveCustFeedback(@RequestBody SaveCustFeedbackRequest request) {
        log.info("VisitMinutesController_saveCustFeedback_request:{}", JSON.toJSONString(request));
        return visitMinutesService.saveCustFeedback(request);
    }

    /**
     * @api {POST} /inner/customer/visitMinutes/updateManage updateManage
     * @apiVersion 1.0.0
     * @apiGroup VisitMinutesController
     * @apiName updateManage
     * @apiDescription 修改陪访人/主管
     * @apiParam (请求体) {String} visitMinutesIds 拜访纪要ID
     * @apiParam (请求体) {Boolean} isClear 是否清空原有人员
     * @apiParam (请求体) {String} newUserId 新用户ID，不清空时必填
     * @apiParamExample 请求体示例
     * {"newUserId":"S","visitMinutesIds":"wi","isClear":true}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.verifyMsg 验证消息
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"J","data":{"verifyMsg":"Yo"},"description":"Q913lu","timestampServer":"bKNksb"}
     */
    @PostMapping("/updateManage")
    public CgiResponse<UpdateManageVO> updateManage(@RequestBody UpdateManageRequest request) {
        log.info("VisitMinutesController_updateManage_request:{}", JSON.toJSONString(request));
        return visitMinutesService.updateManage(request);
    }

    /**
     * @api {POST} /inner/customer/visitMinutes/searchUser searchUser
     * @apiVersion 1.0.0
     * @apiGroup CommunicateController
     * @apiName searchUser
     * @apiDescription 用户搜索
     * @apiParam (请求体) {String} searchParam 搜索参数
     * @apiParam (请求体) {String} searchType 搜索类型 1:项目经理 2:所有正常用户
     * @apiParamExample 请求体示例
     * {"searchParam":"RJVf2QZgNc","searchType":"1ryoSsYl"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.userList
     * @apiSuccess (响应结果) {String} data.userList.userId 用户ID
     * @apiSuccess (响应结果) {String} data.userList.userName 用户名
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"PTnd","data":{"userList":[{"userName":"FKvxJKHA5y","userId":"c4"}]},"description":"oGv5WIn","timestampServer":"RUl8YXN"}
     */
    @PostMapping("/searchUser")
    public CgiResponse<SearchUserVO> searchUser(@RequestBody SearchUserRequest request) {
        log.info("CommunicateController_searchUser_request:{}", JSON.toJSONString(request));
        return visitMinutesService.searchUser(request);
    }
}