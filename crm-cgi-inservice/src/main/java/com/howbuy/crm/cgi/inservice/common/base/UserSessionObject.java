package com.howbuy.crm.cgi.inservice.common.base;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2023/3/31 14:08
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class UserSessionObject implements Serializable {

    private static final long serialVersionUID = -5370304420416694144L;

    /**
     * 用户ID即conscode
     */
    private String userId;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 登录需要的cookie,下载报告需要绕过登录
     */
    private Map<String, String> cookieMap;

    /**
     *部门code
     */
    private String orgCode;
    /**
     *所属分组code
     */
    private String teamCode;

    /**
     * 广度 code
     */
    private String topgddata;

    /**
     * 指定菜单code - 最大广度
     */
    private Map<String, String> maxMenuGd;

}
