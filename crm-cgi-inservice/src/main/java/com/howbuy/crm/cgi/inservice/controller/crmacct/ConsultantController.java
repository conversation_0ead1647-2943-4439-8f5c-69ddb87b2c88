package com.howbuy.crm.cgi.inservice.controller.crmacct;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.base.ListBody;
import com.howbuy.crm.cgi.common.constants.AuthConstant;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.inservice.common.base.UserSessionObject;
import com.howbuy.crm.cgi.inservice.interceptor.UserThreadLocal;
import com.howbuy.crm.cgi.inservice.request.common.QueryConsParamVO;
import com.howbuy.crm.cgi.inservice.request.common.QueryConsultantRequest;
import com.howbuy.crm.cgi.inservice.vo.common.ConsultantVO;
import com.howbuy.crm.cgi.manager.domain.crmcore.ConsultantInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.crmcore.ConsultantOuterService;
import com.howbuy.crm.consultant.dto.ConsultantSimpleInfoDto;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 机构部门controller
 * @date 2023年8月3日 09:41:07
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping("/crmacct/consultant")
public class ConsultantController {

    @Autowired
    private ConsultantOuterService consultantOuterService;


    /**
     * @api {POST} /inner/crmacct/consultant/queryconsultant queryConsultant()
     * @apiVersion 1.0.0
     * @apiGroup ConsultantController
     * @apiName queryConsultant()
     * @apiDescription 查询投顾列表信息
     * @apiParam (请求体) {String} consCode 投顾code
     * @apiParam (请求体) {String} orgCode 组织架构code . 查询当前组织架构下的投顾
     * @apiParam (请求体) {String} recursive 是否为递归查询 组织架构下的投顾      1-是 0-否。 默认1-是
     * @apiParam (请求体) {String} status 状态（1-正常|有效； 0-删除|无效）
     * @apiParamExample 请求体示例
     * {"orgCode":"ZByH","recursive":"Dhat","consCode":"2aqzqqbg4","status":"grb45"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.rows 数据列表
     * @apiSuccess (响应结果) {String} data.rows.consCode 投顾编码
     * @apiSuccess (响应结果) {String} data.rows.consName 投顾名称
     * @apiSuccess (响应结果) {String} data.rows.status 状态（1-正常|有效； 0-删除|无效）
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Fl9mo","data":{"returnCode":"kKLV4N7MZQ","description":"ba","rows":[{"consName":"i","consCode":"l4I","status":"CCUymhQ8B"}]},"description":"R","timestampServer":"NW1lwX"}
     */
    @PostMapping("/queryconsultant")
    public CgiResponse<ListBody<ConsultantVO>> queryConsultant(@RequestBody QueryConsultantRequest request){
        List<ConsultantVO> voList= Lists.newArrayList();
        List<ConsultantInfoDTO>  dtoList;

        if(request.getFuzzyTerm() != null) {
            dtoList = consultantOuterService.getConsultantListByParam(request.getFuzzyTerm());
        }else {
            dtoList=consultantOuterService.
                    getConsultantList(request.getOrgCode(),
                            YesNoEnum.YES.getCode().equals(request.getRecursive()),
                            request.getStatus());
        }


        dtoList.forEach(dto->{
            ConsultantVO vo=new ConsultantVO();
            vo.setStatus(dto.getConsStatus());
            vo.setConsName(dto.getConsName());
            vo.setConsCode(dto.getConsCode());
            vo.setCenterOrgName(dto.getCenterOrgName());
            vo.setDistrictName(dto.getDistrictName());
            vo.setPartOrgName(dto.getPartOrgName());
            voList.add(vo);
        });

        return CgiResponse.ok(new ListBody<>(voList));
    }


    /**
     * @api {POST} /inner/crmacct/consultant/queryauthconsultant queryAuthConsultant()
     * @apiVersion 1.0.0
     * @apiGroup ConsultantController
     * @apiName queryAuthConsultant()
     * @apiDescription 根据权限，查询投顾列表
     * @apiParam (请求体) {String} selectOrgCode 选择的组织
     * @apiParam (请求体) {String} menuCode 菜单code
     * @apiParamExample 请求体示例
     * {"selectOrgCode":"rWg9xjhrS","menuCode":"mnLoT"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.rows 数据列表
     * @apiSuccess (响应结果) {String} data.rows.consCode 投顾编码
     * @apiSuccess (响应结果) {String} data.rows.consName 投顾名称
     * @apiSuccess (响应结果) {String} data.rows.status 状态（1-正常|有效； 0-删除|无效）
     * @apiSuccess (响应结果) {String} data.rows.districtName 所属区域
     * @apiSuccess (响应结果) {String} data.rows.centerOrgName 中心 -组织架构 名称
     * @apiSuccess (响应结果) {String} data.rows.partOrgName 分公司 -组织架构 名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"iM","data":{"rows":[{"centerOrgName":"RGerSW","districtName":"fSR5Vk0","partOrgName":"8","consName":"K7n","consCode":"j4J2fTDY0","status":"B9V5jc"}]},"description":"iw9r","timestampServer":"R0BUk"}
     */
    @PostMapping("/queryauthconsultant")
    public CgiResponse<ListBody<ConsultantVO>> queryAuthConsultant(@RequestBody QueryConsParamVO request) {
        log.info("根据用户权限，查询投顾列表，入参:{}", JSON.toJSONString(request));

        // 获取当前用户信息和权限
        UserSessionObject userSessionObject = UserThreadLocal.userSessionThreadLocal.get();
        String userId = userSessionObject.getUserId();
        
        // 获取用户数据访问权限
        ConsultantAuthData authData = getConsultantAuthData(userId, request.getMenuCode(), userSessionObject);
        
        // 判断是否只能看到自己
        if (shouldOnlyViewSelf(authData.getTopgd(), authData.getAuthorOrg(), request.getOrgCode())) {
            return CgiResponse.ok(new ListBody<>(getSelfConsultantList(userSessionObject)));
        }

        // 查询部门下的所有投顾
        List<ConsultantVO> voList = getConsultantListByOrgCode(request.getOrgCode());
        
        return CgiResponse.ok(new ListBody<>(voList));
    }
    
    /**
     * 获取投顾权限数据
     * @param userId 用户ID
     * @param menuCode 菜单代码
     * @param userSessionObject 用户会话对象
     * @return 投顾权限数据
     */
    private ConsultantAuthData getConsultantAuthData(String userId, String menuCode, UserSessionObject userSessionObject) {
        ConsultantAuthData authData = new ConsultantAuthData();
        
        // 查询当前投顾信息
        ConsultantSimpleInfoDto consultantInfo = consultantOuterService.getInfoByconsCode(userId);
        String outletCode = consultantInfo.getOutletCode();
        String teamCode = consultantInfo.getTeamCode();
        log.info("根据用户权限，查询投顾列表，登录人userId:{}, outletCode:{},teamCode:{}", 
                JSON.toJSONString(userId), JSON.toJSONString(outletCode), JSON.toJSONString(teamCode));
        
        // 获取数据广度
        String topgd = userSessionObject.getTopgddata();
        
        // 根据菜单获取数据广度
        if (StringUtil.isNotNullStr(menuCode)) {
            Map<String, String> menugd = userSessionObject.getMaxMenuGd();
            if (menugd != null && menugd.containsKey(menuCode)) {
                topgd = menugd.get(menuCode);
            }
        }
        log.info("根据用户权限，查询投顾列表，最大的数据广度topGd:{}", JSON.toJSONString(topgd));
        
        // 计算授权组织
        String authorOrg = determineAuthorizedOrg(topgd, outletCode, teamCode);
        log.info("根据用户权限，查询投顾列表，能看到的最大部门authorOrg:{}", JSON.toJSONString(authorOrg));
        
        authData.setTopgd(topgd);
        authData.setAuthorOrg(authorOrg);
        authData.setTeamCode(teamCode);
        authData.setOutletCode(outletCode);
        
        return authData;
    }
    
    /**
     * 确定用户有权限访问的组织
     * @param topgd 数据广度
     * @param outletCode 部门代码
     * @param teamCode 团队代码
     * @return 授权的组织代码
     */
    private String determineAuthorizedOrg(String topgd, String outletCode, String teamCode) {
        if (AuthConstant.DATARANGE_GD_ALL.equals(topgd) || AuthConstant.DATARANGE_GD_ALL_NOWFP.equals(topgd)) {
            return "0";
        } else if (AuthConstant.DATARANGE_GD_OUTLET.equals(topgd)) {
            return outletCode;
        } else if (AuthConstant.DATARANGE_GD_TEARM.equals(topgd)) {
            return teamCode;
        } else {
            return StringUtil.isNotNullStr(teamCode) ? teamCode : outletCode;
        }
    }
    
    /**
     * 判断是否只能查看自己
     * @param topgd 数据广度
     * @param authorOrg 授权组织
     * @param selectOrgCode 选择的组织代码
     * @return 是否只能查看自己
     */
    private boolean shouldOnlyViewSelf(String topgd, String authorOrg, String selectOrgCode) {
        return topgd == null || 
               ((AuthConstant.DATARANGE_GD_SELF.equals(topgd) || AuthConstant.DATARANGE_GD_SELFANDSER.equals(topgd))
                && authorOrg.equals(selectOrgCode));
    }
    
    /**
     * 获取自己的投顾信息
     * @param userSessionObject 用户会话对象
     * @return 投顾列表
     */
    private List<ConsultantVO> getSelfConsultantList(UserSessionObject userSessionObject) {
        List<ConsultantVO> voList = Lists.newArrayList();
        ConsultantVO cmConsultant = new ConsultantVO();
        cmConsultant.setConsCode(userSessionObject.getUserId());
        cmConsultant.setConsName(userSessionObject.getUserName());
        voList.add(cmConsultant);
        log.info("根据用户权限，查询投顾列表，登录人没有权限 或 只能查看自己的客户，直接返回登录人");
        return voList;
    }
    
    /**
     * 根据组织代码获取投顾列表
     * @param orgCode 组织代码
     * @return 投顾列表
     */
    private List<ConsultantVO> getConsultantListByOrgCode(String orgCode) {
        List<ConsultantVO> voList = Lists.newArrayList();
        
        // 查询当前部门下的所有投顾（递归部门下所有组织的投顾）
        List<ConsultantInfoDTO> dtoList = consultantOuterService.getConsultantList(orgCode, true, null);
        
        // 转换为VO
        dtoList.forEach(dto -> {
            voList.add(convertToConsultantVO(dto));
        });
        
        return voList;
    }
    
    /**
     * 将DTO转换为VO
     * @param dto 投顾信息DTO
     * @return 投顾VO
     */
    private ConsultantVO convertToConsultantVO(ConsultantInfoDTO dto) {
        ConsultantVO vo = new ConsultantVO();
        vo.setStatus(dto.getConsStatus());
        vo.setConsName(dto.getConsName());
        vo.setConsCode(dto.getConsCode());
        vo.setCenterOrgName(dto.getCenterOrgName());
        vo.setDistrictName(dto.getDistrictName());
        vo.setPartOrgName(dto.getPartOrgName());
        return vo;
    }
    
    /**
     * 投顾权限数据
     */
    private static class ConsultantAuthData {
        private String topgd;
        private String authorOrg;
        private String teamCode;
        private String outletCode;
        
        public String getTopgd() {
            return topgd;
        }
        
        public void setTopgd(String topgd) {
            this.topgd = topgd;
        }
        
        public String getAuthorOrg() {
            return authorOrg;
        }
        
        public void setAuthorOrg(String authorOrg) {
            this.authorOrg = authorOrg;
        }
        
        public String getTeamCode() {
            return teamCode;
        }
        
        public void setTeamCode(String teamCode) {
            this.teamCode = teamCode;
        }
        
        public String getOutletCode() {
            return outletCode;
        }
        
        public void setOutletCode(String outletCode) {
            this.outletCode = outletCode;
        }
    }
}
