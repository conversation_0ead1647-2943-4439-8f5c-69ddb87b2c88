/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.controller.crmacct;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.inservice.interceptor.UserThreadLocal;
import com.howbuy.crm.cgi.inservice.request.customer.AddCommunicateRequest;
import com.howbuy.crm.cgi.inservice.request.customer.QueryAssetReportRequest;
import com.howbuy.crm.cgi.inservice.service.crmacct.CommunicateService;
import com.howbuy.crm.cgi.inservice.vo.customer.AddCommunicateVO;
import com.howbuy.crm.cgi.inservice.vo.customer.QueryAssetReportVO;
import com.howbuy.crm.cgi.inservice.vo.customer.VisitInitVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 客户沟通记录控制器
 * @author: jin.wang03
 * @date: 2025-04-08 19:27:00
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping("/customer/communicate")
public class CommunicateController {

    @Autowired
    private CommunicateService communicateService;

    /**
     * @api {POST} /inner/customer/communicate/visitInit visitInit
     * @apiVersion 1.0.0
     * @apiGroup CommunicateController
     * @apiName visitInit
     * @apiDescription 获取客户沟通记录新增页初始化数据
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} consCode 投顾编码
     * @apiParamExample 请求体示例
     * {"consCode":"ABC123"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.projectManagerList 项目经理列表
     * @apiSuccess (响应结果) {String} data.projectManagerList.code 用户编码
     * @apiSuccess (响应结果) {String} data.projectManagerList.name 用户名称
     * @apiSuccess (响应结果) {Array} data.manageUserList 主管列表
     * @apiSuccess (响应结果) {String} data.manageUserList.code 用户编码
     * @apiSuccess (响应结果) {String} data.manageUserList.name 用户名称
     * @apiSuccess (响应结果) {Array} data.supervisorUserList 总部业资列表
     * @apiSuccess (响应结果) {String} data.supervisorUserList.code 用户编码
     * @apiSuccess (响应结果) {String} data.supervisorUserList.name 用户名称
     * @apiSuccess (响应结果) {Array} data.otherUserList 其他列表
     * @apiSuccess (响应结果) {String} data.otherUserList.code 用户编码
     * @apiSuccess (响应结果) {String} data.otherUserList.name 用户名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","data":{"projectManagerList":[{"code":"PM001","name":"项目经理1"}],"manageUserList":[{"code":"MG001","name":"主管1"}],"supervisorUserList":[{"code":"SV001","name":"总部业资1"}],"otherUserList":[{"code":"OT001","name":"其他人员1"}]},"description":"成功","timestampServer":"1649398490000"}
     */
    @PostMapping("/visitInit")
    public CgiResponse<VisitInitVO> visitInit() {
        String userId = UserThreadLocal.getCurrentUserId();
        VisitInitVO visitInitVO = communicateService.getVisitInitData(userId);
        return CgiResponse.ok(visitInitVO);
    }


    /**
     * @api {POST} /inner/customer/communicate/queryassetreport queryassetreport()
     * @apiVersion 1.0.0
     * @apiGroup CommunicateController
     * @apiName queryassetreport()
     * @apiDescription 查询IPS报告列表接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} consCustNo 客户号
     * @apiParamExample 请求体示例
     * {
     *     "consCustNo": "123456"
     * }
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.reportList 报告列表
     * @apiSuccess (响应结果) {String} data.reportList.reportId 报告ID
     * @apiSuccess (响应结果) {String} data.reportList.reportName 报告名称
     * @apiSuccess (响应结果) {String} data.reportList.createTime 创建时间
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *     "code": "0000",
     *     "data": {
     *         "reportList": [{
     *             "reportId": "123",
     *             "reportName": "IPS报告",
     *             "createTime": "2024-02-28 10:00:00"
     *         }]
     *     },
     *     "description": "成功",
     *     "timestampServer": "1677196800000"
     * }
     */
    @PostMapping("/queryassetreport")
    public CgiResponse<QueryAssetReportVO> queryassetreport(@RequestBody QueryAssetReportRequest request) {
        request.setConsCode(UserThreadLocal.getCurrentUserId());
        QueryAssetReportVO vo = communicateService.queryAssetReport(request);
        return CgiResponse.ok(vo);
    }

    /**
     * @api {POST} /inner/customer/communicate/add add()
     * @apiVersion 1.0.0
     * @apiGroup CommunicateController
     * @apiName add
     * @apiDescription 新增客户沟通记录
     * @apiParam (请求体) {Object} communicateReq 沟通记录信息
     * @apiParam (请求体) {String} communicateReq.consCustNo 客户号
     * @apiParam (请求体) {String} communicateReq.visitDt 拜访日期(格式YYYYMMDD)
     * @apiParam (请求体) {String} communicateReq.visittype 沟通方式(见面、线上会议、电话等)
     * @apiParam (请求体) {String} communicateReq.commcontent 沟通内容摘要
     * @apiParam (请求体) {String} communicateReq.consulttype 沟通类型
     * @apiParam (请求体) {Object} bookingReq 预约信息
     * @apiParam (请求体) {Boolean} bookingReq.hasRemind 是否新增提醒
     * @apiParam (请求体) {String} bookingReq.nextvisittype 预约沟通方式
     * @apiParam (请求体) {String} bookingReq.nextdt 预约日期(格式YYYYMMDD)
     * @apiParam (请求体) {String} bookingReq.nextstarttime 预约开始时间(格式HH:MM)
     * @apiParam (请求体) {String} bookingReq.nextendtime 预约结束时间(格式HH:MM)
     * @apiParam (请求体) {String} bookingReq.nextvisitcontent 预约内容
     * @apiParam (请求体) {Object} visitMinutesReq 拜访纪要信息
     * @apiParam (请求体) {String} visitMinutesReq.visitPurpose 拜访目的(多选，逗号分隔，如"1,2,3")
     * @apiParam (请求体) {String} visitMinutesReq.visitPurposeOther 拜访目的其他说明
     * @apiParam (请求体) {String} visitMinutesReq.assetReportId IPS报告ID
     * @apiParam (请求体) {String} visitMinutesReq.giveInformation 提供资料
     * @apiParam (请求体) {String} visitMinutesReq.attendRole 客户参与人员及角色
     * @apiParam (请求体) {String} visitMinutesReq.productServiceFeedback 对产品或服务的具体反馈
     * @apiParam (请求体) {String} visitMinutesReq.ipsFeedback 对于IPS报告反馈
     * @apiParam (请求体) {String} visitMinutesReq.addAmountRmb 近期可用于加仓的金额(人民币)
     * @apiParam (请求体) {String} visitMinutesReq.addAmountForeign 近期可用于加仓的金额(外币)
     * @apiParam (请求体) {String} visitMinutesReq.focusAsset 近期关注的资产类别或具体产品
     * @apiParam (请求体) {String} visitMinutesReq.estimateNeedBusiness 评估客户需求
     * @apiParam (请求体) {String} visitMinutesReq.nextPlan 下一步工作计划
     * @apiParam (请求体) {Array} accompanyingList 陪访人列表
     * @apiParam (请求体) {String} accompanyingList.accompanyingType 陪访人类型(1-项目经理 2-主管 3-总部业资 4-其他)
     * @apiParam (请求体) {String} accompanyingList.accompanyingUserId 陪访人用户ID
     * @apiParamExample 请求体示例
     * {"bookingReq":{"nextvisitcontent":"uioF06","hasRemind":true,"nextvisittype":"9o2K78oe","nextendtime":"XECz","nextstarttime":"77V","nextdt":"JfLyiBV"},"visitMinutesReq":{"giveInformation":"G8YvT","attendRole":"NBjIN","visitPurposeOther":"SU","estimateNeedBusiness":"ukLE8r3","visitPurpose":"rwiE","addAmountForeign":"qeAg8uF","nextPlan":"3UDo","productServiceFeedback":"bA1YUxWP","addAmountRmb":"FJR","focusAsset":"9ZA","ipsFeedback":"zQ4kh","assetReportId":"RSIDqn"},"communicateReq":{"consulttype":"B","consCustNo":"jDfpuVaf4C","visitDt":"LjP","visittype":"mLYel","commcontent":"0"},"accompanyingList":[{"accompanyingType":"W5UD","accompanyingUserId":"7o"}]}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.communicateId 沟通记录ID
     * @apiSuccess (响应结果) {String} data.visitMinutesId 拜访纪要ID
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample 响应结果示例
     * {"code":"phcC5bLN","data":{"communicateId":"PyCUH","visitMinutesId":"d4PbBH"},"description":"BkyqGl9Z7n","timestampServer":"ZHmj"}
     */
    @PostMapping("/add")
    public CgiResponse<AddCommunicateVO> add(@RequestBody AddCommunicateRequest request) {
        log.info("新增客户沟通记录接口开始，request:{}", JSON.toJSONString(request));
        request.setOperator(UserThreadLocal.getCurrentUserId());
        AddCommunicateVO result = communicateService.addCommunicate(request);
        log.info("新增客户沟通记录接口结束");
        return CgiResponse.ok(result);
    }
}