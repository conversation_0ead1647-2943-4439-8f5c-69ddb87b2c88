package com.howbuy.crm.cgi.inservice.controller.crmacct;

import com.howbuy.crm.cgi.common.base.BodyRequest;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.inservice.request.common.QueryCustInfoRequest;
import com.howbuy.crm.cgi.inservice.request.common.QueryOrgRequest;
import com.howbuy.crm.cgi.inservice.service.crmacct.AuthService;
import com.howbuy.crm.cgi.inservice.util.AuthUtil;
import com.howbuy.crm.cgi.inservice.vo.common.PageAuthOrgAndConsVO;
import com.howbuy.crm.cgi.manager.outerservice.crmcore.OrganazitonOuterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description: 机构部门controller
 * @date 2023年8月3日 09:41:07
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping("/crmacct/organization")
public class OrganizationController {

    @Autowired
    private OrganazitonOuterService organazitonOuterService;
    @Autowired
    private AuthService authService;


    /**
     * @api {POST} /inner/crmacct/organization/queryauthorgtree
     * @apiVersion 1.0.0
     * @apiGroup OrganizationController
     * @apiName queryAuthOrgTree
     * @apiDescription 查询当前登录用户，根据权限可见的部门机构列表
     * @apiParam (请求体) {Object} requestBody
     * @apiParamExample 请求体示例
     * null
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.organizationList 默认 组织架构列表
     * @apiSuccess (响应结果) {String} data.organizationList.orgCode 部门编码
     * @apiSuccess (响应结果) {String} data.organizationList.orgName 部门名称
     * @apiSuccess (响应结果) {String} data.organizationList.parentOrgCode 父部门编码
     * @apiSuccess (响应结果) {String} data.organizationList.status 状态（0：正常；1：删除）
     * @apiSuccess (响应结果) {String} data.defaultOrgCode 默认选中的 组织结构 code
     * @apiSuccess (响应结果) {Array} data.consultList 选中的组织结构 对应的 投顾列表
     * @apiSuccess (响应结果) {String} data.consultList.consCode 投顾编码
     * @apiSuccess (响应结果) {String} data.consultList.consName 投顾名称
     * @apiSuccess (响应结果) {String} data.consultList.status 状态（1-正常|有效； 0-删除|无效）
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"rDY","data":{"returnCode":"gUsq","description":"5zHhNHQnx","rows":[{"orgName":"BjmSTq","parentOrgCode":"pWKADK","orgCode":"RQ1WKzS9","status":"QR"}]},"description":"dPM","timestampServer":"CI3PIT"}
     */
    @PostMapping("/queryauthorgtree")
    public CgiResponse<PageAuthOrgAndConsVO> queryAuthOrgTree(@RequestBody BodyRequest request ){
        PageAuthOrgAndConsVO authVo=authService.getAuthOrgAndConsList(null);
        return CgiResponse.ok(authVo);
    }


    /**
     * @api {POST}  /inner/crmacct/organization/queryauthorgtree2
     * @apiVersion 1.0.0
     * @apiGroup OrganizationController
     * @apiName queryauthorgtree2
     * @apiParam (请求体) {String} module 菜单编码
     * @apiParamExample 请求体示例
     * {"module":"fpKYozCDS"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.organizationList 默认 组织架构列表
     * @apiSuccess (响应结果) {String} data.organizationList.orgCode 部门编码
     * @apiSuccess (响应结果) {String} data.organizationList.orgName 部门名称
     * @apiSuccess (响应结果) {String} data.organizationList.parentOrgCode 父部门编码
     * @apiSuccess (响应结果) {String} data.organizationList.status 状态（0：正常；1：删除）
     * @apiSuccess (响应结果) {String} data.defaultOrgCode 默认选中的 组织结构 code
     * @apiSuccess (响应结果) {Array} data.consultList 选中的组织结构 对应的 投顾列表
     * @apiSuccess (响应结果) {String} data.consultList.consCode 投顾编码
     * @apiSuccess (响应结果) {String} data.consultList.consName 投顾名称
     * @apiSuccess (响应结果) {String} data.consultList.status 状态（1-正常|有效； 0-删除|无效）
     * @apiSuccess (响应结果) {String} data.consultList.districtName 所属区域
     * @apiSuccess (响应结果) {String} data.consultList.centerOrgName 中心 -组织架构 名称
     * @apiSuccess (响应结果) {String} data.consultList.partOrgName 分公司 -组织架构 名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"1KXgWZBF","data":{"organizationList":[{"orgName":"T4P","parentOrgCode":"a52","orgCode":"Ow","status":"x5TepB"}],"consultList":[{"centerOrgName":"Sd7","districtName":"BjHUbIQitJ","partOrgName":"dszbRZ","consName":"7t5","consCode":"iK51JgGf","status":"i"}],"defaultOrgCode":"i5qe7dBD"},"description":"PO0oxW","timestampServer":"DaoENhoBm"}
     */
    @PostMapping("/queryauthorgtree2")
    public CgiResponse<PageAuthOrgAndConsVO> queryauthorgtree2(@RequestBody QueryOrgRequest request){
        PageAuthOrgAndConsVO authVo= null;
        try {
            authVo = authService.getAuthOrgAndConsList2(request.getModule(), request.getNotAuthor());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return CgiResponse.ok(authVo);
    }


    /**
     * @api {POST} /inner/crmacct/organization/queryauthorgtree3 queryAuthorgTree3()
     * @apiVersion 1.0.0
     * @apiGroup OrganizationController
     * @apiName queryAuthorgTree3()
     * @apiDescription 根据登录人 指定菜单的权限，获取组织和人员
     * @apiParam (请求体) {String} module 菜单编码
     * @apiParam (请求体) {String} notAuthor 是否授权 1-是
     * @apiParamExample 请求体示例
     * {"notAuthor":"MkQ9gXr1","module":"V"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.organizationList 默认 组织架构列表
     * @apiSuccess (响应结果) {String} data.organizationList.orgCode 部门编码
     * @apiSuccess (响应结果) {String} data.organizationList.orgName 部门名称
     * @apiSuccess (响应结果) {String} data.organizationList.parentOrgCode 父部门编码
     * @apiSuccess (响应结果) {String} data.organizationList.status 状态（0：正常；1：删除）
     * @apiSuccess (响应结果) {String} data.defaultOrgCode 默认选中的 组织结构 code
     * @apiSuccess (响应结果) {Array} data.consultList 选中的组织结构 对应的 投顾列表
     * @apiSuccess (响应结果) {String} data.consultList.consCode 投顾编码
     * @apiSuccess (响应结果) {String} data.consultList.consName 投顾名称
     * @apiSuccess (响应结果) {String} data.consultList.status 状态（1-正常|有效； 0-删除|无效）
     * @apiSuccess (响应结果) {String} data.consultList.districtName 所属区域
     * @apiSuccess (响应结果) {String} data.consultList.centerOrgName 中心 -组织架构 名称
     * @apiSuccess (响应结果) {String} data.consultList.partOrgName 分公司 -组织架构 名称
     * @apiSuccess (响应结果) {String} data.defaultConsCode 默认选中的 投顾 code
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"jbJbolB4j","data":{"defaultConsCode":"RGdNwUz","organizationList":[{"orgName":"qjhhvY7","parentOrgCode":"7GGh","orgCode":"pDnFS","status":"60"}],"consultList":[{"centerOrgName":"yCeEp1xrI","districtName":"mEW","partOrgName":"iqOG6wU","consName":"fk","consCode":"BJdv5F8V","status":"3e6"}],"defaultOrgCode":"pDKSapuV"},"description":"Xll","timestampServer":"bWvtC7fk"}
     */
    @PostMapping("/queryauthorgtree3")
    public CgiResponse<PageAuthOrgAndConsVO> queryAuthorgTree3(@RequestBody QueryOrgRequest request) {
        PageAuthOrgAndConsVO authVo = null;
        try {
            authVo = authService.getAuthOrgAndConsList3(request.getModule(), request.getNotAuthor());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return CgiResponse.ok(authVo);
    }

    /**
     * @api {POST} /inner/crmacct/organization/queryallorgtree queryAllOrgTree()
     * @apiVersion 1.0.0
     * @apiGroup OrganizationController
     * @apiName queryAllOrgTree()
     * @apiDescription 查询所有的部门机构列表
     * @apiParam (请求体) {Object} requestBody
     * @apiParamExample 请求体示例
     * null
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.organizationList 默认 组织架构列表
     * @apiSuccess (响应结果) {String} data.organizationList.orgCode 部门编码
     * @apiSuccess (响应结果) {String} data.organizationList.orgName 部门名称
     * @apiSuccess (响应结果) {String} data.organizationList.parentOrgCode 父部门编码
     * @apiSuccess (响应结果) {String} data.organizationList.status 状态（0：正常；1：删除）
     * @apiSuccess (响应结果) {String} data.defaultOrgCode 默认选中的 组织结构 code
     * @apiSuccess (响应结果) {Array} data.consultList 选中的组织结构 对应的 投顾列表
     * @apiSuccess (响应结果) {String} data.consultList.consCode 投顾编码
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"IxxOOV","data":{"returnCode":"tSFG","description":"pHA4W","rows":[{"orgName":"q","parentOrgCode":"Ss","orgCode":"up1neDRQ","status":"szHZfFRsv"}]},"description":"eIEcPm","timestampServer":"8eAqD5LJO"}
     */
    @PostMapping("/queryallorgtree")
    public CgiResponse<PageAuthOrgAndConsVO> queryAllOrgTree(@RequestBody BodyRequest request ){
        PageAuthOrgAndConsVO authVo=new PageAuthOrgAndConsVO();
        String topOrgCode=AuthUtil.TOP_ORG_CODE;
        authVo.setDefaultOrgCode(topOrgCode);
        authVo.setOrganizationList(authService.getListByTopOrgCode(topOrgCode));
        //查询 投顾列表 包含删除的
        authVo.setConsultList(authService.getConsultList(topOrgCode, null));
        return CgiResponse.ok(authVo);
    }


}
