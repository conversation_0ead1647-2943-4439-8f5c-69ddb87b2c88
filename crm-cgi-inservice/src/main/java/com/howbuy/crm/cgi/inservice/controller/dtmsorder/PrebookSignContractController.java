/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.controller.dtmsorder;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.inservice.request.dtmsorder.QueryPrebookSignContractRequest;
import com.howbuy.crm.cgi.inservice.vo.dtmsorder.PrebookSignContractVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.PrebookSignContractDTO;
import com.howbuy.crm.cgi.manager.outerservice.CrmResult;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.CustContractSignOuterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * @description: (查询预约签署的协议code)
 * <AUTHOR>
 * @date 2023/5/17 14:36
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/dtmsorder/prebooksigncontract")
public class PrebookSignContractController {

    @Autowired
    private CustContractSignOuterService custContractSignOuterService;

    /**
     * @api {POST} /dtmsorder/prebooksigncontract/querybypreid queryByPreid
     * @apiVersion 1.0.0
     * @apiGroup PrebookSignContractController
     * @apiName queryByPreid
     * @apiDescription 查询预约签署的协议code
     * @apiParam (请求体) {String} prebookId 预约ID
     * @apiParamExample 请求体示例
     * {"prebookId":"eqpJ5QoVrB"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.fileCodes 预约ID
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"wDDwO4L","data":{"returnCode":"mjQlLAMJP","fileCodes":["UOfxDgWLB"],"description":"L861czXNS"},"description":"dxzZm","timestampServer":"hWzya"}
     */
    @PostMapping("/querybypreid")
    public CgiResponse<PrebookSignContractVO> queryByPreid(@RequestBody QueryPrebookSignContractRequest queryPrebookSignContractRequest) {
        PrebookSignContractDTO data = custContractSignOuterService.queryByPreid(queryPrebookSignContractRequest.getPrebookId());
        PrebookSignContractVO prebookSignContractVO = new PrebookSignContractVO();
        if( Objects.nonNull(data)) {
            prebookSignContractVO.setFileCodes(data.getFileCodes());
        }
        return CgiResponse.ok(prebookSignContractVO);
    }
}