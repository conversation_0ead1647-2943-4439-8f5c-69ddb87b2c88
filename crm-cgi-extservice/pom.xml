<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.howbuy.crm</groupId>
        <artifactId>crm-cgi</artifactId>
        <version>2.0.0.0-RELEASE</version>
    </parent>

    <artifactId>crm-cgi-extservice</artifactId>
    <name>crm-cgi-extservice</name>
    <properties>
        <powermock.version>2.0.7</powermock.version>
        <com.howbuy.howbuy-cms-client>h5-20230131-sm-lcjzzty-opt-RELEASE</com.howbuy.howbuy-cms-client>
    </properties>
    <dependencies>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.howbuy.dfile</groupId>
            <artifactId>howbuy-dfile-service</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-core</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy.dfile</groupId>
            <artifactId>howbuy-dfile-impl-local</artifactId>
        </dependency>

        <dependency>
            <groupId>com.howbuy.dfile</groupId>
            <artifactId>howbuy-dfile-impl-webdav</artifactId>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-trace</artifactId>
        </dependency>

        <dependency>
            <groupId>com.howbuy.dfile</groupId>
            <artifactId>howbuy-dfile-impl-nfs</artifactId>
        </dependency>
        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-cgi-manager</artifactId>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-starter-session</artifactId>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-cms-client</artifactId>
            <version>${com.howbuy.howbuy-cms-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy.dtms</groupId>
            <artifactId>dtms-order-client</artifactId>
        </dependency>

        <!--单元测试 start-->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-testng</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4-rule-agent</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <!--单元测试 end-->
        <!--中文转拼音  start -->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
        </dependency>
        <!--中文转拼音  end -->
    </dependencies>
</project>