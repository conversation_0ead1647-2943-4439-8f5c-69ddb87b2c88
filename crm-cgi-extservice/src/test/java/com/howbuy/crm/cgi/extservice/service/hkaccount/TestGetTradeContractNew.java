/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.hkaccount;

import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.contract.ContractFileBizTypeEnum;
import com.howbuy.crm.cgi.extservice.request.account.QueryTradeContractNewRequest;
import com.howbuy.crm.cgi.extservice.service.TestBase;
import com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct.OpenAcctService;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ContractFileVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenCustFileVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenFileVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.ContractInfoNewVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.agreement.SupplementalAgreementDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.HkFundOrderContractResponseDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.HkFundOrderContractSignDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.HkFundOrderContractSignDtlDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.HkPiggyAgreementDealResponseDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.HkFundOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.agreement.SupplementalAgreementOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkPiggyBankOuterService;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @description: HkTradeContractService#getTradeContractNew方法单元测试
 * @author: system
 * @date: 2024/12/19
 * @since JDK 1.8
 */
public class TestGetTradeContractNew extends TestBase {

    @InjectMocks
    private HkTradeContractService hkTradeContractService;

    @Mock
    private HkFundOuterService hkFundOuterService;

    @Mock
    private QueryFundBasicInfoOuterService fundBasicInfoOuterService;

    @Mock
    private OpenAcctService openAcctService;

    @Mock
    private SupplementalAgreementOuterService supplementalAgreementOuterService;

    @Mock
    private HkPiggyBankOuterService hkPiggyBankOuterService;

    @Before
    public void setUp() {
        // 初始化测试数据
    }

    /**
     * @description: 当所有外部服务返回空数据时，返回空列表
     */
    @Test
    public void testGetTradeContractNew_empty() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");

        // Mock外部服务返回空数据
        PowerMockito.when(fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(Mockito.anyString())).thenReturn(Arrays.asList("HK0001"));
        PowerMockito.when(hkFundOuterService.queryHkFundOrderContractInfo(Mockito.anyString(), Mockito.anyList(), Mockito.anyList())).thenReturn(null);
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(new ArrayList<>());
        PowerMockito.when(openAcctService.getOpenCustFileVO()).thenReturn(new OpenCustFileVO());
        PowerMockito.when(hkPiggyBankOuterService.queryPiggySignContractRecord(Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(new HkPiggyAgreementDealResponseDTO());

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(CollectionUtils.isEmpty(result.getContractFileList()));
        Assert.assertTrue(CollectionUtils.isEmpty(result.getOpenCustPiggyFileList()));
        Assert.assertTrue(CollectionUtils.isEmpty(result.getChangeCustPiggyFileList()));
    }

    /**
     * @description: 正常场景，有交易合同数据，按签署时间倒序排序
     */
    @Test
    public void testGetTradeContractNew_normal() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");

        // Mock基金信息数据
        PowerMockito.when(fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(Mockito.anyString())).thenReturn(Arrays.asList("HK0001"));

        // 创建模拟的交易合同响应数据
        HkFundOrderContractResponseDTO contractResponseDTO = createMockContractResponseDTO();
        PowerMockito.when(hkFundOuterService.queryHkFundOrderContractInfo(Mockito.anyString(), Mockito.anyList(), Mockito.anyList())).thenReturn(contractResponseDTO);

        // Mock其他服务返回空数据
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(new ArrayList<>());
        PowerMockito.when(openAcctService.getOpenCustFileVO()).thenReturn(new OpenCustFileVO());
        PowerMockito.when(hkPiggyBankOuterService.queryPiggySignContractRecord(Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(new HkPiggyAgreementDealResponseDTO());

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertFalse(CollectionUtils.isEmpty(result.getContractFileList()));
        Assert.assertEquals(2, result.getContractFileList().size());

        // 验证排序逻辑：应该按签署时间倒序排列
        List<ContractFileVO> contractFiles = result.getContractFileList();
        Assert.assertEquals("2024-12-01 10:00", contractFiles.get(0).getSignDate());
        Assert.assertEquals("2024-11-15 14:30", contractFiles.get(1).getSignDate());
    }

    /**
     * @description: 查询指定合同类型为交易订单
     */
    @Test
    public void testGetTradeContractNew_tradingOrderType() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        request.setContractTypes(ContractFileBizTypeEnum.TRADING_ORDER.getCode());

        // Mock基金信息数据
        PowerMockito.when(fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(Mockito.anyString())).thenReturn(Arrays.asList("HK0001"));

        // 创建模拟数据
        HkFundOrderContractResponseDTO contractResponseDTO = createMockContractResponseDTO();
        PowerMockito.when(hkFundOuterService.queryHkFundOrderContractInfo(Mockito.anyString(), Mockito.anyList(), Mockito.anyList())).thenReturn(contractResponseDTO);

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertFalse(CollectionUtils.isEmpty(result.getContractFileList()));
        Assert.assertEquals(2, result.getContractFileList().size());
    }

    /**
     * @description: 查询指定合同类型为补签协议
     */
    @Test
    public void testGetTradeContractNew_reSignContractType() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        request.setContractTypes(ContractFileBizTypeEnum.RE_SIGN_CONTRACT.getCode());

        // 创建模拟的补签协议数据
        List<SupplementalAgreementDTO> agreementList = createMockSupplementalAgreementList();
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(agreementList);

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertFalse(CollectionUtils.isEmpty(result.getContractFileList()));
        Assert.assertEquals(1, result.getContractFileList().size());
    }

    /**
     * @description: 查询指定合同类型为开户文件
     */
    @Test
    public void testGetTradeContractNew_openAccountFileType() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        request.setContractTypes(ContractFileBizTypeEnum.OPEN_ACCOUNT_FILE.getCode());

        // 创建模拟的开户合同文件数据
        ContractInfoNewVO.OpenContractFileVO openContractFileVO = createMockOpenContractFileVO();
        PowerMockito.when(openAcctService.getOpenContractFileVO()).thenReturn(openContractFileVO);

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getOpenContractFileVO());
        Assert.assertFalse(CollectionUtils.isEmpty(result.getOpenContractFileVO().getOpenFileList()));
        Assert.assertEquals(1, result.getOpenContractFileVO().getOpenFileList().size());
        Assert.assertEquals("开户申请表.pdf", result.getOpenContractFileVO().getOpenFileList().get(0).getFilaName());
        Assert.assertEquals("开户文件", result.getOpenContractFileVO().getTypeName());
    }

    /**
     * @description: 测试时间范围过滤功能
     */
    @Test
    public void testGetTradeContractNew_timeRangeFilter() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        request.setStartTime("20240101");
        request.setEndTime("********");

        // Mock基金信息数据
        PowerMockito.when(fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(Mockito.anyString())).thenReturn(Arrays.asList("HK0001"));

        // 创建模拟数据
        HkFundOrderContractResponseDTO contractResponseDTO = createMockContractResponseDTO();
        PowerMockito.when(hkFundOuterService.queryHkFundOrderContractInfo(Mockito.anyString(), Mockito.anyList(), Mockito.anyList())).thenReturn(contractResponseDTO);
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(new ArrayList<>());
        PowerMockito.when(openAcctService.getOpenCustFileVO()).thenReturn(new OpenCustFileVO());
        PowerMockito.when(hkPiggyBankOuterService.queryPiggySignContractRecord(Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(new HkPiggyAgreementDealResponseDTO());

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        // 验证时间范围过滤逻辑
        if (result.getContractFileList() != null) {
            for (ContractFileVO contractFile : result.getContractFileList()) {
                String signDate = contractFile.getSignDate();
                if (signDate != null) {
                    Assert.assertNotNull("签署时间不应为null", signDate);
                }
            }
        }
    }

    /**
     * 创建模拟的交易合同响应数据
     */
    private HkFundOrderContractResponseDTO createMockContractResponseDTO() {
        HkFundOrderContractResponseDTO responseDTO = new HkFundOrderContractResponseDTO();
        
        List<HkFundOrderContractSignDTO> contractSignList = new ArrayList<>();
        
        // 创建第一个合同签署记录
        HkFundOrderContractSignDTO contractSign1 = new HkFundOrderContractSignDTO();
        contractSign1.setDealNo("ORDER001");
        contractSign1.setOrderStatus("3"); // 确认成功
        contractSign1.setMiddleBusiCode("1122"); // 申购
        contractSign1.setFundCode("HK0001");
        contractSign1.setFundName("测试基金1");
        contractSign1.setFundAbbr("测试基金1");
        contractSign1.setSignDate("2024-12-01 10:00:00");
        
        List<HkFundOrderContractSignDtlDTO> detailList1 = new ArrayList<>();
        HkFundOrderContractSignDtlDTO detail1 = new HkFundOrderContractSignDtlDTO();
        detail1.setFileType("01");
        detail1.setFileName("申购合同1.pdf");
        detail1.setFileTypeDesc("申购合同");
        detail1.setFilePath("/contract/order001.pdf");
        detailList1.add(detail1);
        contractSign1.setOrderContractSignDtlVOList(detailList1);
        
        // 创建第二个合同签署记录
        HkFundOrderContractSignDTO contractSign2 = new HkFundOrderContractSignDTO();
        contractSign2.setDealNo("ORDER002");
        contractSign2.setOrderStatus("3"); // 确认成功
        contractSign2.setMiddleBusiCode("1124"); // 赎回
        contractSign2.setFundCode("HK0001");
        contractSign2.setFundName("测试基金1");
        contractSign2.setFundAbbr("测试基金1");
        contractSign2.setSignDate("2024-11-15 14:30:00");
        
        List<HkFundOrderContractSignDtlDTO> detailList2 = new ArrayList<>();
        HkFundOrderContractSignDtlDTO detail2 = new HkFundOrderContractSignDtlDTO();
        detail2.setFileType("02");
        detail2.setFileName("赎回合同1.pdf");
        detail2.setFileTypeDesc("赎回合同");
        detail2.setFilePath("/contract/order002.pdf");
        detailList2.add(detail2);
        contractSign2.setOrderContractSignDtlVOList(detailList2);
        
        contractSignList.add(contractSign1);
        contractSignList.add(contractSign2);
        
        responseDTO.setOrderContractSignVOList(contractSignList);
        return responseDTO;
    }

    /**
     * 创建模拟的补签协议数据
     */
    private List<SupplementalAgreementDTO> createMockSupplementalAgreementList() {
        List<SupplementalAgreementDTO> agreementList = new ArrayList<>();

        SupplementalAgreementDTO agreement = new SupplementalAgreementDTO();
        agreement.setFundCode("HK0001");
        agreement.setFundName("测试基金");
        agreement.setAgreementName("补充协议");
        agreement.setAgreementUrl("/path/agreement.pdf");
        agreement.setSignDate("2024-03-01 10:00:00");
        agreementList.add(agreement);

        return agreementList;
    }

    /**
     * 创建模拟的开户文件数据
     */
    private OpenCustFileVO createMockOpenCustFileVO() {
        OpenCustFileVO openCustFileVO = new OpenCustFileVO();

        List<OpenFileVO> openFileList = new ArrayList<>();
        OpenFileVO openFile = new OpenFileVO("开户申请表.pdf", "/open/account.pdf", "开户文件", "001", "1");
        openFileList.add(openFile);

        openCustFileVO.setOpenFileVOList(openFileList);
        openCustFileVO.setEbrokerID("EBROKER001");
        openCustFileVO.setEmailMask("test***@example.com");

        return openCustFileVO;
    }

    /**
     * 创建模拟的开户合同文件数据
     */
    private ContractInfoNewVO.OpenContractFileVO createMockOpenContractFileVO() {
        ContractInfoNewVO.OpenContractFileVO openContractFileVO = new ContractInfoNewVO.OpenContractFileVO();

        List<ContractInfoNewVO.OpenFileInfo> openFileList = new ArrayList<>();
        ContractInfoNewVO.OpenFileInfo openFile = new ContractInfoNewVO.OpenFileInfo();
        openFile.setFilaName("开户申请表.pdf");
        openFile.setFileUrl("/open/account.pdf");
        openFile.setFileType("001");
        openFile.setFileTypeName("开户文件");
        openFile.setFileUrlType("1");
        openFile.setUpDt("********");
        openFileList.add(openFile);

        openContractFileVO.setOpenFileList(openFileList);
        openContractFileVO.setSignDate("********");
        openContractFileVO.setTypeName("开户文件");
        openContractFileVO.setEmailMask("test***@example.com");

        return openContractFileVO;
    }

    /**
     * @description: 测试时间范围类型功能
     */
    @Test
    public void testGetTradeContractNew_timeRangeType() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        request.setTimeRangeType("1"); // 近一月
        request.setContractTypes(ContractFileBizTypeEnum.TRADING_ORDER.getCode());

        // Mock基金信息数据
        PowerMockito.when(fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(Mockito.anyString())).thenReturn(Arrays.asList("HK0001"));

        // 创建模拟数据
        HkFundOrderContractResponseDTO contractResponseDTO = createMockContractResponseDTO();
        PowerMockito.when(hkFundOuterService.queryHkFundOrderContractInfo(Mockito.anyString(), Mockito.anyList(), Mockito.anyList())).thenReturn(contractResponseDTO);

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        // 验证时间范围设置
        Assert.assertNotNull(request.getStartTime());
        Assert.assertNotNull(request.getEndTime());
    }

    /**
     * @description: 测试查询储蓄罐合同类型
     */
    @Test
    public void testGetTradeContractNew_piggyBankType() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        request.setContractTypes(ContractFileBizTypeEnum.PIGGY_SIGN_FILE.getCode());

        // 创建模拟的储蓄罐数据
        HkPiggyAgreementDealResponseDTO piggyResponseDTO = new HkPiggyAgreementDealResponseDTO();
        PowerMockito.when(hkPiggyBankOuterService.queryPiggySignContractRecord(Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(piggyResponseDTO);

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(CollectionUtils.isEmpty(result.getOpenCustPiggyFileList()));
        Assert.assertTrue(CollectionUtils.isEmpty(result.getChangeCustPiggyFileList()));
    }

    /**
     * @description: 测试无效合同类型异常
     */
    @Test(expected = NullPointerException.class)
    public void testGetTradeContractNew_invalidContractType() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        request.setContractTypes("999"); // 无效的合同类型

        // 执行测试，期望抛出NullPointerException
        hkTradeContractService.getTradeContractNew(request);
    }

    /**
     * @description: 测试空参数场景
     */
    @Test
    public void testGetTradeContractNew_nullParameters() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode(null);
        request.setHkCustNo(null);

        // Mock外部服务返回空数据
        PowerMockito.when(fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(Mockito.anyString())).thenReturn(new ArrayList<>());
        PowerMockito.when(hkFundOuterService.queryHkFundOrderContractInfo(Mockito.anyString(), Mockito.anyList(), Mockito.anyList())).thenReturn(null);
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(new ArrayList<>());
        PowerMockito.when(openAcctService.getOpenCustFileVO()).thenReturn(new OpenCustFileVO());
        PowerMockito.when(hkPiggyBankOuterService.queryPiggySignContractRecord(Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(new HkPiggyAgreementDealResponseDTO());

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(CollectionUtils.isEmpty(result.getContractFileList()));
    }

    /**
     * @description: 测试时间范围类型转换错误场景
     */
    @Test(expected = NumberFormatException.class)
    public void testGetTradeContractNew_timeRangeTypeError() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        request.setTimeRangeType("invalid"); // 无效的时间范围类型，会导致Integer.parseInt抛出NumberFormatException
        request.setContractTypes(ContractFileBizTypeEnum.TRADING_ORDER.getCode()); // 设置有效的合同类型

        // 执行测试，期望抛出NumberFormatException
        // 当timeRangeType="invalid"时，第161行Integer.parseInt(request.getTimeRangeType())会抛出NumberFormatException
        // 由于业务代码没有捕获这个异常，它会向上抛出
        hkTradeContractService.getTradeContractNew(request);

        // 如果执行到这里说明没有抛出预期的异常，测试失败
        Assert.fail("应该抛出NumberFormatException");
    }

    /**
     * @description: 测试混合场景 - 不指定合同类型查询所有合同
     */
    @Test
    public void testGetTradeContractNew_allContractTypes() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        // 明确设置contractTypes为null或空字符串，确保查询所有合同
        request.setContractTypes(null);

        // Mock基金信息数据
        PowerMockito.when(fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(Mockito.anyString())).thenReturn(Arrays.asList("HK0001"));

        // Mock交易合同数据
        HkFundOrderContractResponseDTO contractResponseDTO = createMockContractResponseDTO();
        PowerMockito.when(hkFundOuterService.queryHkFundOrderContractInfo(Mockito.anyString(), Mockito.anyList(), Mockito.anyList())).thenReturn(contractResponseDTO);

        // Mock补充协议数据
        List<SupplementalAgreementDTO> agreementList = createMockSupplementalAgreementList();
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(agreementList);

        // Mock开户文件数据
        OpenCustFileVO openCustFileVO = createMockOpenCustFileVO();
        PowerMockito.when(openAcctService.getOpenCustFileVO()).thenReturn(openCustFileVO);

        // Mock储蓄罐数据
        HkPiggyAgreementDealResponseDTO piggyResponseDTO = createMockPiggyResponseDTO();
        PowerMockito.when(hkPiggyBankOuterService.queryPiggySignContractRecord(Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(piggyResponseDTO);

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertFalse(CollectionUtils.isEmpty(result.getContractFileList()));
        Assert.assertEquals(3, result.getContractFileList().size()); // 2个交易合同 + 1个补充协议
        Assert.assertFalse(CollectionUtils.isEmpty(result.getOpenCustPiggyFileList()));
        Assert.assertFalse(CollectionUtils.isEmpty(result.getChangeCustPiggyFileList()));
    }

    /**
     * 创建模拟的储蓄罐响应数据
     */
    private HkPiggyAgreementDealResponseDTO createMockPiggyResponseDTO() {
        HkPiggyAgreementDealResponseDTO responseDTO = new HkPiggyAgreementDealResponseDTO();

        List<HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO> dealList = new ArrayList<>();

        // 创建开通储蓄罐记录
        HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO openDeal = new HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO();
        openDeal.setAgreementSignType("2"); // 线上自主申请开通 (AGREEMENT_SIGN_TYPE_ONLINE)
        openDeal.setAgreementSignDt("********");

        List<HkPiggyAgreementDealResponseDTO.PiggyAgreementDealFileDTO> openFileList = new ArrayList<>();
        HkPiggyAgreementDealResponseDTO.PiggyAgreementDealFileDTO openFile = new HkPiggyAgreementDealResponseDTO.PiggyAgreementDealFileDTO();
        openFile.setFileName("储蓄罐开通协议.pdf");
        openFile.setFilePathUrl("/piggy/open.pdf");
        openFile.setFileCode("001");
        openFileList.add(openFile);
        openDeal.setFilePathList(openFileList);
        dealList.add(openDeal);

        // 创建变更储蓄罐记录
        HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO changeDeal = new HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO();
        changeDeal.setAgreementSignType("4"); // 底层基金更换同意 (AGREEMENT_SIGN_TYPE_FUND_CHANGE)
        changeDeal.setAgreementSignDt("20240315");

        List<HkPiggyAgreementDealResponseDTO.PiggyAgreementDealFileDTO> changeFileList = new ArrayList<>();
        HkPiggyAgreementDealResponseDTO.PiggyAgreementDealFileDTO changeFile = new HkPiggyAgreementDealResponseDTO.PiggyAgreementDealFileDTO();
        changeFile.setFileName("储蓄罐变更协议.pdf");
        changeFile.setFilePathUrl("/piggy/change.pdf");
        changeFile.setFileCode("002");
        changeFileList.add(changeFile);
        changeDeal.setFilePathList(changeFileList);
        dealList.add(changeDeal);

        responseDTO.setHkPiggyDealDTOList(dealList);
        return responseDTO;
    }

    /**
     * @description: 测试基金代码为空时的场景
     */
    @Test
    public void testGetTradeContractNew_emptyFundCode() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode(""); // 空基金代码
        request.setHkCustNo("TEST001");

        // Mock外部服务返回空数据
        PowerMockito.when(fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(Mockito.anyString())).thenReturn(new ArrayList<>());
        PowerMockito.when(hkFundOuterService.queryHkFundOrderContractInfo(Mockito.anyString(), Mockito.anyList(), Mockito.anyList())).thenReturn(null);
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(new ArrayList<>());
        PowerMockito.when(openAcctService.getOpenCustFileVO()).thenReturn(new OpenCustFileVO());
        PowerMockito.when(hkPiggyBankOuterService.queryPiggySignContractRecord(Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(new HkPiggyAgreementDealResponseDTO());

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(CollectionUtils.isEmpty(result.getContractFileList()));
    }

    /**
     * @description: 测试外部服务返回null的异常场景
     */
    @Test
    public void testGetTradeContractNew_externalServiceNull() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");

        // Mock外部服务返回null
        PowerMockito.when(fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(Mockito.anyString())).thenReturn(null);
        PowerMockito.when(hkFundOuterService.queryHkFundOrderContractInfo(Mockito.anyString(), Mockito.anyList(), Mockito.anyList())).thenReturn(null);
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        PowerMockito.when(openAcctService.getOpenCustFileVO()).thenReturn(null);
        PowerMockito.when(hkPiggyBankOuterService.queryPiggySignContractRecord(Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(null);

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(CollectionUtils.isEmpty(result.getContractFileList()));
    }

    /**
     * @description: 测试时间范围过滤边界条件
     */
    @Test
    public void testGetTradeContractNew_timeRangeBoundary() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        request.setStartTime("********"); // 开始时间
        request.setEndTime("********");   // 结束时间相同，边界条件

        // Mock基金信息数据
        PowerMockito.when(fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(Mockito.anyString())).thenReturn(Arrays.asList("HK0001"));

        // 创建模拟数据
        HkFundOrderContractResponseDTO contractResponseDTO = createMockContractResponseDTO();
        PowerMockito.when(hkFundOuterService.queryHkFundOrderContractInfo(Mockito.anyString(), Mockito.anyList(), Mockito.anyList())).thenReturn(contractResponseDTO);
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(new ArrayList<>());
        PowerMockito.when(openAcctService.getOpenCustFileVO()).thenReturn(new OpenCustFileVO());
        PowerMockito.when(hkPiggyBankOuterService.queryPiggySignContractRecord(Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(new HkPiggyAgreementDealResponseDTO());

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        // 由于时间范围限制，可能会过滤掉一些合同
        if (result.getContractFileList() != null) {
            for (ContractFileVO contractFile : result.getContractFileList()) {
                String signDate = contractFile.getSignDate();
                if (signDate != null) {
                    Assert.assertNotNull("签署时间不应为null", signDate);
                }
            }
        }
    }

    /**
     * @description: 测试储蓄罐合同类型有数据的场景
     */
    @Test
    public void testGetTradeContractNew_piggyBankTypeWithData() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        request.setContractTypes(ContractFileBizTypeEnum.PIGGY_SIGN_FILE.getCode());

        // 创建模拟的储蓄罐数据，确保包含正确的协议签署类型
        HkPiggyAgreementDealResponseDTO piggyResponseDTO = createMockPiggyResponseDTO();
        PowerMockito.when(hkPiggyBankOuterService.queryPiggySignContractRecord(Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(piggyResponseDTO);

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        // 注意：根据业务逻辑，只有协议签署类型为"1"(线上签署)和"2"(基金变更)的记录才会被处理
        Assert.assertFalse(CollectionUtils.isEmpty(result.getOpenCustPiggyFileList()));
        Assert.assertFalse(CollectionUtils.isEmpty(result.getChangeCustPiggyFileList()));
        Assert.assertEquals(1, result.getOpenCustPiggyFileList().size());
        Assert.assertEquals(1, result.getChangeCustPiggyFileList().size());

        // 验证储蓄罐文件的具体内容
        ContractInfoNewVO.PiggySignContractInfo openPiggyInfo = result.getOpenCustPiggyFileList().get(0);
        Assert.assertEquals("********", openPiggyInfo.getSignDate());
        Assert.assertFalse(CollectionUtils.isEmpty(openPiggyInfo.getContractList()));

        ContractInfoNewVO.PiggySignContractInfo changePiggyInfo = result.getChangeCustPiggyFileList().get(0);
        Assert.assertEquals("20240315", changePiggyInfo.getSignDate());
        Assert.assertFalse(CollectionUtils.isEmpty(changePiggyInfo.getContractList()));
    }

    /**
     * @description: 测试时间范围类型转换逻辑
     */
    @Test
    public void testGetTradeContractNew_timeRangeTypeConversion() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        request.setTimeRangeType("1"); // 时间范围类型：近一月
        request.setContractTypes(ContractFileBizTypeEnum.TRADING_ORDER.getCode()); // 合同类型

        // 验证时间设置前的状态
        Assert.assertNull("执行前startTime应该为null", request.getStartTime());
        Assert.assertNull("执行前endTime应该为null", request.getEndTime());

        // Mock基金信息数据
        PowerMockito.when(fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(Mockito.anyString())).thenReturn(Arrays.asList("HK0001"));

        // 创建在时间范围内的模拟数据
        HkFundOrderContractResponseDTO contractResponseDTO = createMockContractResponseDTOForTimeRange();
        PowerMockito.when(hkFundOuterService.queryHkFundOrderContractInfo(Mockito.anyString(), Mockito.anyList(), Mockito.anyList())).thenReturn(contractResponseDTO);

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull("返回结果不应为null", result);

        // 验证时间范围类型转换逻辑
        Assert.assertNotNull("startTime应该被设置", request.getStartTime());
        Assert.assertNotNull("endTime应该被设置", request.getEndTime());

        // 验证startTime是1个月前的日期（格式：yyyyMMdd）
        Assert.assertEquals("startTime格式应该是8位数字", 8, request.getStartTime().length());
        Assert.assertTrue("startTime应该是数字格式", request.getStartTime().matches("\\d{8}"));

        // 验证endTime是当前日期（格式：yyyyMMdd）
        Assert.assertEquals("endTime格式应该是8位数字", 8, request.getEndTime().length());
        Assert.assertTrue("endTime应该是数字格式", request.getEndTime().matches("\\d{8}"));

        // 验证时间范围：startTime应该小于endTime
        Assert.assertTrue("startTime应该小于endTime", request.getStartTime().compareTo(request.getEndTime()) < 0);


        Assert.assertNotNull("合同文件列表不应为null", result.getContractFileList());
    }

    /**
     * @description: 测试只设置开始时间的场景
     */
    @Test
    public void testGetTradeContractNew_onlyStartTime() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        request.setStartTime("20240101"); // 只设置开始时间
        // 不设置结束时间

        // Mock基金信息数据
        PowerMockito.when(fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(Mockito.anyString())).thenReturn(Arrays.asList("HK0001"));

        // 创建模拟数据
        HkFundOrderContractResponseDTO contractResponseDTO = createMockContractResponseDTO();
        PowerMockito.when(hkFundOuterService.queryHkFundOrderContractInfo(Mockito.anyString(), Mockito.anyList(), Mockito.anyList())).thenReturn(contractResponseDTO);
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(new ArrayList<>());
        PowerMockito.when(openAcctService.getOpenCustFileVO()).thenReturn(new OpenCustFileVO());
        PowerMockito.when(hkPiggyBankOuterService.queryPiggySignContractRecord(Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(new HkPiggyAgreementDealResponseDTO());

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        // 只有开始时间，没有结束时间，时间过滤不会生效
        Assert.assertFalse(CollectionUtils.isEmpty(result.getContractFileList()));
    }

    /**
     * @description: 测试只设置结束时间的场景
     */
    @Test
    public void testGetTradeContractNew_onlyEndTime() {
        // 准备测试数据
        QueryTradeContractNewRequest request = new QueryTradeContractNewRequest();
        request.setFundCode("HK0001");
        request.setHkCustNo("TEST001");
        request.setEndTime("********"); // 只设置结束时间
        // 不设置开始时间

        // Mock基金信息数据
        PowerMockito.when(fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(Mockito.anyString())).thenReturn(Arrays.asList("HK0001"));

        // 创建模拟数据
        HkFundOrderContractResponseDTO contractResponseDTO = createMockContractResponseDTO();
        PowerMockito.when(hkFundOuterService.queryHkFundOrderContractInfo(Mockito.anyString(), Mockito.anyList(), Mockito.anyList())).thenReturn(contractResponseDTO);
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(new ArrayList<>());
        PowerMockito.when(openAcctService.getOpenCustFileVO()).thenReturn(new OpenCustFileVO());
        PowerMockito.when(hkPiggyBankOuterService.queryPiggySignContractRecord(Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(new HkPiggyAgreementDealResponseDTO());

        // 执行测试
        ContractInfoNewVO result = hkTradeContractService.getTradeContractNew(request);

        // 验证结果
        Assert.assertNotNull(result);
        // 只有结束时间，没有开始时间，时间过滤不会生效
        Assert.assertFalse(CollectionUtils.isEmpty(result.getContractFileList()));
    }

    /**
     * 创建在时间范围内的模拟交易合同响应数据
     */
    private HkFundOrderContractResponseDTO createMockContractResponseDTOForTimeRange() {
        HkFundOrderContractResponseDTO responseDTO = new HkFundOrderContractResponseDTO();

        List<HkFundOrderContractSignDTO> contractSignList = new ArrayList<>();

        // 获取当前日期和1个月前的日期，确保Mock数据在时间范围内
        String currentDate = DateUtils.getCurrentDate(DateUtils.YYYY_MM_DD_HH_MM_SS);

        // 创建第一个合同签署记录（当前日期）
        HkFundOrderContractSignDTO contractSign1 = new HkFundOrderContractSignDTO();
        contractSign1.setDealNo("ORDER001");
        contractSign1.setOrderStatus("3"); // 确认成功
        contractSign1.setMiddleBusiCode("1122"); // 申购
        contractSign1.setFundCode("HK0001");
        contractSign1.setFundName("测试基金1");
        contractSign1.setFundAbbr("测试基金1");
        contractSign1.setSignDate(currentDate); // 使用当前日期

        List<HkFundOrderContractSignDtlDTO> detailList1 = new ArrayList<>();
        HkFundOrderContractSignDtlDTO detail1 = new HkFundOrderContractSignDtlDTO();
        detail1.setFileType("01");
        detail1.setFileName("申购合同1.pdf");
        detail1.setFileTypeDesc("申购合同");
        detail1.setFilePath("/contract/order001.pdf");
        detailList1.add(detail1);
        contractSign1.setOrderContractSignDtlVOList(detailList1);

        // 创建第二个合同签署记录（15天前）
        HkFundOrderContractSignDTO contractSign2 = new HkFundOrderContractSignDTO();
        contractSign2.setDealNo("ORDER002");
        contractSign2.setOrderStatus("3"); // 确认成功
        contractSign2.setMiddleBusiCode("1124"); // 赎回
        contractSign2.setFundCode("HK0001");
        contractSign2.setFundName("测试基金1");
        contractSign2.setFundAbbr("测试基金1");
        String fifteenDaysAgo = java.time.LocalDate.now().minusDays(15)
                .atStartOfDay() // 设置时间为 00:00:00
                .format(java.time.format.DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS));        contractSign2.setSignDate(fifteenDaysAgo); // 使用15天前的日期

        List<HkFundOrderContractSignDtlDTO> detailList2 = new ArrayList<>();
        HkFundOrderContractSignDtlDTO detail2 = new HkFundOrderContractSignDtlDTO();
        detail2.setFileType("02");
        detail2.setFileName("赎回合同1.pdf");
        detail2.setFileTypeDesc("赎回合同");
        detail2.setFilePath("/contract/order002.pdf");
        detailList2.add(detail2);
        contractSign2.setOrderContractSignDtlVOList(detailList2);

        contractSignList.add(contractSign1);
        contractSignList.add(contractSign2);

        responseDTO.setOrderContractSignVOList(contractSignList);
        return responseDTO;
    }
}