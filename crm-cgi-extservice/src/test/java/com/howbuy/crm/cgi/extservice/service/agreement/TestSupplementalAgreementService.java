/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.agreement;

import com.howbuy.crm.cgi.extservice.common.enums.agreement.SupplementalAgreementSignStatusEnum;
import com.howbuy.crm.cgi.extservice.request.agreement.SupplementalAgreementDetailRequest;
import com.howbuy.crm.cgi.extservice.request.agreement.SupplementalAgreementListRequest;
import com.howbuy.crm.cgi.extservice.service.TestBase;
import com.howbuy.crm.cgi.extservice.vo.agreement.SupplementalAgreementDetailVO;
import com.howbuy.crm.cgi.extservice.vo.agreement.SupplementalAgreementListVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.agreement.SupplementalAgreementDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.agreement.SupplementalAgreementOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 补充协议服务单元测试
 * @date 2024/3/6 18:20
 * @since JDK 1.8
 */
public class TestSupplementalAgreementService extends TestBase {

    @InjectMocks
    private SupplementalAgreementService supplementalAgreementService = new SupplementalAgreementService();

    @Mock
    private SupplementalAgreementOuterService supplementalAgreementOuterService;

    @Mock
    private QueryFundBasicInfoOuterService queryFundBasicInfoOuterService;

    /**
     * 当没有未签署的补充协议时，返回空列表
     */
    @Test
    public void testQuerySupplementalAgreementList_empty() {
        // 准备测试数据
        SupplementalAgreementListRequest request = new SupplementalAgreementListRequest();
        request.setHkCustNo("HK123456");

        // Mock外部服务返回空列表
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(
            Mockito.anyString(), 
            Mockito.eq(SupplementalAgreementSignStatusEnum.UNSIGNED.getCode()),Mockito.any()
        )).thenReturn(new ArrayList<>());

        // 执行测试
        SupplementalAgreementListVO result = supplementalAgreementService.querySupplementalAgreementList(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(CollectionUtils.isEmpty(result.getFundAgreementList()));
    }

    /**
     * 正常场景：有未签署的补充协议，且能查询到对应的基金信息
     */
    @Test
    public void testQuerySupplementalAgreementList_normal() {
        // 准备测试数据
        SupplementalAgreementListRequest request = new SupplementalAgreementListRequest();
        request.setHkCustNo("HK123456");

        // Mock补充协议数据
        List<SupplementalAgreementDTO> agreementDTOS = new ArrayList<>();
        SupplementalAgreementDTO dto = new SupplementalAgreementDTO();
        dto.setFundCode("000001");
        dto.setAgreementName("测试协议");
        agreementDTOS.add(dto);

        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(
            Mockito.anyString(), 
            Mockito.eq(SupplementalAgreementSignStatusEnum.UNSIGNED.getCode()),Mockito.any()
        )).thenReturn(agreementDTOS);

        // Mock基金信息数据
        List<FundBasicInfoDTO> fundInfoDTOS = new ArrayList<>();
        FundBasicInfoDTO fundInfo = new FundBasicInfoDTO();
        fundInfo.setFundCode("000001");
        fundInfo.setFundName("测试基金");
        fundInfo.setFundAbbr("测试基金");
        fundInfoDTOS.add(fundInfo);

        PowerMockito.when(queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList(Mockito.anyList()))
            .thenReturn(fundInfoDTOS);

        // 执行测试
        SupplementalAgreementListVO result = supplementalAgreementService.querySupplementalAgreementList(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertFalse(CollectionUtils.isEmpty(result.getFundAgreementList()));
        Assert.assertEquals(1, result.getFundAgreementList().size());
        Assert.assertEquals("000001", result.getFundAgreementList().get(0).getFundCode());
        Assert.assertEquals("测试基金", result.getFundAgreementList().get(0).getFundAddr());
    }

    /**
     * 异常场景：有未签署的补充协议，但查询不到对应的基金信息
     */
    @Test
    public void testQuerySupplementalAgreementList_no_fund_info() {
        // 准备测试数据
        SupplementalAgreementListRequest request = new SupplementalAgreementListRequest();
        request.setHkCustNo("HK123456");

        // Mock补充协议数据
        List<SupplementalAgreementDTO> agreementDTOS = new ArrayList<>();
        SupplementalAgreementDTO dto = new SupplementalAgreementDTO();
        dto.setFundCode("000001");
        dto.setAgreementName("测试协议");
        agreementDTOS.add(dto);

        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementList(
            Mockito.anyString(), 
            Mockito.eq(SupplementalAgreementSignStatusEnum.UNSIGNED.getCode()),Mockito.any()
        )).thenReturn(agreementDTOS);

        // Mock基金信息返回空列表
        PowerMockito.when(queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList(Mockito.anyList()))
            .thenReturn(new ArrayList<>());

        // 执行测试
        SupplementalAgreementListVO result = supplementalAgreementService.querySupplementalAgreementList(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertFalse(CollectionUtils.isEmpty(result.getFundAgreementList()));
        Assert.assertEquals(1, result.getFundAgreementList().size());
        Assert.assertEquals("000001", result.getFundAgreementList().get(0).getFundCode());
        Assert.assertNull(result.getFundAgreementList().get(0).getFundAddr());
    }

    /**
     * @description: 当没有未签署的补充协议时，返回空列表
     */
    @Test
    public void testQuerySupplementalAgreementDetail_empty() {
        // 准备测试数据
        SupplementalAgreementDetailRequest request = new SupplementalAgreementDetailRequest();
        request.setHkCustNo("HK123456");
        request.setFundCode("000001");

        // Mock外部服务返回空列表
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementDetail(
            Mockito.anyString(), 
            Mockito.anyString(),
            Mockito.eq(SupplementalAgreementSignStatusEnum.UNSIGNED.getCode())
        )).thenReturn(new ArrayList<>());

        // 执行测试
        SupplementalAgreementDetailVO result = supplementalAgreementService.querySupplementalAgreementDetail(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(CollectionUtils.isEmpty(result.getSupplementalAgreementDetailDtlList()));
    }

    /**
     * @description: 正常场景，有未签署的补充协议，按截止时间倒序排序
     */
    @Test
    public void testQuerySupplementalAgreementDetail_normal() {
        // 准备测试数据
        SupplementalAgreementDetailRequest request = new SupplementalAgreementDetailRequest();
        request.setHkCustNo("HK123456");
        request.setFundCode("000001");

        // Mock补充协议数据
        List<SupplementalAgreementDTO> agreementDTOS = new ArrayList<>();
        
        SupplementalAgreementDTO dto1 = new SupplementalAgreementDTO();
        dto1.setFundCode("000001");
        dto1.setAgreementName("测试协议1");
        dto1.setAgreementSignEndDt("2024-03-01 10:00");
        agreementDTOS.add(dto1);

        SupplementalAgreementDTO dto2 = new SupplementalAgreementDTO();
        dto2.setFundCode("000001");
        dto2.setAgreementName("测试协议2");
        dto2.setAgreementSignEndDt("2024-03-02 10:00");
        agreementDTOS.add(dto2);

        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementDetail(
            Mockito.anyString(), 
            Mockito.anyString(),
            Mockito.eq(SupplementalAgreementSignStatusEnum.UNSIGNED.getCode())
        )).thenReturn(agreementDTOS);

        // 执行测试
        SupplementalAgreementDetailVO result = supplementalAgreementService.querySupplementalAgreementDetail(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertFalse(CollectionUtils.isEmpty(result.getSupplementalAgreementDetailDtlList()));
        Assert.assertEquals(2, result.getSupplementalAgreementDetailDtlList().size());
        // 验证排序是否正确（按截止时间倒序）
//        Assert.assertEquals("2024-03-02 10:00", result.getSupplementalAgreementDetailDtlList().get(0).getAgreementSignEndDt());
//        Assert.assertEquals("2024-03-01 10:00", result.getSupplementalAgreementDetailDtlList().get(1).getAgreementSignEndDt());
    }
} 