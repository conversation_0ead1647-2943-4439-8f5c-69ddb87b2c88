package com.howbuy.crm.cgi.extservice.service.hkaccount;

import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.FundTxCodeListRequest;
import com.howbuy.crm.cgi.extservice.service.TestBase;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.QueryFundTxAcctNoListVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.QueryFundTxAcctOuterService;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 基金交易代码列表查询单元测试
 * @date 2024/3/11 14:20
 * @since JDK 1.8
 */
public class TestQueryFundTxCodeList extends TestBase {

    @InjectMocks
    private HkCommonService hkCommonService = new HkCommonService();

    @Mock
    private QueryFundTxAcctOuterService queryFundTxAcctOuterService;

    /**
     * @description: 当没有基金交易账号时，返回空列表
     */
    @Test
    public void testQueryFundTxCodeList_empty() {
        // 准备测试数据
        FundTxCodeListRequest request = new FundTxCodeListRequest();
        request.setHkCustNo("HK123456");

        // Mock外部服务返回空列表
        PowerMockito.when(queryFundTxAcctOuterService.queryFundTxCodeList(
                Mockito.anyString())).thenReturn(new ArrayList<>());

        // 执行测试
        QueryFundTxAcctNoListVO result = hkCommonService.queryFundTxAcctNoList(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(CollectionUtils.isEmpty(result.getFundTxCodeList()));
    }

    /**
     * @description: 只有一个全委账户场景
     */
    @Test
    public void testQueryFundTxCodeList_singleFullCommission() {
        // 准备测试数据
        FundTxCodeListRequest request = new FundTxCodeListRequest();
        request.setHkCustNo("HK123456");

        // Mock基金交易账号数据
        List<HkFundTxAcctDTO> accountList = new ArrayList<>();

        HkFundTxAcctDTO dto = new HkFundTxAcctDTO();
        dto.setFundTxAcctNo("TX123456");
        dto.setFundTxAccType(YesNoEnum.YES.getCode()); // 全委账户
        dto.setFundTxAcctStat("1");
        accountList.add(dto);

        PowerMockito.when(queryFundTxAcctOuterService.queryFundTxCodeList(
                Mockito.anyString())).thenReturn(accountList);

        // 执行测试
        QueryFundTxAcctNoListVO result = hkCommonService.queryFundTxAcctNoList(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertFalse(CollectionUtils.isEmpty(result.getFundTxCodeList()));
        Assert.assertEquals(1, result.getFundTxCodeList().size());
        Assert.assertEquals("TX123456", result.getFundTxCodeList().get(0).getFundTxCode());
        Assert.assertEquals("全权委托账户", result.getFundTxCodeList().get(0).getFundName()); // 单个账户不带序号
    }

    /**
     * @description: 只有一个非全委账户场景
     */
    @Test
    public void testQueryFundTxCodeList_singleNonFullCommission() {
        // 准备测试数据
        FundTxCodeListRequest request = new FundTxCodeListRequest();
        request.setHkCustNo("HK123456");

        // Mock基金交易账号数据
        List<HkFundTxAcctDTO> accountList = new ArrayList<>();

        HkFundTxAcctDTO dto = new HkFundTxAcctDTO();
        dto.setFundTxAcctNo("TX123456");
        dto.setFundTxAccType(YesNoEnum.NO.getCode()); // 非全委账户
        dto.setFundTxAcctStat("1");
        accountList.add(dto);

        PowerMockito.when(queryFundTxAcctOuterService.queryFundTxCodeList(
                Mockito.anyString())).thenReturn(accountList);

        // 执行测试
        QueryFundTxAcctNoListVO result = hkCommonService.queryFundTxAcctNoList(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertFalse(CollectionUtils.isEmpty(result.getFundTxCodeList()));
        Assert.assertEquals(1, result.getFundTxCodeList().size());
        Assert.assertEquals("TX123456", result.getFundTxCodeList().get(0).getFundTxCode());
        Assert.assertEquals("证券投资账户", result.getFundTxCodeList().get(0).getFundName());
    }

    /**
     * @description: 多个全委账户场景，验证序号生成
     */
    @Test
    public void testQueryFundTxCodeList_multipleFullCommission() {
        // 准备测试数据
        FundTxCodeListRequest request = new FundTxCodeListRequest();
        request.setHkCustNo("HK123456");

        // Mock基金交易账号数据
        List<HkFundTxAcctDTO> accountList = new ArrayList<>();

        HkFundTxAcctDTO dto1 = new HkFundTxAcctDTO();
        dto1.setFundTxAcctNo("TX123456");
        dto1.setFundTxAccType(YesNoEnum.YES.getCode()); // 全委账户
        dto1.setFundTxAcctStat("1");
        accountList.add(dto1);

        HkFundTxAcctDTO dto2 = new HkFundTxAcctDTO();
        dto2.setFundTxAcctNo("TX234567");
        dto2.setFundTxAccType(YesNoEnum.YES.getCode()); // 全委账户
        dto2.setFundTxAcctStat("1");
        accountList.add(dto2);

        PowerMockito.when(queryFundTxAcctOuterService.queryFundTxCodeList(
                Mockito.anyString())).thenReturn(accountList);

        // 执行测试
        QueryFundTxAcctNoListVO result = hkCommonService.queryFundTxAcctNoList(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertFalse(CollectionUtils.isEmpty(result.getFundTxCodeList()));
        Assert.assertEquals(2, result.getFundTxCodeList().size());
        // 验证第一个账户名称包含序号01
        Assert.assertTrue(result.getFundTxCodeList().get(0).getFundName().contains("01"));
        // 验证第二个账户名称包含序号02
        Assert.assertTrue(result.getFundTxCodeList().get(1).getFundName().contains("02"));
    }

    /**
     * @description: 混合场景 - 既有全委又有非全委账户
     */
    @Test
    public void testQueryFundTxCodeList_mixedAccounts() {
        // 准备测试数据
        FundTxCodeListRequest request = new FundTxCodeListRequest();
        request.setHkCustNo("HK123456");

        // Mock基金交易账号数据
        List<HkFundTxAcctDTO> accountList = new ArrayList<>();

        HkFundTxAcctDTO dto1 = new HkFundTxAcctDTO();
        dto1.setFundTxAcctNo("TX123456");
        dto1.setFundTxAccType(YesNoEnum.YES.getCode()); // 全委账户
        accountList.add(dto1);

        HkFundTxAcctDTO dto2 = new HkFundTxAcctDTO();
        dto2.setFundTxAcctNo("TX234567");
        dto2.setFundTxAccType(YesNoEnum.NO.getCode()); // 非全委账户
        accountList.add(dto2);

        HkFundTxAcctDTO dto3 = new HkFundTxAcctDTO();
        dto3.setFundTxAcctNo("TX345678");
        dto3.setFundTxAccType(YesNoEnum.YES.getCode()); // 全委账户
        accountList.add(dto3);

        PowerMockito.when(queryFundTxAcctOuterService.queryFundTxCodeList(
                Mockito.anyString())).thenReturn(accountList);

        // 执行测试
        QueryFundTxAcctNoListVO result = hkCommonService.queryFundTxAcctNoList(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertFalse(CollectionUtils.isEmpty(result.getFundTxCodeList()));
        Assert.assertEquals(3, result.getFundTxCodeList().size());

        // 验证全委账户名称格式
        int fullCommissionCount = 0;
        int nonFullCommissionCount = 0;

        for (QueryFundTxAcctNoListVO.FundTxAcctNoInfo info : result.getFundTxCodeList()) {
            if (info.getFundName().contains("全权委托账户")) {
                fullCommissionCount++;
            } else if (info.getFundName().contains("证券投资账户")) {
                nonFullCommissionCount++;
            }
        }

        Assert.assertEquals(2, fullCommissionCount);
        Assert.assertEquals(1, nonFullCommissionCount);
    }
}