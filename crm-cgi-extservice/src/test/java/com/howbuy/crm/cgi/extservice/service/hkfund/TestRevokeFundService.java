/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.hkfund;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.request.hkfund.HkRevokeFundOrderRequest;
import com.howbuy.crm.cgi.extservice.service.TestBase;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.HkFundOuterService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/6/17 14:50
 * @since JDK 1.8
 */
public class TestRevokeFundService extends TestBase {

    @InjectMocks
    private RevokeFundService revokeFundService = new RevokeFundService();

    @Mock
    private HkFundOuterService hkFundOuterService;

    @Test
    public void testRevokeFundRedeemFundOrder_C021008() {
        HkRevokeFundOrderRequest hkRevokeFundOrderRequest = new HkRevokeFundOrderRequest();
        PowerMockito.doThrow(new BusinessException("C021008", "交易密码错误")).when(hkFundOuterService)
                .redeemFundOrder(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
        try {
            revokeFundService.redeemFundOrder(hkRevokeFundOrderRequest);
        } catch (BusinessException e) {
            Assert.assertEquals("C021008", e.getCode());
        }
    }

    @Test
    public void testRevokeFundRedeemFundOrder_C021009() {
        HkRevokeFundOrderRequest hkRevokeFundOrderRequest = new HkRevokeFundOrderRequest();
        PowerMockito.doThrow(new BusinessException("C021009", "其他异常")).when(hkFundOuterService)
                .redeemFundOrder(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
        try {
            revokeFundService.redeemFundOrder(hkRevokeFundOrderRequest);
        } catch (BusinessException e) {
            Assert.assertEquals(ExceptionCodeEnum.HW_REDEEM_FUND_ORDER_ERROR.getCode(), e.getCode());
            Assert.assertEquals(ExceptionCodeEnum.HW_REDEEM_FUND_ORDER_ERROR.getDescription(), e.getDesc());
        }
    }

}
