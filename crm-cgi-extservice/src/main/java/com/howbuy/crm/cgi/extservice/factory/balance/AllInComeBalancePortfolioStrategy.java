package com.howbuy.crm.cgi.extservice.factory.balance;

import com.howbuy.crm.cgi.common.enums.TxChannelEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.extservice.common.annotation.FundTypeSupport;
import com.howbuy.crm.cgi.extservice.common.enums.balance.HkCustBalanceDisPalyCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.enums.refund.RefundApplyDataSourceEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherTypeEnum;
import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.AllInTransitTradeBalancePortfolioDetail;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.capital.QueryInTransitTradeCapitalRequestDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.piggy.PiggyPayVoucherRecordPageDTO;
import com.howbuy.crm.cgi.manager.domain.hkfin.HkFinRefundInfoRequestDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.PiggyPayVoucherOuterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @description: 在途买入类
 * @author: jinqing.rao
 * @date: 2025/6/24 9:42
 * @since JDK 1.8
 */
@Component
@Slf4j
@FundTypeSupport(HkCustBalanceDisPalyCategoryEnum.ALL_IN_TRANSIT)
public class AllInComeBalancePortfolioStrategy extends AbstractBalancePortfolioStrategy implements BalancePortfolioStrategy<AllInTransitTradeBalancePortfolioDetail> {





    @Override
    public Class<AllInTransitTradeBalancePortfolioDetail> getSupportedType() {
        return AllInTransitTradeBalancePortfolioDetail.class;
    }

    @Override
    public AllInTransitTradeBalancePortfolioDetail calculate(BalanceContent content) {
        // 生成构造器
        AllInTransitTradeBalancePortfolioDetail.AllInTransitTradeBalancePortfolioDetailBuilder builder = AllInTransitTradeBalancePortfolioDetail.builder();

        // 获取基金交易账号
        List<String> hkFundTxAcctNoList = getHkFundTxAcctNoList(content);

        // 查询在途数据
        buildAllInTransitCountAndDealNo(buildAllInComeQueryBalanceDTO(content), builder);

        // 查询在途的打款凭证信息
        PiggyPayVoucherRecordPageDTO piggyPayVoucherRecordPageDTO = getPiggyPayVoucherRecordPageDTO(content);
        buildPiggyPayVoucherCountAndDealNo(piggyPayVoucherRecordPageDTO, builder);

        // 在途提款申请个数信息
        buildHkFinRefundCountAndDealNo(getHkFinRefundInfoRequestDTO(content, hkFundTxAcctNoList), hkFundTxAcctNoList, builder);

        // 查询交易资金信息
        buildInTransitTradeCapitalCountAndDealNo(getQueryInTransitTradeCapitalRequestDTO(content), builder);
        return builder.build();
    }

    /**
     * @description: 获取在途交易资金请求参数
     * @param content 上下文
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.request.capital.QueryInTransitTradeCapitalRequestDTO
     * @author: jinqing.rao
     * @date: 2025/7/9 17:34
     * @since JDK 1.8
     */
    private static QueryInTransitTradeCapitalRequestDTO getQueryInTransitTradeCapitalRequestDTO(BalanceContent content) {
        return QueryInTransitTradeCapitalRequestDTO.builder()
                .hkCustNo(content.getHkCustNo())
                .build();
    }

    /**
     * @param content            上下文
     * @param hkFundTxAcctNoList 基金交易账号
     * @return com.howbuy.crm.cgi.manager.domain.hkfin.HkFinRefundInfoRequestDTO
     * @description: 获取请求参数
     * @author: jinqing.rao
     * @date: 2025/7/9 17:26
     * @since JDK 1.8
     */
    private static HkFinRefundInfoRequestDTO getHkFinRefundInfoRequestDTO(BalanceContent content, List<String> hkFundTxAcctNoList) {
        return HkFinRefundInfoRequestDTO.builder()
                .hkCustNo(content.getHkCustNo())
                .fundTxAcctNoList(hkFundTxAcctNoList)
                .dataSource(RefundApplyDataSourceEnum.ONLINE.getCode())
                .deleteFlag(YesNoEnum.NO.getCode())
                .build();
    }


    /**
     * @param content 上下文
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.response.piggy.PiggyPayVoucherRecordPageDTO
     * @description: 构建请求参数
     * @author: jinqing.rao
     * @date: 2025/7/9 17:22
     * @since JDK 1.8
     */
    private PiggyPayVoucherRecordPageDTO getPiggyPayVoucherRecordPageDTO(BalanceContent content) {
        // 打款凭证相关数据
        List<String> tradeChannelList = Arrays.asList(
                TxChannelEnum.WAP.getCode(),
                TxChannelEnum.HK_APP.getCode(),
                TxChannelEnum.H5.getCode()
        );
        List<String> voucherType = Collections.singletonList(PayVoucherTypeEnum.DEPOSIT_CASH_ACCOUNT.getCode());

        return queryPiggyPayVoucherRecord(content.getHkCustNo(), tradeChannelList, voucherType);
    }

}