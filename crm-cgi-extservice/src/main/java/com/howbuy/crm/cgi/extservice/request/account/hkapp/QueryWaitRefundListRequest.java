/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account.hkapp;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 查询待回款订单列表
 * @author: jinqing.rao
 * @date: 2025/7/14 20:37
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryWaitRefundListRequest implements Serializable {

    private static final long serialVersionUID = 4259399264616137701L;

    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 基金交易账号
     */
    private String fundTxCode;

    /**
     * 包含储蓄罐 1 是 0 否
     */
    private String includePiggy;
}