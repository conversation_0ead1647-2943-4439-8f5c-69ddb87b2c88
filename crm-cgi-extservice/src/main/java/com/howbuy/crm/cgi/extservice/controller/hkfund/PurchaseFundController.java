package com.howbuy.crm.cgi.extservice.controller.hkfund;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.hkfund.*;
import com.howbuy.crm.cgi.extservice.service.hkfund.HKPurchaseFundOrderService;
import com.howbuy.crm.cgi.extservice.vo.hkfund.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 好买香港App 申购基金
 * @date 2024/4/9 14:02
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkfund/purchase/")
public class PurchaseFundController {

    @Resource
    private HKPurchaseFundOrderService hkPurchaseFundService;


    /**
     * @api {POST} /ext/hkfund/purchase/verification purchaseFundVerification()
     * @apiVersion 1.0.0
     * @apiGroup PurchaseFundController
     * @apiName purchaseFundVerification()
     * @apiDescription 购买页-购买校验接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode fundCode
     * @apiParamExample 请求体示例
     * {"fundCode":"qRRDYnu6Br","hkCustNo":"U2"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.buyVerfiyState 购买验证状态      0-校验通过      1-去开户      2-继续处理      3-查看开户进度      4-修改开户资料      5-去入金      6-查看入金进度      7-修改入金资料      8-客户状态异常      19-交易账号未激活      20-未绑定银行卡      21-风险等级不匹配      22-客户衍生品经验为否      23-客户非专业投资者      24-客户资产证明有效期已过期
     * @apiSuccess (响应结果) {String} data.customRiskLevelDesc 客户风险等级描述   风险等级      0-保守型（最低）      1-保守型      2-稳健型      3-平衡型      4-成长型      5-进取型
     * @apiSuccess (响应结果) {String} data.fundRiskLevelDesc 基金风险等级描述  R1-低风险,R2-中低风险 R3-中风险 R4-中高风险 R5-高风险
     * @apiSuccess (响应结果) {String} data.openAcctStep 开户订单信息,填写到具体的步骤,开户步骤标识：1-证件信息页；2-个人信息页；3-职业信息页；4-声明信息页；5-投资经验页；6-银行卡页；7-电子签名页      该字段通过循环查询缓存,判断最大不步骤页
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "oTq",
     *   "data": {
     *     "buyVerfiyState": "9N3D",
     *     "fundRiskLevelDesc": "DREHN",
     *     "customRiskLevelDesc": "d5uOed",
     *     "openAcctStep": "SgG"
     *   },
     *   "description": "06",
     *   "timestampServer": "LLn4"
     * }
     */
    @PostMapping("verification")
    public CgiResponse<HKFundVerificationVO> purchaseFundVerification(@RequestBody HKPurchaseFundBaseRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkPurchaseFundService.purchaseFundVerification(request));
    }

    /**
     * @api {POST} /ext/hkfund/purchase/querypurchasepageinfo queryPurchasePageInfo()
     * @apiVersion 1.0.0
     * @apiGroup PurchaseFundController
     * @apiName queryPurchasePageInfo()
     * @apiDescription 购买页-查询购买页信息接口
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode fundCode
     * @apiParamExample 请求体示例
     * {"fundCode":"O","hkCustNo":"rsH"}
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.mobileAreaCode 手机区号
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱掩码
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.emailDigest 邮箱地址密文
     * @apiSuccess (响应结果) {Object} data.buyFundInfoVO 购买页基金信息
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.fundShortName 基金简称
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.fundEnName 基金英文名称
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.supportPrebook 1:是 0:否
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.openStartDate 开放开始日期  yyyymmdd
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.fundCategory 基金分类          1-公募、2-私募、9-其他
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.openEndDate 开发结束日期
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.payEndDate 打款截止日期
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.payEndTime 打款截止时间
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.tradeDate 交易日期
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.currentDate 当前日期
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.feeCalMode 费用计算方式 0-外扣法、1-内扣法
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.currencyCode 币种
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.currencyDesc 币种描述
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.minAppAmt 最小申购金额
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.maxAppAmt 最大申购金额
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.differential 级差
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.gradationCall 分次Call产品 1-是 0-否
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.firstIntentionRatio 首次意向金比例
     * @apiSuccess (响应结果) {Object} data.prebookBuyInfoVO 预约购买信息
     * @apiSuccess (响应结果) {String} data.prebookBuyInfoVO.prebookPayMethod 预约支付类型 1-电汇、2-支票、3-海外储蓄罐
     * @apiSuccess (响应结果) {String} data.prebookBuyInfoVO.prebookAppAmt 预约申请金额
     * @apiSuccess (响应结果) {String} data.prebookBuyInfoVO.prebookDiscountRate 预约折扣率
     * @apiSuccess (响应结果) {String} data.prebookBuyInfoVO.prebookDealNo 预约单号
     * @apiSuccess (响应结果) {String} data.prebookBuyInfoVO.prebookSubAmt 预约认缴金额
     * @apiSuccess (响应结果) {String} data.prebookBuyInfoVO.prebookPaidAmt 预约实缴金额
     * @apiSuccess (响应结果) {Object} data.payMethodInfoVO 支付方式信息
     * @apiSuccess (响应结果) {String} data.payMethodInfoVO.supportCxgPay 否支持储蓄罐支付  0-不支持 1-支持
     * @apiSuccess (响应结果) {String} data.payMethodInfoVO.hasSignCxg 是否签约储蓄罐  0-未签署 1-签署
     * @apiSuccess (响应结果) {String} data.payMethodInfoVO.agreementValidStatus 1 未生效  2 已生效  3 已过期
     * @apiSuccess (响应结果) {String} data.payMethodInfoVO.cxgOrderEndTime 储蓄罐下单结束时间  hhmmss
     * @apiSuccess (响应结果) {Boolean} data.payMethodInfoVO.beforeCxgOrderEndTime 当前时间的时分秒是否小于等于储蓄罐下单结束时间
     * @apiSuccess (响应结果) {Array} data.payMethodInfoVO.cxgCurrencyVOList 是否支持电汇  0-不支持 1-支持
     * @apiSuccess (响应结果) {Array} data.bankCardList 银行卡列表
     * @apiSuccess (响应结果) {String} data.bankCardList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} data.bankCardList.bankAcctMask 银行卡号掩码
     * @apiSuccess (响应结果) {String} data.bankCardList.bankName bankName
     * @apiSuccess (响应结果) {String} data.bankCardList.bankLogoUrl 银行logo
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "mfzF6sTRr",
     *   "data": {
     *     "mobileAreaCode": "sqM",
     *     "prebookBuyInfoVO": {
     *       "prebookSubAmt": "AvKEKD",
     *       "prebookPaidAmt": "W8N",
     *       "prebookDiscountRate": "YoyEIs",
     *       "prebookAppAmt": "aNY3L9m",
     *       "prebookPayMethod": "RWZ",
     *       "prebookDealNo": "jk70g7U"
     *     },
     *     "bankCardList": [
     *       {
     *         "bankLogoUrl": "4Z",
     *         "bankAcctMask": "ByNu09n4o7",
     *         "cpAcctNo": "XReoTcShEq",
     *         "bankName": "JNd"
     *       }
     *     ],
     *     "payMethodInfoVO": {
     *       "hasSignCxg": "4id",
     *       "supportCxgPay": "wSyY",
     *       "cxgCurrencyVOList": [],
     *       "agreementValidStatus": "he2th",
     *       "beforeCxgOrderEndTime": false,
     *       "cxgOrderEndTime": "YeMloKN2C4"
     *     },
     *     "emailMask": "jVuOj",
     *     "mobileDigest": "Gr",
     *     "emailDigest": "RiqTdPu",
     *     "mobileMask": "XasjDFCA",
     *     "buyFundInfoVO": {
     *       "supportPrebook": "JIHy70KwXG",
     *       "currencyDesc": "WQ1z",
     *       "maxAppAmt": "9",
     *       "gradationCall": "uWBE",
     *       "payEndDate": "jG",
     *       "payEndTime": "K6aB",
     *       "currentDate": "dvxP",
     *       "fundShortName": "bNoKM6yb8",
     *       "tradeDate": "UkNsDy",
     *       "minAppAmt": "TTtRb6h",
     *       "openEndDate": "S142O6Tq",
     *       "differential": "1jY",
     *       "fundEnName": "mjw",
     *       "firstIntentionRatio": "RnJiw1",
     *       "openStartDate": "dO8k0I",
     *       "fundCategory": "Q",
     *       "feeCalMode": "vGAMtiy",
     *       "currencyCode": "j8YvDgLoX"
     *     }
     *   },
     *   "description": "v2HVvw8dBw",
     *   "timestampServer": "couw8J5F5"
     * }
     */
    @PostMapping("querypurchasepageinfo")
    public CgiResponse<HkPurchasePageInfoVO> queryPurchasePageInfo(@RequestBody HKPurchaseFundBaseRequest request) {
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkPurchaseFundService.queryPurchasePageInfo(request));
    }

    /**
     * @api {POST} /ext/hkfund/purchase/feecompute fundFeeCompute()
     * @apiVersion 1.0.0
     * @apiGroup PurchaseFundController
     * @apiName fundFeeCompute()
     * @apiDescription 购买页-手续费计算接口
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} appAmt 申购金额
     * @apiParam (请求体) {String} paidAmt 实缴金额
     * @apiParamExample 请求体示例
     * {"appAmt":"Prpxfr","fundCode":"XuupA","hkCustNo":"JV6Mmvj","paidAmt":"FvVwe"}
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.actualPayAmt 实际支付金额
     * @apiSuccess (响应结果) {String} data.feeRate 手续费费率 ####.00000000
     * @apiSuccess (响应结果) {String} data.fee 手续费
     * @apiSuccess (响应结果) {String} data.originalFee 原始手续费
     * @apiSuccess (响应结果) {String} data.largerPrebookAmt 是否大于预约金额 0-否 1-是
     * @apiSuccess (响应结果) {String} data.validDiscountRate 折扣是否生效 0-否 1-是
     * @apiSuccess (响应结果) {String} data.actualDiscountRate 实际折扣率
     * @apiSuccess (响应结果) {String} data.prebookDiscountRate 预约折扣率
     * @apiSuccess (响应结果) {String} data.feeRateType 分次CALL产品，手续费类型是  1 认缴   2 实缴
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "Gq",
     *   "data": {
     *     "originalFee": "jzSN9PdU",
     *     "fee": "TI9hsT",
     *     "largerPrebookAmt": "uH",
     *     "prebookDiscountRate": "g",
     *     "validDiscountRate": "Usiqnn",
     *     "actualPayAmt": "7nDpJ",
     *     "actualDiscountRate": "rbT5zC",
     *     "feeRate": "aKiUyC6y",
     *     "feeRateType": "MVp"
     *   },
     *   "description": "tZoNpfIO",
     *   "timestampServer": "jE6"
     * }
     */
    @PostMapping("feecompute")
    public CgiResponse<HKPurchaseFundFeeComputeVO> fundFeeCompute(@RequestBody HKPurchaseFundFeeComputeRequest request) {
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkPurchaseFundService.fundFeeCompute(request));
    }

    /**
     * @api {POST} /ext/hkfund/purchase/submit submit()
     * @apiVersion 1.0.0
     * @apiGroup PurchaseFundController
     * @apiName submit()
     * @apiDescription 购买页-购买提交接口
     * @apiParam (请求体) {String} hkCustNo 客户号
     * @apiParam (请求体) {String} txPassword 交易密码
     * @apiParam (请求体) {String} verifyCodeType 验证码类型 申请申购 传15
     * @apiParam (请求体) {String} verifyCode 验证码
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} appAmt 申购金额
     * @apiParam (请求体) {String} paidAmt 实缴金额
     * @apiParam (请求体) {String} fee 手续费
     * @apiParam (请求体) {String} actualPayAmt 实际支付金额
     * @apiParam (请求体) {String} payMethod 1-电汇、2-支票、3-海外储蓄罐
     * @apiParam (请求体) {String} cpAcctNo 1-电汇时必须
     * @apiParam (请求体) {String} agreeCurrencyExchange 0-否 1-是
     * @apiParam (请求体) {String} signFlag 签署协议标识
     * @apiParam (请求体) {String} prebookDealNo 预约单号 非必传
     * @apiParam (请求体) {String} hbSceneId
     * @apiParam (请求体) {String} feeRateType 认缴 实缴 手续费类型  1 认缴 2 实缴
     * @apiParam (请求体) {Array} contractList 合同及列表
     * @apiParam (请求体) {String} contractList.fileCode 文件代码
     * @apiParam (请求体) {String} contractList.fileName 文件名称
     * @apiParam (请求体) {String} contractList.filePathUrl 文件路径
     * @apiParamExample 请求体示例
     * {"txPassword":"qx","signFlag":"5yMgWG5i","verifyCode":"Ixh","appAmt":"bbafMpQTht","agreeCurrencyExchange":"g8qtcMGT","hkCustNo":"jR1an","fee":"nJYpI","paidAmt":"K1mzJgm","verifyCodeType":"W1HG5te6dD","prebookDealNo":"kmOfJvM79l","fundCode":"AQo5XUE","hbSceneId":"5Q","payMethod":"qrXClEl","contractList":[{"filePathUrl":"rBEiXji","fileName":"lbGBtRbDE","fileCode":"G"}],"actualPayAmt":"Id","cpAcctNo":"JFX0lhrini","feeRateType":"YGZxRCad"}
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.orderNo 订单号, 用于结果页查询订单数据
     * @apiSuccess (响应结果) {String} data.paidOrderNo 实缴订单号
     * @apiSuccess (响应结果) {String} data.currentDate 当前时间
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"kchr","data":{"orderNo":"FIZgxtG","currentDate":"OWqbw","paidOrderNo":"e5hQ"},"description":"Kf8tHcA","timestampServer":"RSt"}
     */
    @PostMapping("submit")
    public CgiResponse<HkPurchaseFundSubmitVO> submit(@RequestBody HkPurchaseFundSubmitRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkPurchaseFundService.submit(request));
    }

    /**
     * @api {POST} /ext/hkfund/purchase/applyresult verification()
     * @apiVersion 1.0.0
     * @apiGroup PurchaseFundController
     * @apiName verification()
     * @apiDescription 购买页-购买申请结果接口
     * @apiParam (请求体) {String} orderNo 订单号
     * @apiParam (请求体) {String} paidOrderNo 实缴订单号
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParamExample 请求体示例
     * {"orderNo":"inxxh7jg4m","hkCustNo":"DMEIneR06C","paidOrderNo":"4ONAXRa"}
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.appAmt 申请金额
     * @apiSuccess (响应结果) {String} data.fundShortName 基金简称
     * @apiSuccess (响应结果) {String} data.payEndDate 打款截止日期  yyyy-mm-dd
     * @apiSuccess (响应结果) {String} data.payEndTime 打款截止时间 hh:mm
     * @apiSuccess (响应结果) {String} data.currencyCode 币种
     * @apiSuccess (响应结果) {String} data.currencyDesc 币种描述
     * @apiSuccess (响应结果) {String} data.supportPrebook 是否支持预约交易
     * @apiSuccess (响应结果) {String} data.investorType 投资者资质
     * @apiSuccess (响应结果) {String} data.assetCertVaild 资产证明是否有效
     * @apiSuccess (响应结果) {String} data.payMethod 必须，1-电汇、2-支票、3-海外储蓄罐
     * @apiSuccess (响应结果) {String} data.currentDate 当前时间  yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.investorAuditStatus 1是 0 否   1表示存在审核中的订单
     * @apiSuccess (响应结果) {Object} data.transferBankInfos 转账银行信息列表
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferBankAcctName 转账账户名称
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferBankName 转账银行名称
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferBankAddress 转账银行地址
     * @apiSuccess (响应结果) {Array} data.transferBankInfos.transferBankAccts 转账银行账号列表
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferSwiftCode 国际汇款识别码
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferBankCode 银行代码
     * @apiSuccess (响应结果) {String} data.transferBankInfos.bankLogoUrl 银行logo
     * @apiSuccess (响应结果) {String} data.beforPiggyPayEndTime 1 ： 是 当前时间 小于等于 下单支付截止时间  0 ：否   当前时间 大于等于 下单支付截止时间
     * @apiSuccess (响应结果) {String} data.uploadStatus 是否上传打款凭证 审核结果 0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
     * @apiSuccess (响应结果) {String} data.gradationCall 分次call标识(股权类有此标识:0-否,1-是)
     * @apiSuccess (响应结果) {Object} data.gradationCallVO 分次CALL信息
     * @apiSuccess (响应结果) {String} data.gradationCallVO.subAmt 认缴金额
     * @apiSuccess (响应结果) {String} data.gradationCallVO.paidAmt 实缴金额
     * @apiSuccess (响应结果) {String} data.gradationCallVO.subFee 认缴手续费
     * @apiSuccess (响应结果) {String} data.gradationCallVO.paidFee 实缴手续费
     * @apiSuccess (响应结果) {String} data.gradationCallVO.paidOrderNo 实缴订单号
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"sDAepKaqpn","data":{"currencyDesc":"uInyFJg","supportPrebook":"R5mA","gradationCall":"iLVW4me","appAmt":"h","payEndDate":"2tS","payEndTime":"OLCzGLL","currentDate":"jdh","uploadStatus":"to","fundShortName":"rg2CGEP","beforPiggyPayEndTime":"DhBrHuaj5","investorAuditStatus":"i8CMCv3yN","assetCertVaild":"nIG1Xt","payMethod":"4PTm3ry8Q7","gradationCallVO":{"subAmt":"EFhRHfqd","paidAmt":"vJpX","paidFee":"ydn","subFee":"L9lx","paidOrderNo":"LKm"},"transferBankInfos":{"bankLogoUrl":"cBrcr","transferBankCode":"l9","transferBankAccts":[],"transferBankName":"8ana5U","transferBankAcctName":"ZPa1Y","transferBankAddress":"7Y","transferSwiftCode":"HVjVEfm"},"currencyCode":"XT","investorType":"T1Xx"},"description":"3lNPG","timestampServer":"nD4"}
     */
    @PostMapping("applyresult")
    public CgiResponse<HKPurchaseFundApplyResultVO> queryApplyOrderresult(@RequestBody HKPurchaseFundApplyResultRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkPurchaseFundService.queryApplyOrderResult(request));
    }

    /**
     * @api {POST} /ext/hkfund/purchase/gradationcall/pendingmessagedetail queryPendingMessageDetail()
     * @apiVersion 1.0.0
     * @apiGroup PurchaseFundController
     * @apiName queryPendingMessageDetail()
     * @apiDescription 分次CAll消息详情页面详情接口
     * @apiParam (请求体) {String} orderNo 订单号
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParamExample 请求体示例
     * {"orderNo":"lH","hkCustNo":"Jg"}
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.paidOrderVO 实缴订单信息
     * @apiSuccess (响应结果) {String} data.paidOrderVO.paymentType 支付方式  1 银行卡  2 支票  3 银行卡
     * @apiSuccess (响应结果) {String} data.paidOrderVO.payEndDate 打款截止日期
     * @apiSuccess (响应结果) {String} data.paidOrderVO.payEndTime 打款截止日期 hh:mm
     * @apiSuccess (响应结果) {String} data.paidOrderVO.fundName 基金名称
     * @apiSuccess (响应结果) {String} data.paidOrderVO.paidAmt 实缴金额
     * @apiSuccess (响应结果) {Object} data.buyFundInfoVO 购买页基金信息
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.fundShortName 基金简称
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.fundEnName 基金英文名称
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.supportPrebook 1:是 0:否
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.fundCategory 基金分类          1-公募、2-私募、9-其他
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.currencyCode 币种
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.currencyDesc 币种描述
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.gradationCall 分次Call产品 1-是 0-否
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.feeRateType 手续费业务类型
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.firstIntentionAmt 首次意向金
     * @apiSuccess (响应结果) {String} data.buyFundInfoVO.subAmt 认缴金额
     * @apiSuccess (响应结果) {Object} data.prebookBuyInfoVO 预约购买信息
     * @apiSuccess (响应结果) {String} data.prebookBuyInfoVO.prebookPayMethod 预约支付类型 1-电汇、2-支票、3-海外储蓄罐
     * @apiSuccess (响应结果) {String} data.prebookBuyInfoVO.prebookAppAmt 预约申请金额
     * @apiSuccess (响应结果) {String} data.prebookBuyInfoVO.prebookDiscountRate 预约折扣率
     * @apiSuccess (响应结果) {String} data.prebookBuyInfoVO.prebookDealNo 预约单号
     * @apiSuccess (响应结果) {Object} data.payMethodInfoVO 支付方式信息
     * @apiSuccess (响应结果) {String} data.payMethodInfoVO.supportCxgPay 否支持储蓄罐支付  0-不支持 1-支持
     * @apiSuccess (响应结果) {String} data.payMethodInfoVO.hasSignCxg 是否签约储蓄罐  0-未签署 1-签署
     * @apiSuccess (响应结果) {String} data.payMethodInfoVO.agreementValidStatus 1 未生效  2 已生效  3 已过期
     * @apiSuccess (响应结果) {String} data.payMethodInfoVO.cxgOrderEndTime 储蓄罐下单结束时间  hhmmss
     * @apiSuccess (响应结果) {Boolean} data.payMethodInfoVO.beforeCxgOrderEndTime 当前时间的时分秒是否小于等于储蓄罐下单结束时间
     * @apiSuccess (响应结果) {Array} data.payMethodInfoVO.cxgCurrencyVOList 是否支持电汇  0-不支持 1-支持
     * @apiSuccess (响应结果) {Array} data.bankCardList 银行卡列表
     * @apiSuccess (响应结果) {String} data.bankCardList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} data.bankCardList.bankAcctMask 银行卡号掩码
     * @apiSuccess (响应结果) {String} data.bankCardList.bankName bankName
     * @apiSuccess (响应结果) {String} data.bankCardList.bankLogoUrl 银行logo
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"qamo","data":{"prebookBuyInfoVO":{"prebookDiscountRate":"0OI","prebookAppAmt":"y6m9MQ","prebookPayMethod":"mSgZbcBN","prebookDealNo":"HZwWAie"},"bankCardList":[{"bankLogoUrl":"VH","bankAcctMask":"qz","cpAcctNo":"jPH","bankName":"eOBXkOQc"}],"paidOrderVO":{"payEndDate":"l6YqcOU","payEndTime":"mureC","paidAmt":"2OYk","fundName":"nY1gb8fVM1","paymentType":"SkUFjdDD"},"payMethodInfoVO":{"hasSignCxg":"6xuKzzf2","supportCxgPay":"RXAqyb4","cxgCurrencyVOList":[],"agreementValidStatus":"AE","beforeCxgOrderEndTime":false,"cxgOrderEndTime":"93rsLW1Mz"},"buyFundInfoVO":{"firstIntentionAmt":"t","supportPrebook":"0i","currencyDesc":"Qj","fundEnName":"EAc2","gradationCall":"pKF8r7yyn","subAmt":"xqR17LP","fundCategory":"sYloMCF","fundShortName":"sN0WLSq","currencyCode":"MGI","feeRateType":"aq5G"}},"description":"RfXk","timestampServer":"pkd1zthW"}
     */
    @PostMapping("gradationcall/pendingmessagedetail")
    public CgiResponse<SubPaidPendingDetailVO> queryPendingMessageDetail(@RequestBody HKPurchasePendingMessageDetailRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkPurchaseFundService.queryPendingMessageDetail(request));
    }

    /**
     * @api {POST} /ext/hkfund/purchase/gradationcall/modify/payment/method modifyPaymentMethod()
     * @apiVersion 1.0.0
     * @apiGroup PurchaseFundController
     * @apiName modifyPaymentMethod()
     * @apiDescription 分次CALL产品修改支付方式
     * @apiParam (请求体) {String} dealNo 订单号
     * @apiParam (请求体) {String} payMethod 支付方式
     * @apiParam (请求体) {String} hkCustNo
     * @apiParam (请求体) {String} cpAcctNo 资金账号  可选，支付方式为1-银行卡时必填
     * @apiParamExample 请求体示例
     * {"payMethod":"aDUH5lLwJ","hkCustNo":"Efaa43onXK","cpAcctNo":"2KJUiXb","dealNo":"r5cSvmuK"}
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"97N","description":"rbVpIC","timestampServer":"j8ZzFo0gbL"}
     */
    @PostMapping("gradationcall/modify/payment/method")
    public CgiResponse<Body> modifyPaymentMethod(@RequestBody GradationCallModifyPaymentMethodRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        hkPurchaseFundService.modifyPaymentMethod(request);
        return CgiResponse.ok(new Body());
    }

}
