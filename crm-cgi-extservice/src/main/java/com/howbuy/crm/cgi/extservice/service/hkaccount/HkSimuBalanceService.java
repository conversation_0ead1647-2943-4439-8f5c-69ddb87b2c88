/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.hkaccount;

import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.CurrencyEnum;
import com.howbuy.crm.cgi.common.enums.TxChannelEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.utils.AmtorVolUtil;
import com.howbuy.crm.cgi.common.utils.BigDecimalUtils;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.common.utils.WebUtil;
import com.howbuy.crm.cgi.extservice.common.enums.ShowTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.agreement.SupplementalAgreementSignStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.product.ProductBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.product.ProductSubBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherAuditStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherTypeEnum;
import com.howbuy.crm.cgi.extservice.common.utils.AppVersionUtils;
import com.howbuy.crm.cgi.extservice.common.utils.NumberUtils;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.common.utils.product.ProductUtils;
import com.howbuy.crm.cgi.extservice.request.dtmsorder.QueryFundBalanceDetailRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.SimuBalanceBaseRequest;
import com.howbuy.crm.cgi.extservice.vo.AssetsDetailVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.AssetFundDetailVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.AssetFundTypeVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.FundItemVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.*;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HkAppCenterCashTabVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HkAppCenterFundTabVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.QueryBuyOrSellReqDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.cash.HkCashBalanceChangeDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.agreement.SupplementalAgreementDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.piggy.PiggyPayVoucherRecordPageDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundCanTradeDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.SalesChildFundDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.BalanceBeanDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryBalanceDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.UnconfirmeProductDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hksimu.AssetsDetailDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hksimu.FundDetailItemDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hksimu.TotalAssetDetailInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hksimu.TotalAssetInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hktraderecord.AssetDetailInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hktraderecord.AssetInfoDTO;
import com.howbuy.crm.cgi.manager.enums.TradeButtonShowEnum;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.JudgeBuyOrSellOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.PiggyPayVoucherOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.QueryBalanceOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.QueryHkCashBalanceOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.agreement.SupplementalAgreementOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkSimuBalanceOuterService;
import com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.QueryBalanceDetailResponse;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/3/1 14:55
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkSimuBalanceService {

    @Autowired
    private HkSimuBalanceOuterService hkSimuBalanceOuterService;

    @Autowired
    private QueryBalanceOuterService queryBalanceOuterService;

    @Autowired
    private JudgeBuyOrSellOuterService judgeSellOuterService;

    @Autowired
    private QueryFundBasicInfoOuterService queryFundBasicInfoOuterService;

    @Resource
    private PiggyPayVoucherOuterService piggyPayVoucherOuterService;

    @Resource
    private QueryHkCashBalanceOuterService queryHkCashBalanceOuterService;

    @Resource
    private QueryHkCashBalanceOuterService queryHkCashBalanceService;

    @Resource
    private SupplementalAgreementOuterService supplementalAgreementOuterService;

    @Value("${app.cust.asset.voucher.limit}")
    private String voucherLimit;

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TotalAssetInfoVO
     * @description:(查询个人总持仓相关信息)
     * @author: xufanchao
     * @date: 2024/3/5 10:58
     * @since JDK 1.8
     */
    public TotalAssetInfoVO queryBalance(SimuBalanceBaseRequest request) throws ExecutionException, InterruptedException {
        // 获取香港客户号
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        // 获取登录ip
        String ip = RequestUtil.getParameter(Constants.CUST_IP);
        TotalAssetInfoDTO totalAssetInfoDTO = hkSimuBalanceOuterService.querySimuBalance(loginHkCustNo, null, ip, request.getCurrency());
        // 数据格式转换
        TotalAssetInfoVO totalAssetInfoVO = convertToTotalAssetInfoVO(totalAssetInfoDTO);
        // 版本号控制,2.2新功能需求
        initCustBalanceAssertInfo(request, loginHkCustNo, totalAssetInfoDTO, totalAssetInfoVO);
        //查询用户所有的打款凭证信息,筛选出来审核中的和审核不通过的
        statisticsPayVoucherAuditAndNotPassCount(loginHkCustNo, totalAssetInfoVO);
        // 获取用户需要签署的补充协议
        getSupplementalAgreementVO(loginHkCustNo,totalAssetInfoVO);

        return totalAssetInfoVO;
    }

    /**
     * @param loginHkCustNo
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TotalAssetInfoVO.SupplementalAgreementVO
     * @description:获取用户的补充协议信息
     * @author: jinqing.rao
     * @date: 2025/3/7 13:07
     * @since JDK 1.8
     */
    private void getSupplementalAgreementVO(String loginHkCustNo,TotalAssetInfoVO totalAssetInfoVO) {
        TotalAssetInfoVO.SupplementalAgreementVO supplementalAgreementVO = new TotalAssetInfoVO.SupplementalAgreementVO();
        // 捕获异常,避免影响正常的持仓查询
        try {
            List<SupplementalAgreementDTO> supplementalAgreementDTOS = supplementalAgreementOuterService.querySupplementalAgreementList(loginHkCustNo,
                    SupplementalAgreementSignStatusEnum.UNSIGNED.getCode(),null);
            if (CollectionUtils.isEmpty(supplementalAgreementDTOS)) {
                totalAssetInfoVO.setSupplementalAgreementInfo(supplementalAgreementVO);
                totalAssetInfoVO.setAgencyAgreementCount("0");
                return;
            }
            // 根据基金编码分组
            Map<String, List<SupplementalAgreementDTO>> fundCodeMap = supplementalAgreementDTOS.stream()
                    .filter(f -> DateUtils.isAfterCurrentDateTime(f.getAgreementSignEndDt(), DateUtils.YYYYMMDDHHMMSS))
                    .collect(Collectors.groupingBy(SupplementalAgreementDTO::getFundCode));

            if(fundCodeMap.isEmpty()){
                totalAssetInfoVO.setSupplementalAgreementInfo(supplementalAgreementVO);
                totalAssetInfoVO.setAgencyAgreementCount("0");
                return;
            }
            totalAssetInfoVO.setAgencyAgreementCount(String.valueOf(fundCodeMap.size()));
            if(fundCodeMap.size() == 1){
                supplementalAgreementVO.setFundCode(fundCodeMap.entrySet().iterator().next().getValue().get(0).getFundCode());
            }
        } catch (Exception e) {
            log.error("getSupplementalAgreementVO>>>获取用户需要签署的补充协议失败", e);
        }
        totalAssetInfoVO.setSupplementalAgreementInfo(supplementalAgreementVO);
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.BalanceListInfoVO
     * @description: 获取基金资产详情
     * @author: jinqing.rao
     * @date: 2025/4/7 10:20
     * @since JDK 1.8
     */
    public BalanceListInfoVO queryBalanceDetailByFund(QueryFundBalanceDetailRequest request) {
        QueryBalanceDTO queryBalanceDTO = new QueryBalanceDTO();
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        queryBalanceDTO.setFundCode(request.getFundCode());
        queryBalanceDTO.setHkCustNo(loginHkCustNo);

        QueryBalanceDetailResponse queryBalanceDetailResponse = queryBalanceOuterService.queryBalanceDetail(queryBalanceDTO);
        return convertToBalanceListInfoVO(queryBalanceDetailResponse);

    }

    /**
     * @param queryBalanceDetailResponse
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.BalanceListInfoVO
     * @description: 类型转换
     * @author: jinqing.rao
     * @date: 2025/4/7 10:19
     * @since JDK 1.8
     */
    private BalanceListInfoVO convertToBalanceListInfoVO(QueryBalanceDetailResponse queryBalanceDetailResponse) {
        BalanceListInfoVO balanceListInfoVO = new BalanceListInfoVO();
        if (queryBalanceDetailResponse != null) {
            balanceListInfoVO.setTotalBalanceVol(queryBalanceDetailResponse.getTotalBalanceVol());
            balanceListInfoVO.setAvailableBalanceVol(queryBalanceDetailResponse.getAvailableBalanceVol());
            balanceListInfoVO.setUnavailableBalanceVol(queryBalanceDetailResponse.getUnavailableBalanceVol());
            balanceListInfoVO.setSupportExt(queryBalanceDetailResponse.getSupportExt());
            balanceListInfoVO.setBalanceDetailGroupInfoVOList(transBalanceDetailGroupsList(queryBalanceDetailResponse.getBalanceDetailInfoList()));
        }
        return balanceListInfoVO;
    }

    /**
     * @param balanceDetailInfoList
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.BalanceDetailGroupInfoVO>
     * @description: 资产详情分组
     * @author: jinqing.rao
     * @date: 2025/4/7 10:19
     * @since JDK 1.8
     */
    private List<BalanceDetailGroupInfoVO> transBalanceDetailGroupsList(List<com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.BalanceDetailGroupInfoVO> balanceDetailInfoList) {
        List<BalanceDetailGroupInfoVO> balanceDetailGroupInfoVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(balanceDetailInfoList)) {

            balanceDetailInfoList.forEach(balanceDetailGroupInfoVO -> {
                BalanceDetailGroupInfoVO balanceDetailGroupInfoVO1 = new BalanceDetailGroupInfoVO();
                balanceDetailGroupInfoVO1.setFundName(balanceDetailGroupInfoVO.getFundName());
                balanceDetailGroupInfoVO1.setBalanceDetailList(transBalanceDetail(balanceDetailGroupInfoVO.getBalanceDetailList()));
                balanceDetailGroupInfoVOList.add(balanceDetailGroupInfoVO1);
            });
        }
        return balanceDetailGroupInfoVOList;
    }

    /**
     * @param balanceDetailList
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.BalanceDetailInfoVO>
     * @description: 字长详情转换
     * @author: jinqing.rao
     * @date: 2025/4/7 10:19
     * @since JDK 1.8
     */
    private List<BalanceDetailInfoVO> transBalanceDetail(List<com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.BalanceDetailInfoVO> balanceDetailList) {
        List<BalanceDetailInfoVO> balanceDetailInfoVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(balanceDetailList)) {
            balanceDetailList.forEach(balanceDetailInfoVO -> {
                BalanceDetailInfoVO balanceDetailInfoVO1 = new BalanceDetailInfoVO();
                balanceDetailInfoVO1.setBalanceVol(balanceDetailInfoVO.getBalanceVol());
                balanceDetailInfoVO1.setBalanceVolConfirmDate(balanceDetailInfoVO.getBalanceVolConfirmDate());
                balanceDetailInfoVO1.setLockPeriodExpireDate(balanceDetailInfoVO.getLockPeriodExpireDate());
                balanceDetailInfoVOList.add(balanceDetailInfoVO1);
            });
        }
        return balanceDetailInfoVOList;
    }


    /**
     * @param request
     * @param loginHkCustNo
     * @param totalAssetInfoDTO
     * @param totalAssetInfoVO
     * @return void
     * @description:初始化用户资产中心数据
     * @author: jinqing.rao
     * @date: 2024/8/20 17:30
     * @since JDK 1.8
     */
    private void initCustBalanceAssertInfo(SimuBalanceBaseRequest request, String loginHkCustNo, TotalAssetInfoDTO totalAssetInfoDTO, TotalAssetInfoVO totalAssetInfoVO) {
        boolean compareVersion = AppVersionUtils.compareVersion(request.getVersion(), AppVersionUtils.APP_VERSION_2_0_0);
        if (compareVersion) {
            //获取基金持仓总额和储蓄罐持仓总额，以及对应的币种汇率
            buildCxgAssetOrFundAsset(totalAssetInfoDTO, totalAssetInfoVO,request.getCurrency());
            HkCashBalanceChangeDTO hkCashBalanceChangeDTO = getHkCustNoCashBalancePair(request.getCurrency(), loginHkCustNo);
            // 展示币种对应的总资产
            BigDecimal disCurTotalAsset = totalAssetInfoDTO.getDisCurTotalAsset();
            // 展示币种对应的在途资产
            BigDecimal disCurInTransitTradeAmt = totalAssetInfoDTO.getDisCurInTransitTradeAmt();
            BigDecimal totalAmt = NumberUtils.addBigdecimal(disCurTotalAsset, hkCashBalanceChangeDTO.getTotalBalance());
            totalAssetInfoVO.setTotalAsset(BigDecimal.ZERO.equals(totalAmt) ? "0.00" : NumberUtils.bigDecimalToString(totalAmt));
            //在途资产
            totalAssetInfoVO.setInTransitTradeAmt(null == disCurInTransitTradeAmt ? "0" : NumberUtils.bigDecimalToString(disCurInTransitTradeAmt));
            //基金总资产
            String fundAssert = NumberUtils.bigDecimalToString(totalAssetInfoDTO.getFundTotalAsset(), 2, RoundingMode.DOWN);
            totalAssetInfoVO.setFundAsset(assetFormat(fundAssert));
            //储蓄罐总资产
            String piggyBankAssert = NumberUtils.bigDecimalToString(totalAssetInfoDTO.getPiggyTotalAssert(), 2, RoundingMode.DOWN);
            totalAssetInfoVO.setPiggyBankAsset(assetFormat(piggyBankAssert));
            //现金账户余额
            String cashAssert = NumberUtils.bigDecimalToString(hkCashBalanceChangeDTO.getTotalBalance());
            totalAssetInfoVO.setCashBalanceAsset(assetFormat(cashAssert));
            //现金账户余额时间
            totalAssetInfoVO.setCashBalanceAssetDate(hkCashBalanceChangeDTO.getDataDt());
        }
    }

    /**
     * @param totalAssetInfoDTO
     * @return void
     * @description: 拆分储蓄罐或者基金资产
     * @author: jinqing.rao
     * @date: 2025/4/7 10:18
     * @since JDK 1.8
     */
    private static void buildCxgAssetOrFundAsset(TotalAssetInfoDTO totalAssetInfoDTO, TotalAssetInfoVO totalAssetInfoVO,String currency) {
        // 基金资产包含的汇率关系
        List<TotalAssetInfoVO.BalCurrencyRateVO> fundBalanceRateList = new ArrayList<>();
        // 储蓄罐资产包含的汇率关系
        List<TotalAssetInfoVO.BalCurrencyRateVO> piggyBalanceRateList = new ArrayList<>();
        // 总资产的包含的换算汇率
        List<TotalAssetInfoVO.BalCurrencyRateVO> totalBalanceRateList = new ArrayList<>();

        List<BalanceBeanDTO> balanceList = totalAssetInfoDTO.getBalanceBeanDTOS();
        if (CollectionUtils.isNotEmpty(balanceList)) {
            BigDecimal fundTotalAsset = BigDecimal.ZERO;
            BigDecimal cxgTotalAsset = BigDecimal.ZERO;
            for (BalanceBeanDTO balanceBeanDTO : balanceList) {
                if (null == balanceBeanDTO.getDisCurMarketValue()) {
                    continue;
                }
                // 总资产的包含的换算汇率
                buildBalanceRateList(balanceBeanDTO, totalBalanceRateList,currency);
                totalAssetInfoVO.setTotalBalanceRateList(totalBalanceRateList);
                if (YesNoEnum.YES.getCode().equals(balanceBeanDTO.getSfhwcxg())) {
                    cxgTotalAsset = cxgTotalAsset.add(balanceBeanDTO.getDisCurMarketValue());
                    // 储蓄罐对应的汇率计算
                    buildBalanceRateList(balanceBeanDTO, piggyBalanceRateList,currency);
                } else {
                    fundTotalAsset = getCustFundTotalAsset(balanceBeanDTO, fundTotalAsset);
                    // 基金资产对应的汇率计算
                    buildBalanceRateList(balanceBeanDTO, fundBalanceRateList,currency);
                }
            }
            // 基金对应的资产金额
            totalAssetInfoDTO.setFundTotalAsset(fundTotalAsset);
            totalAssetInfoVO.setFundBalanceRateList(fundBalanceRateList);
            // 储蓄罐对应的资产金额
            totalAssetInfoDTO.setPiggyTotalAssert(cxgTotalAsset);
            totalAssetInfoVO.setPiggyBalanceRateList(piggyBalanceRateList);
        }
        // 未确认明细
        List<UnconfirmeProductDTO> unConfirmProducts = totalAssetInfoDTO.getUnConfirmProducts();
        // 总资产的包含的换算汇率
        List<TotalAssetInfoVO.BalCurrencyRateVO> inTransitBalanceRateList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(unConfirmProducts)) {
            unConfirmProducts.forEach(unConfirmProductDTO -> {
                buildUnConfirmBalanceRateList(unConfirmProductDTO, inTransitBalanceRateList,currency);
            });
            totalAssetInfoVO.setInTransitBalanceRateList(inTransitBalanceRateList);
        }
    }

    /**
     * @param balanceBeanDTO  资金详情
     * @param balanceRateList 资金详情对应的汇率
     * @return void
     * @description: 构建储蓄罐或者基金资产对应的汇率关系
     * @author: jinqing.rao
     * @date: 2024/10/14 20:07
     * @since JDK 1.8
     */
    private static void buildBalanceRateList(BalanceBeanDTO balanceBeanDTO, List<TotalAssetInfoVO.BalCurrencyRateVO> balanceRateList,String currency) {
        if (balanceBeanDTO.getRmbZJJ() == null || balanceBeanDTO.getRmbZJJ().compareTo(BigDecimal.ZERO) == 0) {
            // 过滤点没有汇率的数据
            return;
        }
        // 已经包含了汇率关系，则不处理
        if(CollectionUtils.isNotEmpty(balanceRateList)){
            boolean anyMatch = balanceRateList.stream()
                    .anyMatch(f -> StringUtils.isNotBlank(f.getCurrency()) && f.getCurrency().equals(balanceBeanDTO.getCurrency()));
            if(anyMatch){
                return;
            }
        }
        TotalAssetInfoVO.BalCurrencyRateVO balCurrencyRateVO = new TotalAssetInfoVO.BalCurrencyRateVO();
        balCurrencyRateVO.setCurrency(balanceBeanDTO.getCurrency());

        if (CurrencyEnum.RMB.getCode().equals(currency)) {
            balCurrencyRateVO.setExcRate(NumberUtils.formatDecimalPlaces(NumberUtils.divide(BigDecimal.ONE,balanceBeanDTO.getRmbZJJ(),4,  RoundingMode.DOWN),4,  RoundingMode.DOWN));
        }
        if (CurrencyEnum.USD.getCode().equals(currency)) {
            balCurrencyRateVO.setExcRate(NumberUtils.formatDecimalPlaces(balanceBeanDTO.getRmbZJJ(), 4, RoundingMode.DOWN));
        }
        // 人民币特殊处理
        if (CurrencyEnum.RMB.getCode().equals(balanceBeanDTO.getCurrency())) {
            balCurrencyRateVO.setCurrencyDesc("人民币");
        } else {
            balCurrencyRateVO.setCurrencyDesc(CurrencyEnum.getDescription(balanceBeanDTO.getCurrency()));
        }
        balCurrencyRateVO.setExcRateDate(DateUtils.getCurrentDate(DateUtils.YYYYMMDD));
        balanceRateList.add(balCurrencyRateVO);
    }

    /**
     * @param balanceBeanDTO
     * @param balanceRateList
     * @return void
     * @description: 转换未提交的数据
     * @author: jinqing.rao
     * @date: 2025/4/7 13:24
     * @since JDK 1.8
     */
    private static void buildUnConfirmBalanceRateList(UnconfirmeProductDTO balanceBeanDTO,
                                                      List<TotalAssetInfoVO.BalCurrencyRateVO> balanceRateList,String currency) {
        if (balanceBeanDTO.getRmbZJJ() == null || balanceBeanDTO.getRmbZJJ().compareTo(BigDecimal.ZERO) == 0) {
            // 过滤点没有汇率的数据
            return;
        }
        // 已经包含了汇率关系，则不处理
        if(CollectionUtils.isNotEmpty(balanceRateList)){
            boolean anyMatch = balanceRateList.stream()
                    .anyMatch(f -> StringUtils.isNotBlank(f.getCurrency()) && f.getCurrency().equals(balanceBeanDTO.getCurrency()));
            if(anyMatch){
                return;
            }
        }
        TotalAssetInfoVO.BalCurrencyRateVO balCurrencyRateVO = new TotalAssetInfoVO.BalCurrencyRateVO();
        balCurrencyRateVO.setCurrency(balanceBeanDTO.getCurrency());

        if (CurrencyEnum.RMB.getCode().equals(currency)) {
            balCurrencyRateVO.setExcRate(NumberUtils.formatDecimalPlaces(NumberUtils.divide(BigDecimal.ONE,balanceBeanDTO.getRmbZJJ(),4,  RoundingMode.DOWN),4,  RoundingMode.DOWN));
        }
        if (CurrencyEnum.USD.getCode().equals(currency)) {
            balCurrencyRateVO.setExcRate(NumberUtils.formatDecimalPlaces(balanceBeanDTO.getRmbZJJ(), 4, RoundingMode.DOWN));
        }
        // 人民币特殊处理
        if (CurrencyEnum.RMB.getCode().equals(balanceBeanDTO.getCurrency())) {
            balCurrencyRateVO.setCurrencyDesc("人民币");
        } else {
            balCurrencyRateVO.setCurrencyDesc(CurrencyEnum.getDescription(balanceBeanDTO.getCurrency()));
        }
        balCurrencyRateVO.setExcRateDate(DateUtils.getCurrentDate(DateUtils.YYYYMMDD));
        balanceRateList.add(balCurrencyRateVO);
    }

    /**
     * @param balanceBeanDTO
     * @param fundTotalAsset
     * @return java.math.BigDecimal
     * @description: 千禧年总资产
     * @author: jinqing.rao
     * @date: 2024/10/14 20:08
     * @since JDK 1.8
     */
    private static BigDecimal getCustFundTotalAsset(BalanceBeanDTO balanceBeanDTO, BigDecimal fundTotalAsset) {
        fundTotalAsset = fundTotalAsset.add(balanceBeanDTO.getDisCurMarketValue());
        // 千禧年产品需要将待投金额累计进总资产
        if (YesOrNoEnum.YES.getCode().equals(balanceBeanDTO.getQianXiFlag())) {
            if (balanceBeanDTO.getUnPaidInAmt() != null && balanceBeanDTO.getBalanceVol().compareTo(BigDecimal.ZERO) > 0) {
                fundTotalAsset = fundTotalAsset.add(balanceBeanDTO.getUnPaidInAmt());
            }
        }
        return fundTotalAsset;
    }

    /**
     * @param fundAssert
     * @return java.lang.String
     * @description: 格式转换
     * @author: jinqing.rao
     * @date: 2025/4/7 10:18
     * @since JDK 1.8
     */
    private static String assetFormat(String fundAssert) {
        return StringUtils.isBlank(fundAssert) ? "0.00" : fundAssert;
    }

    /**
     * @param currency 当前展示的币种
     * @param hkCustNo 香港客户号
     * @return HkCashBalanceChangeDTO
     * @description: 查询现金账户余额
     * 会换算成当前币种对应的市值
     * @author: jinqing.rao
     * @date: 2024/8/15 14:39
     * @since JDK 1.8
     */
    private HkCashBalanceChangeDTO getHkCustNoCashBalancePair(String currency, String hkCustNo) {
        //查询香港账户余额
        return queryHkCashBalanceOuterService.queryHkCashBalance(hkCustNo, currency);
    }

    /**
     * @param loginHkCustNo
     * @param totalAssetInfoVO
     * @return void
     * @description: 统计打款凭证审核中和审核不通过的个数
     * 1.先查询所有数据,然后根据状态筛选
     * @author: jinqing.rao
     * @date: 2024/7/31 11:24
     * @since JDK 1.8
     */
    private void statisticsPayVoucherAuditAndNotPassCount(String loginHkCustNo, TotalAssetInfoVO totalAssetInfoVO) {
        List<String> tradeChannelList = Arrays.asList(TxChannelEnum.WAP.getCode(), TxChannelEnum.HK_APP.getCode(), TxChannelEnum.H5.getCode());
        List<String> type = Arrays.asList(PayVoucherTypeEnum.DEPOSIT_CASH_ACCOUNT.getCode());
        PiggyPayVoucherRecordPageDTO piggyPayVoucherRecordPageDTO = piggyPayVoucherOuterService.queryPiggyDepositVoucherRecordList(loginHkCustNo, type, null, tradeChannelList, 1, 500);
        long underReview = 0;
        long notPassCount = 0;
        if (null != piggyPayVoucherRecordPageDTO && CollectionUtils.isNotEmpty(piggyPayVoucherRecordPageDTO.getPiggyPayVoucherRecordListDTO())) {
            List<PiggyPayVoucherRecordPageDTO.PiggyPayVoucherRecordListDTO> underReviewList = piggyPayVoucherRecordPageDTO.getPiggyPayVoucherRecordListDTO().stream().filter(this::underReview).collect(Collectors.toList());
            List<PiggyPayVoucherRecordPageDTO.PiggyPayVoucherRecordListDTO> notPassList = piggyPayVoucherRecordPageDTO.getPiggyPayVoucherRecordListDTO().stream().filter(f -> PayVoucherAuditStatusEnum.REJECT_TO_CUSTOMER.getCode().equals(f.getAuditStatus())).collect(Collectors.toList());
            // 只有一笔审核中的和审核不通过的场景,需要返回对应的凭证号
            if (CollectionUtils.isNotEmpty(underReviewList)) {
                underReview = underReviewList.size();
            }
            if (CollectionUtils.isNotEmpty(notPassList)) {
                notPassCount = notPassList.size();
            }
            // 审核中 或者 审核不通过 场景下 只有一笔打款凭证时，需要设置打款凭证号
            initOneVoucherNo(totalAssetInfoVO, underReview, notPassCount, underReviewList, notPassList);
        }
        //设置打款凭证上传最大限制
        totalAssetInfoVO.setCashBalanceAssetLimit(underReview + notPassCount > Long.parseLong(voucherLimit) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        totalAssetInfoVO.setUnderReviewCount(0 == underReview ? null : String.valueOf(underReview));
        // 审核不通过的字段赋值被删除了,赵梦竹需求删除了
    }

    /**
     * @param totalAssetInfoVO
     * @param underReview
     * @param notPassCount
     * @param underReviewList
     * @param notPassList
     * @return void
     * @description: 审核中 或者 审核不通过 场景下 只有一笔打款凭证时，需要设置打款凭证号
     * @author: jinqing.rao
     * @date: 2024/10/21 13:09
     * @since JDK 1.8
     */
    private static void initOneVoucherNo(TotalAssetInfoVO totalAssetInfoVO, long underReview, long notPassCount, List<PiggyPayVoucherRecordPageDTO.PiggyPayVoucherRecordListDTO> underReviewList, List<PiggyPayVoucherRecordPageDTO.PiggyPayVoucherRecordListDTO> notPassList) {
        //只有一笔审核中的场景
        if (underReview == 1) {
            totalAssetInfoVO.setVoucherNo(underReviewList.get(0).getVoucherNo());
        }
        // 只有一笔审核不通过的场景，需求调整了，不在展示了
    }

    /**
     * @param f
     * @return boolean
     * @description: 审核中的打款凭证
     * @author: jinqing.rao
     * @date: 2024/7/31 11:14
     * @since JDK 1.8
     */
    private boolean underReview(PiggyPayVoucherRecordPageDTO.PiggyPayVoucherRecordListDTO f) {
        return PayVoucherAuditStatusEnum.WAIT_REVIEW.getCode().equals(f.getAuditStatus());
    }

    /**
     * @param fundCode
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.AssetInfoVO
     * @description:(根据基金代码查询持仓详情数据)
     * @author: xufanchao
     * @date: 2024/3/10 19:12
     * @since JDK 1.8
     */
    public AssetInfoVO queryAssetByFundCode(String fundCode, HttpServletRequest request) {
        AssetInfoVO assetInfoVO = new AssetInfoVO();

        // 获取香港客户号
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        // 获取登录ip
        String ip = RequestUtil.getParameter(Constants.CUST_IP);
        AssetInfoDTO assetInfoDTO = hkSimuBalanceOuterService.querySimuBalaceByFundCode(loginHkCustNo, fundCode);
        // 数据格式转换
        transToAssetInfoVO(assetInfoDTO, assetInfoVO, loginHkCustNo, request);
        // 设置补签协议信息
        buildSupplementalAgreement(fundCode, assetInfoVO, loginHkCustNo);

        return assetInfoVO;
    }

    /**
     * @description: 设置补签协议信息
     * @param fundCode	基金编码
     * @param assetInfoVO	展示VO
     * @param loginHkCustNo 香港客户号
     * @return void
     * @author: jinqing.rao
     * @date: 2025/7/7 19:00
     * @since JDK 1.8
     */
    private void buildSupplementalAgreement(String fundCode, AssetInfoVO assetInfoVO, String loginHkCustNo) {
        try {
            List<String> fundCodeList = new ArrayList<>();
            // 查询当前产品的补签协议
            if(YesNoEnum.YES.getCode().equals(assetInfoVO.getMotherChildFund())){
                // 母子基金
                fundCodeList = queryFundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(fundCode);
            }else{
                // 非母子基金
                fundCodeList.add(fundCode);
            }
            // 查询补签协议
            List<SupplementalAgreementDTO> supplementalAgreementDTOList = supplementalAgreementOuterService.querySupplementalAgreementListByFundCodeList(loginHkCustNo,
                    SupplementalAgreementSignStatusEnum.UNSIGNED.getCode(), fundCodeList);
            if(CollectionUtils.isEmpty(supplementalAgreementDTOList)){
                return;
            }
            // 根据基金编码分组
            Map<String, List<SupplementalAgreementDTO>> fundCodeMap = supplementalAgreementDTOList.stream()
                    .filter(f -> DateUtils.isAfterCurrentDateTime(f.getAgreementSignEndDt(), DateUtils.YYYYMMDDHHMMSS))
                    .collect(Collectors.groupingBy(SupplementalAgreementDTO::getFundCode));

            if(fundCodeMap.isEmpty()){
                assetInfoVO.setSupplementalAgreementCount(Constants.ZERO);
                return ;
            }
            assetInfoVO.setSupplementalAgreementCount(String.valueOf(fundCodeMap.size()));
            if(fundCodeMap.size() == 1){
                assetInfoVO.setSupplementalAgreementFundCode(fundCodeMap.entrySet().iterator().next().getValue().get(0).getFundCode());
            }
        } catch (Exception e) {
            log.error("buildSupplementalAgreement  >>> 资产详情页 补签协议提示语,获取补签协议异常", e);
            assetInfoVO.setSupplementalAgreementCount(Constants.ZERO);
        }
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TotalAssetDetailInfoVO
     * @description:(查询持仓的列表接口)
     * @author: xufanchao
     * @date: 2024/3/8 15:36
     * @since JDK 1.8
     */
    public TotalAssetDetailInfoVO queryBalanceList(SimuBalanceBaseRequest request) {
        // 获取香港客户号
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        TotalAssetDetailInfoDTO totalAssetDetailInfoDTO = hkSimuBalanceOuterService.querySimuBabalnceDetailList(loginHkCustNo, null, request.getCurrency(), request.getFundTxCode());
        TotalAssetDetailInfoVO totalAssetDetailInfoVO = transToTotalAssetInfoVO(totalAssetDetailInfoDTO, request.getVersion());
        //添加现金余额
        builderCashBalanceAsset(totalAssetDetailInfoVO, totalAssetDetailInfoDTO, request);
        return totalAssetDetailInfoVO;
    }

    /**
     * @param totalAssetDetailInfoVO
     * @param totalAssetDetailInfoDTO
     * @param request
     * @return void
     * @description: 资产详情页添加现金余额到总资产上, 兼容历史代码逻辑, 隐藏需要返回****
     * @author: jinqing.rao
     * @date: 2024/8/30 15:37
     * @since JDK 1.8
     */
    private void builderCashBalanceAsset(TotalAssetDetailInfoVO totalAssetDetailInfoVO, TotalAssetDetailInfoDTO totalAssetDetailInfoDTO, SimuBalanceBaseRequest request) {
        boolean newVersion = AppVersionUtils.compareVersion(request.getVersion(), AppVersionUtils.APP_VERSION_2_0_0);
        totalAssetDetailInfoVO.setTotalAsset(totalAssetDetailInfoDTO.getTotalAsset().toString());
        if (newVersion) {
            String showAsset = totalAssetDetailInfoDTO.getShowAsset();
            HkCashBalanceChangeDTO hkCashBalanceChangeDTO = getHkCustNoCashBalancePair(request.getCurrency(), request.getHkCustNo());
            totalAssetDetailInfoVO.setCashBalanceAssetFlag(getInTransitFlag(hkCashBalanceChangeDTO.getTotalBalance()));
            //如果是隐藏逻辑,直接返回 ****,主要是兼容历史逻辑
            if (YesNoEnum.YES.getCode().equals(showAsset)) {
                totalAssetDetailInfoVO.setCashBalanceAsset("****");
                totalAssetDetailInfoVO.setTotalAsset("****");
                return;
            }
            //设置香港账户余额
            totalAssetDetailInfoVO.setCashBalanceAsset(NumberUtils.formatToThousandths(hkCashBalanceChangeDTO.getTotalBalance(), 2, RoundingMode.DOWN));
            BigDecimal totalAssert = NumberUtils.addBigdecimal(totalAssetDetailInfoDTO.getDisCurTotalAsset(), hkCashBalanceChangeDTO.getTotalBalance());
            totalAssetDetailInfoVO.setTotalAsset(NumberUtils.bigDecimalToString(totalAssert, 2, RoundingMode.DOWN));
        }
    }

    /**
     * @param totalAssetDetailInfoDTO
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TotalAssetDetailInfoVO
     * @description:(数据格式转换)
     * @author: xufanchao
     * @date: 2024/3/10 16:25
     * @since JDK 1.8
     */
    private TotalAssetDetailInfoVO transToTotalAssetInfoVO(TotalAssetDetailInfoDTO totalAssetDetailInfoDTO, String version) {
        boolean newVersion = AppVersionUtils.compareVersion(version, AppVersionUtils.APP_VERSION_2_0_0);

        String showAsset = totalAssetDetailInfoDTO.getShowAsset();
        TotalAssetDetailInfoVO totalAssetDetailInfoVO = new TotalAssetDetailInfoVO();
        //totalAssetDetailInfoVO.setTotalAsset(AmtorVolUtil.isTotalAllShow(totalAssetDetailInfoDTO.getTotalAsset(), showAsset));
        totalAssetDetailInfoVO.setShowAsset(totalAssetDetailInfoDTO.getShowAsset());
        if (CollectionUtils.isNotEmpty(totalAssetDetailInfoDTO.getTotalAssetsDetail())) {
            List<AssetsDetailVO> assetsDetailVOList = getAssetsDetailVOS(totalAssetDetailInfoDTO.getTotalAssetsDetail(), totalAssetDetailInfoDTO.getShowAsset());
            totalAssetDetailInfoVO.setTotalAssetsDetail(assetsDetailVOList);
        }
        totalAssetDetailInfoVO.setAssetFundTypeList(totalAssetDetailInfoDTO.getAssetFundTypeList().stream()
                .map(assetFundTypeDTO -> {
                    AssetFundTypeVO assetFundTypeVO = new AssetFundTypeVO();
                    assetFundTypeVO.setFundSetType(assetFundTypeDTO.getFundSetType());
                    assetFundTypeVO.setAssetFundDetailList(assetFundTypeDTO.getAssetFundDetailList().stream()
                            .map(assetFundDetailDTO -> {
                                AssetFundDetailVO assetFundDetailVO = new AssetFundDetailVO();
                                assetFundDetailVO.setFundName(assetFundDetailDTO.getFundName());
                                assetFundDetailVO.setFundCode(assetFundDetailDTO.getFundCode());
                                if (assetFundDetailDTO.getQianXiFlag().equals(YesNoEnum.YES.getCode())) {
                                    assetFundDetailVO.setShowType(ShowTypeEnum.MILLENNIUM.getCode());
                                }
                                String currencyCode = assetFundDetailDTO.getCurrencyCode();
                                assetFundDetailVO.setCurrencyMarketValue(AmtorVolUtil.isAmtShow(assetFundDetailDTO.getCurrencyMarketValue(), currencyCode, showAsset));
                                assetFundDetailVO.setCurrentAssetCurrency(AmtorVolUtil.isAmtShow(assetFundDetailDTO.getCurrentAssetCurrency(), currencyCode, showAsset));
                                // 当前币种收益 > 0 1 正数, 0 相同 ,-1 负数 showDirection
                                if (assetFundDetailDTO.getCurrentAssetCurrency() != null && assetFundDetailDTO.getCurrentAssetCurrency().compareTo(BigDecimal.ZERO) > 0) {
                                    assetFundDetailVO.setShowDirection("1");
                                } else if (assetFundDetailDTO.getCurrentAssetCurrency() != null && assetFundDetailDTO.getCurrentAssetCurrency().compareTo(BigDecimal.ZERO) == 0) {
                                    assetFundDetailVO.setShowDirection("0");
                                } else {
                                    assetFundDetailVO.setShowDirection("-1");
                                }
                                assetFundDetailVO.setBalanceFactor(AmtorVolUtil.isTwoNumShow(assetFundDetailDTO.getBalanceFactor(), showAsset));
                                assetFundDetailVO.setBalanceFactorDate(assetFundDetailDTO.getBalanceFactorDate());
                                assetFundDetailVO.setConvertFinish(assetFundDetailDTO.getConvertFinish());
                                assetFundDetailVO.setCurrencyMarketValueExFee(AmtorVolUtil.isOtherAmtShow(assetFundDetailDTO.getCurrencyMarketValueExFee(), showAsset));
                                assetFundDetailVO.setNaFlag(assetFundDetailDTO.getNaProductFeeType());
                                assetFundDetailVO.setStageEstablishFlag(assetFundDetailDTO.getStageEstablishFlag());
                                assetFundDetailVO.setIncomeCalStat(assetFundDetailDTO.getIncomeCalStat());
                                assetFundDetailVO.setCurrencyDesc(assetFundDetailDTO.getCurrencyDesc());
                                assetFundDetailVO.setReceivManageFee(AmtorVolUtil.isOtherAmtShow(assetFundDetailDTO.getReceivManageFee(), showAsset));
                                assetFundDetailVO.setReceivPreformFee(AmtorVolUtil.isOtherAmtShow(assetFundDetailDTO.getReceivPreformFee(), showAsset));
                                assetFundDetailVO.setNav(AmtorVolUtil.isShowNavAmt(assetFundDetailDTO.getNav(), currencyCode, showAsset));
                                assetFundDetailVO.setNavDivFlag(assetFundDetailDTO.getNavDivFlag());
                                assetFundTypeVO.setTotalAsset(AmtorVolUtil.isAllShow(assetFundTypeDTO.getTotalAsset(), showAsset));
                                assetFundTypeVO.setDisCurTotalAsset(AmtorVolUtil.isAllShow(assetFundTypeDTO.getDisCurTotalAsset(), showAsset));
                                totalAssetDetailInfoVO.setInTransitTradeAmtFlag(getInTransitFlag(totalAssetDetailInfoDTO.getInTransitTradeAmt()));
                                totalAssetDetailInfoVO.setInTransitTradeAmt(AmtorVolUtil.isTotalAllShowCurrency(totalAssetDetailInfoDTO.getInTransitTradeAmt(), currencyCode, showAsset));
                                assetFundDetailVO.setNavDate(assetFundDetailDTO.getNavDate());
                                assetFundDetailVO.setPaidInAmt(AmtorVolUtil.isAmtShow(assetFundDetailDTO.getPaidInAmt(), currencyCode, showAsset));
                                assetFundDetailVO.setCurrencyUnPaidInAmt(AmtorVolUtil.isAmtShow(assetFundDetailDTO.getCurrencyUnPaidInAmt(), currencyCode, showAsset));
                                assetFundDetailVO.setTotalBalanceVol(AmtorVolUtil.isVolShow(assetFundDetailDTO.getTotalBalanceVol(), showAsset));
                                assetFundDetailVO.setYieldRate(Objects.isNull(assetFundDetailDTO.getYieldRate()) ? "--" : AmtorVolUtil.getYield(assetFundDetailDTO.getYieldRate(), showAsset));
                                if (assetFundDetailDTO.getFundItemVoList().size() > 1) {
                                    assetFundDetailVO.setHasDetail(YesNoEnum.YES.getCode());
                                    assetFundDetailVO.setFundItemVOList(buildFundItemVOList(assetFundDetailDTO.getFundItemVoList(), showAsset));
                                } else {
                                    assetFundDetailVO.setHasDetail(YesNoEnum.NO.getCode());
                                }
                                return assetFundDetailVO;
                            })
                            .collect(Collectors.toList()));
                    return assetFundTypeVO;
                })
                .collect(Collectors.toList()));
        // 获取 AssetFundTypeList,根据  fundSetType 进行分组
        Map<String, List<AssetFundTypeVO>> typeFundMap = totalAssetDetailInfoVO.getAssetFundTypeList().stream().collect(Collectors.groupingBy(AssetFundTypeVO::getFundSetType));
        // 根据分组数据，计算总持仓总和 然后把基础数据合并
        List<AssetFundTypeVO> assetFundTypeList = new ArrayList<>();
        // 遍历typeFundMap 合并总资产数据
        for (List<AssetFundTypeVO> assetFundTypeVOS : typeFundMap.values()) {
            AssetFundTypeVO assetFundTypeVO = new AssetFundTypeVO();
            assetFundTypeVO.setTotalAsset(AmtorVolUtil.isAmtShow(getTotalAssetSum(assetFundTypeVOS), "", showAsset));
            if (newVersion) {
                assetFundTypeVO.setTotalAsset(AmtorVolUtil.isAmtShow(getDisCurTotalAssetSum(assetFundTypeVOS), "", showAsset));
            }
            assetFundTypeVO.setDisCurTotalAsset(AmtorVolUtil.isAmtShow(getDisCurTotalAssetSum(assetFundTypeVOS), "", showAsset));
            assetFundTypeVO.setFundSetType(assetFundTypeVOS.get(0).getFundSetType());
            assetFundTypeVO.setAssetFundDetailList(assetFundTypeVOS.stream().map(AssetFundTypeVO::getAssetFundDetailList).flatMap(List::stream).collect(Collectors.toList()));
            assetFundTypeList.add(assetFundTypeVO);
        }
        totalAssetDetailInfoVO.setAssetFundTypeList(assetFundTypeList);
        return totalAssetDetailInfoVO;
    }

    private String getInTransitFlag(BigDecimal inTransitTradeAmt) {
        // 当数据== 0 的时候返回隐藏
        if (null == inTransitTradeAmt || inTransitTradeAmt.compareTo(BigDecimal.ZERO) == 0) {
            return YesNoEnum.NO.getCode();
        }
        return YesNoEnum.YES.getCode();
    }

    public static BigDecimal getDisCurTotalAssetSum(List<AssetFundTypeVO> assetFundTypeList) {
        // 使用Stream API计算总资产合计
        return assetFundTypeList.stream()
                .map(AssetFundTypeVO::getDisCurTotalAsset)
                .filter(totalAsset -> totalAsset != null && !totalAsset.equals("****"))
                .map(totalAsset -> {
                    return new BigDecimal(totalAsset);
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * @param assetFundTypeList
     * @return double
     * @description:(数据格式转换)
     * @author: xufanchao
     * @date: 2024/3/27 11:32
     * @since JDK 1.8
     */
    public static BigDecimal getTotalAssetSum(List<AssetFundTypeVO> assetFundTypeList) {
        // 使用Stream API计算总资产合计
        return assetFundTypeList.stream()
                .map(AssetFundTypeVO::getTotalAsset)
                .filter(totalAsset -> totalAsset != null && !totalAsset.equals("****"))
                .map(totalAsset -> {
                    return new BigDecimal(totalAsset);
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * @param fundItemVoList
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.hkaccount.FundItemVO>
     * @description:(构建持仓的详情数据)
     * @author: xufanchao
     * @date: 2024/3/10 16:38
     * @since JDK 1.8
     */
    private List<FundItemVO> buildFundItemVOList(List<FundDetailItemDTO> fundItemVoList, String showAsset) {
        return fundItemVoList.stream()
                .map(fundDetailItemDTO -> {
                    FundItemVO fundItemVO = new FundItemVO();
                    String currencyCode = fundDetailItemDTO.getCurrencyCode();
                    fundItemVO.setCurrencyMarketValue(AmtorVolUtil.isAmtShow(fundDetailItemDTO.getCurrencyMarketValue(), currencyCode, showAsset));
                    fundItemVO.setNav(AmtorVolUtil.isShowNavAmt(fundDetailItemDTO.getNav(), currencyCode, showAsset));
                    fundItemVO.setNavDate(fundDetailItemDTO.getNavDate());
                    fundItemVO.setNavDivFlag(fundDetailItemDTO.getNavDivFlag());
                    fundItemVO.setCurrencyMarketValueExFee(AmtorVolUtil.isOtherAmtShow(fundDetailItemDTO.getCurrencyMarketValueExFee(), showAsset));
                    fundItemVO.setTotalBalanceVol(AmtorVolUtil.isVolShow(fundDetailItemDTO.getTotalBalanceVol(), showAsset));
                    fundItemVO.setCurrentAssetCurrency(AmtorVolUtil.isAmtShow(fundDetailItemDTO.getCurrentAssetCurrency(), currencyCode, showAsset));
                    fundItemVO.setReceivManageFee(AmtorVolUtil.isOtherAmtShow(fundDetailItemDTO.getReceivManageFee(), showAsset));
                    fundItemVO.setReceivPreformFee(AmtorVolUtil.isOtherAmtShow(fundDetailItemDTO.getReceivPreformFee(), showAsset));
                    fundItemVO.setCurrencyDesc(fundDetailItemDTO.getCurrencyDesc());
                    fundItemVO.setIncomeCalStat(fundDetailItemDTO.getIncomeCalStat());
                    fundItemVO.setNavDivFlag(fundDetailItemDTO.getNavDivFlag());
                    fundItemVO.setYieldRate(Objects.isNull(fundDetailItemDTO.getYieldRate()) ? "--" : AmtorVolUtil.getYield(fundDetailItemDTO.getYieldRate(), showAsset));
                    return fundItemVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * @param totalAssetDetailInfoDTO 资产详情页的弹框数据
     * @param showAsset               资产是否隐藏显示
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.AssetsDetailVO>
     * @description:(构建详情页的持仓数据)
     * @author: xufanchao
     * @date: 2024/3/10 16:26
     * @since JDK 1.8
     */
    private static List<AssetsDetailVO> getAssetsDetailVOS(List<AssetsDetailDTO> totalAssetDetailInfoDTO, String showAsset) {
        return totalAssetDetailInfoDTO.stream()
                .map(assetsDetailDTO -> {
                    AssetsDetailVO assetsDetailVO = new AssetsDetailVO();
                    assetsDetailVO.setFundName(assetsDetailDTO.getFundName());
                    // 当资产隐藏时，待投金额显示为****
                    if (YesNoEnum.YES.getCode().equals(showAsset)) {
                        assetsDetailVO.setUnPaidInAmt("****");
                    } else {
                        assetsDetailVO.setUnPaidInAmt(assetsDetailDTO.getUnPaidInAmt());
                    }
                    return assetsDetailVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * @param totalAssetInfoDTO
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TotalAssetInfoVO
     * @description:(数据格式转换)
     * @author: xufanchao
     * @date: 2024/3/5 10:57
     * @since JDK 1.8
     */
    private TotalAssetInfoVO convertToTotalAssetInfoVO(TotalAssetInfoDTO totalAssetInfoDTO) {
        TotalAssetInfoVO totalAssetInfoVO = new TotalAssetInfoVO();
        totalAssetInfoVO.setShowAsset(totalAssetInfoDTO.getShowAsset());
        totalAssetInfoVO.setInTransitTradeNums(Objects.isNull(totalAssetInfoDTO.getInTransitTradeNums()) ? "0" : totalAssetInfoDTO.getInTransitTradeNums());
        totalAssetInfoVO.setInTransitTradeAmt(Objects.isNull(totalAssetInfoDTO.getInTransitTradeAmt()) ? "0.00" : totalAssetInfoDTO.getInTransitTradeAmt().toString());
        totalAssetInfoVO.setInTransitDealNo(totalAssetInfoDTO.getInTransitDealNo());
        totalAssetInfoVO.setWaitSignNums(Objects.isNull(totalAssetInfoDTO.getWaitSignNums()) ? "0" : totalAssetInfoDTO.getWaitSignNums());
        totalAssetInfoVO.setFundReceivedNums(Objects.isNull(totalAssetInfoDTO.getFundReceivedNums()) ? "0" : totalAssetInfoDTO.getFundReceivedNums());
        if (CollectionUtils.isNotEmpty(totalAssetInfoDTO.getTotalAssetsDetail())) {
            List<AssetsDetailVO> assetsDetailVOList = totalAssetInfoDTO.getTotalAssetsDetail().stream()
                    .map(assetsDetailDTO -> {
                        AssetsDetailVO assetsDetailVO = new AssetsDetailVO();
                        assetsDetailVO.setFundName(assetsDetailDTO.getFundName());
                        assetsDetailVO.setUnPaidInAmt(assetsDetailDTO.getUnPaidInAmt());
                        return assetsDetailVO;
                    })
                    .collect(Collectors.toList());
            totalAssetInfoVO.setTotalAssetsDetail(assetsDetailVOList);
        }
        totalAssetInfoVO.setTotalAsset(Objects.isNull(totalAssetInfoDTO.getTotalAsset()) ? "0.00" : totalAssetInfoDTO.getTotalAsset());
        return totalAssetInfoVO;

    }


    /**
     * @param assetInfoDTO
     * @param assetInfoVO
     * @param loginHkCustNo
     * @param request
     * @return void
     * @description:(数据格式转换)
     * @author: xufanchao
     * @date: 2024/3/10 19:18
     * @since JDK 1.8
     */
    private void transToAssetInfoVO(AssetInfoDTO assetInfoDTO, AssetInfoVO assetInfoVO, String loginHkCustNo, HttpServletRequest request) {
        // 获取币种
        String currency = assetInfoDTO.getCurrency();
        assetInfoVO.setFundName(assetInfoDTO.getFundName());
        assetInfoVO.setFundCode(assetInfoDTO.getFundCode());
        assetInfoVO.setCurrencyMarketValue(AmtorVolUtil.getAmt(assetInfoDTO.getCurrencyMarketValue(), currency));
        assetInfoVO.setCurrencyDesc(assetInfoDTO.getCurrencyDesc());
        assetInfoVO.setCurrency(assetInfoDTO.getCurrency());
        assetInfoVO.setNav(AmtorVolUtil.getNavAmt(assetInfoDTO.getNav(), assetInfoDTO.getCurrency()));
        assetInfoVO.setNavDate(assetInfoDTO.getNavDate());
        assetInfoVO.setPaidInAmt(AmtorVolUtil.getAmt(assetInfoDTO.getPaidInAmt(), currency));
        assetInfoVO.setCurrencyUnPaidInAmt(AmtorVolUtil.getAmt(assetInfoDTO.getCurrencyUnPaidInAmt(), currency));
        assetInfoVO.setTotalBalanceVol(AmtorVolUtil.getVol(assetInfoDTO.getTotalBalanceVol()));
        assetInfoVO.setDayIncomeRate(AmtorVolUtil.getYield(assetInfoDTO.getDayIncomeRate(), null));
        assetInfoVO.setNavDivFlag(assetInfoDTO.getNavDivFlag());
        assetInfoVO.setCrisisFlag(assetInfoDTO.getCrisisFlag());
        assetInfoVO.setQianXiFlag(assetInfoDTO.getQianXiFlag());
        assetInfoVO.setStageEstablishFlag(assetInfoDTO.getStageEstablishFlag());
        assetInfoVO.setBalanceFactor(AmtorVolUtil.getTwoNum(assetInfoDTO.getBalanceFactor()));
        assetInfoVO.setBalanceFactorDate(assetInfoDTO.getBalanceFactorDate());
        assetInfoVO.setConvertFinish(assetInfoDTO.getConvertFinish());
        assetInfoVO.setNaFlag(assetInfoDTO.getNaFlag());
        assetInfoVO.setIncomeCalStat(assetInfoDTO.getIncomeCalStat());
        assetInfoVO.setReceivManageFee(AmtorVolUtil.getOtherAmt(assetInfoDTO.getReceivManageFee(), currency));
        assetInfoVO.setReceivPreformFee(AmtorVolUtil.getOtherAmt(assetInfoDTO.getReceivPreformFee(), currency));
        assetInfoVO.setCurrencyMarketValueExFee(AmtorVolUtil.getAmt(assetInfoDTO.getCurrencyMarketValueExFee(), currency));
        assetInfoVO.setCurrentAssetCurrency(AmtorVolUtil.getAmt(assetInfoDTO.getCurrentAssetCurrency(), currency));
        assetInfoVO.setCurrencyDesc(assetInfoDTO.getCurrencyDesc());
        assetInfoVO.setYieldRate(Objects.isNull(assetInfoDTO.getYieldRate()) ? "--" : assetInfoDTO.getYieldRate().toString());
        assetInfoVO.setAssetDetailInfoVOList(buildAssetDetailInfoVOList(assetInfoDTO.getAssetDetailInfoVOList()));
        // 产品业务大类
        ProductBizTypeEnum productBizTypeEnum = ProductUtils.getProductType(assetInfoDTO.getHkSaleFlag(), assetInfoDTO.getSfhwcxg(), assetInfoDTO.getProductType(), assetInfoDTO.getProductSubType());
        if(null != productBizTypeEnum){
            assetInfoVO.setProductBusiType(productBizTypeEnum.getCode());
        }
        // 产品业务子类
        ProductSubBizTypeEnum productSubBizTypeEnum = ProductUtils.getProductSubType(assetInfoDTO.getHkSaleFlag(), assetInfoDTO.getSfhwcxg(), assetInfoDTO.getProductType(), assetInfoDTO.getProductSubType());
        if(null != productSubBizTypeEnum){
            assetInfoVO.setProductBusiSubType(productSubBizTypeEnum.getCode());
        }
        // 最新收益 四舍五入
        assetInfoVO.setLatestIncome(BigDecimalUtils.bigDecimalToString(assetInfoDTO.getLatestIncome(), 2, RoundingMode.HALF_UP));
        // 最新收益时间 (时间有特殊的格式化)
        assetInfoVO.setLatestIncomeDate(DateUtils.formatIfNotCurrentYear(assetInfoDTO.getLatestIncomeDate()));
        // 最新收益率
        assetInfoVO.setLatestIncomeRate(BigDecimalUtils.bigDecimalToString(assetInfoDTO.getLatestIncomeRate(), 4, RoundingMode.HALF_UP));
        // 初始化成本
        assetInfoVO.setInitInvestCost(NumberUtils.bigDecimalToString(assetInfoDTO.getInitInvestCost(),2,RoundingMode.DOWN));
        // 累计收益
        assetInfoVO.setAccumIncome(NumberUtils.bigDecimalToString(assetInfoDTO.getAccumIncome(),2,RoundingMode.DOWN));
        // 累计总回款
        assetInfoVO.setAccumTotalBack(NumberUtils.bigDecimalToString(assetInfoDTO.getAccumCollection(),2,RoundingMode.DOWN));
        // 累计总回款比例 总回款 / 初始化成本
        if(null != assetInfoDTO.getAccumCollection() && null != assetInfoDTO.getInitInvestCost() && BigDecimal.ZERO.compareTo(assetInfoDTO.getInitInvestCost()) < 0){
            BigDecimal accumBackRatio = NumberUtils.divide(assetInfoDTO.getAccumCollection(), assetInfoDTO.getInitInvestCost(), 2, RoundingMode.HALF_UP);
            assetInfoVO.setAccumBackRatio(NumberUtils.bigDecimalToString(accumBackRatio,2,RoundingMode.HALF_UP));
        }

        // 产品期限说明
        assetInfoVO.setProductTermDes(assetInfoDTO.getCpqxsm());
        // 产品期限编码(非交易拼接好返回)
        assetInfoVO.setProductTermCode(assetInfoDTO.getProductTermCode());
        // 单位成本
        assetInfoVO.setUnitCost(NumberUtils.bigDecimalToString(assetInfoDTO.getUnitCost(),2,RoundingMode.DOWN));
        // 累计认缴金额
        assetInfoVO.setSubTotalAmt(NumberUtils.bigDecimalToString(assetInfoDTO.getSubTotalAmt(),2,RoundingMode.DOWN));
        // 累计实缴百分比
        assetInfoVO.setPaidSubTotalRatio(assetInfoDTO.getPaidSubTotalRatio());
        // 累计认缴金额
        assetInfoVO.setPaidTotalAmt(NumberUtils.bigDecimalToString(assetInfoDTO.getPaidTotalAmt(),2,RoundingMode.DOWN));
        // 查询基金信息
        FundBasicInfoDTO fundBasicInfoDTO = queryFundBasicInfoOuterService.queryFundBasicInfo(assetInfoDTO.getFundCode());
        // 分次CALL
        assetInfoVO.setGradationCall(fundBasicInfoDTO.getGradationCall());
        //是否可买入和卖出
        canBuyAndSell(assetInfoVO, loginHkCustNo, request);
    }

    /**
     * @param assetInfoVO
     * @param loginHkCustNo
     * @param request
     * @return void
     * @description:是否可买入和卖出
     * <AUTHOR>
     * @date 2024/5/29 18:18
     * @since JDK 1.8
     */
    private void canBuyAndSell(AssetInfoVO assetInfoVO, String loginHkCustNo, HttpServletRequest request) {
        // 查询母子基金结构
        SalesChildFundDTO salesChildFundDTO = queryFundBasicInfoOuterService.querySalesChildFundByMainFundCode(assetInfoVO.getFundCode());
        // 买入按钮处理
        // 非母子结构基金
        if (Objects.isNull(salesChildFundDTO) || YesNoEnum.NO.getCode().equals(salesChildFundDTO.getIsMotherChildFund())) {
            FundCanTradeDTO fundCanTradeDTO = judgeSellOuterService.queryCanBuy(buildQueryDTO(assetInfoVO.getFundCode(), loginHkCustNo, request));
            assetInfoVO.setIsCanBuy(fundCanTradeDTO.getTradeStatus());
            assetInfoVO.setNotCanBuyCode(fundCanTradeDTO.getTradeCode());
        } else {
            // 母子结构基金
            // 在售子基金个数==1
            if (CollectionUtils.isNotEmpty(salesChildFundDTO.getSalesChildFundCodeList())
                    && salesChildFundDTO.getSalesChildFundCodeList().size() == 1) {
                assetInfoVO.setBuySubFundCode(salesChildFundDTO.getSalesChildFundCodeList().get(0));
                FundCanTradeDTO fundCanTradeDTO = judgeSellOuterService.queryCanBuy(buildQueryDTO(assetInfoVO.getBuySubFundCode(), loginHkCustNo, request));
                assetInfoVO.setIsCanBuy(fundCanTradeDTO.getTradeStatus());
                assetInfoVO.setNotCanBuyCode(fundCanTradeDTO.getTradeCode());
            } else {
                assetInfoVO.setIsCanBuy(TradeButtonShowEnum.CAN_NOT_TRADE.getCode());
            }
        }
        // 卖出按钮处理
        // 卖出按钮根据基金代码查询是否可卖出
        FundCanTradeDTO fundCanTradeDTO = judgeSellOuterService.queryCanSell(buildQueryDTO(assetInfoVO.getFundCode(), loginHkCustNo, request));
        assetInfoVO.setIsCanSell(fundCanTradeDTO.getTradeStatus());
        assetInfoVO.setNotCanSellCode(fundCanTradeDTO.getTradeCode());
        // 详情列表为空直接返回
        if (CollectionUtils.isEmpty(assetInfoVO.getAssetDetailInfoVOList())) {
            return;
        }
        // 详情列表 ==1
        if (assetInfoVO.getAssetDetailInfoVOList().size() == 1) {
            AssetDetailInfoVO assetDetailInfoVO = assetInfoVO.getAssetDetailInfoVOList().get(0);
            // 母子结构基金 卖出按钮根据子基金查询是否可卖出
            if (Objects.nonNull(salesChildFundDTO) && YesNoEnum.YES.getCode().equals(salesChildFundDTO.getIsMotherChildFund())) {
                FundCanTradeDTO subFundCanTradeDTO = judgeSellOuterService.queryCanSell(buildQueryDTO(assetDetailInfoVO.getSubFundCode(), loginHkCustNo, request));
                assetInfoVO.setIsCanSell(subFundCanTradeDTO.getTradeStatus());
                assetInfoVO.setNotCanSellCode(subFundCanTradeDTO.getTradeCode());
            }
            // 子基金卖出按钮不展示
            assetDetailInfoVO.setIsCanSell(TradeButtonShowEnum.NOT_SHOW.getCode());
        } else {
            // 详情列表 > 1
            // 卖出按钮不展示
            assetInfoVO.setIsCanSell(TradeButtonShowEnum.NOT_SHOW.getCode());
            // 子基金卖出按钮根据子基金查询是否可卖出
            assetInfoVO.getAssetDetailInfoVOList().forEach(assetDetailInfoVO -> {
                FundCanTradeDTO subFundCanTradeDTO = judgeSellOuterService.queryCanSell(buildQueryDTO(assetDetailInfoVO.getSubFundCode(), loginHkCustNo, request));
                assetDetailInfoVO.setIsCanSell(subFundCanTradeDTO.getTradeStatus());
                assetDetailInfoVO.setNotCanSellCode(subFundCanTradeDTO.getTradeCode());
            });
        }
    }

    /**
     * @description: 构建查询参数
     * @param fundCode
     * @param loginHkCustNo
     * @param request
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.QueryBuyOrSellReqDTO
     * @author: jinqing.rao
     * @date: 2025/4/25 10:44
     * @since JDK 1.8
     */
    private QueryBuyOrSellReqDTO buildQueryDTO(String fundCode, String loginHkCustNo, HttpServletRequest request) {
        QueryBuyOrSellReqDTO queryBuyOrSellReqDTO = new QueryBuyOrSellReqDTO();

        queryBuyOrSellReqDTO.setAppTm(DateUtil.date2String(new Date(), DateUtil.StR_PATTERN_HHMMSS));
        queryBuyOrSellReqDTO.setAppDt(DateUtil.date2String(new Date(), DateUtil.SHORT_DATEPATTERN));
        queryBuyOrSellReqDTO.setFundCode(fundCode);
        queryBuyOrSellReqDTO.setTradeChannel(TxChannelEnum.HK_APP.getCode());
        queryBuyOrSellReqDTO.setIpAddress(WebUtil.getCustIP(request));
        queryBuyOrSellReqDTO.setHkCustNo(loginHkCustNo);
        return queryBuyOrSellReqDTO;
    }

    /**
     * @param assetDetailInfoVOList
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.AssetDetailInfoVO>
     * @description:(数据格式转换)
     * @author: xufanchao
     * @date: 2024/3/10 19:18
     * @since JDK 1.8
     */
    private List<AssetDetailInfoVO> buildAssetDetailInfoVOList(List<AssetDetailInfoDTO> assetDetailInfoVOList) {
        List<AssetDetailInfoVO> assetDetailInfoVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(assetDetailInfoVOList)) {
            assetDetailInfoVOList.forEach(assetDetailInfoDTO -> {
                AssetDetailInfoVO assetDetailInfoVO = new AssetDetailInfoVO();
                String currency = assetDetailInfoDTO.getCurrency();
                assetDetailInfoVO.setFundName(assetDetailInfoDTO.getFundName());
                assetDetailInfoVO.setFundCode(assetDetailInfoDTO.getFundCode());
                assetDetailInfoVO.setSubFundName(assetDetailInfoDTO.getSubFundName());
                assetDetailInfoVO.setSubFundCode(assetDetailInfoDTO.getSubFundCode());
                assetDetailInfoVO.setCurrencyMarketValue(AmtorVolUtil.getAmt(assetDetailInfoDTO.getCurrencyMarketValue(), currency));
                assetDetailInfoVO.setCurrencyDesc(assetDetailInfoDTO.getCurrencyDesc());
                assetDetailInfoVO.setCurrency(assetDetailInfoDTO.getCurrency());
                assetDetailInfoVO.setNav(AmtorVolUtil.getNavAmt(assetDetailInfoDTO.getNav(), currency));
                assetDetailInfoVO.setNavDate(assetDetailInfoDTO.getNavDate());
                assetDetailInfoVO.setPaidInAmt(AmtorVolUtil.getAmt(assetDetailInfoDTO.getPaidInAmt(), currency));
                assetDetailInfoVO.setCurrencyUnPaidInAmt(AmtorVolUtil.getAmt(assetDetailInfoDTO.getCurrencyUnPaidInAmt(), currency));
                assetDetailInfoVO.setTotalBalanceVol(AmtorVolUtil.getVol(assetDetailInfoDTO.getTotalBalanceVol()));
                assetDetailInfoVO.setDayIncomeRate(AmtorVolUtil.getYield(assetDetailInfoDTO.getDayIncomeRate(), ""));
                assetDetailInfoVO.setNavDivFlag(assetDetailInfoDTO.getNavDivFlag());
                assetDetailInfoVO.setCrisisFlag(assetDetailInfoDTO.getCrisisFlag());
                assetDetailInfoVO.setQianXiFlag(assetDetailInfoDTO.getQianXiFlag());
                assetDetailInfoVO.setStageEstablishFlag(assetDetailInfoDTO.getStageEstablishFlag());
                assetDetailInfoVO.setNaFlag(assetDetailInfoDTO.getNaFlag());
                assetDetailInfoVO.setBalanceFactor(AmtorVolUtil.getTwoNum(assetDetailInfoDTO.getBalanceFactor()));
                assetDetailInfoVO.setBalanceFactorDate(assetDetailInfoDTO.getBalanceFactorDate());
                assetDetailInfoVO.setIncomeCalStat(assetDetailInfoDTO.getIncomeCalStat());
                assetDetailInfoVO.setConvertFinish(assetDetailInfoDTO.getConvertFinish());
                assetDetailInfoVO.setReceivManageFee(AmtorVolUtil.getOtherAmt(assetDetailInfoDTO.getReceivManageFee(), currency));
                assetDetailInfoVO.setReceivPreformFee(AmtorVolUtil.getOtherAmt(assetDetailInfoDTO.getReceivPreformFee(), currency));
                assetDetailInfoVO.setCurrencyMarketValueExFee(AmtorVolUtil.getAmt(assetDetailInfoDTO.getCurrencyMarketValueExFee(), currency));
                assetDetailInfoVO.setCurrentAssetCurrency(AmtorVolUtil.getAmt(assetDetailInfoDTO.getCurrentAssetCurrency(), currency));
                assetDetailInfoVO.setCurrencyDesc(assetDetailInfoDTO.getCurrencyDesc());
                assetDetailInfoVO.setYieldRate(Objects.isNull(assetDetailInfoDTO.getYieldRate()) ? "--" : assetDetailInfoDTO.getYieldRate().toString());
                assetDetailInfoVOS.add(assetDetailInfoVO);
            });
        }
        return assetDetailInfoVOS;
    }


    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HkAppCenterFundTabVO
     * @description: 获取基金TAB页的基础信息
     * @author: jinqing.rao
     * @date: 2024/8/7 10:22
     * @since JDK 1.8
     */
    public HkAppCenterFundTabVO queryFundTabInfo(SimuBalanceBaseRequest request) {
        HkAppCenterFundTabVO appCenterFundTabVO = new HkAppCenterFundTabVO();
        // 获取香港客户号
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        // 获取登录ip
        String ip = RequestUtil.getParameter(Constants.CUST_IP);
        TotalAssetInfoDTO totalAssetInfoDTO = hkSimuBalanceOuterService.querySimuBalanceFundHold(loginHkCustNo, null, ip, request.getCurrency());
        //获取基金持仓总额和储蓄罐持仓总额
        List<BalanceBeanDTO> balanceList = totalAssetInfoDTO.getBalanceBeanDTOS();
        if (CollectionUtils.isNotEmpty(balanceList)) {
            //基金总资产
            BigDecimal fundTotalAsset = BigDecimal.ZERO;
            //千禧年产品的待投金额
            BigDecimal currencyUnPaidInAmt = BigDecimal.ZERO;
            //千禧年产品基金Code和待投金额MAP
            Map<String, BigDecimal> qxnProductMap = new HashMap<>();
            for (BalanceBeanDTO balanceBeanDTO : balanceList) {
                //判断是否是千禧年的待投金额
                if (YesNoEnum.YES.getCode().equals(balanceBeanDTO.getQianXiFlag())) {
                    BigDecimal unPaidInAmt = balanceBeanDTO.getUnPaidInAmt();
                    currencyUnPaidInAmt = NumberUtils.addBigdecimal(currencyUnPaidInAmt, unPaidInAmt);
                    //同一个产品, 取最大
                    qxnProductMap.put(balanceBeanDTO.getProductCode(), currencyUnPaidInAmt);
                }
                //非储蓄罐产品
                if (!YesNoEnum.YES.getCode().equals(balanceBeanDTO.getSfhwcxg())) {
                    fundTotalAsset = NumberUtils.addBigdecimal(fundTotalAsset, balanceBeanDTO.getDisCurMarketValue());
                }
            }
            //qxnProductMap 不为空,批量查询获取基金简称
            if (MapUtils.isNotEmpty(qxnProductMap)) {
                List<String> productCodes = new ArrayList<>(qxnProductMap.keySet());
                List<FundBasicInfoDTO> fundBasicInfoDTOS = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList(productCodes);
                List<HkAppCenterFundTabVO.QXNFundInfo> qxnFundInfoList = fundBasicInfoDTOS.stream().map(m -> {
                    HkAppCenterFundTabVO.QXNFundInfo qxnFundInfo = new HkAppCenterFundTabVO.QXNFundInfo();
                    qxnFundInfo.setFundAddr(m.getFundAbbr());
                    BigDecimal bigDecimal = qxnProductMap.get(m.getFundCode());
                    qxnFundInfo.setResidueAmt(NumberUtils.bigDecimalToString(bigDecimal));
                    return qxnFundInfo;
                }).collect(Collectors.toList());
                appCenterFundTabVO.setFundInfoList(qxnFundInfoList);
            }
            appCenterFundTabVO.setFundTotalAsset(NumberUtils.bigDecimalToString(fundTotalAsset, 2, RoundingMode.DOWN));
        }
        return appCenterFundTabVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HkAppCenterFundTabVO
     * @description: App个人中心资产详情页现金余额TAB页接口
     * @author: jinqing.rao
     * @date: 2024/8/7 14:20
     * @since JDK 1.8
     */
    public HkAppCenterCashTabVO queryCashTabInfo(SimuBalanceBaseRequest request) {
        HkAppCenterCashTabVO appCenterCashTabVO = new HkAppCenterCashTabVO();
        HkCashBalanceChangeDTO balanceChangeDTO = queryHkCashBalanceService.queryHkCashBalance(request.getHkCustNo(), request.getCurrency());
        appCenterCashTabVO.setCashBalanceAsset(NumberUtils.bigDecimalToString(balanceChangeDTO.getTotalBalance(), 2, RoundingMode.DOWN));
        //币种资金明细
        if (CollectionUtils.isNotEmpty(balanceChangeDTO.getQueryEbrokerCustBalanceDtlDTO())) {
            List<HkAppCenterCashTabVO.AppCenterCashTabDetail> cashTabDetails = balanceChangeDTO.getQueryEbrokerCustBalanceDtlDTO().stream()
                    .filter(f -> null != f.getCurBalance() && f.getCurBalance().compareTo(BigDecimal.ZERO) > 0)
                    .map(m -> {
                        HkAppCenterCashTabVO.AppCenterCashTabDetail appCenterCashTabDetail = new HkAppCenterCashTabVO.AppCenterCashTabDetail();
                        appCenterCashTabDetail.setCurrency(m.getCurCode());
                        appCenterCashTabDetail.setOriginalAmt(NumberUtils.bigDecimalToString(m.getCurBalance(), 2, RoundingMode.DOWN));
                        appCenterCashTabDetail.setConversionAmt(NumberUtils.bigDecimalToString(m.getChangeBalance(), 2, RoundingMode.DOWN));
                        appCenterCashTabDetail.setExcRate(NumberUtils.bigDecimalToString(m.getCurrencyRate(), 4, RoundingMode.DOWN));
                        //展示币种是人民币时,展示的汇率需要特殊处理，因为 汇率格式，人民币：1外币兑换多少人民币 美元：1美元兑换多少外币，如果展示币种是人民币，需要换算成 1元 = N 外币
                        if (CurrencyEnum.RMB.getCode().equals(request.getCurrency())) {
                            BigDecimal rmbRate = NumberUtils.divide(BigDecimal.ONE, m.getCurrencyRate(), 4, RoundingMode.DOWN);
                            appCenterCashTabDetail.setExcRate(NumberUtils.formatDecimalPlaces(rmbRate,4, RoundingMode.DOWN));
                        }
                        // 人民币特殊处理
                        if (CurrencyEnum.RMB.getCode().equals(m.getCurCode())) {
                            appCenterCashTabDetail.setCurrencyDesc("人民币");
                        } else {
                            appCenterCashTabDetail.setCurrencyDesc(CurrencyEnum.getDescription(m.getCurCode()));
                        }
                        appCenterCashTabDetail.setExcRateDate(balanceChangeDTO.getDataDt());
                        return appCenterCashTabDetail;
                    })
                    // 排序规则 人民币、美元、港币、欧元
                    .sorted(ComparingByCurrency()).collect(Collectors.toList());
            appCenterCashTabVO.setCashCurrencyList(cashTabDetails);
        }
        return appCenterCashTabVO;
    }

    /**
     * @param
     * @return java.util.Comparator<com.howbuy.crm.cgi.extservice.vo.hkfund.HkAppCenterCashTabVO.AppCenterCashTabDetail>
     * @description: 排序规则 人民币、美元、港币、欧元
     * @author: jinqing.rao
     * @date: 2024/10/14 10:33
     * @since JDK 1.8
     */
    private static Comparator<HkAppCenterCashTabVO.AppCenterCashTabDetail> ComparingByCurrency() {
        // 定义排序顺序映射
        Map<String, Integer> currencyOrder = new HashMap<>();
        currencyOrder.put(CurrencyEnum.RMB.getCode(), 1);
        currencyOrder.put(CurrencyEnum.USD.getCode(), 2);
        currencyOrder.put(CurrencyEnum.HKD.getCode(), 3);
        currencyOrder.put(CurrencyEnum.EUR.getCode(), 4);
        currencyOrder.put(CurrencyEnum.JPY.getCode(), 5);
        return Comparator.comparing(item -> null == currencyOrder.get(item.getCurrency()) ? Integer.MAX_VALUE : currencyOrder.get(item.getCurrency()));
    }


}