package com.howbuy.crm.cgi.extservice.vo.balance;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 资金明细查询响应类
 * @author: 陈杰文
 * @date: 2025-06-17 18:07:37
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFinancialDetailListVO extends Body implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 页大小
     */
    private Integer size;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 期末余额
     */
    private String closingBalance;

    /**
     * 更新时间 YYYYMMDDhhmmss
     */
    private String updateTime;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 币种资金列表
     */
    private List<CurrencyFinancialVO> currencyFinancialListVO;

    /**
     * 资金明细列表
     */
    private List<FinancialDetailVO> financiaDetailListVO;
} 