package com.howbuy.crm.cgi.extservice.factory.balance;

import com.howbuy.crm.cgi.extservice.common.annotation.FundTypeSupport;
import com.howbuy.crm.cgi.extservice.common.enums.balance.HkCustBalanceDisPalyCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.utils.balance.BalanceUtils;
import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.FullBalancePortfolioDetail;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.AcctBalanceDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.BalanceBeanDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryBalanceDTO;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 全委基金持仓策略类
 * @author: 陈杰文
 * @date: 2025-07-03 13:11:28
 * @since JDK 1.8
 */
@Component
@Slf4j
@FundTypeSupport(HkCustBalanceDisPalyCategoryEnum.FULL_BALANCE)
public class FullBalancePortfolioStrategy extends AbstractBalancePortfolioStrategy implements BalancePortfolioStrategy<FullBalancePortfolioDetail> {

    @Override
    public FullBalancePortfolioDetail calculate(BalanceContent content) {
        FullBalancePortfolioDetail.FullBalancePortfolioDetailBuilder builder = FullBalancePortfolioDetail.builder();
        if (CollectionUtils.isEmpty(content.getFundTxAcctNoDTOList())) {
            log.info("没有非全委的基金交易账号,非全委的基金策略不执行，返回 空数据 , hkCustNo :{} ", content.getHkCustNo());
            return builder.build();
        }
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = content.getFundTxAcctNoDTOList().stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.FULL.getCode().equals(m.getFundTxAccType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hkFundTxAcctDTOS)) {
            log.error("用户有基金交易账号,没有非全委的基金交易账号,非全委的基金策略不执行，返回 空数据 , hkCustNo :{} ", content.getHkCustNo());
            return builder.build();
        }

        List<String> fullFundTxAcctNoList = BalanceUtils.extractFieldList(hkFundTxAcctDTOS, HkFundTxAcctDTO::getFundTxAcctNo);
        // 获取储蓄罐基金
        List<FundBasicInfoDTO> fundBasicInfoDTOList = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList();
        List<String> piggyFundCodeList = BalanceUtils.extractFieldList(fundBasicInfoDTOList, FundBasicInfoDTO::getFundCode);

        QueryBalanceDTO queryBalanceDTO = new QueryBalanceDTO();
        queryBalanceDTO.setHkCustNo(content.getHkCustNo());
        queryBalanceDTO.setExcludeFundCodeList(piggyFundCodeList);
        queryBalanceDTO.setFundTxAcctNoList(fullFundTxAcctNoList);
        queryBalanceDTO.setDisPlayCurrency(content.getDisPlayCurrencyCode());

        AcctBalanceDTO acctBalanceDTO = queryBalanceOuterService.queryHkCustBalance(queryBalanceDTO);
        List<BalanceBeanDTO> balanceList = acctBalanceDTO.getBalanceList();

        // 计算总收益
        BigDecimal totalIncome = BalanceUtils.sumBigDecimal(balanceList, BalanceBeanDTO::getDisCurCurrentAssetCurrency);

        // 收益状态
        String incomeCalStat = BalanceUtils.calculateIncomeCalStat(balanceList);

        return builder
                .totalAsset(acctBalanceDTO.getDisPlayCurrencyTotalMarketValue())
                .totalIncome(totalIncome)
                .incomeStatus(incomeCalStat)
                .inTransitTradeAmt(acctBalanceDTO.getTotalUnconfirmedAmt())
                .totalCashCollection(acctBalanceDTO.getTotalCashCollection())
                .itemList(balanceList)
                .build();
    }

    @Override
    public Class<FullBalancePortfolioDetail> getSupportedType() {
        return FullBalancePortfolioDetail.class;
    }
}