/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.factory.domain;

import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 持仓资产总览
 * @date 2025/6/23 16:21
 * @since JDK 1.8
 */
@Builder
@Getter
public class BalancePortfolioSummary implements PortfolioDetail {
    /**
     * 总资产
     */
    private TotalBalancePortfolioDetail totalPortfolio;

    /**
     * 现金余额
     */
    private BalancePortfolioDetail cashPortfolio;
    /**
     * 非全委
     */
    private BalancePortfolioDetail nonFullPortfolio;

    /**
     * 全委
     */
    private BalancePortfolioDetail fullPortfolio;

    /**
     * 储蓄罐
     */
    private PiggyBalancePortfolio piggyPortfolio;

}
