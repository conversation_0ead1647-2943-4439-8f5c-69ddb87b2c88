package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 海外App个人中心响应类
 * <AUTHOR>
 * @date 2024/2/23 9:06
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkAcctAppPersonalCenterVO  extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = 4684058728119928244L;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 手机号掩码
     */
    private String mobileMask;
    /**
     * 手机号摘要
     */
    private String mobileDigest;

    /**
     * 手机号地区码
     */
    private String mobileAreaCode;
    /**
     * 邮箱地址掩码
     */
    private String emailMask;
    /**
     * 邮箱地址密文
     */
    private String emailDigest;

    /**
     * 客户风险等级
     * 风险等级
     * 0-保守型（最低）
     * 1-保守型
     * 2-稳健型
     * 3-平衡型
     * 4-成长型
     * 5-进取型
     */
    private String riskToleranceLevel;
    /**
     * 银行卡数量
     */
    private String bindCardCount;

    /**
     * 客户风测过期标识 1：是 0：否
     */
    private String riskToleranceExpire;

    /**
     * 客户状态0-正常 1-注销 2-休眠 3-注册 4-开户申请成功
     */
    private String custState;

    /**
     *0:普通(去认证) 1:专业
     */
    private String investorType;

    /**
     * 一账通绑定状态 0-未绑定 1-已绑定
     */
    private String hboneBindStatus;

    /**
     * 开户入金状态
     * 01-去开户；02-继续开户；03-查看开户进度；04-修改开户资料；05-去入金；06-查看入金进度；07- 修改入金资料；08-隐藏开户入金区域
     */
    private String openDepositsStatus;

    /**
     *开户订单信息,填写到具体的步骤,开户步骤标识：01-证件信息页；02-个人信息页；03-职业信息页；04-声明信息页；05-投资经验页；06-银行卡页；07-电子签名页
     * 该字段通过循环查询缓存,判断最大不步骤页
     */
    protected String openAcctStep;


    /**
     * 开户的具体进度
     */
    private String openAcctStepProgress;


    /**
     * 开户方式 0-线下 1-线上
     */
    private String openType;


    /**
     * 交易密码设置标识 0正常状态 1重置状态 2-未设置(包含为空的情况)
     */
    private String custTxPasswdType;

    /**
     * 登录密码设置标识	 0正常状态 1重置状态 2-未设置
     */
    private String custLoginPasswdType;

    /**
     * 客户证件类型
     */
    private String idType;

    /**
     * 证件类型描述
     */
    private String idTypeDesc;

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 姓名
     */
    private String custName;


    /**
     * 登录是否激活  1:是   0：否
     */
    private String loginActivate;

    /**
     *交易是否激活  1:是  0：否
     */
    private String transactionActivation;



    /**
     * 专业投资者 1:是 0:否
     */
    private String investorAuditStatus;


    /**
     * 专业投资者 资产有效时间，1 :是 0：否
     */
    private String investorAssetsEffectiveStatus;

    /**
     * 是否签署海外储蓄罐
     */
    private String piggyBankSignStatus;

    /**
     * 是否具有衍生工具知识 0-无 1-有
     */
    private String derivativeKnowledge;

    /**
     * 是否有海外报告 1 是 0 否
     */
    private String hasOverseasReport;

    /**
     * 海外报告最新时间
     */
    private String overseasReportLatestDt;

    /**
     * 性别 0-女，1-男
     */
    private String gender;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 香港客户号明文
     */
    private String hkCustNoPlaintext;

    /**
     * 是否上传纸质签名 1是 0否
     */
    private String paperSignUploaded;

    /**
     * 纸质签名订单号
     */
    private String paperSignOrderNo;

}
