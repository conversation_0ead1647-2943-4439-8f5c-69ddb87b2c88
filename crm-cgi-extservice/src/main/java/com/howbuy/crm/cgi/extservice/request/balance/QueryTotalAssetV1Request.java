package com.howbuy.crm.cgi.extservice.request.balance;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * @description: 查询总资产接口入参
 * @author: jinqing.rao
 * @date: 2025/6/17 9:46
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryTotalAssetV1Request {

    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 当前展示的币种代码
     */
    @NotBlank(message = "当前展示的币种代码不能为空")
    private String disPlayCurrencyCode;
} 