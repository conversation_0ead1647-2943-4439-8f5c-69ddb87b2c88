package com.howbuy.crm.cgi.extservice.service.statement;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.statement.SendStatementRequest;
import com.howbuy.crm.cgi.extservice.request.statement.StatementOrderQueryRequest;
import com.howbuy.crm.cgi.extservice.vo.statement.StatementOrderQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @description: 结单服务类
 * @author: 陈杰文
 * @date: 2025-06-17 15:20:00
 * @since JDK 1.8
 */
@Slf4j
@Service
public class StatementService {

    /**
     * @description: 查询日结订单
     * @param request 请求参数
     * @return StatementOrderQueryVO
     * @author: 陈杰文
     * @date: 2025-06-17 15:20:00
     */
    public StatementOrderQueryVO queryStatementOrder(StatementOrderQueryRequest request) {
        // TODO: 实现业务逻辑
        // 1. 根据香港客户号，调海外账户中心接口，查询客户的结单邮箱
        // 2. 根据基金交易账号，结单类型，查询已生成且已成功发送给客户的结单文件。调用海外中台接口 QueryStatementFilePageFacade
        // 3. 加密处理结单文件的URL信息
        // 4. 在FileBizTypeEnum中添加结单文件的配置目录配置枚举,调用com.howbuy.crm.cgi.extservice.common.utils.file.FileUploadUtils#getEncryptUrl 加密方法，完成URL加密处理即可
        // 5. 数据组装:返回结单邮箱，结单文件信息，默认按结单文件时间倒序排
        
        return new StatementOrderQueryVO();
    }

    /**
     * @description: 发送结单邮箱
     * @param request 请求参数
     * @author: 陈杰文
     * @date: 2025-07-03 16:42:18
     */
    public void sendStatement(SendStatementRequest request) {
        // TODO: 实现发送结单邮箱业务逻辑
        // 根据业务类型处理结单邮箱发送逻辑
        log.info("发送结单邮箱请求：bizType={}, hkCustNo={}", request.getBizType(), request.getHkCustNo());
    }
} 