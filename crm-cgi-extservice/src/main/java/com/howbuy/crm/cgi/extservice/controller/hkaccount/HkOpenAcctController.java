package com.howbuy.crm.cgi.extservice.controller.hkaccount;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.ParamsException;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.request.account.*;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAccIdentityOcrRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAppUploadFileRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.*;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.bankinfo.HkOpenAcctBankRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.declare.HkOpenAcctDeclareRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.occupation.HkOpenAcctOccupationRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.personalInfo.HkOpenPersonalInfoRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.idInfo.HkAcctOpenIdInfoRequest;
import com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct.OpenAcctService;
import com.howbuy.crm.cgi.extservice.validator.hkopen.HkOpenAcctValidator;
import com.howbuy.crm.cgi.extservice.vo.dtmsproduct.LicenseeDetailVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.*;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.*;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.bankinfo.HkOpenAcctBankInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.declare.HkOpenAcctDeclareVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.idinfo.HkOpenAcctIdInfoRespVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.idinfo.HkAcctOpenIdDetailVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.investinfo.HkOpenAcctInvestVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.pdf.HkOpenAcctPdfVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.personalinfo.HkOpenPersonalInfoResponseVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.signnature.HkOpenAcctESignatureVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description: 海外小程序 线上开户-填写资料1
 * @date 2023/11/29 15:32
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/openacct")
public class HkOpenAcctController {


    @Resource
    private OpenAcctService openAcctService;


    /**
     * @api {GET} /ext/hkaccount/openacct/queryopeniddetail queryOpenIdDetail()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName queryOpenIdDetail()
     * @apiDescription 步骤1:证件信息填写页详情查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.openIdValueVO 开户证件信息
     * @apiSuccess (响应结果) {String} data.openIdValueVO.idAreaCode 证件所属地区编码
     * @apiSuccess (响应结果) {String} data.openIdValueVO.idAreaCodeDesc 证件所属地区名称
     * @apiSuccess (响应结果) {String} data.openIdValueVO.idType 证件类型
     * @apiSuccess (响应结果) {String} data.openIdValueVO.idTypeDesc 证件类型名称
     * @apiSuccess (响应结果) {String} data.openIdValueVO.frontPictureUrl 正面图片地址
     * @apiSuccess (响应结果) {String} data.openIdValueVO.frontThumbnailUrl 正面缩略图URL
     * @apiSuccess (响应结果) {String} data.openIdValueVO.backPictureUrl 反面图片URL
     * @apiSuccess (响应结果) {String} data.openIdValueVO.backThumbnailUrl 反面缩略图URL
     * @apiSuccess (响应结果) {String} data.openIdValueVO.idNo 证件号码
     * @apiSuccess (响应结果) {String} data.openIdValueVO.idAlwaysValidFlag 证件是否永久有效
     * @apiSuccess (响应结果) {String} data.openIdValueVO.idExpireTime 证件有效期
     * @apiSuccess (响应结果) {String} data.openIdValueVO.extendFileJson 扩展字段,主要服务前端交互的字段,由前端控制,不做校验。json格式
     * @apiSuccess (响应结果) {String} data.openIdValueVO.openSaveType z保存状态
     * @apiSuccess (响应结果) {Array} data.checkResult 证件信息审核结果信息
     * @apiSuccess (响应结果) {String} data.checkResult.fileType 字段类型描述
     * @apiSuccess (响应结果) {String} data.checkResult.fileName 字段名称
     * @apiSuccess (响应结果) {String} data.checkResult.reason 错误原因
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"FYIdh8gwAD","data":{"openIdValueVO":{"backThumbnailUrl":"Wi8t","idType":"yJm1K","frontPictureUrl":"jz5a","idExpireTime":"L","idAlwaysValidFlag":"Wpf","openSaveType":"Px4","backPictureUrl":"bn","idAreaCode":"UqPPyqW","idNo":"22D0","idAreaCodeDesc":"K","extendFileJson":"8Sv501Js9w","frontThumbnailUrl":"A","idTypeDesc":"6ZeM"},"checkResult":[{"reason":"mTYna9fZS","fileName":"NaePyQLE","fileType":"ICi"}]},"description":"P","timestampServer":"sGOeZsY"}
     */
    @GetMapping("queryopeniddetail")
    public CgiResponse<HkAcctOpenIdDetailVO> queryOpenIdDetail() {
        return CgiResponse.ok(openAcctService.queryOpenIdDetail());
    }


    /**
     * @api {POST} /ext/hkaccount/openacct/saveopenid saveOpenId()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName saveOpenId()
     * @apiDescription 步骤1:证件信息填写页保存接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} idAreaCode 证件所属地区编码
     * @apiParam (请求体) {String} idAreaCodeDesc 证件所属地区名称
     * @apiParam (请求体) {String} idType 证件类型
     * @apiParam (请求体) {String} idTypeDesc 证件类型名称
     * @apiParam (请求体) {String} frontPictureUrl 证件类型名称
     * @apiParam (请求体) {String} frontThumbnailUrl 正面缩略图URL
     * @apiParam (请求体) {String} backPictureUrl 反面图片URL
     * @apiParam (请求体) {String} backThumbnailUrl 反面缩略图URL
     * @apiParam (请求体) {String} idNo 证件号码
     * @apiParam (请求体) {String} idAlwaysValidFlag 证件是否永久有效
     * @apiParam (请求体) {String} idExpireTime 证件有效期
     * @apiParam (请求体) {String} extendFileJson 扩展字段,主要服务前端交互的字段,由前端控制,不做校验。json格式
     * @apiParam (请求体) {String} openSaveType 开户保存类型 01-保存，02-退出
     * @apiParamExample 请求体示例
     * {"backThumbnailUrl":"Rahv","idType":"xBx5JU4ate","frontPictureUrl":"QNy0D","idExpireTime":"3","idAlwaysValidFlag":"Qjst7Ljuh","openSaveType":"4O0","backPictureUrl":"WCr9P7","idAreaCode":"XA","idNo":"sK2xQ27k8","idAreaCodeDesc":"E3iEKzyd7","extendFileJson":"gLxism","frontThumbnailUrl":"R","idTypeDesc":"8I65g"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.checkResult 校验信息或者审核不通过的信息,需要提示到具体字段
     * @apiSuccess (响应结果) {String} data.checkResult.fileType 字段类型描述
     * @apiSuccess (响应结果) {String} data.checkResult.fileName 字段名称
     * @apiSuccess (响应结果) {String} data.checkResult.reason 错误原因
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Xa12","data":{"checkResult":[{"reason":"M","fileName":"NGCu","fileType":"xZhm"}]},"description":"UoVPXZX","timestampServer":"U5GJefyls9"}
     */
    @PostMapping("saveopenid")
    public CgiResponse<HkOpenAcctIdInfoRespVO> saveOpenId(@RequestBody HkAcctOpenIdInfoRequest request) {
        //参数校验
        HkOpenAcctValidator.validator(request);
        return openAcctService.saveOpenIdInfo(request);
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/temporarystorageopenid temporaryStorageOpenId()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName temporaryStorageOpenId()
     * @apiDescription 步骤1:证件信息填写页暂存接口,不校验保存参数
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} idAreaCode 证件所属地区编码
     * @apiParam (请求体) {String} idAreaCodeDesc 证件所属地区名称
     * @apiParam (请求体) {String} idType 证件类型
     * @apiParam (请求体) {String} idTypeDesc 证件类型名称
     * @apiParam (请求体) {String} frontPictureUrl 证件类型名称
     * @apiParam (请求体) {String} frontThumbnailUrl 正面缩略图URL
     * @apiParam (请求体) {String} backPictureUrl 反面图片URL
     * @apiParam (请求体) {String} backThumbnailUrl 反面缩略图URL
     * @apiParam (请求体) {String} idNo 证件号码
     * @apiParam (请求体) {String} idAlwaysValidFlag 证件是否永久有效
     * @apiParam (请求体) {String} idExpireTime 证件有效期
     * @apiParam (请求体) {String} extendFileJson 扩展字段,主要服务前端交互的字段,由前端控制,不做校验。json格式
     * @apiParam (请求体) {String} openSaveType 开户保存类型 01-保存，02-退出
     * @apiParamExample 请求体示例
     * {"backThumbnailUrl":"BgypG0BL","idType":"Pb2LjOSLR","frontPictureUrl":"CXV","idExpireTime":"YvY","idAlwaysValidFlag":"vW65fIX","openSaveType":"8pyJLwq","backPictureUrl":"43n","idAreaCode":"72eiYk","idNo":"W","idAreaCodeDesc":"LI9J","extendFileJson":"UPq9nwC1","frontThumbnailUrl":"Il0sTVhlb","idTypeDesc":"G"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.checkResult 校验信息或者审核不通过的信息,需要提示到具体字段
     * @apiSuccess (响应结果) {String} data.checkResult.fileType 字段类型描述
     * @apiSuccess (响应结果) {String} data.checkResult.fileName 字段名称
     * @apiSuccess (响应结果) {String} data.checkResult.reason 错误原因
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0K14IHcj","data":{"checkResult":[{"reason":"yPa","fileName":"mWvmJG1gP","fileType":"a5Gmt"}]},"description":"N3","timestampServer":"N"}
     */
    @PostMapping("temporarystorageopenid")
    public CgiResponse<HkOpenAcctIdInfoRespVO> temporaryStorageOpenId(@RequestBody HkAcctOpenIdInfoRequest request) {
        HkOpenAcctIdInfoRespVO acctIdInfoRespVO = openAcctService.temporaryStorageOpenId(request);
        return CgiResponse.ok(acctIdInfoRespVO);
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/saveopenpersonalinfo saveOpenPersonalInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName saveOpenPersonalInfo()
     * @apiDescription 步骤2:个人信息填写页保存接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} custChineseName 中文姓名
     * @apiParam (请求体) {String} idType 证件类型
     * @apiParam (请求体) {String} idNo 证件号
     * @apiParam (请求体) {String} cnSurname 中文姓
     * @apiParam (请求体) {String} cnGivenName 中文名
     * @apiParam (请求体) {String} custEnName 英文姓名
     * @apiParam (请求体) {String} enSurname 英文姓
     * @apiParam (请求体) {String} enGivenName 英文名
     * @apiParam (请求体) {String} gender 性别 0-女 1-男 2-非自然人
     * @apiParam (请求体) {String} formerNameFlag 是否有曾用名 1:是  0:否
     * @apiParam (请求体) {String} cnFormerSurName 中文曾用姓
     * @apiParam (请求体) {String} cnFormerName 中文曾用名
     * @apiParam (请求体) {String} enFormerSurName 英文曾用姓
     * @apiParam (请求体) {String} enFormerName 英文曾用名
     * @apiParam (请求体) {String} birthday 出生日期
     * @apiParam (请求体) {String} nationality 国籍
     * @apiParam (请求体) {String} nationalityDesc 国籍
     * @apiParam (请求体) {String} marriageStat 婚姻状况 婚姻状况 0-未婚 1-已婚 2-其他
     * @apiParam (请求体) {String} marriageStatDesc 婚姻情况
     * @apiParam (请求体) {String} marriageStatRemark 婚姻情况 其他类型 婚姻简介
     * @apiParam (请求体) {String} eduLevel 教育程度 教育水平 01-小学或以下 02-中学 03-大专或预科 04-大学或本科 05-硕士或以上
     * @apiParam (请求体) {String} mobileAreaCode 手机区号
     * @apiParam (请求体) {String} mobile 手机号
     * @apiParam (请求体) {String} email 电子邮箱
     * @apiParam (请求体) {String} mailingFlag 通讯地址 1: 同现居住地址一样 0: 其他
     * @apiParam (请求体) {String} birthFlag 出生地址 1: 同现居住地址一样 0: 其他
     * @apiParam (请求体) {Array} residenceInfoCertUrls 地址证明URL列表
     * @apiParam (请求体) {String} residenceInfoCertUrls.url 图片地址
     * @apiParam (请求体) {String} residenceInfoCertUrls.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} residenceInfoCertUrls.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiParam (请求体) {Array} mailingInfoCertUrls 通讯地址信息
     * @apiParam (请求体) {String} mailingInfoCertUrls.url 图片地址
     * @apiParam (请求体) {String} mailingInfoCertUrls.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} mailingInfoCertUrls.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiParam (请求体) {Object} residenceDetail 现居地地址
     * @apiParam (请求体) {String} residenceDetail.detailAddrCn 详细地址中文信息
     * @apiParam (请求体) {String} residenceDetail.compareDetail 比较拼接地址
     * @apiParam (请求体) {String} residenceDetail.detailAddrEn 详细地址英文信息
     * @apiParam (请求体) {Object} mailingDetail 通讯地地址
     * @apiParam (请求体) {String} mailingDetail.detailAddrCn 详细地址中文信息
     * @apiParam (请求体) {String} mailingDetail.compareDetail 比较拼接地址
     * @apiParam (请求体) {String} mailingDetail.detailAddrEn 详细地址英文信息
     * @apiParam (请求体) {Object} birthDetail 出生地地址
     * @apiParam (请求体) {String} birthDetail.detailAddrCn 详细地址中文信息
     * @apiParam (请求体) {String} birthDetail.compareDetail 比较拼接地址
     * @apiParam (请求体) {String} birthDetail.detailAddrEn 详细地址英文信息
     * @apiParam (请求体) {String} openSaveType 01-保存，02-退出
     * @apiParamExample 请求体示例
     * {"birthday":"xlaiTK0","marriageStatRemark":"hDj2OK","gender":"uO0Tei","cnFormerSurName":"TgGvaMSv5","openSaveType":"gR6C8w658","enFormerName":"wndEub67o","idNo":"RqIu","cnSurname":"NJN","formerNameFlag":"f","mobileAreaCode":"QQp5w59CaN","custChineseName":"M0","mailingDetail":{"detailAddrCn":"LYbcs1dbx","detailAddrEn":"17NDsWP","compareDetail":"mMR3APPCme"},"email":"qzRkY8","custEnName":"pFVgOSC8Y","mailingInfoCertUrls":[{"exampleFileFormatType":"5q3","url":"9M","thumbnailUrl":"TLfjcblkN"}],"idType":"tFInZNZg","mailingFlag":"49","eduLevel":"a89Fljd2jB","marriageStatDesc":"TZf3xdFo","mobile":"q","residenceDetail":{"detailAddrCn":"8NDWnj3","detailAddrEn":"xWn7cB7BY","compareDetail":"9"},"birthFlag":"22VM","cnFormerName":"YggeWma","enFormerSurName":"P","residenceInfoCertUrls":[{"exampleFileFormatType":"8nlV","url":"yDt","thumbnailUrl":"atQMIA7e"}],"enGivenName":"Pq9DWCWv","nationality":"NyT","marriageStat":"VXVta6X","birthDetail":{"detailAddrCn":"zBkw2TQ","detailAddrEn":"oLu","compareDetail":"LtqIFrXZ"},"enSurname":"6XosqnlSz","nationalityDesc":"iTYx","cnGivenName":"fqLO2CKF"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"N6fx27n5X2","description":"Vfvvln","timestampServer":"7FZk2"}
     */
    @PostMapping("saveopenpersonalinfo")
    public CgiResponse<HkOpenPersonalInfoResponseVO> saveOpenPersonalInfo(@RequestBody HkOpenPersonalInfoRequest request) {
        //参数校验
        HkOpenAcctValidator.validator(request);
        return openAcctService.saveOpenPersonalInfo(request);
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/temporarystorageopenpersonalinfo temporaryStorageOpenPersonalInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName temporaryStorageOpenPersonalInfo()
     * @apiDescription 步骤2:个人信息页-个人信息页暂存接口,不校验参数信息
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} custChineseName 中文姓名
     * @apiParam (请求体) {String} idType 证件类型
     * @apiParam (请求体) {String} idNo 证件号
     * @apiParam (请求体) {String} cnSurname 中文姓
     * @apiParam (请求体) {String} cnGivenName 中文名
     * @apiParam (请求体) {String} custEnName 英文姓名
     * @apiParam (请求体) {String} enSurname 英文姓
     * @apiParam (请求体) {String} enGivenName 英文名
     * @apiParam (请求体) {String} gender 性别 0-女 1-男 2-非自然人
     * @apiParam (请求体) {String} formerNameFlag 是否有曾用名 1:是  0:否
     * @apiParam (请求体) {String} cnFormerSurName 中文曾用姓
     * @apiParam (请求体) {String} cnFormerName 中文曾用名
     * @apiParam (请求体) {String} enFormerSurName 英文曾用姓
     * @apiParam (请求体) {String} enFormerName 英文曾用名
     * @apiParam (请求体) {String} birthday 出生日期
     * @apiParam (请求体) {String} nationality 国籍
     * @apiParam (请求体) {String} nationalityDesc 国籍
     * @apiParam (请求体) {String} marriageStat 婚姻状况 婚姻状况 0-未婚 1-已婚 2-其他
     * @apiParam (请求体) {String} marriageStatDesc 婚姻情况
     * @apiParam (请求体) {String} marriageStatRemark 婚姻情况 其他类型 婚姻简介
     * @apiParam (请求体) {String} eduLevel 教育程度 教育水平 01-小学或以下 02-中学 03-大专或预科 04-大学或本科 05-硕士或以上
     * @apiParam (请求体) {String} mobileAreaCode 手机区号
     * @apiParam (请求体) {String} mobile 手机号
     * @apiParam (请求体) {String} email 电子邮箱
     * @apiParam (请求体) {String} mailingFlag 通讯地址 1: 同现居住地址一样 0: 其他
     * @apiParam (请求体) {String} birthFlag 出生地址 1: 同现居住地址一样 0: 其他
     * @apiParam (请求体) {Array} residenceInfoCertUrls 地址证明URL列表
     * @apiParam (请求体) {String} residenceInfoCertUrls.url 图片地址
     * @apiParam (请求体) {String} residenceInfoCertUrls.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} residenceInfoCertUrls.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiParam (请求体) {Array} mailingInfoCertUrls 通讯地址信息
     * @apiParam (请求体) {String} mailingInfoCertUrls.url 图片地址
     * @apiParam (请求体) {String} mailingInfoCertUrls.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} mailingInfoCertUrls.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiParam (请求体) {Object} residenceDetail 现居地地址
     * @apiParam (请求体) {String} residenceDetail.detailAddrCn 详细地址中文信息
     * @apiParam (请求体) {String} residenceDetail.compareDetail 比较拼接地址
     * @apiParam (请求体) {String} residenceDetail.detailAddrEn 详细地址英文信息
     * @apiParam (请求体) {Object} mailingDetail 通讯地地址
     * @apiParam (请求体) {String} mailingDetail.detailAddrCn 详细地址中文信息
     * @apiParam (请求体) {String} mailingDetail.compareDetail 比较拼接地址
     * @apiParam (请求体) {String} mailingDetail.detailAddrEn 详细地址英文信息
     * @apiParam (请求体) {Object} birthDetail 出生地地址
     * @apiParam (请求体) {String} birthDetail.detailAddrCn 详细地址中文信息
     * @apiParam (请求体) {String} birthDetail.compareDetail 比较拼接地址
     * @apiParam (请求体) {String} birthDetail.detailAddrEn 详细地址英文信息
     * @apiParam (请求体) {String} openSaveType 01-保存，02-退出
     * @apiParamExample 请求体示例
     * {"birthday":"Kdh","marriageStatRemark":"D1j1nPyG7G","gender":"tQ","cnFormerSurName":"f0u","openSaveType":"6TFMBY","enFormerName":"Wf","idNo":"7XQGpUau","cnSurname":"U","formerNameFlag":"SXkof6iUJf","mobileAreaCode":"o9Bc","custChineseName":"klVv7b","mailingDetail":{"detailAddrCn":"nJBKEqvS","detailAddrEn":"9xQ","compareDetail":"kg4sS"},"email":"ePLX16","custEnName":"JYMXDPb","mailingInfoCertUrls":[{"exampleFileFormatType":"xTKZioNAZ","url":"EJauqRtcXr","thumbnailUrl":"6bHn"}],"idType":"6Y","mailingFlag":"V","eduLevel":"NZ3Zf","marriageStatDesc":"Udd4ZhNiui","mobile":"ur8c","residenceDetail":{"detailAddrCn":"AsB","detailAddrEn":"pf","compareDetail":"411"},"birthFlag":"l04wyOzg0","cnFormerName":"B5LpILe","enFormerSurName":"c3","residenceInfoCertUrls":[{"exampleFileFormatType":"aGVx","url":"8VryyxYr","thumbnailUrl":"aQ4RZA"}],"enGivenName":"JvbWgUB9P","nationality":"AGuKdvt","marriageStat":"3zJbjRMqU","birthDetail":{"detailAddrCn":"2udWc","detailAddrEn":"E5jUyw","compareDetail":"6HiwR5"},"enSurname":"Mbl","nationalityDesc":"KvYjW2Wa","cnGivenName":"N1O"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"qZ","description":"8N","timestampServer":"7qe1Cs"}
     */
    @PostMapping("temporarystorageopenpersonalinfo")
    public CgiResponse<Body> temporaryStorageOpenPersonalInfo(@RequestBody HkOpenPersonalInfoRequest request) {
        openAcctService.temporaryStorageOpenPersonalInfo(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/queryopenpersonalinfodetail queryOpenPersonalInfoDetail()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName queryOpenPersonalInfoDetail()
     * @apiDescription 步骤2:个人信息页-查询个人信息详情接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.custChineseName 中文姓名
     * @apiSuccess (响应结果) {String} data.cnSurname 中文姓
     * @apiSuccess (响应结果) {String} data.cnGivenName 中文名
     * @apiSuccess (响应结果) {String} data.custEnName 英文姓名
     * @apiSuccess (响应结果) {String} data.enSurname 英文姓
     * @apiSuccess (响应结果) {String} data.enGivenName 英文名
     * @apiSuccess (响应结果) {String} data.gender 性别 0-女 1-男 2-非自然人
     * @apiSuccess (响应结果) {String} data.formerNameFlag 是否有曾用名
     * @apiSuccess (响应结果) {String} data.cnFormerSurName 中文曾用姓
     * @apiSuccess (响应结果) {String} data.cnFormerName 中文曾用名
     * @apiSuccess (响应结果) {String} data.enFormerSurName 英文曾用姓
     * @apiSuccess (响应结果) {String} data.enFormerName 英文曾用名
     * @apiSuccess (响应结果) {String} data.birthday 出生日期
     * @apiSuccess (响应结果) {String} data.nationality 国籍
     * @apiSuccess (响应结果) {String} data.nationalityDesc 国籍描述
     * @apiSuccess (响应结果) {String} data.marriageStat 婚姻状况
     * @apiSuccess (响应结果) {String} data.marriageStatDesc 婚姻情况
     * @apiSuccess (响应结果) {String} data.marriageStatRemark 婚姻情况 其他类型 婚姻简介
     * @apiSuccess (响应结果) {String} data.eduLevel 教育程度
     * @apiSuccess (响应结果) {String} data.mobileAreaCode 手机区号
     * @apiSuccess (响应结果) {String} data.mobile 手机号
     * @apiSuccess (响应结果) {String} data.email 电子邮箱
     * @apiSuccess (响应结果) {String} data.mailingFlag 通讯地址 1: 同现居住地址一样 0: 其他
     * @apiSuccess (响应结果) {String} data.birthFlag 出生地址 1: 同现居住地址一样 0: 其他
     * @apiSuccess (响应结果) {Array} data.checkResult 海外开户个人信息审核结果信息,如果审核通过则为空。错误会具体到哪个字段
     * @apiSuccess (响应结果) {String} data.checkResult.fileType 字段类型描述
     * @apiSuccess (响应结果) {String} data.checkResult.fileName 字段名称
     * @apiSuccess (响应结果) {String} data.checkResult.reason 错误原因
     * @apiSuccess (响应结果) {Object} data.residenceDetail 现居地信息
     * @apiSuccess (响应结果) {String} data.residenceDetail.detailAddrCn 详细地址中文
     * @apiSuccess (响应结果) {String} data.residenceDetail.detailAddrEn 详细地址英文
     * @apiSuccess (响应结果) {String} data.residenceDetail.compareDetail 比较地址
     * @apiSuccess (响应结果) {Array} data.residenceDetail.residenceCertUrls 地址证明URL列表
     * @apiSuccess (响应结果) {Object} data.mailingDetail 通讯地
     * @apiSuccess (响应结果) {String} data.mailingDetail.detailAddrCn 详细地址中文
     * @apiSuccess (响应结果) {String} data.mailingDetail.detailAddrEn 详细地址英文
     * @apiSuccess (响应结果) {String} data.mailingDetail.compareDetail 比较地址
     * @apiSuccess (响应结果) {Array} data.mailingDetail.residenceCertUrls 地址证明URL列表
     * @apiSuccess (响应结果) {Object} data.birthDetail 出生地
     * @apiSuccess (响应结果) {String} data.birthDetail.detailAddrCn 详细地址中文
     * @apiSuccess (响应结果) {String} data.birthDetail.detailAddrEn 详细地址英文
     * @apiSuccess (响应结果) {String} data.birthDetail.compareDetail 比较地址
     * @apiSuccess (响应结果) {Array} data.birthDetail.residenceCertUrls 地址证明URL列表
     * @apiSuccess (响应结果) {Array} data.residenceInfoCertUrls 地址证明URL列表
     * @apiSuccess (响应结果) {String} data.residenceInfoCertUrls.url 图片地址
     * @apiSuccess (响应结果) {String} data.residenceInfoCertUrls.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.residenceInfoCertUrls.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiSuccess (响应结果) {Array} data.mailingInfoCertUrls 通讯地址信息
     * @apiSuccess (响应结果) {String} data.mailingInfoCertUrls.url 图片地址
     * @apiSuccess (响应结果) {String} data.mailingInfoCertUrls.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.mailingInfoCertUrls.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"B","data":{"birthday":"eL7lZ0","marriageStatRemark":"Bp","gender":"tLwReEDe","cnFormerSurName":"155wuMaA","enFormerName":"QWQifm2Ynt","checkResult":[{"reason":"yharhPg4Q","fileName":"vK4I02iE1n","fileType":"3chJ"}],"cnSurname":"j2dDW","formerNameFlag":"PET6djO","mobileAreaCode":"X6P","custChineseName":"ej","mailingDetail":{"detailAddrCn":"amh5","detailAddrEn":"E","compareDetail":"kOPjeEMfj","residenceCertUrls":[]},"email":"wuf61j","custEnName":"Ee","mailingInfoCertUrls":[{"exampleFileFormatType":"VvKI8dkYBI","url":"zZFF","thumbnailUrl":"sQJfvqtIS"}],"mailingFlag":"LMwJa","eduLevel":"lgl","marriageStatDesc":"2ZJ","mobile":"Ta9TmIL","residenceDetail":{"detailAddrCn":"W6C","detailAddrEn":"KtdaIxK16P","compareDetail":"y","residenceCertUrls":[]},"birthFlag":"r","cnFormerName":"p2KwD","enFormerSurName":"game2uml","residenceInfoCertUrls":[{"exampleFileFormatType":"Us4EwFITw4","url":"snxttX","thumbnailUrl":"OOt9f"}],"enGivenName":"MqqN5Zn","nationality":"SjhsgdLNAz","marriageStat":"Zjsq","birthDetail":{"detailAddrCn":"A9kWofGlnw","detailAddrEn":"1f","compareDetail":"lq9waUKTmK","residenceCertUrls":[]},"enSurname":"0H","nationalityDesc":"3t8","cnGivenName":"O"},"description":"7jUFdRt5","timestampServer":"B1U2Ek"}
     */
    @PostMapping("queryopenpersonalinfodetail")
    public CgiResponse<OpenPersonalInfoVO> queryOpenPersonalInfoDetail() {
        return CgiResponse.ok(openAcctService.queryOpenPersonalInfoDetail());
    }


    /**
     * @api {GET} /ext/hkaccount/openacct/queryopenoccupationinfodetail queryOpenOccupationInfoDetail()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName queryOpenOccupationInfoDetail()
     * @apiDescription 步骤3:职业及税务信息页-查询职业及税务信息详情接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.openOccupationValueVO 职业信息
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.emplStatus 就业状况 01-雇主 02-全职 03-兼职 04-主妇 05-学生 06-退休 07-非在职
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.emplCompanyName 就业公司名称
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.emplAddrCn 就业公司地址-详细地址
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.emplAddrEn 就业公司地址-英文详细地址
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.emplNatureOfBusiness 业务性质
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.emplNatureOfBusinessDesc 业务性质描述
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.designation 职位/称衔
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.incLevel 每年收入水平
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.taxJurisdictionCode 税务管辖区代码
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.hasTin 是否有税务编号
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.noTinReason 无税务编号理由
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.noObtainTinReason 不能取的税务编号的原因
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.tinType 税务编号类型
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.tinTypeDesc 税务编号类型说明
     * @apiSuccess (响应结果) {String} data.openOccupationValueVO.tin 税务编号
     * @apiSuccess (响应结果) {Array} data.checkResult 审核不通过的情景,会具体到哪个字段的错误原因,如果没有错误则为空.服用了职业信息字段信息,方便前端取值
     * @apiSuccess (响应结果) {String} data.checkResult.fileType 字段类型描述
     * @apiSuccess (响应结果) {String} data.checkResult.fileName 字段名称
     * @apiSuccess (响应结果) {String} data.checkResult.reason 错误原因
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"9kMrL0EbqW","data":{"checkResult":[{"reason":"WtX1p2jsOU","fileName":"ST1dG","fileType":"PVdS"}],"openOccupationValueVO":{"emplStatus":"LSn3DP6z","emplAddrEn":"xSepPK","emplNatureOfBusinessDesc":"WnqsH5avFy","tinType":"YMVVOp4IA","taxJurisdictionCode":"6f","tinTypeDesc":"zt0JMEK1l5","incLevel":"JvPkZgt9","noTinReason":"sc41","tin":"Zl","emplCompanyName":"y8TAZ","emplNatureOfBusiness":"kh5dsorHo","designation":"eHw9ugkC","hasTin":"SrLIUWEq","emplAddrCn":"PLWWRLOWc","noObtainTinReason":"5tone1"}},"description":"NlbX58MUv2","timestampServer":"aPRx8P"}
     */
    @GetMapping("queryopenoccupationinfodetail")
    public CgiResponse<OpenOccupationVO> queryOpenOccupationInfoDetail() {
        return CgiResponse.ok(openAcctService.queryOpenOccupationInfoDetail());
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/saveopenoccupationinfo saveOpenOccupationInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName saveOpenOccupationInfo()
     * @apiDescription 步骤3: 职业及税务信息填写页-保存接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} emplStatus 就业状况 01-雇主 02-全职 03-兼职 04-主妇 05-学生 06-退休 07-非在职
     * @apiParam (请求体) {String} emplCompanyName 就业公司名称
     * @apiParam (请求体) {String} emplAddrCn 就业公司地址-详细地址
     * @apiParam (请求体) {String} emplAddrEn 就业公司地址-英文详细地址
     * @apiParam (请求体) {String} emplNatureOfBusiness 业务性质
     * @apiParam (请求体) {String} emplNatureOfBusinessDesc 业务性质描述
     * @apiParam (请求体) {String} designation 职位/称衔
     * @apiParam (请求体) {String} taxJurisdictionCode 税务管辖区代码
     * @apiParam (请求体) {String} hasTin 是否有税务编号 1是 0否
     * @apiParam (请求体) {String} noTinReason 无税务编号理由      ['01-账户持有人的居留司法税务管辖区并没有向其居民发出税务编号;02-账户持有人不能取得税务编号;03-账户持有人无须提供税务编号']
     * @apiParam (请求体) {String} noObtainTinReason 不能取的税务编号的原因
     * @apiParam (请求体) {String} tinType 税务编号类型
     * @apiParam (请求体) {String} tinTypeDesc 税务编号类型说明
     * @apiParam (请求体) {String} tin 税务编号
     * @apiParam (请求体) {String} openSaveType 开户保存类型 必填，01-保存，02-退出
     * @apiParamExample 请求体示例
     * {"emplStatus":"veltu","emplAddrEn":"Ya","emplNatureOfBusinessDesc":"B6L84W6UdP","tinType":"r4lZ8PVJ4F","openSaveType":"pw","taxJurisdictionCode":"R","tinTypeDesc":"bIlJ5","incLevel":"y","noTinReason":"M","tin":"lHk49Kinj","emplCompanyName":"0","emplNatureOfBusiness":"zF","designation":"JUbO9O","hasTin":"e7sMN","emplAddrCn":"VQuDtdLSD","noObtainTinReason":"Y"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer 耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"BXE","description":"QBRrA","timestampServer":"bA1uf"}
     */
    @PostMapping("saveopenoccupationinfo")
    public CgiResponse<Body> saveOpenOccupationInfo(@RequestBody HkOpenAcctOccupationRequest request) {
        openAcctService.saveOpenOccupationInfo(request);
        return CgiResponse.ok(null);
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/temporarystorageopenoccupationinfo temporaryStorageOpenOccupationInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName temporaryStorageOpenOccupationInfo()
     * @apiDescription 步骤3: 职业及税务信息填写页-暂存接口,不校验提交参数信息
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} emplStatus 就业状况 01-雇主 02-全职 03-兼职 04-主妇 05-学生 06-退休 07-非在职
     * @apiParam (请求体) {String} emplCompanyName 就业公司名称
     * @apiParam (请求体) {String} emplAddrCn 就业公司地址-详细地址
     * @apiParam (请求体) {String} emplAddrEn 就业公司地址-英文详细地址
     * @apiParam (请求体) {String} emplNatureOfBusiness 业务性质
     * @apiParam (请求体) {String} emplNatureOfBusinessDesc 业务性质描述
     * @apiParam (请求体) {String} designation 职位/称衔
     * @apiParam (请求体) {String} incLevel 每年收入水平      ['01-≦HK$500', '000;      02-HK$500', '001', '-', 'HK$1', '000', '000;      03-HK$1', '000', '001', '-', 'HK$2', '000', '000;      04-HK$2', '000', '001', '-', 'HK$5', '000', '000;      05->HK$5', '000', '000']
     * @apiParam (请求体) {String} taxJurisdictionCode 税务管辖区代码
     * @apiParam (请求体) {String} hasTin 是否有税务编号 1是 0否
     * @apiParam (请求体) {String} noTinReason 无税务编号理由      ['01-账户持有人的居留司法税务管辖区并没有向其居民发出税务编号;02-账户持有人不能取得税务编号;03-账户持有人无须提供税务编号']
     * @apiParam (请求体) {String} noObtainTinReason 不能取的税务编号的原因
     * @apiParam (请求体) {String} tinType 税务编号类型
     * @apiParam (请求体) {String} tinTypeDesc 税务编号类型说明
     * @apiParam (请求体) {String} tin 税务编号
     * @apiParam (请求体) {String} openSaveType 开户保存类型 必填，01-保存，02-退出
     * @apiParamExample 请求体示例
     * {"emplStatus":"vsZu7ik","emplAddrEn":"q8ltb","emplNatureOfBusinessDesc":"yLov7vmNV","tinType":"NwST0","openSaveType":"8H","taxJurisdictionCode":"N4Uf3Gz","tinTypeDesc":"LoMxkJfq","incLevel":"qI","noTinReason":"OtjMi","tin":"Skte3yDgl","emplCompanyName":"NDV3jZSmU","emplNatureOfBusiness":"toQrGR","designation":"VQ","hasTin":"W8rJ6wPRip","emplAddrCn":"GSowNnds","noObtainTinReason":"WIzua841Q9"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"1K","description":"T","timestampServer":"YeKIbWfLP"}
     */
    @PostMapping("temporarystorageopenoccupationinfo")
    public CgiResponse<Body> temporaryStorageOpenOccupationInfo(@RequestBody HkOpenAcctOccupationRequest request) {
        openAcctService.temporaryStorageOpenOccupationInfo(request);
        return CgiResponse.ok(null);
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/saveopendeclareinfo 声明信息-开户暂存接口  线上开户-填写资料4
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName saveOpenDeclareInfo()
     * @apiDescription 步骤4:声明信息填写页-开户保存接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} declareOne 声明1选项
     * @apiParam (请求体) {String} companyName 公司名称
     * @apiParam (请求体) {String} exchangeName 交易所名称
     * @apiParam (请求体) {String} tradeNo 交易编号
     * @apiParam (请求体) {String} declareTwo 声明2选项
     * @apiParam (请求体) {String} licensedNo 持证号码
     * @apiParam (请求体) {Array} employerAgreeThumbnailUrls 雇主书面同意书URL列表
     * @apiParam (请求体) {String} employerAgreeThumbnailUrls.url 图片地址
     * @apiParam (请求体) {String} employerAgreeThumbnailUrls.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} employerAgreeThumbnailUrls.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiParam (请求体) {String} declareThree 声明3选项
     * @apiParam (请求体) {String} relateName 相关人士姓名
     * @apiParam (请求体) {String} declareFour 声明4选项
     * @apiParam (请求体) {String} declareFourDesc 详细说明
     * @apiParam (请求体) {String} openSaveType 开户暂存类型 必填，01-保存，02-退出
     * @apiParamExample 请求体示例
     * {"employerAgreeThumbnailUrls":[{"exampleFileFormatType":"gfxe","url":"M3nANSEYaN","thumbnailUrl":"XFZMlB7CmA"}],"relateName":"Alkum","declareFourDesc":"3","declareTwo":"3hulTwoi","declareThree":"C","tradeNo":"pUHfWitEs","licensedNo":"SfAGay","openSaveType":"UWBK4","companyName":"Z","exchangeName":"uS","declareFour":"E0f","declareOne":"oW2z2NJ"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer 耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"CimIqIfL","description":"zqWV97p8M","timestampServer":"uNEmYp5Gb2"}
     */
    @PostMapping("saveopendeclareinfo")
    public CgiResponse<Body> saveOpenDeclareInfo(@RequestBody HkOpenAcctDeclareRequest request) {
        //参数校验
        HkOpenAcctValidator.validatorOpenDeclareRequest(request);
        openAcctService.saveOpenDeclareInfo(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/temporarystorageopendeclareinfo temporaryStorageOpenDeclareInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName temporaryStorageOpenDeclareInfo()
     * @apiDescription 步骤4:声明信息填写页-开户暂存接口,不校验参数
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} declareOne 声明1选项
     * @apiParam (请求体) {String} companyName 公司名称
     * @apiParam (请求体) {String} exchangeName 交易所名称
     * @apiParam (请求体) {String} tradeNo 交易编号
     * @apiParam (请求体) {String} declareTwo 声明2选项
     * @apiParam (请求体) {String} licensedNo 持证号码
     * @apiParam (请求体) {Array} employerAgreeThumbnailUrls 雇主书面同意书URL列表
     * @apiParam (请求体) {String} employerAgreeThumbnailUrls.url 图片地址
     * @apiParam (请求体) {String} employerAgreeThumbnailUrls.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} employerAgreeThumbnailUrls.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiParam (请求体) {String} declareThree 声明3选项
     * @apiParam (请求体) {String} relateName 相关人士姓名
     * @apiParam (请求体) {String} declareFour 声明4选项
     * @apiParam (请求体) {String} declareFourDesc 详细说明
     * @apiParam (请求体) {String} openSaveType 开户暂存类型 必填，01-保存，02-退出
     * @apiParamExample 请求体示例
     * {"employerAgreeThumbnailUrls":[{"exampleFileFormatType":"iGysovX9","url":"FVVScKA","thumbnailUrl":"dUe7hlyp"}],"relateName":"G2Ds","declareFourDesc":"zHzfA","declareTwo":"dJm8wub5","declareThree":"NV6GO","tradeNo":"vTqAS","licensedNo":"nmmRekA","openSaveType":"AmgCixaJXQ","companyName":"gGvs7BLjiC","exchangeName":"UmiYHI","declareFour":"gbU19T8","declareOne":"yRgM"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Acd","description":"eAJhNr","timestampServer":"Rwl"}
     */
    @PostMapping("temporarystorageopendeclareinfo")
    public CgiResponse<Body> temporaryStorageOpenDeclareInfo(@RequestBody HkOpenAcctDeclareRequest request) {
        openAcctService.temporaryStorageOpenDeclareInfo(request);
        return CgiResponse.ok(new Body());
    }


    /**
     * @api {GET} /ext/hkaccount/openacct/queryopendeclareinfodetail 声明信息-开户暂存查询接口 线上开户-填写资料4
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName queryOpenDeclareInfo()
     * @apiDescription 步骤4:声明信息-开户声明信息查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.openDeclareValueVO 声明信息
     * @apiSuccess (响应结果) {String} data.openDeclareValueVO.declareOne 声明1选项
     * @apiSuccess (响应结果) {String} data.openDeclareValueVO.companyName 公司名称
     * @apiSuccess (响应结果) {String} data.openDeclareValueVO.exchangeName 交易所名称
     * @apiSuccess (响应结果) {String} data.openDeclareValueVO.tradeNo 交易编号
     * @apiSuccess (响应结果) {String} data.openDeclareValueVO.declareTwo 声明2选项
     * @apiSuccess (响应结果) {String} data.openDeclareValueVO.licensedNo 持证号码
     * @apiSuccess (响应结果) {Array} data.openDeclareValueVO.employerAgreeThumbnailUrls 雇主书面同意书URL列表
     * @apiSuccess (响应结果) {String} data.openDeclareValueVO.declareThree 声明3选项
     * @apiSuccess (响应结果) {String} data.openDeclareValueVO.relateName 相关人士姓名
     * @apiSuccess (响应结果) {String} data.openDeclareValueVO.declareFour 声明4选项
     * @apiSuccess (响应结果) {String} data.openDeclareValueVO.declareFourDesc 详细说明
     * @apiSuccess (响应结果) {String} data.openDeclareValueVO.openSaveType 开户暂存类型 必填，01-保存，02-退出
     * @apiSuccess (响应结果) {Array} data.checkResult 审核不通过原因,会具体到哪个字段,如果没有则为空。审核通过没有返回信息,采用同一套字段，方便前端取值
     * @apiSuccess (响应结果) {String} data.checkResult.fileType 字段类型描述
     * @apiSuccess (响应结果) {String} data.checkResult.fileName 字段名称
     * @apiSuccess (响应结果) {String} data.checkResult.reason 错误原因
     * @apiSuccess (响应结果) {String} timestampServer 耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"5q","data":{"openDeclareValueVO":{"employerAgreeThumbnailUrls":[],"relateName":"lpZvz6K7","declareFourDesc":"T6W85o7j","declareTwo":"4MR4","declareThree":"jHTm","tradeNo":"vEv7WgjOj4","licensedNo":"lkXjs","openSaveType":"LFq","companyName":"rK4PrW","exchangeName":"Qq5vxDo1","declareFour":"d3o","declareOne":"P2lrewea"},"checkResult":[{"reason":"TA274IJ","fileName":"aivZl","fileType":"oD6"}]},"description":"FHg","timestampServer":"dK"}
     */
    @GetMapping("queryopendeclareinfodetail")
    public CgiResponse<HkOpenAcctDeclareVO> queryOpenDeclareInfoDetail() {
        return CgiResponse.ok(openAcctService.queryOpenDeclareInfoDetail());
    }


    /**
     * @api {GET} /ext/hkaccount/openacct/queryopeninvestinfodetail queryOpenInvestInfoDetail()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName queryOpenInvestInfoDetail()
     * @apiDescription 步骤5:投资经验填写页-投资经验开户查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.riskToleranceLevelVO 风险测评结果页信息
     * @apiSuccess (响应结果) {String} data.riskToleranceLevelVO.examId 问卷ID
     * @apiSuccess (响应结果) {String} data.riskToleranceLevelVO.riskToleranceLevel 风险测评试算等级 风险承受能力 1-低风险等级 2-中低风险等级 3-中风险等级 4-中高风险等级 5-高风险等级
     * @apiSuccess (响应结果) {String} data.riskToleranceLevelVO.riskToleranceLevelDesc 风险测评试算等级描述
     * @apiSuccess (响应结果) {Number} data.riskToleranceLevelVO.score 风险测评试算分数
     * @apiSuccess (响应结果) {String} data.riskToleranceLevelVO.assessmentTime 测算时间 日期格式：yyyyMMdd
     * @apiSuccess (响应结果) {String} data.riskToleranceLevelVO.expirationDate 有效时间  日期格式：yyyyMMdd
     * @apiSuccess (响应结果) {String} data.riskToleranceLevelVO.riskToleranceInvalid 分险测评无效 1: 是 0:否
     * @apiSuccess (响应结果) {Object} data.openInvestValueVO 投资经验信息
     * @apiSuccess (响应结果) {String} data.openInvestValueVO.investorQualification 投资者资质
     * @apiSuccess (响应结果) {Array} data.openInvestValueVO.assetCertUrlList 资产证明URL列表
     * @apiSuccess (响应结果) {String} data.openInvestValueVO.examId 问卷id
     * @apiSuccess (响应结果) {Array} data.openInvestValueVO.answerDTOList 答案列表
     * @apiSuccess (响应结果) {Array} data.openInvestValueVO.wealthSource 财富来源选项 多选
     * @apiSuccess (响应结果) {String} data.openInvestValueVO.wealthSourceDesc 财富来源描述
     * @apiSuccess (响应结果) {Array} data.openInvestValueVO.fundTransferMeans 资金来源地
     * @apiSuccess (响应结果) {String} data.openInvestValueVO.fundTransferMeansDesc 资金来源地说明
     * @apiSuccess (响应结果) {Array} data.openInvestValueVO.acceptDeclare 阅读申明信息
     * @apiSuccess (响应结果) {Array} data.checkResult 投资经验信息 审核不通过会具体到哪个字段错误的原因,前端根据返回的错误原因做相应的提示
     * @apiSuccess (响应结果) {String} data.checkResult.fileType 字段类型描述
     * @apiSuccess (响应结果) {String} data.checkResult.fileName 字段名称
     * @apiSuccess (响应结果) {String} data.checkResult.reason 错误原因
     * @apiSuccess (响应结果) {String} timestampServer 耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"h8I3be","data":{"riskToleranceLevelVO":{"score":8351.85007886188,"assessmentTime":"HddEG7P2h","riskToleranceLevel":"Tri1","examId":"V0oVC","riskToleranceLevelDesc":"eCt1H","expirationDate":"3uZX3i","riskToleranceInvalid":"2q9s4"},"openInvestValueVO":{"acceptDeclare":["gFrskkr"],"answerDTOList":[],"assetCertUrlList":[],"examId":"L6oNt5cwH","wealthSourceDesc":"m6AQgMI","investorQualification":"rzJ8GNML","fundTransferMeans":["gCcOL3"],"fundTransferMeansDesc":"reX","wealthSource":["9"]},"checkResult":[{"reason":"YM30Qx","fileName":"IomYkw","fileType":"kRbURj8I"}]},"description":"zRs0QNxmvo","timestampServer":"ikLx"}
     */
    @GetMapping("queryopeninvestinfodetail")
    public CgiResponse<HkOpenAcctInvestVO> queryOpenInvestInfoDetail() {
        return CgiResponse.ok(openAcctService.queryOpenInvestInfoDetail());
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/saveopeninvestinfo 步骤5:投资经验填写页-投资经验开户保存接口
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName saveOpenInvestInfo()
     * @apiDescription 投资经验开户暂存接口-填写资料5
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} investorQualification 投资者资质  PRO-投资者资质专业, 'NORMAL-投资者资质普通'
     * @apiParam (请求体) {Array} acceptDeclare 接受阅读申明
     * @apiParam (请求体) {Array} assetCertUrlList 资产证明URL列表
     * @apiParam (请求体) {String} assetCertUrlList.url 图片地址
     * @apiParam (请求体) {String} assetCertUrlList.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} assetCertUrlList.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiParam (请求体) {String} examId 问卷id
     * @apiParam (请求体) {Array} wealthSource 财富来源选项 多选     ['01-薪金及/或花红', '02-业务收入', '03-退休金', '04-礼物', '05-储蓄', '06-投资回报', '07-遗赠', '08-其他']
     * @apiParam (请求体) {String} wealthSourceDesc 财富来源描述

     * @apiParam (请求体) {String} fundTransferMeansDesc 资金来源地说明
     * @apiParam (请求体) {String} openSaveType 开户保存类型 必填，01-保存，02-退出
     * @apiParamExample 请求体示例
     * {"acceptDeclare":["0"],"openSaveType":"9gZaCl6c9A","assetCertUrlList":[{"exampleFileFormatType":"ajLwdK","url":"N4","thumbnailUrl":"uF6O"}],"examId":"3n2S","wealthSourceDesc":"WyrgPFQXa","investorQualification":"wQVBBvo","fundTransferMeans":["I5Y9Wu5"],"fundTransferMeansDesc":"I3A0ZlH","wealthSource":["Af1B"]}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer 业务耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"k","description":"KDp7","timestampServer":"Jlwoz"}
     */
    @PostMapping("saveopeninvestinfo")
    public CgiResponse<Body> saveOpenInvestInfo(@RequestBody OpenInvestRequest request) {
        openAcctService.saveOpenInvestInfo(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/temporarystorageopeninvestinfo temporaryStorageOpenInvestInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName temporaryStorageOpenInvestInfo()
     * @apiDescription 步骤5:投资经验填写页-投资经验开户暂存接口,不校验接口参数
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} investorQualification 投资者资质  PRO-投资者资质专业, 'NORMAL-投资者资质普通'
     * @apiParam (请求体) {Array} acceptDeclare 接受阅读申明
     * @apiParam (请求体) {Array} assetCertUrlList 资产证明URL列表
     * @apiParam (请求体) {String} assetCertUrlList.url 图片地址
     * @apiParam (请求体) {String} assetCertUrlList.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} assetCertUrlList.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiParam (请求体) {String} examId 问卷id
     * @apiParam (请求体) {Array} wealthSource 财富来源选项 多选     ['01-薪金及/或花红', '02-业务收入', '03-退休金', '04-礼物', '05-储蓄', '06-投资回报', '07-遗赠', '08-其他']
     * @apiParam (请求体) {String} wealthSourceDesc 财富来源描述
     * @apiParam (请求体) {Array} fundTransferMeans 资金来源地     ['01-香港', '02-中国', '03-美国', '04-其他']
     * @apiParam (请求体) {String} fundTransferMeansDesc 资金来源地说明
     * @apiParam (请求体) {String} openSaveType 开户保存类型 必填，01-保存，02-退出
     * @apiParamExample 请求体示例
     * {"acceptDeclare":["pTob00"],"openSaveType":"ZY68d","assetCertUrlList":[{"exampleFileFormatType":"KRO0a","url":"DEqwjGJnki","thumbnailUrl":"RPpQ0y"}],"examId":"gOE4yuixES","wealthSourceDesc":"NOcQcB","investorQualification":"kAs7f5l","fundTransferMeans":["HO7Nt2D0RQ"],"fundTransferMeansDesc":"XfLhsjBBb","wealthSource":["ekP5f"]}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"ne","description":"w","timestampServer":"km0QlZB"}
     */
    @PostMapping("temporarystorageopeninvestinfo")
    public CgiResponse<Body> temporaryStorageOpenInvestInfo(@RequestBody OpenInvestRequest request) {
        openAcctService.temporaryStorageOpenInvestInfo(request);
        return CgiResponse.ok(new Body());
    }


    /**
     * @api {GET} /ext/hkaccount/openacct/queryopenbankinfodetail queryOpenBankInfoDetail()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName queryOpenBankInfoDetail()
     * @apiDescription 步骤6:银行信息填写页-银行卡信息开户查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.openBankValueVO 银行卡详细信息
     * @apiSuccess (响应结果) {String} data.openBankValueVO.bankCode 银行代码
     * @apiSuccess (响应结果) {String} data.openBankValueVO.bankAcctName 银行账户名称
     * @apiSuccess (响应结果) {String} data.openBankValueVO.bankAcct 银行账号
     * @apiSuccess (响应结果) {String} data.openBankValueVO.jointAccount 是否银行联名账户  1:是 0：否
     * @apiSuccess (响应结果) {Array} data.openBankValueVO.jointAccountFileList 关系证明文件列表
     * @apiSuccess (响应结果) {String} data.openBankValueVO.swiftCode 银行SWIFT编码
     * @apiSuccess (响应结果) {String} data.openBankValueVO.brokerBankCode 代理银行代码
     * @apiSuccess (响应结果) {String} data.openBankValueVO.brokerBankAcct 代理银行账号
     * @apiSuccess (响应结果) {Array} data.openBankValueVO.currencyVOList 币种列表  156：人民币  840：美元 344：港元 other:其他
     * @apiSuccess (响应结果) {Array} data.openBankValueVO.bankAcctImageList 银行账号照片列表
     * @apiSuccess (响应结果) {String} data.openBankValueVO.openSaveType 1：保存 2： 退出
     * @apiSuccess (响应结果) {Array} data.checkResult 审核不通过结果原因,如果审核通过，则为空。具体到哪个字段不通过，则在该字段中说明。
     * @apiSuccess (响应结果) {String} data.checkResult.fileType 字段类型描述
     * @apiSuccess (响应结果) {String} data.checkResult.fileName 字段名称
     * @apiSuccess (响应结果) {String} data.checkResult.reason 错误原因
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"KPrtWPlOM","data":{"openBankValueVO":{"jointAccount":"ERJk8t2","bankCode":"A2j12Ez","brokerBankCode":"p1ZOJ9EW","bankAcctName":"R","openSaveType":"TEkOG","bankAcctImageList":[],"bankAcct":"0","currencyVOList":[],"swiftCode":"B7","brokerBankAcct":"N2VhrH","jointAccountFileList":[]},"checkResult":[{"reason":"7ykU","fileName":"I","fileType":"q78s"}]},"description":"kkK","timestampServer":"jR1Kf"}
     */
    @GetMapping("queryopenbankinfodetail")
    public CgiResponse<HkOpenAcctBankInfoVO> queryOpenBankInfoDetail() {
        return CgiResponse.ok(openAcctService.queryOpenBankInfoDetail());
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/saveopenbankinfo 银行卡信息暂存接口
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName saveOpenBankInfo()
     * @apiDescription 步骤6:银行信息填写页-银行卡信息保存接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} bankCode 银行代码
     * @apiParam (请求体) {String} bankAcctName 银行账户名称(英文名)
     * @apiParam (请求体) {String} bankAcctChineseName 银行账户中文名称
     * @apiParam (请求体) {String} bankAcct 银行账号
     * @apiParam (请求体) {String} jointAccount 是否银行联名账户  1:是 0：否
     * @apiParam (请求体) {Array} jointAccountFileList 关系证明文件列表
     * @apiParam (请求体) {String} jointAccountFileList.url 图片地址
     * @apiParam (请求体) {String} jointAccountFileList.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} jointAccountFileList.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiParam (请求体) {String} swiftCode 银行SWIFT编码
     * @apiParam (请求体) {String} brokerBankCode 代理银行代码
     * @apiParam (请求体) {String} brokerBankName 代理银行名称（英文名)
     * @apiParam (请求体) {String} brokerBankChineseName 代理银行的中文名
     * @apiParam (请求体) {String} brokerBankAcct 代理银行账号
     * @apiParam (请求体) {String} brokerSwiftCode 代理银行swiftCode
     * @apiParam (请求体) {Array} currencyVOList 币种列表
     * @apiParam (请求体) {String} currencyVOList.currencyCode 货币代码
     * @apiParam (请求体) {String} currencyVOList.currencyDesc 货币说明
     * @apiParam (请求体) {String} currencyVOList.remark 当货币是其他类型时,备注必传
     * @apiParam (请求体) {Array} bankAcctImageList 银行账号照片列表
     * @apiParam (请求体) {String} bankAcctImageList.url 图片地址
     * @apiParam (请求体) {String} bankAcctImageList.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} bankAcctImageList.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiParam (请求体) {String} bankAcctHolder 账号持有人
     * @apiParam (请求体) {String} openSaveType 1:保存 2:退出
     * @apiParamExample 请求体示例
     * {"bankCode":"s8","bankAcctName":"PSSYf6nQx","openSaveType":"G8J","brokerBankChineseName":"g","bankAcctImageList":[{"exampleFileFormatType":"45StmfQ","url":"gdzv5","thumbnailUrl":"nHQ0DZsA"}],"bankAcct":"FgvGD5W","currencyVOList":[{"currencyDesc":"usU","remark":"qDK7Hbw","currencyCode":"AnzP"}],"swiftCode":"2AH","bankAcctChineseName":"Bibo4QH","jointAccountFileList":[{"exampleFileFormatType":"uDRdjjpHP","url":"Q","thumbnailUrl":"n"}],"jointAccount":"kyBHSU","brokerBankCode":"B0","brokerBankName":"srAISg0l0","brokerBankAcct":"xvozw","brokerSwiftCode":"BqV","bankAcctHolder":"Px"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer 耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"3AZlSI4","description":"raWf107ugC","timestampServer":"J1ZG0Yat5"}
     */
    @PostMapping("saveopenbankinfo")
    public CgiResponse<Body> saveOpenBankInfo(@RequestBody HkOpenAcctBankRequest request) {
        openAcctService.saveOpenBankInfo(request);
        return CgiResponse.ok(null);
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/temporarystorageopenbankinfo temporaryStorageOpenBankInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName temporaryStorageOpenBankInfo()
     * @apiDescription 步骤6:银行信息填写页-银行卡信息暂存接口,不校验参数
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} bankCode 银行代码
     * @apiParam (请求体) {String} bankAcctName 银行账户名称
     * @apiParam (请求体) {String} bankAcct 银行账号
     * @apiParam (请求体) {String} jointAccount 是否银行联名账户  1:是 0：否
     * @apiParam (请求体) {Array} jointAccountFileList 关系证明文件列表
     * @apiParam (请求体) {String} jointAccountFileList.url 图片地址
     * @apiParam (请求体) {String} jointAccountFileList.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} jointAccountFileList.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiParam (请求体) {String} swiftCode 银行SWIFT编码
     * @apiParam (请求体) {String} brokerBankCode 代理银行代码
     * @apiParam (请求体) {String} brokerBankName 代理银行名称
     * @apiParam (请求体) {String} brokerBankAcct 代理银行账号
     * @apiParam (请求体) {String} brokerSwiftCode 代理银行swiftCode
     * @apiParam (请求体) {Array} currencyVOList 币种列表
     * @apiParam (请求体) {String} currencyVOList.currencyCode 货币代码
     * @apiParam (请求体) {String} currencyVOList.currencyDesc 货币说明
     * @apiParam (请求体) {String} currencyVOList.remark 当货币是其他类型时,备注必传
     * @apiParam (请求体) {Array} bankAcctImageList 银行账号照片列表
     * @apiParam (请求体) {String} bankAcctImageList.url 图片地址
     * @apiParam (请求体) {String} bankAcctImageList.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} bankAcctImageList.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiParam (请求体) {String} bankAcctHolder 账号持有人
     * @apiParam (请求体) {String} openSaveType 1:保存 2:退出
     * @apiParamExample 请求体示例
     * {"bankCode":"O","bankAcctName":"lmeip","openSaveType":"4X4A6P","bankAcctImageList":[{"exampleFileFormatType":"j","url":"0rpBi","thumbnailUrl":"Dx"}],"bankAcct":"Dvaa","currencyVOList":[{"currencyDesc":"xeRT","remark":"OKIZBz8SnW","currencyCode":"r"}],"swiftCode":"NnhrZ","jointAccountFileList":[{"exampleFileFormatType":"DojxpDCGR","url":"N","thumbnailUrl":"K1GHW"}],"jointAccount":"G6BGWjm","brokerBankCode":"aWRtHwweBm","brokerBankName":"BpPcBPGwjw","brokerBankAcct":"5M3eZLnhO","brokerSwiftCode":"1","bankAcctHolder":"bDkgboEO5"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"QyVZ03t3","description":"xTGvOtKCn","timestampServer":"FhsSjmY"}
     */
    @PostMapping("temporarystorageopenbankinfo")
    public CgiResponse<Body> temporaryStorageOpenBankInfo(@RequestBody HkOpenAcctBankRequest request) {
        openAcctService.temporaryStorageOpenBankInfo(request);
        return CgiResponse.ok(null);
    }


    /**
     * @api {GET} /ext/hkaccount/openacct/queryopensignatureimages queryOpenSignatureImages()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName queryOpenSignatureImages()
     * @apiDescription 步骤7:电子签名页-电子签名开户查询,文件签署填写签名资料查询页
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.eSignatureImages 电子签名图片URL
     * @apiSuccess (响应结果) {String} data.eSignatureImages.url 图片地址
     * @apiSuccess (响应结果) {String} data.eSignatureImages.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.eSignatureImages.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiSuccess (响应结果) {Array} data.materialList 材料表展示信息
     * @apiSuccess (响应结果) {String} data.materialList.materialCode 材料编码
     * @apiSuccess (响应结果) {String} data.materialList.materialDesc 材料表描述
     * @apiSuccess (响应结果) {String} data.materialList.url 材料地址
     * @apiSuccess (响应结果) {String} data.userName 用户名称
     * @apiSuccess (响应结果) {Array} data.checkResult 审核错误的提示信息
     * @apiSuccess (响应结果) {String} data.checkResult.fileType 字段类型描述
     * @apiSuccess (响应结果) {String} data.checkResult.fileName 字段名称
     * @apiSuccess (响应结果) {String} data.checkResult.reason 错误原因
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"H","data":{"materialList":[{"materialDesc":"wrF8jW0","materialCode":"T","url":"EKDtCt"}],"userName":"FTbqP","checkResult":[{"reason":"APWuJ9Io","fileName":"i","fileType":"ZHN"}],"eSignatureImages":[{"exampleFileFormatType":"RwnVsFMe","url":"sWmogNlU6","thumbnailUrl":"E5TtL2Wl1m"}]},"description":"0t5hUXmZ","timestampServer":"C"}
     */
    @GetMapping("queryopensignatureimages")
    public CgiResponse<HkOpenAcctESignatureVO> queryOpenSignatureImages() {
        return CgiResponse.ok(openAcctService.queryOpenSignatureImages());
    }


    /**
     * @api {POST} /ext/hkaccount/openacct/saveopensignature 开户提交/修改接口
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName saveOpenSignature()
     * @apiDescription 步骤7:电子签名页-电子签保存接口,开户提交/修改接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {Object} eSignatureImageUrls 电子签名图片URL
     * @apiParam (请求体) {String} eSignatureImageUrls.url 图片地址
     * @apiParam (请求体) {String} eSignatureImageUrls.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} eSignatureImageUrls.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiParam (请求体) {String} useTermsFilePath 用户条款文件路径
     * @apiParam (请求体) {String} warnStatementFilePath 警告声明文件路径
     * @apiParamExample 请求体示例
     * {"warnStatementFilePath":"siwKbTl5L","eSignatureImageUrls":{"exampleFileFormatType":"7Bsx2","url":"WZyeV","thumbnailUrl":"QUELaoB8"},"useTermsFilePath":"jx6"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.eSignatureImages 电子签名图片URL
     * @apiSuccess (响应结果) {String} data.eSignatureImages.url 图片地址
     * @apiSuccess (响应结果) {String} data.eSignatureImages.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.eSignatureImages.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiSuccess (响应结果) {Array} data.materialList 材料表展示信息
     * @apiSuccess (响应结果) {String} data.materialList.materialCode 材料编码
     * @apiSuccess (响应结果) {String} data.materialList.materialDesc 材料表描述
     * @apiSuccess (响应结果) {String} data.materialList.url 材料地址
     * @apiSuccess (响应结果) {String} data.userName 用户名称
     * @apiSuccess (响应结果) {Array} data.checkResult 审核错误的提示信息
     * @apiSuccess (响应结果) {String} data.checkResult.fileType 字段类型描述
     * @apiSuccess (响应结果) {String} data.checkResult.fileName 字段名称
     * @apiSuccess (响应结果) {String} data.checkResult.reason 错误原因
     * @apiSuccess (响应结果) {String} timestampServer 耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"rojQi5ar","data":{"materialList":[{"materialDesc":"YiZW","materialCode":"IzVDC","url":"7JaT9W0"}],"userName":"ImHXYDlm","checkResult":[{"reason":"RnxrUsB4xS","fileName":"SPaTJCvI","fileType":"THobIw4i"}],"eSignatureImages":[{"exampleFileFormatType":"YF5IPx","url":"YptNc434l","thumbnailUrl":"qoP3"}]},"description":"2Cjr","timestampServer":"l95"}
     */
    @PostMapping("saveopensignature")
    public CgiResponse<HkOpenAcctESignatureVO> saveOpenSignature(@RequestBody ESignatureRequest request) {
        HkOpenAcctValidator.validator(request);
        openAcctService.saveOpenSignature(request);
        return CgiResponse.ok(null);
    }


    /**
     * @api {POST} /ext/hkaccount/openacct/batchuploadidphoto batchUploadIdPhoto()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName batchUploadIdPhoto()
     * @apiDescription 公共接口-开户接口文件上传接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} imageType 证件类型 00-证件；01-现居地址证明；02-通讯地址证明；03-雇主书面同意书；04-资产证明文件；05-银行子账号照片；06-联名账户补充材料；07-电子签名图片；08-入金打款凭证；
     * @apiParam (请求体) {String} fileBase64 文件流
     * @apiParam (请求体) {String} imageFormat 文件格式类型
     * @apiParam (请求体) {String} imageName 文件名称
     * @apiParamExample 请求体示例
     * {"imageFormat":"M","imageName":"hl8P","imageType":"ikNvXeD","fileBase64":"8H5cjfy"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.openImageVOList 开户文件上传接口返回值
     * @apiSuccess (响应结果) {String} data.openImageVOList.url 图片地址
     * @apiSuccess (响应结果) {String} data.openImageVOList.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.openImageVOList.exampleFileFormatType 文件类型  文件后缀格式
     * @apiSuccess (响应结果) {String} timestampServer 耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"5psavUYrl","data":{"openImageVOList":[{"exampleFileFormatType":"e","url":"CHM","thumbnailUrl":"FFSy0z"}]},"description":"UcPgriMgy4","timestampServer":"8zslA"}
     */
    @PostMapping("batchuploadidphoto")
    public CgiResponse<HkOpenImageVO> batchUploadIdPhoto(@RequestBody UploadIdPhotoRequest request) {
        BasicDataTypeValidator.validator(request);
        if(StringUtils.isBlank(request.getFileBase64()) && CollectionUtils.isEmpty(request.getFileBase64List())){
            throw new ParamsException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(),"文件信息流不能为空");
        }
        return CgiResponse.ok(openAcctService.batchUploadIdPhoto(request));
    }


    /**
     * @api {POST} /ext/hkaccount/openacct/appuploadfilebystream uploadFileByStream()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName uploadFileByStream()
     * @apiDescription 文件流的方式上传文件
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} hkCustNo 香港客户号
     * @apiParam (请求参数) {Object} file 文件流
     * @apiParam (请求参数) {String} imageType 图片类型
     * @apiParamExample 请求参数示例
     * file=null&hkCustNo=jvU19WdOh&imageType=romrXy
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.openImageVOList 开户文件上传接口返回值
     * @apiSuccess (响应结果) {String} data.openImageVOList.url 图片地址
     * @apiSuccess (响应结果) {String} data.openImageVOList.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.openImageVOList.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"OaxaMAu0p","data":{"openImageVOList":[{"exampleFileFormatType":"mbAt1XozK","url":"On1Y","thumbnailUrl":"ZzG1xCwoOV"}]},"description":"ZF","timestampServer":"YkX0LyJ"}
     */
    @PostMapping("appuploadfilebystream")
    public CgiResponse<HkOpenImageVO> uploadFileByStream(HkAppUploadFileRequest request) {
        if(null == request.getFile() || request.getFile().length == 0){
            throw new ParamsException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(),"文件信息流不能为空");
        }
        return CgiResponse.appOk(openAcctService.uploadFileByStream(request));
    }


    /**
     * @api {POST} /ext/hkaccount/openacct/appuploadfile uploadFile()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName uploadFile()
     * @apiDescription 上传文件接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} hkCustNo 香港客户号
     * @apiParam (请求参数) {Array} file 文件流
     * @apiParam (请求参数) {String} imageType 文件类型 不同的类型存储的文件目录不一样
     * @apiParamExample 请求参数示例
     * file=&hkCustNo=EUh&imageType=TR2
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.openImageVOList 开户文件上传接口返回值
     * @apiSuccess (响应结果) {String} data.openImageVOList.url 图片地址
     * @apiSuccess (响应结果) {String} data.openImageVOList.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.openImageVOList.exampleFileFormatType 文件类型
     * @apiSuccess (响应结果) {String} data.openImageVOList.fileName 文件名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"1BjrrHhh","data":{"openImageVOList":[{"fileName":"Rp","exampleFileFormatType":"Lk","url":"fO3WOpOBVV","thumbnailUrl":"kfiqHmVb3c"}]},"description":"rT2WT","timestampServer":"xp9O"}
     */
    @PostMapping("appuploadfile")
    public CgiResponse<HkOpenImageVO> uploadFile(HkAppUploadFileRequest request) {
        if(null == request.getFile() || request.getFile().length == 0){
            throw new ParamsException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(),"文件信息流不能为空");
        }
        return CgiResponse.appOk(openAcctService.uploadFile(request));
    }

    /**
     * @api {GET} /ext/hkaccount/openacct/querybankinfolist 银行列表查询接口
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName queryBankInfoList()
     * @apiDescription 步骤6:银行信息填写页-银行列表查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} swiftOrBankName 银行名称或者swift编码模糊查询
     * @apiParamExample 请求参数示例
     * swiftOrBankName=4moSfaUp
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.bankInfoList
     * @apiSuccess (响应结果) {String} data.bankInfoList.swiftCode swift编码
     * @apiSuccess (响应结果) {String} data.bankInfoList.bankName 银行名称(英文名)
     * @apiSuccess (响应结果) {String} data.bankInfoList.bankChineseName 银行的中文名称
     * @apiSuccess (响应结果) {String} data.bankInfoList.bankCode 银行代码
     * @apiSuccess (响应结果) {String} data.bankInfoList.bankLogoUrl 银行logoUrl
     * @apiSuccess (响应结果) {String} timestampServer 耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"1pCBsO0Y","data":{"bankInfoList":[{"bankLogoUrl":"PbwBOBlg","bankCode":"P","swiftCode":"diU15GuVp","bankName":"Le"}]},"description":"io","timestampServer":"i"}
     */
    @GetMapping("querybankinfolist")
    public CgiResponse<BankInfoVO> queryBankInfoList(@RequestParam(value = "swiftOrBankName", required = false) String swiftOrBankName) {
        return CgiResponse.ok(openAcctService.queryBankInfoList(swiftOrBankName));
    }


    /**
     * @api {POST} /ext/hkaccount/openacct/queryidentitycardocrdetail OCR上传识别接口
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName queryIdentityCardOcrDetail()
     * @apiDescription 步骤1：证件信息填写页-OCR上传识别接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} frontPictureBase64 正面图片,用MultipartFile接受,如果不传值，会异常
     * @apiParam (请求体) {String} backPictureBase64 反面图片，用MultipartFile接受,如果不传值，会异常
     * @apiParam (请求体) {String} imageFormat 图片格式，后缀类型
     * @apiParam (请求体) {String} saveFile 是否保存文件 1:是 0:否
     * @apiParamExample 请求体示例
     * {"imageFormat":"VYyzPJz","frontPictureBase64":"f3GZdIT","backPictureBase64":"HKHbNduN","saveFile":"ELO"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.idNo 证件号码
     * @apiSuccess (响应结果) {String} data.expireTime 证件有效期
     * @apiSuccess (响应结果) {String} data.idAlwaysValidFlag 是否是长期有效 1:是 0：否
     * @apiSuccess (响应结果) {String} data.idAddress 证件地址
     * @apiSuccess (响应结果) {Object} data.frontImage 正面照片的图片路径和缩略图路径
     * @apiSuccess (响应结果) {String} data.frontImage.url 图片地址
     * @apiSuccess (响应结果) {String} data.frontImage.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.frontImage.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiSuccess (响应结果) {Object} data.backImage 反面照片的图片路径和缩略图路径
     * @apiSuccess (响应结果) {String} data.backImage.url 图片地址
     * @apiSuccess (响应结果) {String} data.backImage.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.backImage.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiSuccess (响应结果) {String} timestampServer 耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"CZNRu","data":{"expireTime":"GyrgPHJh","frontImage":{"exampleFileFormatType":"p8oYZ7If","url":"gjMOaYTOYC","thumbnailUrl":"CsXKfm5"},"idAlwaysValidFlag":"8FwUjiWA","custName":"rnBMLGUnJ","idNo":"dRZTR7s1O","idAddress":"Vb1W","backImage":{"exampleFileFormatType":"gWpL","url":"2oXfTl","thumbnailUrl":"T4AfPw"}},"description":"hrNRaNbT","timestampServer":"wnFswrr7qh"}
     */
    @PostMapping("queryidentitycardocrdetail")
    public CgiResponse<IdentityCardVO> queryIdentityCardOcrDetail(@RequestBody IdentityCardOcrRequest request) {
        return CgiResponse.ok(openAcctService.queryIdentityCardOcrDetail(request));
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/queryidentityocrdetailbyfilestream queryIdentityOcrDetailByFileStream()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName queryIdentityOcrDetailByFileStream()
     * @apiDescription 身份证OCR文件流识别接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {Object} frontFileStream 正面图片,用MultipartFile接受,如果不传值，会异常
     * @apiParam (请求参数) {Object} backFileStream 反面图片，用MultipartFile接受,如果不传值，会异常
     * @apiParamExample 请求参数示例
     * backFileStream=null&frontFileStream=null
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.idNo 证件号码
     * @apiSuccess (响应结果) {String} data.expireTime 证件有效期
     * @apiSuccess (响应结果) {String} data.idAlwaysValidFlag 是否是长期有效 1:是 0：否
     * @apiSuccess (响应结果) {String} data.idAddress 证件地址
     * @apiSuccess (响应结果) {Object} data.frontImage 正面照片的图片路径和缩略图路径
     * @apiSuccess (响应结果) {String} data.frontImage.url 图片地址
     * @apiSuccess (响应结果) {String} data.frontImage.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.frontImage.exampleFileFormatType 文件类型
     * @apiSuccess (响应结果) {Object} data.backImage 反面照片的图片路径和缩略图路径
     * @apiSuccess (响应结果) {String} data.backImage.url 图片地址
     * @apiSuccess (响应结果) {String} data.backImage.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.backImage.exampleFileFormatType 文件类型
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"34mzlAG","data":{"expireTime":"nQQDUnOm","frontImage":{"exampleFileFormatType":"uxcvdmBFp6","url":"Pp","thumbnailUrl":"mytwr"},"idAlwaysValidFlag":"DUGZkwg4","custName":"1ChTAa0fx","idNo":"0Ap","idAddress":"phDVa","backImage":{"exampleFileFormatType":"9cEH0","url":"78kNkb","thumbnailUrl":"cZdK4amMm4"}},"description":"yl5iGnHrAF","timestampServer":"takqmsNqrc"}
     */
    @PostMapping("queryidentityocrdetailbyfilestream")
    public CgiResponse<IdentityCardVO> queryIdentityOcrDetailByFileStream(HkAccIdentityOcrRequest request) {
       return CgiResponse.ok(openAcctService.queryIdentityOcrDetailByFileStream(request));
    }
    /**
     * @api {POST} /ext/hkaccount/openacct/queryrealnameauthenticationresult 步骤2：个人信息页-实名信息验证接口
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName queryRealNameAuthenticationResult()
     * @apiDescription 实名信息验证接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} custName 客户姓名
     * @apiParam (请求体) {String} idType 证件类型
     * @apiParam (请求体) {String} idNo 证件号码
     * @apiParamExample 请求体示例
     * {"idType":"N69PBkGeXB","custName":"uzD58QxgY","idNo":"AS8EpQ1"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer 业务耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"qB","description":"hSibMWR8jB","timestampServer":"MWV"}
     */
    @PostMapping("queryrealnameauthenticationresult")
    public CgiResponse<Body> queryRealNameAuthenticationResult(@RequestBody RealNameAuthResultRequest request) {
        openAcctService.queryRealNameAuthenticationResult(request);
        return CgiResponse.ok(new Body());
    }


    /**
     * @api {POST} /ext/hkaccount/openacct/saveriskdisclosure 分险披露保存接口
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName saveRiskDisclosure()
     * @apiDescription 步骤7:文件签署和签名-分险披露保存接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} audioPlayStatus 音频播放状态 0-未播放完成；1-已播放完成
     * @apiParam (请求体) {String} remainderTime 0-未播放完成时必填
     * @apiParamExample 请求体示例
     * {"audioPlayStatus":"z3Gfj9d","remainderTime":"hU1P"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer 业务耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"1Vz29XcB","description":"pYUE","timestampServer":"EhPOjP"}
     */
    @PostMapping("saveriskdisclosure")
    public CgiResponse<Body> saveRiskDisclosure(@RequestBody RiskDisclosureRequest request) {
        openAcctService.saveRiskDisclosure(request);
        return CgiResponse.ok(null);
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/temporarystorageriskdisclosure temporaryStorageRiskDisclosure()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName temporaryStorageRiskDisclosure()
     * @apiDescription 步骤7:文件签署和签名-分险披露暂存接口不保存数据
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} audioPlayStatus 音频播放状态 0-未播放完成；1-已播放完成
     * @apiParam (请求体) {String} remainderTime 0-未播放完成时必填
     * @apiParamExample 请求体示例
     * {"audioPlayStatus":"BUfh","remainderTime":"2VV3C"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"AqhL88lp","description":"VNSYYz","timestampServer":"mHfXoH"}
     */
    @PostMapping("temporarystorageriskdisclosure")
    public CgiResponse<Body> temporaryStorageRiskDisclosure(@RequestBody RiskDisclosureRequest request) {
        openAcctService.temporaryStorageRiskDisclosure(request);
        return CgiResponse.ok(null);
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/sendopenfiletoemail sendOpenFileToEmail()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName sendOpenFileToEmail()
     * @apiDescription 开户文件发送邮箱接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"4xpE","description":"nwyhURjX","timestampServer":"11DAa0"}
     */
    @PostMapping("/sendopenfiletoemail")
    @ResponseBody
    public CgiResponse<Body> sendOpenFileToEmail() {
        try {
            String email = openAcctService.sendOpenFileToEmail();
            return CgiResponse.ok("请到您绑定的邮箱下载附件查看  邮箱地址：" + email, null);
        } catch (Exception e) {
            return CgiResponse.error(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), ExceptionCodeEnum.SYSTEM_ERROR.getDescription());
        }
    }


    /**
     * @api {POST} /ext/hkaccount/openacct/queryaddressinfobytype queryAddressInfoByType()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName queryAddressInfoByType()
     * @apiDescription 步骤2:个人信息填写页-根据地址类型查询地址详情信息
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} addressType 地址类型  1：居住地址  2：通讯地址  3：出生地  4: 银行填写页公司地址
     * @apiParamExample 请求体示例
     * {"addressType":"IAQqS"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.countryCode 国家代码
     * @apiSuccess (响应结果) {String} data.countryDesc 国家代码名称描述
     * @apiSuccess (响应结果) {String} data.countryEnglishDesc 国家英文描述
     * @apiSuccess (响应结果) {String} data.provCode 省份代码
     * @apiSuccess (响应结果) {String} data.provDesc 省份代码名称
     * @apiSuccess (响应结果) {String} data.cityCode 城市代码
     * @apiSuccess (响应结果) {String} data.cityDesc 城市代码名称
     * @apiSuccess (响应结果) {String} data.countyCode 区县代码
     * @apiSuccess (响应结果) {String} data.provEnglishDesc 省代码英文名称
     * @apiSuccess (响应结果) {String} data.cityEnglishDesc 城市英文
     * @apiSuccess (响应结果) {String} data.countyEnglishDesc 区县英文
     * @apiSuccess (响应结果) {String} data.detailAddrCn 详细地址中文描述
     * @apiSuccess (响应结果) {String} data.detailAddrEn 详细地址英文描述
     * @apiSuccess (响应结果) {String} data.townEn 城/镇（英文）
     * @apiSuccess (响应结果) {String} data.stateEn 省/州（英文）
     * @apiSuccess (响应结果) {Array} data.residenceCertUrls 地址证明URL列表
     * @apiSuccess (响应结果) {String} data.residenceCertUrls.url 图片地址
     * @apiSuccess (响应结果) {String} data.residenceCertUrls.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.residenceCertUrls.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"MQH4G","data":{"detailAddrCn":"sylduO2","cityDesc":"cW7IJl8B","provCode":"KXSu0y","cityCode":"iCKJFy","detailAddrEn":"NGXsO","provDesc":"i5ZEwt","provEnglishDesc":"mE","countyEnglishDesc":"18V","residenceCertUrls":[{"exampleFileFormatType":"dCE2t2J","url":"vhdxF","thumbnailUrl":"KYsnZ3sh"}],"countryDesc":"3","cityEnglishDesc":"im4Ly","stateEn":"44K9H","countyCode":"sWMUW","countryCode":"yri","countryEnglishDesc":"MXlxnvQA2K","townEn":"DELbOZYnz"},"description":"WY0k0c","timestampServer":"ug"}
     */
    @PostMapping("/queryaddressinfobytype")
    public CgiResponse<HkOpenAcctAddressInfoVO> queryAddressInfoByType(@RequestBody HkOpenAcctAddressRequest request) {
        HkOpenAcctValidator.validatorStringType(request.getAddressType(),"地址类型不能为空");
        return CgiResponse.ok(openAcctService.queryAddressInfoByType(request));
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/saveaddressinfo saveAddressInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName saveAddressInfo()
     * @apiDescription 步骤2:个人信息填写页-地址信息保存接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} addressType 地址类型  1：居住地址  2：通讯地址  3：出生地  4: 银行填写页公司地址
     * @apiParam (请求体) {String} transAddress 是否需要翻译详细地址, 0:不需要, 1:需要
     * @apiParam (请求体) {String} countryCode 国家代码
     * @apiParam (请求体) {String} countryDesc 国家代码名称描述
     * @apiParam (请求体) {String} countryEnglishDesc 国家英文描述
     * @apiParam (请求体) {String} provCode 省份代码
     * @apiParam (请求体) {String} provDesc 省份代码名称
     * @apiParam (请求体) {String} cityCode 城市代码
     * @apiParam (请求体) {String} cityDesc 城市代码名称
     * @apiParam (请求体) {String} countyCode 区县代码
     * @apiParam (请求体) {String} provEnglishDesc 省代码英文名称
     * @apiParam (请求体) {String} cityEnglishDesc 城市英文
     * @apiParam (请求体) {String} countyEnglishDesc 区县英文
     * @apiParam (请求体) {String} detailAddrCn 详细地址中文描述
     * @apiParam (请求体) {String} detailAddrEn 详细地址英文描述
     * @apiParam (请求体) {String} townEn 城/镇（英文）
     * @apiParam (请求体) {String} stateEn 省/州（英文）
     * @apiParamExample 请求体示例
     * {"detailAddrCn":"IKa","cityDesc":"AIQKEfz","provCode":"1y52k","transAddress":"pduov","addressType":"ik","cityCode":"W","detailAddrEn":"chQrf","provDesc":"lh0RPyX40","provEnglishDesc":"t","countyEnglishDesc":"6j6lr7eV","countryDesc":"dsx4fR3","cityEnglishDesc":"thIn7XMY1O","stateEn":"ZApT8Jcnk","countyCode":"lD9n","countryCode":"lukQ1SUKJ","countryEnglishDesc":"BEFCgqUNov","townEn":"zyaqoO7i"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.detailAddrCn 详细地址中文描述
     * @apiSuccess (响应结果) {String} data.detailAddrEn 详细地址英文描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0p0","data":{"detailAddrCn":"lM3Y","detailAddrEn":"o"},"description":"cyz","timestampServer":"yjkUm"}
     */
    @PostMapping("/saveaddressinfo")
    public CgiResponse<HkOpenAcctProCitySaveVO> saveAddressInfo(@RequestBody HkOpenAcctProCityRequest request) {
        //基本参数校验
        HkOpenAcctValidator.validator(request);
        //业务逻辑参数校验
        HkOpenAcctValidator.validatorOpenAddressInfoByType(request);
        return CgiResponse.ok(openAcctService.saveAddressInfo(request));
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/checkmobileemail checkMobileEmail()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName checkMobileEmail()
     * @apiDescription 验证手机号或者验证码是否绑定其他手机号
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} type 类型 mobile:手机号 email:邮箱
     * @apiParam (请求体) {String} mobile 手机号
     * @apiParam (请求体) {String} email 邮箱
     * @apiParam (请求体) {String} areaCode 手机区号
     * @apiParamExample 请求体示例
     * {"areaCode":"QdoJz0IY","mobile":"Ws","type":"cz","email":"WSDdpWI"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"m","description":"7R3rVmE3yG","timestampServer":"9ix"}
     */
    @PostMapping("/checkmobileemail")
    public CgiResponse<Body> checkMobileEmail(@RequestBody HkOpenAcctCheckAccountRequest request) {
        openAcctService.checkMobileEmail(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {GET} /ext/hkaccount/openacct/querylicenseeinfodetail 持牌人信息查询接口
     * @apiVersion 1.0.0
     * @apiGroup LicenseeController
     * @apiName queryLicenseeInfoDetail()
     * @apiDescription 步骤7:文件签署页-持牌人信息查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.licenseeName 持牌人姓名
     * @apiSuccess (响应结果) {String} data.licenseeCode 持牌人执政编号
     * @apiSuccess (响应结果) {String} data.licenseeImageUrl 持牌人照片url
     * @apiSuccess (响应结果) {String} data.licenseeAudioUrl 持牌人录音url
     * @apiSuccess (响应结果) {String} data.licenseeAudioTime 持牌人录音时长
     * @apiSuccess (响应结果) {String} data.riskDiscloseList 风险披露列表
     * @apiSuccess (响应结果) {String} data.audioPlayStatus 音频播放状态 0-未播放完成；1-已播放完成
     * @apiSuccess (响应结果) {String} data.remainderTime 剩余时间 0-未播放完成时有值
     * @apiSuccess (响应结果) {String} timestampServer 耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"em6Bx","data":{"audioPlayStatus":"xZ","licenseeAudioUrl":"U","riskDiscloseList":"h7a","licenseeCode":"ooL9A","licenseeName":"j6Z","remainderTime":"ZPI","licenseeImageUrl":"r33cN","licenseeAudioTime":"ZO2DwjbH"},"description":"kXQOP","timestampServer":"KKh99PevkF"}
     */
    @GetMapping("querylicenseeinfodetail")
    public CgiResponse<LicenseeDetailVO> queryLicenseeInfoDetail() {
        return CgiResponse.ok(openAcctService.queryLicenseeInfoDetail());
    }


    /**
     * @api {POST} /ext/hkaccount/openacct/verificationelectronicsignature verificationElectronicSignature()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName verificationElectronicSignature()
     * @apiDescription 步骤7:文件签署页-电子签名验证接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} signatureImageBase64 签名图片base字符串
     * @apiParam (请求体) {String} imageFormat
     * @apiParamExample 请求体示例
     * {"imageFormat":"mVALvda","signatureImageBase64":"I6nAMHhgL"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sameIdName 是否和证件信息一致
     * @apiSuccess (响应结果) {String} data.signature 签名信息
     * @apiSuccess (响应结果) {Array} data.eSignatureImages 电子签名图片URL
     * @apiSuccess (响应结果) {String} data.eSignatureImages.url 图片地址
     * @apiSuccess (响应结果) {String} data.eSignatureImages.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.eSignatureImages.exampleFileFormatType 文件类型 0：图片 1：PDF
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"iEAu","data":{"sameIdName":"xRIneRbd","signature":"MxfNFoZB","eSignatureImages":[{"exampleFileFormatType":"NbyTrE5","url":"Ndv","thumbnailUrl":"ouFO"}]},"description":"m1","timestampServer":"VKY40RO9k"}
     */
    @PostMapping("verificationelectronicsignature")
    public CgiResponse<ESignatureImageVO> verificationElectronicSignature(@RequestBody HkOpenAccESignatureImageRequest request ) {
        return CgiResponse.ok(openAcctService.verificationElectronicSignature(request));
    }

    /**
     * @api {GET} /ext/hkaccount/openacct/querySubmitResult querySubmitResult()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName querySubmitResult()
     * @apiDescription 开户结果信息查询
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.status
     * @apiSuccess (响应结果) {Array} data.checkResult 证件信息审核结果信息
     * @apiSuccess (响应结果) {String} data.checkResult.fileType 字段类型描述
     * @apiSuccess (响应结果) {String} data.checkResult.fileName 字段名称
     * @apiSuccess (响应结果) {String} data.checkResult.reason 错误原因
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"udEXvpe","data":{"checkResult":[{"reason":"G2e6","fileName":"h","fileType":"Qua4zy0"}],"status":"xcuZCavjsO"},"description":"KD1l7kYu","timestampServer":"bGb5"}
     */
    @GetMapping("querySubmitResult")
    public CgiResponse<HkOpenAcctSubmitResultVO> querySubmitResult() {
        return CgiResponse.ok(openAcctService.querySubmitResult());
    }


    /**
     * @api {POST} /ext/hkaccount/openacct/downloaduploadimage downLoadUploadImage()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName downLoadUploadImage()
     * @apiDescription 公共接口-根据相对路劲获取文件流
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} relativeUrl 相对地址链接
     * @apiParamExample 请求体示例
     * {"relativeUrl":"RmKzPRdDG"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.imageBase 图片Base64
     * @apiSuccess (响应结果) {String} data.imageName 图片名称
     * @apiSuccess (响应结果) {String} data.imageFormat 图片格式
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"g","data":{"imageFormat":"tqtww","imageBase":"Kj2zzdw","imageName":"DkelaB"},"description":"yNKA","timestampServer":"o"}
     */
    @PostMapping("downloaduploadimage")
    public CgiResponse<HkOpenDownImageVO> downLoadUploadImage(@RequestBody KhOpenAcctImageRequest hkOpenAcctImageRequest) {
        return CgiResponse.ok(openAcctService.downLoadUploadImage(hkOpenAcctImageRequest));
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/pdf/previewpdf previewPdfByBizCode()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctPdfController
     * @apiName previewPdfByBizCode()
     * @apiDescription 获取PDF文件信息
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustomerNo 香港客户编号
     * @apiParam (请求体) {String} channelCode 渠道编码 0:CGI缓存  1: 账户中心
     * @apiParam (请求体) {String} bizCode 业务编码
     * @apiParamExample 请求体示例
     * {"hkCustomerNo":"e","bizCode":"w","channelCode":"xe"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.pdfCode PDf base64编码 暂定格式
     * @apiSuccess (响应结果) {String} data.pdfName pdf名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"dvqDqTh","data":{"pdfCode":"3VbOG8","pdfName":"LiFD"},"description":"ZjJKreuNXx","timestampServer":"n9RcDz0w"}
     */
    @PostMapping("/pdf/previewpdf")
    public CgiResponse<HkOpenAcctPdfVO> previewPdfByBizCode(@RequestBody HkOpenAcctPdfRequest request) {
        return CgiResponse.ok(openAcctService.previewPdfByBizCode(request));
    }


    /**
     * @api {GET} /ext/hkaccount/openacct/pdf/previewpdfbystream previewPdfByBizCode()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName previewPdfByBizCode()
     * @apiDescription 获取PDF文件流接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} hkCustNo
     * @apiParam (请求参数) {String} bizCode
     * @apiParam (请求参数) {String} channelCode
     * @apiParamExample 请求参数示例
     * bizCode=BCMz9Otr5&hkCustNo=wmG8UV&channelCode=G
     * @apiSuccess (响应结果) {Object} response
     */
    @GetMapping("/pdf/previewpdfbystream")
    public void previewPdfByBizCode(@RequestParam(value = "hkCustNo") String hkCustNo,
                                                            @RequestParam(value = "bizCode") String bizCode,
                                                            @RequestParam(value = "channelCode") String channelCode, HttpServletResponse response) {
        openAcctService.previewPdfStreamByBizCode(hkCustNo,bizCode, channelCode,response);
    }


    /**
     * @api {POST} /ext/hkaccount/openacct/pdf/previewbystream previewNewPdfByBizCode()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName previewNewPdfByBizCode()
     * @apiDescription 获取PDF文件流接口
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} bizCode 业务码
     * @apiParam (请求体) {String} channelCode 渠道号
     * @apiParamExample 请求体示例
     * {"bizCode":"RZ","hkCustNo":"hOYVkme","channelCode":"6q8xaO"}
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @PostMapping("/pdf/previewbystream")
    public void previewNewPdfByBizCode(@RequestBody HkOpenAcctPdfPreviewRequest request, HttpServletResponse response) {
        String hkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        openAcctService.previewPdfStreamByBizCode(hkCustNo, request.getBizCode(), request.getChannelCode(), response);
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/queryexpiratpage queryExpiratPage()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctController
     * @apiName queryExpiratPage()
     * @apiDescription 查询用户开户缓存失效页面
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"QqV"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.expirePage 过期页面 0表示正常,没有过期
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"kT","data":{"expirePage":1808},"description":"qf","timestampServer":"MaU"}
     */
    @PostMapping("/queryexpiratpage")
    public CgiResponse<HkOpenAcctExpirationPageVO> queryExpiratPage(@RequestBody HkOpenAcctExpirationPageRequest request) {
        return CgiResponse.ok(openAcctService.queryExpirePage(request));
    }
}
