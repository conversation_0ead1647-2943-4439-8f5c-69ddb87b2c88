package com.howbuy.crm.cgi.extservice.service.business.balance;

import com.howbuy.crm.cgi.extservice.common.enums.balance.HkCustBalanceDisPalyCategoryEnum;
import com.howbuy.crm.cgi.extservice.factory.balance.BalancePortfolioStrategy;
import com.howbuy.crm.cgi.extservice.factory.balance.PortfolioStrategyFactory;
import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PortfolioCalculator {

    private final PortfolioStrategyFactory strategyFactory;

    @Autowired
    public PortfolioCalculator(PortfolioStrategyFactory strategyFactory) {
        this.strategyFactory = strategyFactory;
    }

    /**
     * @description: 计算不同类型的持仓组合
     * @param content 上下文
     * @return com.howbuy.crm.cgi.extservice.factory.domain.BalancePortfolioSummary
     * @author: jinqing.rao
     * @date: 2025/6/23 16:57
     * @since JDK 1.8
     */
//    public BalancePortfolioSummary calculate(BalanceContent content) {
//
//        return BalancePortfolioSummary.builder()
//                .nonFullPortfolio(calculate(content, HkCustBalanceDisPalyCategoryEnum.NON_FULL, BalancePortfolioDetail.class))
//                .fullPortfolio(calculate(content, HkCustBalanceDisPalyCategoryEnum.FULL, BalancePortfolioDetail.class))
//                .piggyPortfolio(calculate(content, HkCustBalanceDisPalyCategoryEnum.NON_FULL_PIGGY, BalancePortfolioDetail.class))
//                .inTransitTradeBalancePortfolioDetail(calculate(content, HkCustBalanceDisPalyCategoryEnum.IN_TRANSIT, InTransitTradeBalancePortfolioDetail.class))
//                .totalPortfolio(calculate(content, HkCustBalanceDisPalyCategoryEnum.ALL_BALANCE, TotalBalancePortfolioDetail.class))
//                .build();
//    }
    
    /**
     * @description: 根据不同的策略类型获取策略
     * @param content 上下文
     * @param type
     * @return com.howbuy.crm.cgi.extservice.factory.domain.BalancePortfolioDetail
     * @author: jinqing.rao
     * @date: 2025/6/23 16:58
     * @since JDK 1.8
     */
    public <T extends PortfolioDetail> T calculate(BalanceContent content, HkCustBalanceDisPalyCategoryEnum type, Class<T> returnType) {
        BalancePortfolioStrategy<T> strategy = strategyFactory.getStrategy(type, returnType);
        return strategy != null ? strategy.calculate(content) : null;
    }
}
