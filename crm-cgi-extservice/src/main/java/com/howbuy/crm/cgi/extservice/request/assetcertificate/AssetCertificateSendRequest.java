package com.howbuy.crm.cgi.extservice.request.assetcertificate;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: 资产证明发送请求类
 * @author: 陈杰文
 * @date: 2025-06-17 17:32:12
 * @since JDK 1.8
 */
@Setter
@Getter
public class AssetCertificateSendRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空")
    private String idNo;

    /**
     * 交易密码
     */
    @NotBlank(message = "交易密码不能为空")
    private String tradePassword;
} 