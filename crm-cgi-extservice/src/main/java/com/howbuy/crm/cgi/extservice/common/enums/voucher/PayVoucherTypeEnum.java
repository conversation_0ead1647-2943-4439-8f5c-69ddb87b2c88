package com.howbuy.crm.cgi.extservice.common.enums.voucher;

public enum PayVoucherTypeEnum {
    // 上传打款凭证类型 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
    OPEN_ACCOUNT_CONFIRM("0", "开户入金确认凭证","开户入金凭证"),
    TRADE_ORDER("1", "交易下单凭证","交易打款凭证"),
    DEPOSIT_CASH_ACCOUNT("2", "存入现金账户凭证","现金存入凭证");

    private final String code;

    private final String disCode;
    private final String desc;

    PayVoucherTypeEnum(String code, String desc,String disCode) {
        this.code = code;
        this.desc = desc;
        this.disCode = disCode;
    }
    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

    public String getDisCode() {
		return disCode;
	}

    public static String getByCode(String code) {
        for (PayVoucherTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.getDesc();
            }
        }
        return null;
    }

    /**
     * @description: 展示名称
     * @param code
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/6/17 15:32
     * @since JDK 1.8
     */
    public static String getDisCodeByCode(String code) {
        for (PayVoucherTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.getDisCode();
            }
        }
        return null;
    }
}
