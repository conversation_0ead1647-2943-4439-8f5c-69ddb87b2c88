package com.howbuy.crm.cgi.extservice.vo.hkaccount;

/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 邮箱修改检查响应类
 * @author: 陈杰文
 * @date: 2025-06-17 11:11:28
 * @since JDK 1.8
 */
@Setter
@Getter
public class EmailUpdateCheckVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 邮箱业务场景
     * 1：用户没有维护过结单邮箱 
     * 2：用户有维护【一个】结单邮箱 
     * 3：用户有维护【多个】结单邮箱，且有和用户开户绑定的旧邮箱一致的邮箱
     * 4：用户有维护【多个】结单邮箱，但没有和用户开户绑定的旧邮箱一致的邮箱
     */
    private String emailBizScenario;
} 