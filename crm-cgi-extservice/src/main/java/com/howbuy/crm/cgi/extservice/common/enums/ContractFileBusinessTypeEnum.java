package com.howbuy.crm.cgi.extservice.common.enums;

/**
 * @description: 补充协议业务类型枚举
 * <AUTHOR>
 * @date 2024/03/06 18:20
 * @since JDK 1.8
 */
public enum ContractFileBusinessTypeEnum {

    /**
     * 协议签署
     */
    SIGN("0000", "协议签署", "协议签署"),
    /**
     * 认购
     */
    SUBSCRIPTION("1120", "认购","购买"),

    /**
     * 申购
     */
    PURCHASE("1122", "申购","购买"),

    /**
     * 赎回
     */
    REDEEM("1124", "赎回","赎回"),
    /**
     * 认缴
     */
    SUB("112A", "认缴","认缴"),

    /**
     * 实缴
     */
    SUB_PAID("112B", "实缴","实缴"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;


    private final String displayDesc;

    ContractFileBusinessTypeEnum(String code, String desc,String displayDesc) {
        this.code = code;
        this.desc = desc;
        this.displayDesc = displayDesc;
    }

    /**
     * @description: 根据编码获取描述
     * @param code 编码
     * @return String 描述
     */
    public static String getDisplayDescByCode(String code) {
        for (ContractFileBusinessTypeEnum busiTypeEnum : values()) {
            if (busiTypeEnum.getCode().equals(code)) {
                return busiTypeEnum.getDisplayDesc();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getDisplayDesc() {
        return displayDesc;
    }
} 