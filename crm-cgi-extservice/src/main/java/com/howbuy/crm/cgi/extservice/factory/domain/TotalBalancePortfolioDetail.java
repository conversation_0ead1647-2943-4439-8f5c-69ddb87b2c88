package com.howbuy.crm.cgi.extservice.factory.domain;

import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.BalanceBeanDTO;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;


@Getter
@Builder
public class TotalBalancePortfolioDetail implements PortfolioDetail{
    /**
     * 总资产
     */
    private BigDecimal totalAsset;      // 总资产

    /**
     * 买入待确认金额(之前的在途金额)
     */
    private BigDecimal inTransitTradeAmt;

    /**
     * 基金持仓资产
     */
    private BigDecimal fundAsset;

    /**
     * 基金持仓收益
     */
    private BigDecimal fundIncome;

    /**
     * 基金持仓收益计算状态 0-计算中;1-计算成功
     */
    private String fundIncomeCalStatus;

    /**
     * 储蓄罐资产
     */
    private BigDecimal piggyAsset;

    /**
     * 储蓄罐收益
     */
    private BigDecimal piggyIncome;

    /**
     * 储蓄罐收益计算状态 0-计算中;1-计算成功
     */
    private String piggyIncomeCalStatus;

    /**
     * 全委基金持仓资产
     */
    private BigDecimal fullFundAsset;

    /**
     * 全委基金持仓收益
     */
    private BigDecimal fullFundIncome;

    /**
     * 全委基金持仓收益计算状态 0-计算中;1-计算成功
     */
    private String fullFundIncomeCalStatus;

    /**
     * 存在NA产品 0-不存在、1-存在
     */
    private String existsNaFund;
    /**
     * 明细列表
     */
    private List<BalanceBeanDTO> itemList;  // 明细列表
}