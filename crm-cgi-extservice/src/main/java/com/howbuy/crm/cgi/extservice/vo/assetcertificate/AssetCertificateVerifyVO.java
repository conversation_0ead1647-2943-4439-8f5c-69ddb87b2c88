package com.howbuy.crm.cgi.extservice.vo.assetcertificate;

/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT Co., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 资产证明身份验证响应类
 * @author: 陈杰文
 * @date: 2025-06-17 17:36:24
 * @since JDK 1.8
 */
@Setter
@Getter
public class AssetCertificateVerifyVO extends Body implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 邮箱掩码
     */
    private String emailMask;
} 