package com.howbuy.crm.cgi.extservice.factory.balance;

import com.howbuy.crm.cgi.extservice.common.annotation.FundTypeSupport;
import com.howbuy.crm.cgi.extservice.common.enums.balance.HkCustBalanceDisPalyCategoryEnum;
import com.howbuy.crm.cgi.extservice.factory.balance.BalancePortfolioStrategy;
import com.howbuy.crm.cgi.extservice.factory.domain.PortfolioDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PortfolioStrategyFactory {

    private final Map<HkCustBalanceDisPalyCategoryEnum, BalancePortfolioStrategy<?>> strategies;

    @Autowired
    public PortfolioStrategyFactory(List<BalancePortfolioStrategy<?>> strategyList) {
        this.strategies = strategyList.stream()
                .filter(strategy -> strategy.getClass().isAnnotationPresent(FundTypeSupport.class))
                .collect(Collectors.toMap(
                        s -> s.getClass().getAnnotation(FundTypeSupport.class).value(),
                        s -> s
                ));
    }

    public  <T extends PortfolioDetail> BalancePortfolioStrategy<T> getStrategy(HkCustBalanceDisPalyCategoryEnum type, Class<T> returnType) {
        BalancePortfolioStrategy<?> strategy = strategies.get(type);
        if (strategy == null || !returnType.isAssignableFrom(strategy.getSupportedType())) {
            return null;
        }
        return (BalancePortfolioStrategy<T>) strategy;
    }
}