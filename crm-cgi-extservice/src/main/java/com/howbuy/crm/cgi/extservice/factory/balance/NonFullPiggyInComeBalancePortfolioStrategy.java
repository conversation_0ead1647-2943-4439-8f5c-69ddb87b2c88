package com.howbuy.crm.cgi.extservice.factory.balance;

import com.howbuy.crm.cgi.extservice.common.annotation.FundTypeSupport;
import com.howbuy.crm.cgi.extservice.common.enums.balance.HkCustBalanceDisPalyCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.utils.balance.BalanceUtils;
import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.AllInTransitTradeBalancePortfolioDetail;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.capital.QueryInTransitTradeCapitalRequestDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryBalanceDTO;
import com.howbuy.dtms.common.enums.BusinessCodeEnum;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 在途买入类
 * @author: jinqing.rao
 * @date: 2025/6/24 9:42
 * @since JDK 1.8
 */
@Component
@Slf4j
@FundTypeSupport(HkCustBalanceDisPalyCategoryEnum.NON_FULL_PIGGY_IN_TRANSIT)
public class NonFullPiggyInComeBalancePortfolioStrategy extends AbstractBalancePortfolioStrategy implements BalancePortfolioStrategy<AllInTransitTradeBalancePortfolioDetail> {

    private static final List<String> midBusinessCodeList = Arrays.asList(
            BusinessCodeEnum.REDEEM.getMCode(),
            BusinessCodeEnum._119B.getMCode(),
            BusinessCodeEnum.DIV.getMCode(),
            BusinessCodeEnum.FORCE_REDEEM.getMCode(),
            BusinessCodeEnum.FUND_TERMINATION.getMCode(),
            BusinessCodeEnum.FUND_LIQUIDATION.getMCode());

    @Override
    public Class<AllInTransitTradeBalancePortfolioDetail> getSupportedType() {
        return AllInTransitTradeBalancePortfolioDetail.class;
    }

    @Override
    public AllInTransitTradeBalancePortfolioDetail calculate(BalanceContent content) {

        if(CollectionUtils.isEmpty(content.getFundTxAcctNoDTOList())){
            log.info("没有非全委的基金交易账号,非全委的储蓄罐在途策略不执行，返回 空数据 , hkCustNo :{} ",content.getHkCustNo());
            return AllInTransitTradeBalancePortfolioDetail.builder().build();
        }
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = content.getFundTxAcctNoDTOList().stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.NON_FULL.getCode().equals(m.getFundTxAccType()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(hkFundTxAcctDTOS)){
            log.error("用户有基金交易账号,没有非全委的基金交易账号,非全委的储蓄罐在途策略不执行，返回 空数据 , hkCustNo :{} ",content.getHkCustNo());
        }
        // 构造器
        AllInTransitTradeBalancePortfolioDetail.AllInTransitTradeBalancePortfolioDetailBuilder builder = AllInTransitTradeBalancePortfolioDetail.builder();

        List<String> nonFullFundTxAcctNoList = BalanceUtils.extractFieldList(hkFundTxAcctDTOS, HkFundTxAcctDTO::getFundTxAcctNo);

        // 查询在途数据
        buildAllInTransitCountAndDealNo(buildNonFullPiggyQueryBalanceDTO(content, nonFullFundTxAcctNoList), builder);

        // 查询交易资金信息
        QueryInTransitTradeCapitalRequestDTO requestDTO = QueryInTransitTradeCapitalRequestDTO.builder()
                .hkCustNo(content.getHkCustNo())
                .midBusinessCodeList(midBusinessCodeList)
                .build();
        buildInTransitTradeCapitalCountAndDealNo(requestDTO, builder);
        return builder.build();
    }

    /**
     * @description: 构建请求参数
     * @param content
     * @param nonFullFundTxAcctNoList
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryBalanceDTO
     * @author: jinqing.rao
     * @date: 2025/7/9 17:06
     * @since JDK 1.8
     */
    private QueryBalanceDTO buildNonFullPiggyQueryBalanceDTO(BalanceContent content, List<String> nonFullFundTxAcctNoList) {
        // 获取储蓄罐基金
        List<FundBasicInfoDTO> fundBasicInfoDTOList = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList();
        List<String> piggyFundCodeList = BalanceUtils.extractFieldList(fundBasicInfoDTOList, FundBasicInfoDTO::getFundCode);
        // 查询在途信息接口
        QueryBalanceDTO queryBalanceDTO = new QueryBalanceDTO();
        queryBalanceDTO.setHkCustNo(content.getHkCustNo());
        queryBalanceDTO.setFundTxAcctNoList(nonFullFundTxAcctNoList);
        queryBalanceDTO.setFundCodeList(piggyFundCodeList);
        queryBalanceDTO.setDisPlayCurrency(content.getDisPlayCurrencyCode());
        return queryBalanceDTO;
    }
}