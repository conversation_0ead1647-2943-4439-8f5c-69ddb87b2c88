/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.portrait.qa;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 问答回复项响应对象
 *
 * <AUTHOR>
 * @date 2025-03-21 10:26:47
 */
@Getter
@Setter
public class QaReplyAnswerVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标签名称（回复类型为标签时返回）
     */
    private String tagName;

    /**
     * 标签code（回复类型为标签时返回标签code）
     */
    private String tagCode;

    /**
     * 素材id
     */
    private String materialId;

    /**
     * 素材标题（回复类型为素材、产品报告时返回）
     */
    private String materialTitle;

    /**
     * 素材链接（回复类型为素材、产品报告时返回）
     */
    private String materialUrl;

    /**
     * 素材内容标题（回复类型为素材时返回）
     */
    private String materialContentTitle;

    /**
     * 发送次数（回复类型为素材、产品报告时返回）
     */
    private String sendCount;

    /**
     * 学习进度（回复类型为素材时返回）
     */
    private String learningProgress;

    /**
     * 素材内容类型(1:文章、2:视频、3:直播、4:研习社课程、5-报告)
     */
    private String materialContentType;

    /**
     * replyType=3(报告时返回)
     * 报告来源(1:CMS报告；2:参数中心报告)
     */
    private String reportSource;

    /**
     * 产品代码（回复类型为产品时返回）
     */
    private String fundCode;

    /**
     * 产品简称（回复类型为产品时返回）
     */
    private String fundName;
} 