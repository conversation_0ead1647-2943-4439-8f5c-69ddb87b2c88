/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.fund;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.CurrencyEnum;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.TxChannelEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.common.utils.PassWordUtil;
import com.howbuy.crm.cgi.extservice.common.enums.FileBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HKFundPayVoucherStateEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HKFundPrebookEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundMBusiCodeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundPayMethodEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherTypeEnum;
import com.howbuy.crm.cgi.extservice.common.utils.NumberUtils;
import com.howbuy.crm.cgi.extservice.common.utils.ReflectUtils;
import com.howbuy.crm.cgi.extservice.common.utils.file.FileUploadUtils;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKFundAgreementRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKPurchaseFundPayVoucherUploadRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.HkPurchaseFundSubmitRequest;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.*;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherDetailVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.*;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.FundTradeCalendarInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkBankCardInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.PiggyAgreementSignDetailDTO;
import com.howbuy.dtms.common.enums.HwPayVoucherFileSourceEnum;
import crm.howbuy.base.utils.DesUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 申购基金VO转换工具类
 * @date 2024/4/11 10:32
 * @since JDK 1.8
 */
public class HkPurchaseFundConvert {
    /**
     * @param fundBasicInfoDTO
     * @param buyInfoDTO
     * @param hkBankAcctList
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HkPurchasePageInfoVO
     * @description: 海外基金购买页面, 获取基金信息
     * @author: jinqing.rao
     * @date: 2024/4/11 17:54
     * @since JDK 1.8
     */
    public static HkPurchasePageInfoVO toHkPurchasePageInfoVO(FundBasicInfoDTO fundBasicInfoDTO, BuyInfoDTO buyInfoDTO,
                                                              List<HkBankCardInfoDTO> hkBankAcctList, HkCustInfoDTO hkCustInfo,
                                                              PiggyAgreementSignDetailDTO hkCustPiggyAgreement) {
        HkPurchasePageInfoVO hkPurchasePageInfoVO = new HkPurchasePageInfoVO();
        //设置手机区号 手机掩码 邮箱掩码
        hkPurchasePageInfoVO.setMobileAreaCode(hkCustInfo.getMobileAreaCode());
        hkPurchasePageInfoVO.setMobileMask(hkCustInfo.getMobileMask());
        hkPurchasePageInfoVO.setEmailMask(hkCustInfo.getEmailMask());
        hkPurchasePageInfoVO.setMobileDigest(hkCustInfo.getMobileDigest());
        hkPurchasePageInfoVO.setEmailDigest(hkCustInfo.getEmailDigest());
        HkPurchasePageInfoVO.FundInfoVO fundInfoVO = new HkPurchasePageInfoVO.FundInfoVO();
        fundInfoVO.setFundShortName(fundBasicInfoDTO.getFundAbbr());
        fundInfoVO.setFundEnName(fundBasicInfoDTO.getFundNameEn());
        fundInfoVO.setSupportPrebook(HKFundPrebookEnum.isSupportBuyPrebook(fundBasicInfoDTO.getIsScheduledTrade()) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        fundInfoVO.setFeeCalMode(fundBasicInfoDTO.getFeeCalMode());
        fundInfoVO.setFundCategory(fundBasicInfoDTO.getFundCategory());
        fundInfoVO.setCurrencyCode(fundBasicInfoDTO.getCurrency());
        fundInfoVO.setCurrencyDesc(CurrencyEnum.getDescription(fundBasicInfoDTO.getCurrency()));
        BuyFundInfoDTO buyFundInfoVO = buyInfoDTO.getBuyFundInfoVO();
        if (null != buyFundInfoVO) {
            fundInfoVO.setOpenStartDate(formatDate(buyFundInfoVO.getOpenStartDt()));
            fundInfoVO.setOpenEndDate(formatDate(buyFundInfoVO.getOpenEndDt()));
            fundInfoVO.setPayEndDate(formatDate(buyFundInfoVO.getPayEndDt()));
            fundInfoVO.setPayEndTime(formatTime(buyFundInfoVO.getPayEndTm()));
            fundInfoVO.setTradeDate(formatDate(buyFundInfoVO.getTradeDt()));
            // 返回当前日期给前端,用于日期的比对，展示用。不涉及到的提交
            fundInfoVO.setCurrentDate(DateUtils.getCurrentDate(DateUtils.YYYY_MM_DD));
            fundInfoVO.setMinAppAmt(bigDecimalToString(buyFundInfoVO.getMinAppAmt()));
            fundInfoVO.setMaxAppAmt(bigDecimalToString(buyFundInfoVO.getMaxAppAmt()));
            fundInfoVO.setDifferential(bigDecimalToString(buyFundInfoVO.getDifferential()));
            fundInfoVO.setGradationCall(fundBasicInfoDTO.getGradationCall());
            // 意向金比例
            fundInfoVO.setFirstIntentionRatio(NumberUtils.bigDecimalToString(buyFundInfoVO.getFirstPayInRatio()));
            hkPurchasePageInfoVO.setBuyFundInfoVO(fundInfoVO);
        }
        //预约信息
        PrebookBuyInfoDTO buyPrebookBuyInfo = buyInfoDTO.getPrebookBuyInfoVO();
        if (null != buyPrebookBuyInfo) {
            HkPurchasePageInfoVO.PrebookBuyInfoVO prebookBuyInfoVO = new HkPurchasePageInfoVO.PrebookBuyInfoVO();
            prebookBuyInfoVO.setPrebookPayMethod(buyPrebookBuyInfo.getPrebookPayMethod());
            prebookBuyInfoVO.setPrebookAppAmt(bigDecimalToString(buyPrebookBuyInfo.getPrebookAppAmt()));
            prebookBuyInfoVO.setPrebookDiscountRate(bigDecimalToString(buyPrebookBuyInfo.getPrebookDiscountRate()));
            prebookBuyInfoVO.setPrebookDealNo(buyPrebookBuyInfo.getPrebookDealNo());
            prebookBuyInfoVO.setPrebookSubAmt(bigDecimalToString(buyPrebookBuyInfo.getPrebookSubAmt()));
            prebookBuyInfoVO.setPrebookPaidAmt(bigDecimalToString(buyPrebookBuyInfo.getPrebookPaidAmt()));
            hkPurchasePageInfoVO.setPrebookBuyInfoVO(prebookBuyInfoVO);
        }
        //支付方式
        builderPayMethodInfoVO(buyInfoDTO, hkPurchasePageInfoVO, hkCustPiggyAgreement);
        //初始化银行卡信息
        builderBuyFundBankCardVO(hkBankAcctList, hkPurchasePageInfoVO);
        return hkPurchasePageInfoVO;
    }


    /**
     * @param fundBasicInfoDTO     基金信息
     * @param changePayMethodPageDTO           购买信息
     * @param hkBankAcctList       银行卡信息
     * @param hkCustPiggyAgreement 储蓄罐签约信息
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.SubPaidPendingDetailVO
     * @description: 认缴+首次实缴消息代办详情页
     * @author: jinqing.rao
     * @date: 2025/4/16 14:54
     * @since JDK 1.8
     */
    public static SubPaidPendingDetailVO toHkPurchaseSubAndFirstPaidPendingMessageVO(FundBasicInfoDTO fundBasicInfoDTO, ChangePayMethodPageDTO changePayMethodPageDTO,
                                                                                     List<HkBankCardInfoDTO> hkBankAcctList,
                                                                                     PiggyAgreementSignDetailDTO hkCustPiggyAgreement) {
        SubPaidPendingDetailVO subPaidPendingDetailVO = new SubPaidPendingDetailVO();

        // 实缴订单信息
        SubPaidPendingDetailVO.PaidOrderVO paidOrderVO = new SubPaidPendingDetailVO.PaidOrderVO();
        paidOrderVO.setPaymentType(changePayMethodPageDTO.getChangePayMethodOrderDTO().getPaymentType());
        paidOrderVO.setPayEndDate(changePayMethodPageDTO.getChangePayMethodOrderDTO().getPayEndDate());
        paidOrderVO.setPayEndTime(formatTime(changePayMethodPageDTO.getChangePayMethodOrderDTO().getPayEndTime()));
        paidOrderVO.setFundName(fundBasicInfoDTO.getFundAbbr());
        paidOrderVO.setCpAcctNo(changePayMethodPageDTO.getChangePayMethodOrderDTO().getCpAcctNo());
        paidOrderVO.setPaidAmt(NumberUtils.formatDecimalPlaces(changePayMethodPageDTO.getChangePayMethodOrderDTO().getAppAmt(),2,RoundingMode.DOWN));
        subPaidPendingDetailVO.setPaidOrderVO(paidOrderVO);
        // 基金信息
        SubPaidPendingDetailVO.FundInfoVO fundInfoVO = new SubPaidPendingDetailVO.FundInfoVO();
        fundInfoVO.setFundShortName(fundBasicInfoDTO.getFundAbbr());
        fundInfoVO.setFundEnName(fundBasicInfoDTO.getFundNameEn());
        fundInfoVO.setSupportPrebook(HKFundPrebookEnum.isSupportBuyPrebook(fundBasicInfoDTO.getIsScheduledTrade()) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        fundInfoVO.setCurrencyCode(fundBasicInfoDTO.getCurrency());
        fundInfoVO.setCurrencyDesc(CurrencyEnum.getDescription(fundBasicInfoDTO.getCurrency()));
        subPaidPendingDetailVO.setBuyFundInfoVO(fundInfoVO);

        //支付方式
        builderPayMethodInfoVO(changePayMethodPageDTO, subPaidPendingDetailVO, hkCustPiggyAgreement);
        //初始化银行卡信息
        builderBuyFundBankCardVO(hkBankAcctList, subPaidPendingDetailVO);
        return subPaidPendingDetailVO;
    }

    /**
     * @param hkBankAcctList
     * @param subPaidPendingDetailVO
     * @return void
     * @description: 构建银行卡信息
     * @author: jinqing.rao
     * @date: 2025/4/16 14:58
     * @since JDK 1.8
     */
    private static void builderBuyFundBankCardVO(List<HkBankCardInfoDTO> hkBankAcctList, SubPaidPendingDetailVO subPaidPendingDetailVO) {
        if (CollectionUtils.isNotEmpty(hkBankAcctList)) {
            List<SubPaidPendingDetailVO.BuyFundBankCardVO> fundBankCardVOS = hkBankAcctList.stream().map(m -> {
                SubPaidPendingDetailVO.BuyFundBankCardVO buyFundBankCardVO = new SubPaidPendingDetailVO.BuyFundBankCardVO();
                buyFundBankCardVO.setCpAcctNo(m.getHkCpAcctNo());
                buyFundBankCardVO.setBankAcctMask(m.getBankAcctMask());
                buyFundBankCardVO.setBankName(StringUtils.isBlank(m.getBankChineseName()) ? m.getBankName() : m.getBankChineseName());
                buyFundBankCardVO.setBankLogoUrl(m.getBankLogoUrl());
                return buyFundBankCardVO;
            }).collect(Collectors.toList());
            subPaidPendingDetailVO.setBankCardList(fundBankCardVOS);
        }
    }

    /**
     * @param hkBankAcctList
     * @param hkPurchasePageInfoVO
     * @return void
     * @description: 初始化银行卡信息
     * @author: jinqing.rao
     * @date: 2025/4/15 13:31
     * @since JDK 1.8
     */
    private static void builderBuyFundBankCardVO(List<HkBankCardInfoDTO> hkBankAcctList, HkPurchasePageInfoVO hkPurchasePageInfoVO) {
        if (CollectionUtils.isNotEmpty(hkBankAcctList)) {
            List<HkPurchasePageInfoVO.BuyFundBankCardVO> fundBankCardVOS = hkBankAcctList.stream().map(m -> {
                HkPurchasePageInfoVO.BuyFundBankCardVO buyFundBankCardVO = new HkPurchasePageInfoVO.BuyFundBankCardVO();
                buyFundBankCardVO.setCpAcctNo(m.getHkCpAcctNo());
                buyFundBankCardVO.setBankAcctMask(m.getBankAcctMask());
                buyFundBankCardVO.setBankName(StringUtils.isBlank(m.getBankChineseName()) ? m.getBankName() : m.getBankChineseName());
                buyFundBankCardVO.setBankLogoUrl(m.getBankLogoUrl());
                return buyFundBankCardVO;
            }).collect(Collectors.toList());
            hkPurchasePageInfoVO.setBankCardList(fundBankCardVOS);
        }
    }

    /**
     * @param changePayMethodPageDTO
     * @param subPaidPendingDetailVO
     * @param hkCustPiggyAgreement
     * @return void
     * @description:(请在此添加描述)
     * @author: jinqing.rao
     * @date: 2025/4/16 14:56
     * @since JDK 1.8
     */
    private static void builderPayMethodInfoVO(ChangePayMethodPageDTO changePayMethodPageDTO, SubPaidPendingDetailVO subPaidPendingDetailVO,
                                               PiggyAgreementSignDetailDTO hkCustPiggyAgreement) {
        PayMethodInfoDTO buyPayMethodInfoVO = changePayMethodPageDTO.getPayMethodInfoVO();
        if (null != buyPayMethodInfoVO) {
            SubPaidPendingDetailVO.PayMethodInfoVO payMethodInfoVO = new SubPaidPendingDetailVO.PayMethodInfoVO();
            payMethodInfoVO.setSupportCxgPay(buyPayMethodInfoVO.getIsSupportCxgPay());
            payMethodInfoVO.setHasSignCxg(buyPayMethodInfoVO.getHasSignCxg());
            payMethodInfoVO.setCxgOrderEndTime(buyPayMethodInfoVO.getCxgOrderEndTm());
            String agreementValidStatus = agreementValidStatus(hkCustPiggyAgreement);
            payMethodInfoVO.setAgreementValidStatus(agreementValidStatus);
            if (StringUtils.isNotBlank(buyPayMethodInfoVO.getCxgOrderEndTm())) {
                payMethodInfoVO.setBeforeCxgOrderEndTime(DateUtils.notAfterTargetTimeHHMMSS(buyPayMethodInfoVO.getCxgOrderEndTm()));
            }
            List<String> cxgCurrencyList = buyPayMethodInfoVO.getCxgCurrencyList();
            if (CollectionUtils.isNotEmpty(cxgCurrencyList)) {
                List<SubPaidPendingDetailVO.PayMethodInfoVO.FundCurrencyVO> fundCurrencyVOS = cxgCurrencyList.stream().map(m -> {
                    SubPaidPendingDetailVO.PayMethodInfoVO.FundCurrencyVO fundCurrencyVO = new SubPaidPendingDetailVO.PayMethodInfoVO.FundCurrencyVO();
                    fundCurrencyVO.setCxgCurrencyCode(m);
                    fundCurrencyVO.setCxgCurrencyDesc(CurrencyEnum.getEnDescription(m));
                    return fundCurrencyVO;
                }).collect(Collectors.toList());
                payMethodInfoVO.setCxgCurrencyVOList(fundCurrencyVOS);
            }
            subPaidPendingDetailVO.setPayMethodInfoVO(payMethodInfoVO);
        }
    }

    /**
     * @param buyInfoDTO
     * @param hkPurchasePageInfoVO
     * @param hkCustPiggyAgreement
     * @return void
     * @description: 构建支付方式信息
     * @author: jinqing.rao
     * @date: 2025/4/15 13:31
     * @since JDK 1.8
     */
    private static void builderPayMethodInfoVO(BuyInfoDTO buyInfoDTO, HkPurchasePageInfoVO hkPurchasePageInfoVO,
                                               PiggyAgreementSignDetailDTO hkCustPiggyAgreement) {
        PayMethodInfoDTO buyPayMethodInfoVO = buyInfoDTO.getPayMethodInfoVO();
        if (null != buyPayMethodInfoVO) {
            HkPurchasePageInfoVO.PayMethodInfoVO payMethodInfoVO = new HkPurchasePageInfoVO.PayMethodInfoVO();
            payMethodInfoVO.setSupportCxgPay(buyPayMethodInfoVO.getIsSupportCxgPay());
            payMethodInfoVO.setHasSignCxg(buyPayMethodInfoVO.getHasSignCxg());
            payMethodInfoVO.setCxgOrderEndTime(buyPayMethodInfoVO.getCxgOrderEndTm());
            String agreementValidStatus = agreementValidStatus(hkCustPiggyAgreement);
            payMethodInfoVO.setAgreementValidStatus(agreementValidStatus);
            if (StringUtils.isNotBlank(buyPayMethodInfoVO.getCxgOrderEndTm())) {
                payMethodInfoVO.setBeforeCxgOrderEndTime(DateUtils.notAfterTargetTimeHHMMSS(buyPayMethodInfoVO.getCxgOrderEndTm()));
            }
            List<String> cxgCurrencyList = buyPayMethodInfoVO.getCxgCurrencyList();
            if (CollectionUtils.isNotEmpty(cxgCurrencyList)) {
                List<HkPurchasePageInfoVO.PayMethodInfoVO.FundCurrencyVO> fundCurrencyVOS = cxgCurrencyList.stream().map(m -> {
                    HkPurchasePageInfoVO.PayMethodInfoVO.FundCurrencyVO fundCurrencyVO = new HkPurchasePageInfoVO.PayMethodInfoVO.FundCurrencyVO();
                    fundCurrencyVO.setCxgCurrencyCode(m);
                    fundCurrencyVO.setCxgCurrencyDesc(CurrencyEnum.getEnDescription(m));
                    return fundCurrencyVO;
                }).collect(Collectors.toList());
                payMethodInfoVO.setCxgCurrencyVOList(fundCurrencyVOS);
            }
            hkPurchasePageInfoVO.setPayMethodInfoVO(payMethodInfoVO);
        }
    }

    /**
     * @param hkCustPiggyAgreement
     * @return java.lang.String
     * @description: 获取储蓄罐签约状态的有效期
     * @author: jinqing.rao
     * @date: 2024/10/18 13:39
     * @since JDK 1.8
     */
    private static String agreementValidStatus(PiggyAgreementSignDetailDTO hkCustPiggyAgreement) {
        if (StringUtils.isAnyBlank(hkCustPiggyAgreement.getAgreementSignDt(), hkCustPiggyAgreement.getAgreementSignExpiredDt())) {
            return null;
        }
        String agreementValidStatus = null;
        // 签署时间>当前时间
        boolean beforeCurrentDate = DateUtils.isAfterCurrentDate(hkCustPiggyAgreement.getAgreementSignDt(), DateUtils.YYYYMMDD);
        if (beforeCurrentDate) {
            agreementValidStatus = "1";
        }
        // 签署时间<=当前时间且有效期未过期
        boolean beforeExpiredDate = DateUtils.currentDateInRangeLeftClose(hkCustPiggyAgreement.getAgreementSignDt(), hkCustPiggyAgreement.getAgreementSignExpiredDt(), DateUtils.YYYYMMDD);
        if (beforeExpiredDate) {
            agreementValidStatus = "2";
        }
        boolean afterCurrentDate = DateUtils.isBeforeCurrentDate(hkCustPiggyAgreement.getAgreementSignExpiredDt(), DateUtils.YYYYMMDD);
        if (afterCurrentDate) {
            agreementValidStatus = "3";
        }
        return agreementValidStatus;
    }

    /**
     * @param date
     * @return java.lang.String
     * @description: 日期格式化
     * @author: jinqing.rao
     * @date: 2025/4/15 13:30
     * @since JDK 1.8
     */
    private static String formatDate(String date) {
        return DateUtils.format(date, DateUtils.YYYYMMDD, DateUtils.YYYY_MM_DD);
    }

    /**
     * @param date
     * @return java.lang.String
     * @description: 时间格式化
     * @author: jinqing.rao
     * @date: 2025/4/15 13:30
     * @since JDK 1.8
     */
    private static String formatTime(String date) {
        return DateUtils.format(date, DateUtils.HHMMSS, DateUtils.HH_MM);
    }

    /**
     * @param date
     * @return java.lang.String
     * @description: BigDecimal 转 String
     * @author: jinqing.rao
     * @date: 2025/4/15 13:30
     * @since JDK 1.8
     */
    private static String bigDecimalToString(BigDecimal date) {
        return null == date ? null : date.toString();
    }

    /**
     * @param feeComputeInfoDTO
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundFeeComputeVO
     * @description: 手续费计算赋值方法
     * @author: jinqing.rao
     * @date: 2025/4/15 13:28
     * @since JDK 1.8
     */
    public static HKPurchaseFundFeeComputeVO toHKPurchaseFundFeeComputeVO(HKPurchaseFundFeeComputeInfoDTO feeComputeInfoDTO) {
        HKPurchaseFundFeeComputeVO hkPurchaseFundFeeComputeVO = new HKPurchaseFundFeeComputeVO();
        //将feeComputeInfoDTO 对象转换成 hkPurchaseFundFeeComputeVO
        hkPurchaseFundFeeComputeVO.setActualDiscountRate(bigDecimalToString(feeComputeInfoDTO.getActualDiscountRate()));
        hkPurchaseFundFeeComputeVO.setPrebookDiscountRate(bigDecimalToString(feeComputeInfoDTO.getPrebookDiscountRate()));
        hkPurchaseFundFeeComputeVO.setActualPayAmt(bigDecimalToString(feeComputeInfoDTO.getActualPayAmt()));
        hkPurchaseFundFeeComputeVO.setFee(bigDecimalToString(feeComputeInfoDTO.getEstimateFee()));
        hkPurchaseFundFeeComputeVO.setFeeRate(bigDecimalToString(feeComputeInfoDTO.getFeeRate()));
        hkPurchaseFundFeeComputeVO.setLargerPrebookAmt(feeComputeInfoDTO.getIsLargerPrebookAmt());
        hkPurchaseFundFeeComputeVO.setOriginalFee(bigDecimalToString(feeComputeInfoDTO.getOriginalFee()));
        hkPurchaseFundFeeComputeVO.setValidDiscountRate(feeComputeInfoDTO.getValidDiscountRate());
        hkPurchaseFundFeeComputeVO.setFeeRateType(feeComputeInfoDTO.getFeeRateType());
        return hkPurchaseFundFeeComputeVO;
    }

    /**
     * @param request
     * @param dealNo
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.HkPurchaseFundSubmitInfoDTO
     * @description: 组合购买基金提交信息
     * @author: jinqing.rao
     * @date: 2025/4/15 13:28
     * @since JDK 1.8
     */
    public static HkPurchaseFundSubmitInfoDTO toHkPurchaseFundSubmitInfoDTO(HkPurchaseFundSubmitRequest request, String dealNo) {
        HkPurchaseFundSubmitInfoDTO hkPurchaseFundSubmitInfoDTO = new HkPurchaseFundSubmitInfoDTO();
        hkPurchaseFundSubmitInfoDTO.setHkCustNo(request.getHkCustNo());
        hkPurchaseFundSubmitInfoDTO.setTxPassword(PassWordUtil.encrypt(request.getTxPassword()));
        hkPurchaseFundSubmitInfoDTO.setFundCode(request.getFundCode());
        hkPurchaseFundSubmitInfoDTO.setBuyAmt(NumberUtils.strToBigDecimal(request.getAppAmt()));
        hkPurchaseFundSubmitInfoDTO.setPaidAmt(NumberUtils.strToBigDecimal(request.getPaidAmt()));
        hkPurchaseFundSubmitInfoDTO.setEstimateFee(NumberUtils.strToBigDecimal(request.getFee()));
        hkPurchaseFundSubmitInfoDTO.setPayMethod(request.getPayMethod());
        hkPurchaseFundSubmitInfoDTO.setCpAcctNo(request.getCpAcctNo());
        hkPurchaseFundSubmitInfoDTO.setIsAgreeCurrencyExchange(request.getAgreeCurrencyExchange());
        hkPurchaseFundSubmitInfoDTO.setDealNo(dealNo);
        hkPurchaseFundSubmitInfoDTO.setExternalDealNo(request.getHbSceneId());
        hkPurchaseFundSubmitInfoDTO.setPaidExternalDealNo(UuidUtils.generateUuid());
        hkPurchaseFundSubmitInfoDTO.setPrebookDealNo(request.getPrebookDealNo());
        hkPurchaseFundSubmitInfoDTO.setFeeRateType(request.getFeeRateType());
        return hkPurchaseFundSubmitInfoDTO;
    }

    /**
     * @param hkCustNo
     * @param fundCode
     * @param signFlag
     * @param tradeMode
     * @param contractList
     * @param prebookId
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.DigitalSignSignDTO
     * @description: 购买基金签约信息
     * @author: jinqing.rao
     * @date: 2025/4/15 13:29
     * @since JDK 1.8
     */
    public static DigitalSignSignDTO toCustFundContractSignDTO(String hkCustNo, String fundCode, String signFlag, String tradeMode, List<HKFundAgreementRequest> contractList, String prebookId) {
        DigitalSignSignDTO digitalSignSignDTO = new DigitalSignSignDTO();
        digitalSignSignDTO.setHkCustNo(hkCustNo);
        digitalSignSignDTO.setFundCode(fundCode);
        digitalSignSignDTO.setContractSignFlag(signFlag);
        digitalSignSignDTO.setTradeMode(tradeMode);
        digitalSignSignDTO.setPerbookId(prebookId);
        if (CollectionUtils.isNotEmpty(contractList)) {
            List<AgreementDTO> hkFundAgreementDTOList = contractList.stream().map(m -> {
                AgreementDTO agreementDTO = new AgreementDTO();
                agreementDTO.setFileCode(m.getFileCode());
                agreementDTO.setFileName(m.getFileName());
                agreementDTO.setFilePathUrl(m.getFilePathUrl());
                return agreementDTO;
            }).collect(Collectors.toList());
            digitalSignSignDTO.setContractList(hkFundAgreementDTOList);
        }
        return digitalSignSignDTO;
    }

    /**
     * @param hkPurchaseFundOrderInfoDTO
     * @param fundBasicInfoDTO
     * @param hkCustInfo
     * @param transferBank
     * @param investorAuditStatus
     * @param fundTradeCalendarInfoDTO
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundApplyResultVO
     * @description: 购买基金申请结果
     * @author: jinqing.rao
     * @date: 2025/4/15 13:29
     * @since JDK 1.8
     */
    public static HKPurchaseFundApplyResultVO toHKPurchaseFundApplyResultVO(HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO, FundBasicInfoDTO fundBasicInfoDTO,
                                                                            HkCustInfoDTO hkCustInfo, String transferBank, String investorAuditStatus,
                                                                            FundTradeCalendarInfoDTO fundTradeCalendarInfoDTO, HKPurchaseFundOrderInfoDTO paidFundOrderInfoDTO) {
        HKPurchaseFundApplyResultVO hkPurchaseFundApplyResultVO = new HKPurchaseFundApplyResultVO();
        hkPurchaseFundApplyResultVO.setFundShortName(fundBasicInfoDTO.getFundAbbr());
        BigDecimal appAmt = hkPurchaseFundOrderInfoDTO.getAppAmt();
        hkPurchaseFundApplyResultVO.setAppAmt(NumberUtils.formatToThousandths(appAmt, 2, RoundingMode.DOWN));
        hkPurchaseFundApplyResultVO.setGradationCall(fundBasicInfoDTO.getGradationCall());
        // 打款截止时间
        if (null != fundTradeCalendarInfoDTO) {
            hkPurchaseFundApplyResultVO.setPayEndDate(DateUtils.format(fundTradeCalendarInfoDTO.getPaymentDeadlineDt(), DateUtils.YYYYMMDD, DateUtils.YYYY_MM_DD));
            hkPurchaseFundApplyResultVO.setPayEndTime(DateUtils.format(fundTradeCalendarInfoDTO.getPaymentDeadlineTime(), DateUtils.HHMMSS, DateUtils.HH_MM));
        } else {
            hkPurchaseFundApplyResultVO.setPayEndDate(DateUtils.format(hkPurchaseFundOrderInfoDTO.getPayEndDt(), DateUtils.YYYYMMDD, DateUtils.YYYY_MM_DD));
            hkPurchaseFundApplyResultVO.setPayEndTime(DateUtils.format(hkPurchaseFundOrderInfoDTO.getPayEndTm(), DateUtils.HHMMSS, DateUtils.HH_MM));
        }
        hkPurchaseFundApplyResultVO.setCurrencyCode(hkPurchaseFundOrderInfoDTO.getCurrency());
        hkPurchaseFundApplyResultVO.setCurrencyDesc(CurrencyEnum.getDescription(hkPurchaseFundOrderInfoDTO.getCurrency()));
        hkPurchaseFundApplyResultVO.setCurrentDate(DateUtils.getCurrentDate(DateUtils.YYYY_MM_DD));
        hkPurchaseFundApplyResultVO.setInvestorAuditStatus(investorAuditStatus);
        boolean supportBuyPrebook = HKFundPrebookEnum.isSupportBuyPrebook(hkPurchaseFundOrderInfoDTO.getSupportPrebookFlag());
        hkPurchaseFundApplyResultVO.setSupportPrebook(supportBuyPrebook ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        hkPurchaseFundApplyResultVO.setPayMethod(HkFundPayMethodEnum.getPaymentEnumListByValue(hkPurchaseFundOrderInfoDTO.getPaymentTypeList()).get(0).getCode());
        if (null != hkCustInfo) {
            hkPurchaseFundApplyResultVO.setInvestorType(hkCustInfo.getInvestorQualification());
            boolean beforeCurrentDate = DateUtils.isBeforeCurrentDate(hkCustInfo.getAssetCertExpiredDate(), DateUtils.YYYYMMDD);
            hkPurchaseFundApplyResultVO.setAssetCertVaild(beforeCurrentDate ? YesNoEnum.NO.getCode() : YesNoEnum.YES.getCode());
        }
        // 添加回款银行卡信息
        buildTransferBankInfos(transferBank, hkPurchaseFundApplyResultVO);

        //前端用户展示不同的提示语, fundBasicInfoDTO.getPaymentEndTm() 在不支持预约时,会重新赋值
        setBeforPiggyPayEndTime(fundBasicInfoDTO, hkPurchaseFundApplyResultVO);

        // 添加打款凭证上传状态
        buildUploadStatus(hkPurchaseFundOrderInfoDTO,paidFundOrderInfoDTO, hkPurchaseFundApplyResultVO);

        // 添加实缴订单信息
        buildGradationCallVO(hkPurchaseFundOrderInfoDTO, paidFundOrderInfoDTO, hkPurchaseFundApplyResultVO);

        return hkPurchaseFundApplyResultVO;
    }

    /**
     * @param transferBank
     * @param hkPurchaseFundApplyResultVO
     * @return void
     * @description: 添加回款银行卡信息
     * @author: jinqing.rao
     * @date: 2025/4/16 14:36
     * @since JDK 1.8
     */
    private static void buildTransferBankInfos(String transferBank, HKPurchaseFundApplyResultVO hkPurchaseFundApplyResultVO) {
        if (StringUtils.isNotBlank(transferBank)) {
            HKPurchaseFundApplyResultVO.TransferBankInfoInnerVO transferBankInfoInnerVO = new HKPurchaseFundApplyResultVO.TransferBankInfoInnerVO();
            transferBankInfoInnerVO = JSON.parseObject(transferBank, HKPurchaseFundApplyResultVO.TransferBankInfoInnerVO.class);
            hkPurchaseFundApplyResultVO.setTransferBankInfos(transferBankInfoInnerVO);
        }
    }

    /**
     * @param hkPurchaseFundOrderInfoDTO
     * @param hkPurchaseFundApplyResultVO
     * @return void
     * @description: 添加打款凭证上传状态
     * @author: jinqing.rao
     * @date: 2025/4/16 14:35
     * @since JDK 1.8
     */
    private static void buildUploadStatus(HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO,HKPurchaseFundOrderInfoDTO paidFundOrderInfoDTO, HKPurchaseFundApplyResultVO hkPurchaseFundApplyResultVO) {
        // 默认未上传
        hkPurchaseFundApplyResultVO.setUploadStatus(HKFundPayVoucherStateEnum.NOT_UPLOAD.getCode());

        String payVoucherStatus = hkPurchaseFundOrderInfoDTO.getPayVoucherStatus();
        if(YesNoEnum.YES.getCode().equals(hkPurchaseFundApplyResultVO.getGradationCall())){
            payVoucherStatus = paidFundOrderInfoDTO.getPayVoucherStatus();
        }
        hkPurchaseFundApplyResultVO.setUploadStatus(StringUtils.isBlank(payVoucherStatus) ? HKFundPayVoucherStateEnum.NOT_UPLOAD.getCode() : payVoucherStatus);
    }

    /**
     * @param hkPurchaseFundOrderInfoDTO  认缴订单信息
     * @param paidFundOrderInfoDTO        实缴订单信息
     * @param hkPurchaseFundApplyResultVO 响应实体类
     * @return void
     * @description: 添加实缴信息
     * @author: jinqing.rao
     * @date: 2025/4/16 14:33
     * @since JDK 1.8
     */
    private static void buildGradationCallVO(HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO, HKPurchaseFundOrderInfoDTO paidFundOrderInfoDTO, HKPurchaseFundApplyResultVO hkPurchaseFundApplyResultVO) {
        if (HkFundMBusiCodeEnum._112A.getMCode().equals(hkPurchaseFundOrderInfoDTO.getMiddleBusiCode())) {
            HKPurchaseFundApplyResultVO.GradationCallVO gradationCallVO = new HKPurchaseFundApplyResultVO.GradationCallVO();
            gradationCallVO.setSubAmt(NumberUtils.bigDecimalToString(hkPurchaseFundOrderInfoDTO.getAppAmt(),2,RoundingMode.DOWN));
            gradationCallVO.setPaidAmt(NumberUtils.bigDecimalToString(paidFundOrderInfoDTO.getAppAmt(),2,RoundingMode.DOWN));
            gradationCallVO.setSubFee(NumberUtils.bigDecimalToString(hkPurchaseFundOrderInfoDTO.getEstimateFee(),2,RoundingMode.DOWN));
            gradationCallVO.setPaidFee(NumberUtils.bigDecimalToString(paidFundOrderInfoDTO.getEstimateFee(),2,RoundingMode.DOWN));
            gradationCallVO.setPaidOrderNo(paidFundOrderInfoDTO.getDealNo().toString());
            hkPurchaseFundApplyResultVO.setGradationCallVO(gradationCallVO);
        }
    }

    /**
     * @param fundBasicInfoDTO
     * @param hkPurchaseFundApplyResultVO
     * @return void
     * @description: 设置预约支付截止时间
     * @author: jinqing.rao
     * @date: 2025/4/15 13:29
     * @since JDK 1.8
     */
    private static void setBeforPiggyPayEndTime(FundBasicInfoDTO fundBasicInfoDTO, HKPurchaseFundApplyResultVO hkPurchaseFundApplyResultVO) {
        if (YesNoEnum.NO.getCode().equals(hkPurchaseFundApplyResultVO.getSupportPrebook()) && StringUtils.isNotBlank(fundBasicInfoDTO.getOrderEndTm())) {
            //当前时间 时分秒
            String currentTime = DateUtils.getCurrentTime(DateUtils.HHMMSS);
            if (currentTime.compareTo(fundBasicInfoDTO.getOrderEndTm()) > 0) {
                hkPurchaseFundApplyResultVO.setBeforPiggyPayEndTime(YesNoEnum.NO.getCode());
            } else {
                hkPurchaseFundApplyResultVO.setBeforPiggyPayEndTime(YesNoEnum.YES.getCode());
            }
        }
    }

    /**
     * @param hkPurchaseFundOrderInfoDTO
     * @param acctByAcctNoAndCustNo
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundPaymentVoucherVO
     * @description: 构建支付凭证信息
     * @author: jinqing.rao
     * @date: 2025/4/15 13:29
     * @since JDK 1.8
     */
    public static HKPurchaseFundPaymentVoucherVO toHKPurchaseFundPaymentVoucherVO(HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO, List<HkBankCardInfoDTO> acctByAcctNoAndCustNo) {
        HKPurchaseFundPaymentVoucherVO paymentVoucherVO = new HKPurchaseFundPaymentVoucherVO();
        paymentVoucherVO.setAppAmt(null == hkPurchaseFundOrderInfoDTO.getAppAmt() ? null : hkPurchaseFundOrderInfoDTO.getAppAmt().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        paymentVoucherVO.setCurrencyCode(hkPurchaseFundOrderInfoDTO.getCurrency());
        paymentVoucherVO.setCurrencyDesc(CurrencyEnum.getDescription(hkPurchaseFundOrderInfoDTO.getCurrency()));
        if (CollectionUtils.isNotEmpty(acctByAcctNoAndCustNo)) {
            HkBankCardInfoDTO hkBankCardInfoDTO = acctByAcctNoAndCustNo.get(0);
            paymentVoucherVO.setBankLogoUrl(hkBankCardInfoDTO.getBankLogoUrl());
            String bankChineseName = hkBankCardInfoDTO.getBankChineseName();
            paymentVoucherVO.setBankName(StringUtils.isBlank(bankChineseName) ? hkPurchaseFundOrderInfoDTO.getBankEnName() : bankChineseName);
            paymentVoucherVO.setSwiftCode(hkBankCardInfoDTO.getSwiftCode());
            paymentVoucherVO.setBankAcctMask(hkBankCardInfoDTO.getBankAcctMask());
            paymentVoucherVO.setCpAcctNo(hkBankCardInfoDTO.getHkCpAcctNo());
        }
        return paymentVoucherVO;
    }

    /**
     * @param hkPurchaseFundOrderInfoDTO
     * @param hkFundPayVoucherRecordDTO
     * @param hkBankCardInfoDTO
     * @param hkCustNo
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundPaymentVoucherRecordVO
     * @description: 构建支付凭证记录信息
     * @author: jinqing.rao
     * @date: 2025/4/15 13:29
     * @since JDK 1.8
     */
    public static HKPurchaseFundPaymentVoucherRecordVO toHKPurchaseFundPaymentVoucherRecordVO(HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO,
                                                                                              HKFundPayVoucherRecordDTO hkFundPayVoucherRecordDTO, HkBankCardInfoDTO hkBankCardInfoDTO, String hkCustNo) {
        HKPurchaseFundPaymentVoucherRecordVO voucherRecordVO = new HKPurchaseFundPaymentVoucherRecordVO();
        // 基础信息
        BuildBaseInfo(hkPurchaseFundOrderInfoDTO, hkFundPayVoucherRecordDTO, voucherRecordVO);
        // 图片地址信息
        buildPayVoucherImagesInfo(hkFundPayVoucherRecordDTO, hkCustNo, voucherRecordVO);
        if (null != hkBankCardInfoDTO) {
            String bankChineseName = hkBankCardInfoDTO.getBankChineseName();
            voucherRecordVO.setBankName(StringUtils.isBlank(bankChineseName) ? hkBankCardInfoDTO.getBankName() : bankChineseName);
            voucherRecordVO.setBankLogoUrl(hkBankCardInfoDTO.getBankLogoUrl());
        }
        boolean onlineChannel = TxChannelEnum.isOnlineChannel(hkFundPayVoucherRecordDTO.getTradeChannel());
        //  1 : 线上  0 : 线下
        voucherRecordVO.setPayVoucherSource(onlineChannel ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        voucherRecordVO.setReturnReason(hkFundPayVoucherRecordDTO.getReturnReason());
        voucherRecordVO.setAuditOpinion(hkFundPayVoucherRecordDTO.getAuditOpinion());
        voucherRecordVO.setSwiftCode(hkFundPayVoucherRecordDTO.getSwiftCode());
        voucherRecordVO.setDuplicatePayment(hkFundPayVoucherRecordDTO.getDuplicateVoucher());
        voucherRecordVO.setOrderStatus(hkPurchaseFundOrderInfoDTO.getOrderStatus());
        //添加审核不通过的原因
        buildCheckResult(hkFundPayVoucherRecordDTO, voucherRecordVO);
        return voucherRecordVO;
    }

    /**
     * @description: 添加图片地址信息
     * @param hkFundPayVoucherRecordDTO	
     * @param hkCustNo	
     * @param voucherRecordVO
     * @return void
     * @author: jinqing.rao
     * @date: 2025/4/29 9:31
     * @since JDK 1.8
     */
    private static void buildPayVoucherImagesInfo(HKFundPayVoucherRecordDTO hkFundPayVoucherRecordDTO, String hkCustNo, HKPurchaseFundPaymentVoucherRecordVO voucherRecordVO) {
        List<HKFundPayVoucherFileDTO> payVoucherFileVOList = hkFundPayVoucherRecordDTO.getPayVoucherFileVOList();
        if (CollectionUtils.isNotEmpty(payVoucherFileVOList)) {
            voucherRecordVO.setPayVoucherImages(payVoucherFileVOList.stream().map(m -> {
                // 交易打款凭证材料  存在在CRM的历史数据
                if(HwPayVoucherFileSourceEnum.CRM.getCode().equals(hkFundPayVoucherRecordDTO.getFileSource())){
                    String url = m.getFilePath();
                    String fileName = m.getFileName();
                    String encryptUrl = FileUploadUtils.getEncryptUrl(m.getFileId(),hkCustNo, url, fileName, FileBizTypeEnum.CRM_PAYMENT_VOUCHER_FILE);
                    //名称加密
                    String encryptFileName = DesUtil.encrypt(fileName, Constants.HK_APP_CUST_NO_KEY);
                    return new HKFileVO(m.getFileId(), encryptUrl, encryptUrl, encryptFileName, m.getFileSuffix());
                }
                // 现有的交易打款凭证记录赋值逻辑
                String url = m.getFilePath();
                String fileName = m.getFileName();
                String encryptUrl = FileUploadUtils.getEncryptUrl(hkCustNo, url, fileName, FileBizTypeEnum.PAYMENT_VOUCHER_SAVE);
                //名称加密
                String encryptFileName = DesUtil.encrypt(fileName, Constants.HK_APP_CUST_NO_KEY);
                return new HKFileVO(m.getFileId(), encryptUrl, encryptUrl, encryptFileName, m.getFileSuffix());
            }).collect(Collectors.toList()));
        }
    }

    /**
     * @description: 添加基础信息
     * @param hkPurchaseFundOrderInfoDTO	
     * @param hkFundPayVoucherRecordDTO	
     * @param voucherRecordVO
     * @return void
     * @author: jinqing.rao
     * @date: 2025/4/29 9:30
     * @since JDK 1.8
     */
    private static void BuildBaseInfo(HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO, HKFundPayVoucherRecordDTO hkFundPayVoucherRecordDTO, HKPurchaseFundPaymentVoucherRecordVO voucherRecordVO) {
        voucherRecordVO.setPayVoucherState(hkPurchaseFundOrderInfoDTO.getPayVoucherStatus());
        voucherRecordVO.setRemitAmt(null == hkFundPayVoucherRecordDTO.getRemitAmt() ? null : hkFundPayVoucherRecordDTO.getRemitAmt().setScale(2).toString());
        voucherRecordVO.setRemitCurCode(hkFundPayVoucherRecordDTO.getCurrency());
        voucherRecordVO.setRemitCurCodeDesc(CurrencyEnum.getDescription(hkFundPayVoucherRecordDTO.getCurrency()));
        voucherRecordVO.setBankAcctMask(hkFundPayVoucherRecordDTO.getBankAcctMask());
        voucherRecordVO.setOrderId(hkFundPayVoucherRecordDTO.getOrderId());
        voucherRecordVO.setFileTypeId(hkFundPayVoucherRecordDTO.getFileTypeId());
        voucherRecordVO.setRemark(hkFundPayVoucherRecordDTO.getRemark());
        voucherRecordVO.setOrderNo(null == hkPurchaseFundOrderInfoDTO.getDealNo() ? null : hkPurchaseFundOrderInfoDTO.getDealNo().toString());
        voucherRecordVO.setVoucherNo(hkFundPayVoucherRecordDTO.getVoucherNo());
        voucherRecordVO.setRemitCpAcctNo(hkFundPayVoucherRecordDTO.getRemitCpAcctNo());
    }

    /**
     * @description: 添加打款凭证原因
     * @param hkFundPayVoucherRecordDTO	
     * @param voucherRecordVO
     * @return void
     * @author: jinqing.rao
     * @date: 2025/4/29 9:29
     * @since JDK 1.8
     */
    private static void buildCheckResult(HKFundPayVoucherRecordDTO hkFundPayVoucherRecordDTO, HKPurchaseFundPaymentVoucherRecordVO voucherRecordVO) {
        if (null == hkFundPayVoucherRecordDTO.getAuditReason()) {
            return;
        }
        PiggyDepositVoucherDetailVO.VoucherAuditResultVO voucherAuditResultVO = new PiggyDepositVoucherDetailVO.VoucherAuditResultVO();
        voucherAuditResultVO.setBankAcctMask(hkFundPayVoucherRecordDTO.getAuditReason().getRemitCpAcctNo());
        voucherAuditResultVO.setRemitCurrency(hkFundPayVoucherRecordDTO.getAuditReason().getRemitCurrency());
        voucherAuditResultVO.setRemark(hkFundPayVoucherRecordDTO.getAuditReason().getRemark());
        voucherAuditResultVO.setRemitAmt(hkFundPayVoucherRecordDTO.getAuditReason().getRemitAmt());
        voucherAuditResultVO.setPayVoucherImages(hkFundPayVoucherRecordDTO.getAuditReason().getFileReason());
        List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS = ReflectUtils.extractFieldInfo(voucherAuditResultVO);
        voucherRecordVO.setCheckResult(hkOpenAcctCheckVOS);
    }

    /**
     * @param request
     * @param hkPurchaseFundOrderInfoDTO
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.HKPurchaseFundPayVoucherSubmitDTO
     * @description: 构建支付凭证信息
     * @author: jinqing.rao
     * @date: 2025/4/15 13:30
     * @since JDK 1.8
     */
    public static HKPurchaseFundPayVoucherSubmitDTO toHkPurchaseFundPaymentVoucherDTO(HKPurchaseFundPayVoucherUploadRequest request, HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO) {
        HKPurchaseFundPayVoucherSubmitDTO voucherSubmitDTO = new HKPurchaseFundPayVoucherSubmitDTO();
        voucherSubmitDTO.setHkCustNo(request.getHkCustNo());
        voucherSubmitDTO.setDealNo(request.getOrderNo());
        voucherSubmitDTO.setBankAcctMask(request.getBankAcctMask());
        voucherSubmitDTO.setBankChineseName(request.getBankName());
        voucherSubmitDTO.setCurrency(request.getRemitCurCode());
        voucherSubmitDTO.setRemitAmt(new BigDecimal(request.getRemitAmt()));
        voucherSubmitDTO.setRemark(request.getRemark());
        voucherSubmitDTO.setSwiftCode(request.getSwiftCode());
        voucherSubmitDTO.setDelFileIdList(request.getDelFileIdList());
        voucherSubmitDTO.setVoucherNo(request.getVoucherNo());
        voucherSubmitDTO.setHkCpAcctNo(hkPurchaseFundOrderInfoDTO.getHkCpAcctNo());
        voucherSubmitDTO.setVoucherType(PayVoucherTypeEnum.TRADE_ORDER.getCode());
        List<HKFundPayVoucherFileDTO> payVoucherFileList = request.getPayVoucherFiles().stream().map(m -> {
            HKFundPayVoucherFileDTO payVoucherFileDTO = new HKFundPayVoucherFileDTO();
            String decryptUrl = FileUploadUtils.getDecryptUrl(m.getUrl(), request.getHkCustNo());
            if (StringUtils.isBlank(decryptUrl)) {
                throw new BusinessException(ExceptionCodeEnum.FUND_FILE_URL_ERROR);
            }
            //机密文件名称
            String decryptFileName = DesUtil.decrypt(m.getFileName(), Constants.HK_APP_CUST_NO_KEY);
            payVoucherFileDTO.setFilePath(decryptUrl);
            payVoucherFileDTO.setFileName(decryptFileName);
            return payVoucherFileDTO;
        }).collect(Collectors.toList());
        voucherSubmitDTO.setPayVoucherFileList(payVoucherFileList);
        voucherSubmitDTO.setOrderId(request.getOrderId());
        voucherSubmitDTO.setFileTypeId(request.getFileTypeId());
        voucherSubmitDTO.setExternalDealNo(request.getHbSceneId());
        return voucherSubmitDTO;
    }
}
