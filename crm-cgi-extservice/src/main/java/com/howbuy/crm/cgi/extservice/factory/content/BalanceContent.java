/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.factory.content;

import com.howbuy.crm.cgi.extservice.common.enums.balance.HkCustBalanceDisPalyCategoryEnum;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.AcctBalanceDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryFinReceiptDTO;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * @description: 资产策略上下文
 * <AUTHOR>
 * @date 2025/6/24 9:27
 * @since JDK 1.8
 */
@Builder
@Getter
public class BalanceContent {

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 展示币种
     */
    private String disPlayCurrencyCode;

    /**
     * 业务类型枚举
     */
    private HkCustBalanceDisPalyCategoryEnum hkCustBalanceDisPalyCategoryEnum;

    /**
     * 基金交易账号
     */
    List<HkFundTxAcctDTO> fundTxAcctNoDTOList;
}
