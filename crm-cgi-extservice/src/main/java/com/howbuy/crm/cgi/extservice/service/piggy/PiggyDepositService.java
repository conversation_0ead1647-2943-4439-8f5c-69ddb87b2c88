/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.piggy;

import com.google.common.collect.Lists;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.TxChannelEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.FundValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.InvstTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.FundStatEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HKFundPrebookEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundMBusiCodeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggySignStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherAuditStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherDisPlayStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherListTimePeriodEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherTypeEnum;
import com.howbuy.crm.cgi.extservice.convert.piggy.PiggyDepositConvert;
import com.howbuy.crm.cgi.extservice.request.piggy.*;
import com.howbuy.crm.cgi.extservice.validator.handle.VerificationHandle;
import com.howbuy.crm.cgi.extservice.validator.piggy.context.PiggyVerificationContext;
import com.howbuy.crm.cgi.extservice.validator.piggy.factory.PiggyValidatorFactory;
import com.howbuy.crm.cgi.extservice.validator.piggy.factory.PiggyVerificationContextFactory;
import com.howbuy.crm.cgi.extservice.vo.piggy.*;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.PayVoucherListPageRequestDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.PiggyDepositVoucherDuplicateCheckDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.piggy.PiggyPayVoucherSubmitRequestDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.PiggyDepositVoucherDuplicateCheckResponseDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.FundTradeCalendarInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.piggy.PiggyPayVoucherRecordDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.piggy.PiggyPayVoucherRecordPageDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.response.fund.FundLimitResponseDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkBankCardInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.PiggyAgreementSignDetailDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.PiggyPayVoucherOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.dubbo.QuerySupportedPiggyBuyFundListOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkBankCardInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkPiggyBankOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/7/18 14:56
 * @since JDK 1.8
 */
@Slf4j
@Service
public class PiggyDepositService {

    @Resource
    private HkPiggyBankOuterService hkPiggyBankOuterService;

    @Resource
    private QueryFundBasicInfoOuterService queryFundBasicInfoOuterService;

    @Resource
    private PiggyVerificationContextFactory piggyVerificationContextFactory;

    @Resource
    private PiggyValidatorFactory piggyValidatorFactory;

    @Resource
    private PiggyPayVoucherOuterService piggyPayVoucherOuterService;

    @Resource
    private HkBankCardInfoOuterService hkBankCardInfoOuterService;

    @Resource
    private QuerySupportedPiggyBuyFundListOuterService querySupportedPiggyBuyFundListOuterService;

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherInitPageVO
     * @description: 海外储蓄罐存入初始化页面信息
     * 1.开通海外储蓄罐
     * <p>
     * 2.没有开通海外储蓄罐
     * @author: jinqing.rao
     * @date: 2024/7/18 14:57
     * @since JDK 1.8
     */
    public PiggyDepositVoucherInitPageVO queryPiggyDepositVoucherInitPageInfo(PiggyBaseParamRequest request) {
        PiggyDepositVoucherInitPageVO voucherInitPageVO = new PiggyDepositVoucherInitPageVO();
        //是否签约海外储蓄罐
        PiggyAgreementSignDetailDTO hkCustPiggyAgreement = hkPiggyBankOuterService.getHkCustPiggyAgreement(request.getHkCustNo());
        //没有开通海外储蓄罐,直接返回
        if (!PiggySignStatusEnum.SIGNED.getCode().equals(hkCustPiggyAgreement.getAgreementState())) {
            voucherInitPageVO.setSignPiggyStatus(hkCustPiggyAgreement.getAgreementState());
            return voucherInitPageVO;
        }
        //获取储蓄罐基金
        FundBasicInfoDTO fundBasicInfoDTO = querySupportedPiggyBuyFundListOuterService.querySupportBuyPiggyBuyFundList(DateUtils.getCurrentDate(DateUtils.YYYYMMDD));
        //支持预约
        boolean supportBuyPrebook = HKFundPrebookEnum.isSupportBuyPrebook(fundBasicInfoDTO.getIsScheduledTrade());
        // 封装数据
        voucherInitPageVO.setSignPiggyStatus(hkCustPiggyAgreement.getAgreementState());
        voucherInitPageVO.setFundAddr(fundBasicInfoDTO.getFundAbbr());
        voucherInitPageVO.setPiggyCurrency(fundBasicInfoDTO.getCurrency());
        voucherInitPageVO.setFundEnName(fundBasicInfoDTO.getFundNameEn());
        HkFundMBusiCodeEnum businessCodeEnum = null;
        // ipoEndDt 为空或者当前日期大于募集结束日期，返回申购
        String ipoEndDt = fundBasicInfoDTO.getIpoEndDt();
        if (StringUtils.isEmpty(ipoEndDt) || DateUtils.getCurrentDate(DateUtils.YYYYMMDD).compareTo(ipoEndDt) > 0) {
            businessCodeEnum = HkFundMBusiCodeEnum.PURCHASE;
        } else {
            businessCodeEnum = HkFundMBusiCodeEnum.SUBS;
        }
        //获取基金的最小金额
        List<FundLimitResponseDTO> fundLimitResponseDTOS = queryFundBasicInfoOuterService.queryFundLimitInfo(fundBasicInfoDTO.getFundCode(), InvstTypeEnum.PERS.getKey(), businessCodeEnum.getCode());
        if (CollectionUtils.isNotEmpty(fundLimitResponseDTOS)) {
            BigDecimal minAppAmt = fundLimitResponseDTOS.get(0).getMinAppAmt();
            voucherInitPageVO.setMinAppAmt(null == minAppAmt ? null : minAppAmt.toString());
        }
        if (supportBuyPrebook) {
            FundTradeCalendarInfoDTO calendarInfoDTO = queryFundBasicInfoOuterService.queryFundTradeCalendarInfo(fundBasicInfoDTO.getFundCode(), businessCodeEnum.getCode(), DateUtils.getCurrentDate(DateUtils.YYYYMMDD), DateUtils.getCurrentDate(DateUtils.HHMMSS));
            voucherInitPageVO.setOpenStartDt(calendarInfoDTO.getOpenStartDt());
            voucherInitPageVO.setOpenEndDt(calendarInfoDTO.getOpenEndDt());
            voucherInitPageVO.setTradeDt(calendarInfoDTO.getOpenEndDt());
            voucherInitPageVO.setPayEndDt(calendarInfoDTO.getPaymentDeadlineDt());
            voucherInitPageVO.setPayEndTm(calendarInfoDTO.getPaymentDeadlineTime());
            voucherInitPageVO.setSupportPrebook(YesNoEnum.YES.getCode());
            return voucherInitPageVO;
        }
        // 不支持预约
        String interval = null;
        if (DateUtils.getCurrentDate(DateUtils.HHMMSS).compareTo(fundBasicInfoDTO.getOrderEndTm()) < 0) {
            interval = "0";
        } else {
            interval = "1";
        }
        String tradeDt = queryFundBasicInfoOuterService.getTradeDt(fundBasicInfoDTO.getFundCode(), DateUtils.getCurrentDate(DateUtils.YYYYMMDD), interval, getFundStatList(businessCodeEnum));
        voucherInitPageVO.setTradeDt(tradeDt);
        voucherInitPageVO.setPayEndDt(tradeDt);
        voucherInitPageVO.setPayEndTm(fundBasicInfoDTO.getPaymentEndTm());
        voucherInitPageVO.setFundCode(fundBasicInfoDTO.getFundCode());
        voucherInitPageVO.setCurrentDt(DateUtils.getCurrentDate(DateUtils.YYYYMMDD));
        return voucherInitPageVO;
    }

    /**
     * @param businessCodeEnum
     * @return java.util.List<java.lang.String>
     * @description: 获取基金状态
     * @author: jinqing.rao
     * @date: 2025/4/10 11:09
     * @since JDK 1.8
     */
    public static List<String> getFundStatList(HkFundMBusiCodeEnum businessCodeEnum) {
        if (HkFundMBusiCodeEnum.SUBS.equals(businessCodeEnum)) {
            return Lists.newArrayList(FundStatEnum.ISSUE.getCode());
        } else if (HkFundMBusiCodeEnum.PURCHASE.equals(businessCodeEnum)) {
            return Lists.newArrayList(FundStatEnum.PURCHASE_REDEMPTION.getCode(), FundStatEnum.STOP_REDEMPTION.getCode());
        } else {
            return Lists.newArrayList(FundStatEnum.PURCHASE_REDEMPTION.getCode(), FundStatEnum.STOP_PURCHASE.getCode());
        }
    }

    /**
     * @param request
     * @return void
     * @description: 打款凭证提交接口
     * @author: jinqing.rao
     * @date: 2025/4/10 11:10
     * @since JDK 1.8
     */
    public void submitPiggyDepositVoucher(PiggyDepositVoucherSubmitRequest request) {
        //根据不同的校验类型,构建校验执行器需要的上下文
        PiggyVerificationContext context = piggyVerificationContextFactory.createContext(new PiggyContextRequest(request.getHkCustNo(), FundValidatorTypeEnum.PIGGY_DEPOSIT_VALIDATOR.getCode()));
        //创建检验管理器
        VerificationHandle<PiggyBankVerificationVO> piggyVerificationDirector = piggyValidatorFactory.createPiggyVerificationDirector(context);
        //执行校验器
        PiggyBankVerificationVO verification = piggyVerificationDirector.verification();
        if (!HkFundVerificationStatusEnum.NORMAL.getCode().equals(verification.getVerfiyState())) {
            HkFundVerificationStatusEnum enumByCode = HkFundVerificationStatusEnum.getEnumByCode(verification.getVerfiyState());
            if (null != enumByCode) {
                throw new BusinessException(enumByCode.getCode(), enumByCode.getDesc());
            }
        }
        PiggyPayVoucherSubmitRequestDTO payVoucherSubmitRequestDTO = PiggyDepositConvert.toPiggyPayVoucherSubmitRequestDTO(request);
        piggyPayVoucherOuterService.submitPiggyPayVoucher(payVoucherSubmitRequestDTO);
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherDetailVO
     * @description: 打款凭证审核记录接口
     * @author: jinqing.rao
     * @date: 2024/7/25 20:09
     * @since JDK 1.8
     */
    public PiggyDepositVoucherDetailVO queryPiggyDepositVoucherRecordDetail(PiggyDepositVoucherDetailRequest request) {
        //是否签约海外储蓄罐
        boolean hasSignPiggy = hasSignPiggy(request.getHkCustNo());
        //查询打款凭证详情
        PiggyPayVoucherRecordDTO piggyPayVoucherRecordDTO = piggyPayVoucherOuterService.queryPiggyDepositVoucherRecordDetail(request.getHkCustNo(), request.getVoucherNo());
        //查询银行卡的默认logo
        List<HkBankCardInfoDTO> bankCardInfoDTOS = hkBankCardInfoOuterService.queryHkBankCardInfoByHkcustNoAndCpAcctNo(request.getHkCustNo(), piggyPayVoucherRecordDTO.getRemitCpAcctNo());
        //初始化打款凭证详情
        PiggyDepositVoucherDetailVO voucherDetailVO = PiggyDepositConvert.toPiggyDepositVoucherDetailVO(piggyPayVoucherRecordDTO);
        //设置储蓄罐
        voucherDetailVO.setWhetherSignPiggy(hasSignPiggy ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        if (CollectionUtils.isNotEmpty(bankCardInfoDTOS)) {
            voucherDetailVO.setBankLogoUrl(bankCardInfoDTOS.get(0).getBankLogoUrl());
        }
        return voucherDetailVO;
    }

    /**
     * @param hkCustNo
     * @return boolean
     * @description: 是否签约储蓄罐
     * 逻辑 : 1 签署状态  2.当前时间在签署有效期内
     * @author: jinqing.rao
     * @date: 2024/8/19 11:13
     * @since JDK 1.8
     */
    private boolean hasSignPiggy(String hkCustNo) {
        PiggyAgreementSignDetailDTO hkCustPiggyAgreement = hkPiggyBankOuterService.getHkCustPiggyAgreement(hkCustNo);
        //没有开通海外储蓄罐,直接返回
        if (!PiggySignStatusEnum.SIGNED.getCode().equals(hkCustPiggyAgreement.getAgreementState())) {
            return false;
        }
        //当前时间不在签约时有效时间内
        return DateUtils.getCurrentDate(DateUtils.YYYYMMDD).compareTo(hkCustPiggyAgreement.getAgreementSignExpiredDt()) <= 0;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherRecordListVO
     * @description: 打款凭证列表查询接口
     * @author: jinqing.rao
     * @date: 2024/7/25 20:09
     * @since JDK 1.8
     */
    public PiggyDepositVoucherRecordListVO queryPiggyDepositVoucherRecordList(PiggyDepositVoucherRecordListRequest request) {
        PayVoucherListPageRequestDTO payVoucherListPageRequestDTO = getPayVoucherListPageRequestDTO(request);
        // 客户维度默认查询 500条，然后做内存分页,主要是为了特殊规则排序，避免下发到SQL上
        PiggyPayVoucherRecordPageDTO piggyPayVoucherRecordPageDTO = piggyPayVoucherOuterService.queryPiggyDepositVoucherRecordList(payVoucherListPageRequestDTO, 1, 500);
        return PiggyDepositConvert.toPiggyDepositVoucherRecordListVO(piggyPayVoucherRecordPageDTO, request.getPage(), request.getSize());
    }

    /**
     * @param request 请求参数
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.request.PayVoucherListPageRequestDTO
     * @description: 构建请求参数
     * @author: jinqing.rao
     * @date: 2025/4/10 10:27
     * @since JDK 1.8
     */
    private static PayVoucherListPageRequestDTO getPayVoucherListPageRequestDTO(PiggyDepositVoucherRecordListRequest request) {
        // APP渠道 H5渠道 小程渠道(已经下线)
        List<String> tradeChannelList = Arrays.asList(TxChannelEnum.WAP.getCode(), TxChannelEnum.HK_APP.getCode(), TxChannelEnum.H5.getCode());
        // 状态转换
        List<String> auditStatusList = new ArrayList<>();

        String duplicateVoucher = buildAuditStatusList(request, auditStatusList);
        // 设置时间
        PayVoucherListPageRequestDTO payVoucherListPageRequestDTO = new PayVoucherListPageRequestDTO();
        String value = PayVoucherListTimePeriodEnum.getValueByCode(request.getTimePeriod());
        if (StringUtils.isNotBlank(value)) {
            String strNextMonth = DateUtils.getStrNextMonth(Integer.parseInt(value));
            payVoucherListPageRequestDTO.setVoucherStartDt(strNextMonth);
            payVoucherListPageRequestDTO.setVoucherEndDt(DateUtils.getCurrentDate(DateUtils.YYYYMMDD));
        } else {
            payVoucherListPageRequestDTO.setVoucherStartDt(request.getVoucherStartDt());
            payVoucherListPageRequestDTO.setVoucherEndDt(request.getVoucherEndDt());
        }
        payVoucherListPageRequestDTO.setHkCustNo(request.getHkCustNo());
        payVoucherListPageRequestDTO.setDuplicateVoucher(duplicateVoucher);
        payVoucherListPageRequestDTO.setAuditStatusList(auditStatusList);
        payVoucherListPageRequestDTO.setTradeChannelList(tradeChannelList);
        payVoucherListPageRequestDTO.setVoucherTypeList(request.getPayVoucherType());
        return payVoucherListPageRequestDTO;
    }

    /**
     * @param request          请求参数
     * @param auditStatusList  审核状态
     * @return java.lang.String
     * @description: 将前端的展示状态转换成对应的审核状态
     * @author: jinqing.rao
     * @date: 2025/4/10 11:08
     * @since JDK 1.8
     */
    private static String buildAuditStatusList(PiggyDepositVoucherRecordListRequest request, List<String> auditStatusList) {
        // 是否重复凭证标识，默认是空，查询所有
        String duplicateVoucher = null;
        if (StringUtils.isNotBlank(request.getVoucherDisStatus())) {
            if (PayVoucherDisPlayStatusEnum.DUPLICATE_VOUCHER.getCode().equals(request.getVoucherDisStatus())) {
                duplicateVoucher = YesNoEnum.YES.getCode();
            }
            if (PayVoucherDisPlayStatusEnum.ALREADY_CREDITED.getCode().equals(request.getVoucherDisStatus())) {
                auditStatusList.add(PayVoucherAuditStatusEnum.APPROVE.getCode());
                duplicateVoucher = YesNoEnum.NO.getCode();
            }
            if (PayVoucherDisPlayStatusEnum.SUBMITTED.getCode().equals(request.getVoucherDisStatus())) {
                auditStatusList.add(PayVoucherAuditStatusEnum.WAIT_REVIEW.getCode());
                duplicateVoucher = YesNoEnum.NO.getCode();
            }
            if (PayVoucherDisPlayStatusEnum.CREDIT_FAILED.getCode().equals(request.getVoucherDisStatus())) {
                auditStatusList.add(PayVoucherAuditStatusEnum.REJECT_TO_CUSTOMER.getCode());
                duplicateVoucher = YesNoEnum.NO.getCode();
            }
        }
        return duplicateVoucher;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.common.base.Body
     * @description: 校验当前选着的银行卡是否解绑
     * @author: jinqing.rao
     * @date: 2024/8/20 17:52
     * @since JDK 1.8
     */
    public void verifyPiggyDepositBank(PiggyDepositBankVerifyRequest request) {
        List<HkBankCardInfoDTO> bankCardInfoDTOS = hkBankCardInfoOuterService.queryHkBankCardInfoByHkcustNoAndCpAcctNo(request.getHkCustNo(), request.getCpAcctNo());
        if (CollectionUtils.isEmpty(bankCardInfoDTOS)) {
            throw new BusinessException(ExceptionCodeEnum.HK_BANK_INFO_QUERY_ERROR.getCode(), "银行卡信息未绑定,请稍后再试");
        }
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherDuplicateCheckVO
     * @description: 校验打款凭证是否重复提交
     * @author: jinqing.rao
     * @date: 2025/4/10 19:25
     * @since JDK 1.8
     */
    public PiggyDepositVoucherDuplicateCheckVO piggyDepositVoucherDuplicateCheck(PiggyDepositVoucherDuplicateCheckRequest request) {
        PiggyDepositVoucherDuplicateCheckDTO checkDTO = new PiggyDepositVoucherDuplicateCheckDTO();
        checkDTO.setCpAcctNo(request.getCpAcctNo());
        checkDTO.setHkCustNo(request.getHkCustNo());
        checkDTO.setSwiftCode(request.getSwiftCode());
        checkDTO.setRemitCurrency(request.getRemitCurrency());
        checkDTO.setRemitAmt(request.getRemitAmt());
        checkDTO.setAuditStatusList(Arrays.asList(PayVoucherAuditStatusEnum.WAIT_EXAMINE.getCode(),PayVoucherAuditStatusEnum.WAIT_REVIEW.getCode()));
        checkDTO.setAppDt(DateUtils.addDaysToDate(-2,DateUtils.YYYYMMDD));
        PiggyDepositVoucherDuplicateCheckResponseDTO duplicateCheck = piggyPayVoucherOuterService.piggyDepositVoucherDuplicateCheck(checkDTO);
        if (null == duplicateCheck || CollectionUtils.isEmpty(duplicateCheck.getPiggyPayVoucherRecordDTO())) {
            return new PiggyDepositVoucherDuplicateCheckVO();
        }
        // 返回对应的凭证类型描述
        List<String> voucherTypeDescList = duplicateCheck.getPiggyPayVoucherRecordDTO().stream()
                .filter(f -> {
                    if(StringUtils.isBlank(request.getVoucherNo())){
                        return true;
                    }
                    // 过滤自己订单
                    return !request.getVoucherNo().equals(f.getVoucherNo());
                })
                .map(m -> PayVoucherTypeEnum.getDisCodeByCode(m.getVoucherType()))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        PiggyDepositVoucherDuplicateCheckVO piggyDepositVoucherDuplicateCheckVO = new PiggyDepositVoucherDuplicateCheckVO();
        piggyDepositVoucherDuplicateCheckVO.setDuplicate(YesNoEnum.YES.getCode());
        piggyDepositVoucherDuplicateCheckVO.setDuplicateNum(String.valueOf(duplicateCheck.getPiggyPayVoucherRecordDTO().size()));
        piggyDepositVoucherDuplicateCheckVO.setDuplicateVoucherTypeDescList(voucherTypeDescList);
        return piggyDepositVoucherDuplicateCheckVO;
    }

    /**
     * @param request
     * @return void
     * @description: 删除打款凭证
     * @author: jinqing.rao
     * @date: 2025/4/11 10:13
     * @since JDK 1.8
     */
    public void deletedDepositVoucher(PiggyDepositVoucherDeletedRequest request) {
        // 查询打款凭证是否存在
        PiggyPayVoucherRecordDTO piggyPayVoucherRecordDTO = piggyPayVoucherOuterService.queryPiggyDepositVoucherRecordDetail(request.getHkCustNo(), request.getVoucherNo());
        if (null == piggyPayVoucherRecordDTO) {
            throw new BusinessException(ExceptionCodeEnum.HK_PAY_VOUCHER_NOT_EXIST);
        }
        List<String> statusNotPassStatus = Arrays.asList(PayVoucherAuditStatusEnum.REJECT_TO_CUSTOMER.getCode(), PayVoucherAuditStatusEnum.VOIDED.getCode());

        // 只有审核状态是审核不通过，或者是重复打款凭证才可以删除
        boolean failureStatus = statusNotPassStatus.contains(piggyPayVoucherRecordDTO.getAuditStatus());
        boolean repeatVoucher = StringUtils.equals(YesNoEnum.YES.getCode(), piggyPayVoucherRecordDTO.getRepeatVoucher());
        if (!failureStatus && !repeatVoucher) {
            throw new BusinessException(ExceptionCodeEnum.HK_PAY_VOUCHER_NOT_SUPPORT_DELETE);
        }
        piggyPayVoucherOuterService.deletedDepositVoucher(request.getHkCustNo(), request.getVoucherNo());
    }
}
