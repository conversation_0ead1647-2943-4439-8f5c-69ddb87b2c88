/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.business.balance;

import com.howbuy.crm.cgi.extservice.common.utils.balance.BalanceUtils;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.QueryAllFundTxAcctBalanceOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.QueryFundTxAcctOuterService;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/6/24 16:01
 * @since JDK 1.8
 */

@Component
public class FundTxAcctNoService {

    @Resource
    private QueryFundTxAcctOuterService queryFundTxAcctOuterService;

    @Resource
    private QueryAllFundTxAcctBalanceOuterService queryAllFundTxAcctBalanceOuterService;

    /**
     * @description: 查询基金交易账户
     * @param hkCustNo 香港客户号
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO>
     * @author: jinqing.rao
     * @date: 2025/6/24 16:02
     * @since JDK 1.8
     */
    public List<HkFundTxAcctDTO> queryFundTxAcctNo(String hkCustNo) {
        return queryFundTxAcctOuterService.queryFundTxCodeList(hkCustNo);
    }

    /**
     * @param hkCustNo 香港客户号
     * @return List<HkFundTxAcctDTO>
     * @description: 查询客户全委基金交易账号列表
     * <AUTHOR>
     * @date 2025-07-11 10:34:43
     */
    public List<HkFundTxAcctDTO> queryFullFundTxAcctNo(String hkCustNo) {
        List<HkFundTxAcctDTO> fundTxAcctNoDTOS = queryAllFundTxAcctBalanceOuterService.queryAllFundTxAcctBalance(hkCustNo);
        if (CollectionUtils.isEmpty(fundTxAcctNoDTOS)) {
            return new ArrayList<>();
        }

        return fundTxAcctNoDTOS.stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.FULL.getCode().equals(m.getFundTxAccType()))
                .collect(Collectors.toList());
    }

    /**
     * @description: 查询用户所有的基金交易账号
     * @param hkCustNo
     * @return java.util.List<java.lang.String>
     * @author: jinqing.rao
     * @date: 2025/6/30 16:38
     * @since JDK 1.8
     */
    public List<String> queryAllFundTxAcctNoList(String hkCustNo) {
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = queryFundTxAcctNo(hkCustNo);
        return BalanceUtils.extractFieldList(hkFundTxAcctDTOS, HkFundTxAcctDTO::getFundTxAcctNo);
    }

    /**
     * @description:(请在此添加描述)
     * @param hkCustNo
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO>
     * @author: jinqing.rao
     * @date: 2025/6/24 16:35
     * @since JDK 1.8
     */
    public List<HkFundTxAcctDTO> queryNonFullFundTxAcctDTOList(String hkCustNo) {
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = queryFundTxAcctNo(hkCustNo);
        if(CollectionUtils.isEmpty(hkFundTxAcctDTOS)){
            return new ArrayList<>();
        }
        return hkFundTxAcctDTOS.stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.NON_FULL.getCode().equals(m.getFundTxAccType()))
                .collect(Collectors.toList());
    }

    /**
     * @description: 获取非全委账户列表
     * @param hkCustNo
     * @return java.util.List<java.lang.String>
     * @author: jinqing.rao
     * @date: 2025/6/25 17:30
     * @since JDK 1.8
     */
    public List<String> getNonFullFundTxAcctNoList(String hkCustNo) {
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = queryNonFullFundTxAcctDTOList(hkCustNo);
        return BalanceUtils.extractFieldList(hkFundTxAcctDTOS, HkFundTxAcctDTO::getFundTxAcctNo);
    }

    /**
     * @description: 查询全委基金交易账号编码
     * @param hkCustNo
     * @return java.util.List<java.lang.String>
     * @author: jinqing.rao
     * @date: 2025/6/25 17:39
     * @since JDK 1.8
     */
    public List<String> getFullFundTxAcctNoList(String hkCustNo) {
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = queryFullFundTxAcctDTOList(hkCustNo);
        return BalanceUtils.extractFieldList(hkFundTxAcctDTOS, HkFundTxAcctDTO::getFundTxAcctNo);
    }

    /**
     * @description: 查询全委码账户列表
     * @param hkCustNo
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO>
     * @author: jinqing.rao
     * @date: 2025/6/25 17:39
     * @since JDK 1.8
     */
    private List<HkFundTxAcctDTO> queryFullFundTxAcctDTOList(String hkCustNo) {
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = queryFundTxAcctNo(hkCustNo);
        if(CollectionUtils.isEmpty(hkFundTxAcctDTOS)){
            return new ArrayList<>();
        }
        return hkFundTxAcctDTOS.stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.FULL.getCode().equals(m.getFundTxAccType()))
                .collect(Collectors.toList());
    }
}
