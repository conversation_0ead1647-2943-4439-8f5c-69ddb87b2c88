package com.howbuy.crm.cgi.extservice.factory.domain;

import com.howbuy.crm.cgi.extservice.vo.balance.QueryPiggyAssetVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.BalanceBeanDTO;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;


@Getter
@Builder
public class PiggyBalancePortfolio implements PortfolioDetail{
    /**
     * 总资产
     */
    private BigDecimal totalAsset;      // 总资产
    /**
     * 储蓄罐在途交易金额
     */
    private BigDecimal piggyInTransitTradeAmt;
    /**
     * 总收益
     */
    private BigDecimal totalIncome;     // 总收益

    /**
     * 收益状态 0-计算中;1-计算成功
     */
    private String incomeStatus;

    /**
     * 储蓄罐签约状态 0-未签约1-已签约2-已暂停
     */
    private String piggySignStatus;
    /**
     * 明细列表
     */
    private List<BalanceBeanDTO> itemList;  // 明细列表

}