/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hk.hkaccount;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.account.NicknameUpdateRequest;
import com.howbuy.crm.cgi.extservice.request.account.EmailUpdateCheckRequest;
import com.howbuy.crm.cgi.extservice.request.account.QueryCustStatusRequest;
import com.howbuy.crm.cgi.extservice.service.hkaccount.CustInfoService;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.EmailUpdateCheckVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.QueryCustStatusVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 账户控制器
 * @author: 陈杰文
 * @date: 2025-06-17 11:11:28
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hk/account")
@Slf4j
public class AccountController {

    @Autowired
    private CustInfoService custInfoService;

    /**
     * @api {POST} /ext/hk/account/cust/nickname/update updateNickname()
     * @apiVersion 1.0.0
     * @apiGroup AccountController
     * @apiName updateNickname()
     * @apiDescription 昵称修改接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号 必传
     * @apiParam (请求体) {String} nickname 昵称 必传 字符长度2-10个字符
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","nickname":"测试昵称"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"A","data":{},"description":"操作成功","timestampServer":"2025-06-17 11:11:28"}
     */
    @PostMapping("/cust/nickname/update")
    public CgiResponse<Body> updateNickname(@RequestBody NicknameUpdateRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        
        // 调用服务层更新昵称
        custInfoService.updateNickname(request);
        
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /ext/hk/account/cust/emailupdate/check checkEmailUpdate()
     * @apiVersion 1.0.0
     * @apiGroup AccountController
     * @apiName checkEmailUpdate()
     * @apiDescription 邮箱修改检查接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号 必传
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.emailBizScenario 邮箱业务场景 1：用户没有维护过结单邮箱 2：用户有维护【一个】结单邮箱 3：用户有维护【多个】结单邮箱，且有和用户开户绑定的旧邮箱一致的邮箱 4：用户有维护【多个】结单邮箱，但没有和用户开户绑定的旧邮箱一致的邮箱
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"A","data":{"emailBizScenario":"1"},"description":"操作成功","timestampServer":"2025-06-17 11:11:28"}
     */
    @PostMapping("/cust/emailupdate/check")
    public CgiResponse<EmailUpdateCheckVO> checkEmailUpdate(@RequestBody EmailUpdateCheckRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        
        // 调用服务层检查邮箱业务场景
        EmailUpdateCheckVO result = custInfoService.checkEmailUpdate(request);
        
        return CgiResponse.ok(result);
    }

    /**
     * @api {POST} /ext/hk/account/cust/querycuststatus queryCustStatus()
     * @apiVersion 1.0.0
     * @apiGroup AccountController
     * @apiName queryCustStatus()
     * @apiDescription 客户状态查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号 必传
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.custState 客户状态 0-正常 1-注销 2-休眠 3-注册 4-开户申请成功
     * @apiSuccess (响应结果) {String} data.openDepositsStatus 开户入金状态   01-去开户；02-继续开户；03-查看开户进度；04-修改开户资料；05-去入金；06-查看入金进度；07- 修改入金资料；08-隐藏开户入金区域
     * @apiSuccess (响应结果) {String} data.openAcctStepFlag 开户步骤标识     01-证件信息页；02-个人信息页；03-职业信息页；04-声明信息页；05-投资经验页；06-银行卡页；07-电子签名页
     * @apiSuccess (响应结果) {String} data.loginActivate 登录是否激活 1:是 0:否
     * @apiSuccess (响应结果) {String} data.transactionActivation 交易是否激活 1:是 0:否
     * @apiSuccess (响应结果) {String} data.bindCardCount 绑卡数量
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"A","data":{"custState":"0","openDepositsStatus":"01","openAcctStepFlag":"01","loginActivate":"1","transactionActivation":"1","bindCardCount":"2"},"description":"操作成功","timestampServer":"2025-06-17 11:11:28"}
     */
    @PostMapping("/cust/querycuststatus")
    public CgiResponse<QueryCustStatusVO> queryCustStatus(@RequestBody QueryCustStatusRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        
        // 调用服务层查询客户状态
        QueryCustStatusVO result = custInfoService.queryCustStatus(request);
        
        return CgiResponse.ok(result);
    }
} 