package com.howbuy.crm.cgi.extservice.request.balance;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: 现金提取记录列表请求类
 * @author: 陈杰文
 * @date: 2025-06-17 17:46:35
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryCashDrawMoneyRecordListRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 时间范围枚举 1-近一个月、2-近半年、3-近一年
     */
    @NotBlank(message = "时间范围枚举不能为空")
    private String timePeriod;

    /**
     * 开始时间 格式：YYYYMMdd
     */
    @NotBlank(message = "开始时间不能为空")
    private String startDt;

    /**
     * 结束时间 格式：YYYYMMdd
     */
    @NotBlank(message = "结束时间不能为空")
    private String endDt;

    /**
     * 提取状态 1-提取中 2-提取成功 3-提取失败
     */
    @NotBlank(message = "提取状态不能为空")
    private String drawMoneyStatus;
} 