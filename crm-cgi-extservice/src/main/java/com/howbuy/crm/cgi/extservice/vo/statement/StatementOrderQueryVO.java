package com.howbuy.crm.cgi.extservice.vo.statement;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 日结订单查询响应类
 * @author: 陈杰文
 * @date: 2025-06-17 15:15:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class StatementOrderQueryVO extends Body implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总条数
     */
    private Integer total;

    /**
     * 结单列表数据
     */
    private List<StatementFileVO> statementOrderList;

    /**
     * 结单邮箱
     */
    private List<StatementEmailVO> statementEmailList;
} 