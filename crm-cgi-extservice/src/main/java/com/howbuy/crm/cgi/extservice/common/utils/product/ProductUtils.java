/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils.product;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.enums.product.ProductBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.product.ProductSubBizTypeEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/7 15:22
 * @since JDK 1.8
 */
@Slf4j
public class ProductUtils {

    /**
     * @description: 产品类型判断
     * @author: jinqing.rao
     * @date: 2025/7/7 11:25
     * @since JDK 1.8
     */
    public static ProductBizTypeEnum getProductType(String hkSaleFlag, String hkPiggyFlag, String productType, String productSubType) {
        // 特殊说明，这里王珺 产品要求只判断满足条件的数据,空数据等不处理

        // 是海外产品
        boolean saleProduct = hkSaleProduct(hkSaleFlag);
        // 是否海外储蓄罐产品
        boolean piggyProduct = hkPiggyProduct(hkPiggyFlag);
        // 海外公募
        if(saleProduct && !piggyProduct && "1".equals(productType)){
            return ProductBizTypeEnum.OVERSEAS_PUBLIC;
        }
        // 海外私募
        if(saleProduct && !piggyProduct){
            return ProductBizTypeEnum.OVERSEAS_PRIVATE;
        }

        log.warn("获取产品类型配置错误，是否海外产品={}, 是否海外储蓄罐产品={}", saleProduct, piggyProduct);
        return null;
    }

    /**
     * @description: 海外储蓄罐产品
     * @param hkPiggyFlag
     * @return void
     * @author: jinqing.rao
     * @date: 2025/7/7 14:39
     * @since JDK 1.8
     */
    public static boolean hkPiggyProduct(String hkPiggyFlag) {
        return YesNoEnum.YES.getCode().equals(hkPiggyFlag);
    }

    /**
     * @description: 海外产品
     * @param hkSaleFlag
     * @return void
     * @author: jinqing.rao
     * @date: 2025/7/7 14:39
     * @since JDK 1.8
     */
    public static boolean hkSaleProduct(String hkSaleFlag) {
        return YesNoEnum.YES.getCode().equals(hkSaleFlag);
    }

    /**
     * @description: 获取产品子分类
     * @param productType
     * @param productSubType
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/7/7 11:27
     * @since JDK 1.8
     */
    public static ProductSubBizTypeEnum getProductSubType(String hkSaleFlag, String hkPiggyFlag, String productType, String productSubType) {
        // 特殊说明，这里王珺 产品要求只判断满足条件的数据,空数据等不处理
        // 是海外产品
        boolean saleProduct = hkSaleProduct(hkSaleFlag);
        // 是否海外储蓄罐产品
        boolean piggyProduct = hkPiggyProduct(hkPiggyFlag);
        if(saleProduct && !piggyProduct && !"1".equals(productType)){
            if(!"2".equals(productSubType) && !"5".equals(productSubType)){
                return ProductSubBizTypeEnum.SUNSHINE_PRIVATE;
            }
            // 私募股权
            if("5".equals(productSubType)){
                return ProductSubBizTypeEnum.PRIVATE_EQUITY;
            }
        }
        return null;
    }
}
