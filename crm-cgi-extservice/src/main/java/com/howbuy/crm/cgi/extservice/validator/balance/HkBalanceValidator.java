/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.balance;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.ParamsException;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.common.enums.balance.HkBalanceDisPlayCurrencyCodeEnum;
import com.howbuy.crm.cgi.extservice.request.balance.QueryTotalAssetV1Request;

/**
 * @description: 资产请求参数校验
 * <AUTHOR>
 * @date 2025/6/23 14:11
 * @since JDK 1.8
 */
public class HkBalanceValidator extends BasicDataTypeValidator {

    public static void validatorParams(QueryTotalAssetV1Request request) {
        // 基础校验
        validator(request);
        // 展示枚举校验
        disPlayCurrencyCodeValidation(request); // 添加额外校验逻辑
    }

    /**
     * @description: 校验展示币种代码
     * @param request
     * @return void
     * @author: jinqing.rao
     * @date: 2025/6/23 14:26
     * @since JDK 1.8
     */
    private static void disPlayCurrencyCodeValidation(QueryTotalAssetV1Request request) {
        if (HkBalanceDisPlayCurrencyCodeEnum.getEnumByCode(request.getDisPlayCurrencyCode()) == null) {
            throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "展示币种代码不合法");
        }
    }

}
