/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils.file;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.extservice.common.enums.FileBizTypeEnum;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO;
import crm.howbuy.base.utils.DesUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @description: 文件上传工具类
 * @date 2024/4/29 17:22
 * @since JDK 1.8
 */
public class FileUploadUtils {

    private static final Logger log = LoggerFactory.getLogger(FileUploadUtils.class);

    public static final String FILE_NAME = "fileName";

    public static final String FILE_ID = "fileId";

    public static final String FILE_BIZ_TYPE = "fileBizType";

    public static final String URL = "url";



    /**
     * 获取URL加密后的url
     * @param hkCusNo 香港客户号
     * @param realFileUrl 文件上传路劲
     * @return
     */
    public static String getEncryptUrl(String hkCusNo, String realFileUrl, String fileName, FileBizTypeEnum fileBizTypeEnum) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(URL, realFileUrl);
        jsonObject.put(Constants.HKCUSTNO, hkCusNo);
        jsonObject.put(FILE_NAME, fileName);
        jsonObject.put(FILE_BIZ_TYPE, fileBizTypeEnum.getCode());
        //数据加密
        String encrypt = DesUtil.encrypt(jsonObject.toJSONString(), Constants.HK_APP_CUST_NO_KEY);
        //去除空格和换行符
        encrypt = encrypt.replaceAll("\\s*", "");
        return encrypt;
    }

    public static String getEncryptUrl(String fileId,String hkCusNo, String realFileUrl, String fileName, FileBizTypeEnum fileBizTypeEnum) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(FILE_ID, fileId);
        jsonObject.put(URL, realFileUrl);
        jsonObject.put(Constants.HKCUSTNO, hkCusNo);
        jsonObject.put(FILE_NAME, fileName);
        jsonObject.put(FILE_BIZ_TYPE, fileBizTypeEnum.getCode());
        //数据加密
        String encrypt = DesUtil.encrypt(jsonObject.toJSONString(), Constants.HK_APP_CUST_NO_KEY);
        //去除空格和换行符
        encrypt = encrypt.replaceAll("\\s*", "");
        return encrypt;
    }

    /**
     * 解密url
     * @param encryptUrl
     * @return
     */
    public static String getDecryptUrl(String encryptUrl,String hkCusNo) {
        if(StringUtils.isBlank(encryptUrl)){
            return null;
        }
        try {
            String decrypt = DesUtil.decrypt(encryptUrl, Constants.HK_APP_CUST_NO_KEY);
            JSONObject jsonObject = JSON.parseObject(decrypt);
            return jsonObject.getString("url");
        } catch (Exception e) {
            log.error("getDecryptUrl >>> 解密异常 hkCusNo：{} , decryptUrl : {}", hkCusNo, encryptUrl);
            return null;
        }
    }

    /**
     * @description: 文件上传,获取自定义的加密String
     * @param multipartFile	文件流
     * @param fileBizType	文件的业务类型
     * @param hkCusNo 香港客户号
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/5/11 15:26
     * @since JDK 1.8
     */
    public static ImageVO uploadFile(MultipartFile multipartFile,String fileBizType,String hkCusNo){
        DefaultUploadFile defaultUploadFile = new DefaultUploadFile(multipartFile, fileBizType, hkCusNo);
        defaultUploadFile.uploadFile();
        String customizeUrl = defaultUploadFile.getEncryptCustomizeUrl();
        String encryptFileName = defaultUploadFile.getEncryptFileName();
        String fileType = defaultUploadFile.getFileType();
        return new ImageVO(customizeUrl, customizeUrl,fileType,encryptFileName);
    }

    /**
     * @description: 根据加密字符串,解密请求参数
     * @param msg
     * @return com.howbuy.crm.cgi.extservice.common.utils.file.DefaultDownLoadFile
     * @author: jinqing.rao
     * @date: 2024/5/11 15:41
     * @since JDK 1.8
     */
    public static DefaultDownLoadFile downloadFile(String msg) {
        return new DefaultDownLoadFile(msg);
    }

    /**
     * 获取文件名的后缀
     * @param filename 完整的文件名
     * @return 文件后缀
     */
    public static String getSuffix(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            // 如果没有找到 . 则返回空字符串
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }
}
