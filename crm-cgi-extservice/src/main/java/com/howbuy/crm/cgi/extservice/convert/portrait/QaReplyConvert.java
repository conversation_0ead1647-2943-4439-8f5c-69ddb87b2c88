/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.portrait;

import com.howbuy.crm.cgi.extservice.vo.portrait.qa.QaReplyVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.qa.QaReplyAnswerVO;
import com.howbuy.crm.cgi.manager.domain.portrait.qa.CmPortraitQaReplyDTO;
import com.howbuy.crm.cgi.manager.domain.portrait.qa.CmPortraitQaReplyAnswerDTO;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 问答回复转换类
 *
 * <AUTHOR>
 * @date 2025-03-21 10:26:47
 */
public class QaReplyConvert {

    /**
     * DTO转VO
     *
     * @param dto DTO对象
     * @return VO对象
     */
    public static QaReplyVO convertToVO(CmPortraitQaReplyDTO dto) {
        if (dto == null) {
            return null;
        }
        QaReplyVO vo = new QaReplyVO();
        vo.setIsHkCons(dto.getIsHkCons());
        vo.setReplyLevel(dto.getReplyLevel());
        vo.setReplyType(dto.getReplyType());
        vo.setReplyTitle(dto.getReplyTitle());
        vo.setSearchCondition(dto.getSearchCondition());
        vo.setFirstReply(dto.getFirstReply());
        vo.setSecondReply(dto.getSecondReply());
        vo.setTotal(dto.getTotal());
        vo.setAnswerList(convertAnswerList(dto.getAnswerList()));
        return vo;
    }

    /**
     * 转换回复列表
     *
     * @param dtoList DTO列表
     * @return VO列表
     */
    private static List<QaReplyAnswerVO> convertAnswerList(List<CmPortraitQaReplyAnswerDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }
        return dtoList.stream()
                .map(QaReplyConvert::convertAnswerDTO)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个回复项
     *
     * @param dto DTO对象
     * @return VO对象
     */
    private static QaReplyAnswerVO convertAnswerDTO(CmPortraitQaReplyAnswerDTO dto) {
        QaReplyAnswerVO vo = new QaReplyAnswerVO();
        vo.setTagName(dto.getTagName());
        vo.setTagCode(dto.getTagCode());
        vo.setMaterialId(dto.getMaterialId());
        vo.setMaterialTitle(dto.getMaterialTitle());
        vo.setMaterialUrl(dto.getMaterialUrl());
        vo.setSendCount(dto.getSendCount());
        vo.setLearningProgress(dto.getLearningProgress());
        vo.setMaterialContentType(dto.getMaterialContentType());
        vo.setReportSource(dto.getReportSource());
        vo.setFundCode(dto.getFundCode());
        vo.setFundName(dto.getFundName());
        return vo;
    }
} 