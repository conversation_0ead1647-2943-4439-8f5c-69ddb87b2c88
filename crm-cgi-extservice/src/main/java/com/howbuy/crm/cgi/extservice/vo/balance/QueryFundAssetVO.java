package com.howbuy.crm.cgi.extservice.vo.balance;

/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 查询基金资产响应类
 * @author: 陈杰文
 * @date: 2025-06-17 14:34:43
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundAssetVO extends Body implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否显示资产 [资产小眼睛]
     */
    private String showAsset;

    /**
     * 基金持仓资产
     */
    private String fundAsset;

    /**
     * 基金持仓收益
     */
    private String fundIncome;

    /**
     * 存在基金海外公募or阳光私募产品
     */
    private String existsFundEquityFund;

    /**
     * 存在基金私募股权产品
     */
    private String existsGqEquityFund;

    /**
     * 基金持仓收益计算状态 0-计算中;1-计算成功
     */
    private String fundIncomeCalStatus;

    /**
     * 基金股权总回款
     */
    private String fundTotalEquityRecovery;

    /**
     * 基金在途交易金额
     */
    private String fundInTransitTradeAmt;

    /**
     * 是否存在全委基金交易账号
     */
    private String existsFullTxAcct;

    /**
     * 基金在途交易笔数
     */
    private String fundInTransitTradeCount;

    /**
     * 基金在途交易订单号
     */
    private String fundInTransitTradeDealNo;

    /**
     * 基金在途交易资金笔数
     */
    private String fundInTransitTradeCapitalCount;

    /**
     * 基金在途交易资金订单号
     */
    private String fundInTransitTradeCapitalDealNo;

    /**
     * 基金补签协议基金个数
     */
    private String fundSupplementalAgreementFundCount;

    /**
     * 基金补签协议基金代码
     */
    private String fundSupplementalAgreementFundCode;

    /**
     * 基金持仓详情列表
     */
    private List<AssetFundDetailVO> assetFundDetailList;
} 