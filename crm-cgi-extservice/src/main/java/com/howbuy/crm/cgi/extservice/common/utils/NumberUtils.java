/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * @description:  小数进度处理
 * <AUTHOR>
 * @date 2024/5/6 10:06
 * @since JDK 1.8
 */
@Slf4j
public class NumberUtils {

    /**
     * @description: BigDecimal小数位操作
     * @param numStr	数字
     * @param precision	 保留位数
     * @param roundingMode	小数位的保留类型
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/5/20 19:46
     * @since JDK 1.8
     */
    public static String bigDecimalToString(BigDecimal numStr, Integer precision,RoundingMode roundingMode){
        if(null == numStr){
            return null;
        }
        // 默认两位
        if(null == precision){
            precision = 2;
        }
        return numStr.setScale(precision, roundingMode).toString();
    }

    /**
     * @description: BigDecimal 转字符串
     * @param numStr
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/8/9 10:10
     * @since JDK 1.8
     */
    public static String bigDecimalToString(BigDecimal numStr){
        if(null == numStr){
            return null;
        }
        return numStr.toString();
    }

    /**
     * @description: bigdecimal 进度处理，不足的用0补充
     * @param numStr	
     * @param precision
     * @param roundingMode	
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/6/6 13:56
     * @since JDK 1.8
     */
    public static String formatToThousandths(BigDecimal numStr, Integer precision,RoundingMode roundingMode){
        if(null == numStr){
            return null;
        }
        // 默认两位
        if(null == precision){
            precision = 2;
        }
        // 如果是0 直接返回 0 加小数位
        if(numStr.compareTo(BigDecimal.ZERO) == 0){
            return "0." + StringUtils.repeat("0", precision);
        }
        BigDecimal roundedValue = numStr.setScale(precision, roundingMode);
        StringBuilder patternBuilder = new StringBuilder("#,###.");
        // 已0.开头的需要特殊处理,不然输出结果小数点前没数字, 例如 .0
        if(numStr.toString().startsWith("0.")){
            patternBuilder = new StringBuilder("0.");
        }
        for (int i = 0; i < precision; i++) {
            patternBuilder.append('0');
        }
        // 创建DecimalFormat实例，使用"."和","作为小数点和千分位分隔符
        DecimalFormat df = new DecimalFormat(patternBuilder.toString());
        df.setGroupingUsed(true); // 开启千分位分隔
        // 格式化四舍五入后的BigDecimal
        return df.format(roundedValue);
    }

    /**
     * 格式化 BigDecimal 为指定小数位数的字符串（截断模式 + 补零）
     *
     * @param number        要格式化的数值
     * @param decimalPlaces 小数位数（>=0）
     * @return 格式化后的字符串，如 3.14（两位小数）
     */
    public static String formatDecimalPlaces(BigDecimal number, int decimalPlaces,RoundingMode roundingMode) {
        if (number == null || decimalPlaces < 0) {
            throw new IllegalArgumentException("参数无效");
        }

        // 动态生成格式模板（如 0.00 表示两位小数）
        String pattern =  "0." + StringUtils.repeat("0", decimalPlaces);
        DecimalFormat df = new DecimalFormat(pattern);
        df.setRoundingMode(roundingMode); // 强制截断
        return df.format(number);
    }
    /**
     * @description: Integer 转Str
     * @param minFeeDays
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/5/10 14:12
     * @since JDK 1.8
     */
    public static String integerToStr(Integer minFeeDays) {
        if(null == minFeeDays){
            return null;
        }
        return minFeeDays.toString();
    }

    public static BigDecimal strToBigDecimal(String redeemVol) {
        if(StringUtils.isBlank(redeemVol)){
            return null;
        }
        return new BigDecimal(redeemVol.replace(",", ""));
    }
    /**
     * 格式话字符类型的数字
     * @param value 数字
     * @param precision 保留的小数位
     * @param roundingMode 保留小数位的方式
     * @return
     */
    public static String formatNumber(String value, Integer precision,RoundingMode roundingMode) {
        if(StringUtils.isBlank(value)){
            return null;
        }
        if(null == roundingMode){
            //不传 默认截取
            roundingMode = RoundingMode.DOWN;
        }
        try {
            return new BigDecimal(value).setScale(precision, roundingMode).toString();
        } catch (Exception e) {
            log.error("NumberUtils>>>formatNumber>> 数字字符格式化错误,value:{},precision:{},roundingMode:{}",value,precision,roundingMode.name());
            return null;
        }
    }
    /**
     * @description: 百分数展示,数值最低精确到小数点后两位，不足则补0，超出则完整展示，示例：1.20%，1.121%
     * @param number 数值
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/6/11 14:06
     * @since JDK 1.8
     */
    public static String formatToTwoDecimalPlaces(BigDecimal number) {
        if(null == number){
            return null;
        }
        // 转换为百分比数值
        BigDecimal percentValue = number.multiply(new BigDecimal("100")).stripTrailingZeros();
        // 获取小数部分的长度
        String percentStr = percentValue.toPlainString();
        int decimalPlaces = percentStr.contains(".") ? percentStr.length() - percentStr.indexOf('.') - 1 : 0;

        // 不足两位小数则补0
        if (decimalPlaces < 2) {
            percentStr = String.format("%." + 2 + "f", percentValue.doubleValue()) + "%";
        } else {
            percentStr += "%";
        }
        return percentStr;
    }

    /**
     * @description: 参数相加
     * @param disCurTotalAsset	
     * @param key
     * @return java.math.BigDecimal
     * @author: jinqing.rao
     * @date: 2024/8/29 16:43
     * @since JDK 1.8
     */
    public static BigDecimal addBigdecimal(BigDecimal disCurTotalAsset, BigDecimal key) {
        if(null == disCurTotalAsset){
            disCurTotalAsset = BigDecimal.ZERO;
        }
        if(null == key){
            key = BigDecimal.ZERO;
        }
        return disCurTotalAsset.add(key);
    }

    public static BigDecimal divide(BigDecimal one, BigDecimal two, int scale, RoundingMode roundingMode) {
        if (getBigDecimal(two).compareTo(BigDecimal.ZERO) == 0) {
            throw new RuntimeException("Cannot divide into 0");
        }
        return one.divide(two, scale, roundingMode);
    }

    public static BigDecimal getBigDecimal(BigDecimal n) {
        return n == null ? BigDecimal.ZERO : n;
    }
}
