/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkaccount;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.account.QueryTradeRecordRequest;
import com.howbuy.crm.cgi.extservice.request.account.QueryTradeContractNewRequest;
import com.howbuy.crm.cgi.extservice.service.hkaccount.HkTradeContractService;
import com.howbuy.crm.cgi.extservice.validator.traderecord.TradeContractValidator;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.ContractInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.ContractInfoNewVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description: (查询交易合同控制类)
 * @date 2024/2/22 18:11
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/tradecontract")
public class TradeContractController {

    @Autowired
    private HkTradeContractService hkTradeContractService;


    /**
     * @api {POST} /ext/hkaccount/tradecontract/query queryTradeContract()
     * @apiVersion 1.0.0
     * @apiGroup TradeContractController
     * @apiName queryTradeContract()
     * @apiDescription 查询合同列表数据
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParamExample 请求体示例
     * {"fundCode":"j"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.ebrokerID ebrokerID
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱验码
     * @apiSuccess (响应结果) {String} data.isSatisfy 是否满足所有条件 (1:满足, 0:不满足)
     * @apiSuccess (响应结果) {String} data.fileType 文件类型 (1: 开户文件 2: 产品合同文件)
     * @apiSuccess (响应结果) {Array} data.openFileList 开户文件列表
     * @apiSuccess (响应结果) {String} data.openFileList.filaName 文件名称
     * @apiSuccess (响应结果) {String} data.openFileList.fileUrl 文件URL
     * @apiSuccess (响应结果) {String} data.openFileList.fileType 文件类型
     * @apiSuccess (响应结果) {String} data.openFileList.fileTypeName 文件类型名称
     * @apiSuccess (响应结果) {Array} data.contractFileList 产品合同文件列表
     * @apiSuccess (响应结果) {String} data.contractFileList.productName 产品名称
     * @apiSuccess (响应结果) {String} data.contractFileList.signDate 签约日期 yy-mm-dd hh:mm:ss
     * @apiSuccess (响应结果) {String} data.contractFileList.contractType 业务类型
     * @apiSuccess (响应结果) {String} data.contractFileList.orderId 资料订单id
     * @apiSuccess (响应结果) {Array} data.contractFileList.fileList 合同文件列表数据
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"rJF2SQoo4","data":{"isSatisfy":"h4Yzwu","openFileList":[{"fileTypeName":"ktAVo","fileUrl":"sg","filaName":"e","fileType":"y6yemoAw"}],"emailMask":"8kE7D2","fileType":"ftc","ebrokerID":"p","contractFileList":[{"orderId":"K","contractType":"G","signDate":"JsbzMTvOW","productName":"pUQ29I","fileList":[]}]},"description":"Xb0QKrJCQ","timestampServer":"mCX3"}
     */
    @PostMapping("/query")
    public CgiResponse<ContractInfoVO> queryTradeContract(@RequestBody QueryTradeRecordRequest queryProductContractRequest) {
        return CgiResponse.ok(hkTradeContractService.getTradeContract(queryProductContractRequest.getFundCode()));
    }


    /**
     * @api {POST} /ext/hkaccount/tradecontract/query/new queryTradeContractNew()
     * @apiVersion 1.0.0
     * @apiGroup TradeContractController
     * @apiName queryTradeContractNew()
     * @apiDescription 合同查询新接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金编码
     * @apiParam (请求体) {String} contractTypes 合同类型集合      1.交易下单 2.补签协议 3.储蓄罐签署/变更文件 4.开户文件
     * @apiParam (请求体) {String} timeRangeType 时间范围类型      1-近一月，2-近三个月，3-近六个月，4.近一年 5-自定义时间范围
     * @apiParam (请求体) {String} startTime 开始时间      格式：yyyyMMdd，timeRangeType=5时必填
     * @apiParam (请求体) {String} endTime 结束时间      格式：yyyyMMdd，timeRangeType=5时必填
     * @apiParamExample 请求体示例
     * {"timeRangeType":"Nz9hZZB5q","fundCode":"8CYdJu","hkCustNo":"9","contractTypes":"uJeBu","startTime":"Q5fv","endTime":"Qv"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.openContractFileVO 开户文件
     * @apiSuccess (响应结果) {Array} data.openContractFileVO.openFileList 开户文件列表
     * @apiSuccess (响应结果) {String} data.openContractFileVO.signDate 签约时间
     * @apiSuccess (响应结果) {String} data.openContractFileVO.typeName 类型名称
     * @apiSuccess (响应结果) {String} data.openContractFileVO.emailMask 邮箱掩码
     * @apiSuccess (响应结果) {Array} data.contractFileList 产品合同文件列表
     * @apiSuccess (响应结果) {String} data.contractFileList.productName 产品名称
     * @apiSuccess (响应结果) {String} data.contractFileList.productCode 产品代码
     * @apiSuccess (响应结果) {String} data.contractFileList.signDate 签约日期 yy-mm-dd hh:mm:ss
     * @apiSuccess (响应结果) {String} data.contractFileList.businessType 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减,
     * @apiSuccess (响应结果) {String} data.contractFileList.businessTypeDesc 业务类型描述
     * @apiSuccess (响应结果) {String} data.contractFileList.contractType 业务类型
     * @apiSuccess (响应结果) {String} data.contractFileList.orderId 资料订单id
     * @apiSuccess (响应结果) {Array} data.contractFileList.fileList 合同文件列表数据
     * @apiSuccess (响应结果) {Array} data.openCustPiggyFileList 储蓄罐开通文件列表
     * @apiSuccess (响应结果) {Array} data.openCustPiggyFileList.contractList 海外储蓄罐签约文件
     * @apiSuccess (响应结果) {String} data.openCustPiggyFileList.signDate 签约时间
     * @apiSuccess (响应结果) {String} data.openCustPiggyFileList.typeName 类型名称
     * @apiSuccess (响应结果) {Array} data.changeCustPiggyFileList 储蓄罐更换文件列表
     * @apiSuccess (响应结果) {Array} data.changeCustPiggyFileList.contractList 海外储蓄罐签约文件
     * @apiSuccess (响应结果) {String} data.changeCustPiggyFileList.signDate 签约时间
     * @apiSuccess (响应结果) {String} data.changeCustPiggyFileList.typeName 类型名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"WnupNhafp","data":{"openCustPiggyFileList":[{"contractList":[],"typeName":"POW3","signDate":"au6IdWQlZ"}],"changeCustPiggyFileList":[{"contractList":[],"typeName":"kwJNhW","signDate":"pS10v"}],"openContractFileVO":{"openFileList":[],"emailMask":"VtqFYS","typeName":"ur3EOUvvT","signDate":"W8"},"contractFileList":[{"productCode":"zBcr","orderId":"Geyka5FuJ","contractType":"fnZkBHczc","businessTypeDesc":"MP12k2","signDate":"9EY7j7","businessType":"ycg","productName":"KDbJy","fileList":[]}]},"description":"ZM","timestampServer":"MNeefADNb"}
     */
    @PostMapping("/query/new")
    public CgiResponse<ContractInfoNewVO> queryTradeContractNew(@RequestBody QueryTradeContractNewRequest request) {
        // 使用专门的交易合同校验器进行参数校验
        TradeContractValidator.validatorParams(request);
        return CgiResponse.ok(hkTradeContractService.getTradeContractNew(request));
    }

}