package com.howbuy.crm.cgi.extservice.vo.papersign;

/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 查询纸质签名详情响应类
 * @author: 陈杰文
 * @date: 2025-06-17 16:54:24
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryPaperSignDetailVO extends Body implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 上传时间 yyyy-mm-dd hh:mm
     */
    private String uploadTime;

    /**
     * 签名文件
     */
    private SignFileVO signFileVO;
} 