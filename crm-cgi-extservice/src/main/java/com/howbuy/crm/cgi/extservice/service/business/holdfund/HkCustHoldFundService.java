/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.business.holdfund;

import com.howbuy.crm.cgi.extservice.common.enums.apppagesource.AppPageSourceEnum;
import com.howbuy.crm.cgi.extservice.service.business.balance.FundTxAcctNoService;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.QueryCustFundBalResponseDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.fundhoid.QueryCustFundBalOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/15 20:51
 * @since JDK 1.8
 */
@Component
public class HkCustHoldFundService {

    @Resource
    private QueryFundBasicInfoOuterService queryFundBasicInfoOuterService;
    @Resource
    private QueryCustFundBalOuterService queryCustFundBalOuterService;

    @Resource
    private FundTxAcctNoService fundTxAcctNoService;

    /**
     * @param channelCode 渠道编码
     * @param hkCustNo    香港客户号
     * @return java.util.List<java.lang.String>
     * @description: 根据渠道获取对应渠道的持仓基金信息
     * @author: jinqing.rao
     * @date: 2025/7/15 20:12
     * @since JDK 1.8
     */
    public List<String> getFundCodeListByChannelCode(String channelCode, String hkCustNo) {

        if (StringUtils.isBlank(channelCode)) {
            return new ArrayList<>();
        }
        AppPageSourceEnum appPageSourceEnum = AppPageSourceEnum.getByCode(channelCode);
        if (null == appPageSourceEnum) {
            return new ArrayList<>();
        }
        // 查询用户的持仓信息
        List<QueryCustFundBalResponseDTO> queryCustFundBalResponseDTOS = queryCustFundBalOuterService.queryCustFundBal(hkCustNo, null);
        if (CollectionUtils.isEmpty(queryCustFundBalResponseDTOS)) {
            return new ArrayList<>();
        }
        // 获取持仓基金
        return getFundHoldCode(hkCustNo, appPageSourceEnum, queryCustFundBalResponseDTOS);
    }

    /**
     * @description: 获取持仓基金
     * @param hkCustNo	香港客户号
     * @param appPageSourceEnum	渠道来源
     * @param queryCustFundBalResponseDTOS 持仓信息
     * @return java.util.List<java.lang.String>
     * @author: jinqing.rao
     * @date: 2025/7/15 20:46
     * @since JDK 1.8
     */
    private List<String> getFundHoldCode(String hkCustNo, AppPageSourceEnum appPageSourceEnum, List<QueryCustFundBalResponseDTO> queryCustFundBalResponseDTOS) {
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = fundTxAcctNoService.queryFundTxAcctNo(hkCustNo);
        if (CollectionUtils.isEmpty(hkFundTxAcctDTOS)) {
            return new ArrayList<>();
        }
        // 获取储蓄罐基金
        List<FundBasicInfoDTO> fundBasicInfoDTOList = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList();
        // 储蓄罐页面跳转
        if (AppPageSourceEnum.PIGGY_BALANCE.equals(appPageSourceEnum)) {
            return getPiggyFundCodeList(hkFundTxAcctDTOS, fundBasicInfoDTOList, queryCustFundBalResponseDTOS);
        }
        // 非全委基金页面跳转
        if(AppPageSourceEnum.NON_FULL_FUND_BALANCE.equals(appPageSourceEnum)){
            // 获取非全委账号
            return getFullFundHoldCode(hkFundTxAcctDTOS, FundTxAcctTypeEnum.NON_FULL, fundBasicInfoDTOList, queryCustFundBalResponseDTOS);

        }
        // 全委基金页面跳转
        if(AppPageSourceEnum.FULL_FUND_BALANCE.equals(appPageSourceEnum)){
            // 获取非全委账号
            return getFullFundHoldCode(hkFundTxAcctDTOS, FundTxAcctTypeEnum.FULL, fundBasicInfoDTOList, queryCustFundBalResponseDTOS);

        }
        return new ArrayList<>();
    }

    /**
     * @description: 获取储蓄罐持仓基金
     * @param hkFundTxAcctDTOS
     * @param fundBasicInfoDTOList
     * @param queryCustFundBalResponseDTOS
     * @return java.util.List<java.lang.String>
     * @author: jinqing.rao
     * @date: 2025/7/15 20:43
     * @since JDK 1.8
     */
    private static List<String> getPiggyFundCodeList(List<HkFundTxAcctDTO> hkFundTxAcctDTOS, List<FundBasicInfoDTO> fundBasicInfoDTOList, List<QueryCustFundBalResponseDTO> queryCustFundBalResponseDTOS) {
        // 获取非全委账号
        List<String> nonFullFundCodeList = hkFundTxAcctDTOS.stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.NON_FULL.getCode().equals(m.getFundTxAccType()))
                .map(HkFundTxAcctDTO::getFundTxAcctNo)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nonFullFundCodeList)) {
            return new ArrayList<>();
        }
        if(CollectionUtils.isEmpty(fundBasicInfoDTOList)){
            return new ArrayList<>();
        }
        List<String> piggyFundCodeList = fundBasicInfoDTOList.stream()
                .map(FundBasicInfoDTO::getFundCode)
                .collect(Collectors.toList());
        return queryCustFundBalResponseDTOS.stream()
                .filter(f -> piggyFundCodeList.contains(f.getFundCode()) && nonFullFundCodeList.contains(f.getFundTxAcctNo()))
                .map(QueryCustFundBalResponseDTO::getFundCode)
                .collect(Collectors.toList());
    }

    /**
     * 获取非储蓄罐基金持仓
     * @param hkFundTxAcctDTOS
     * @param fundBasicInfoDTOList
     * @param queryCustFundBalResponseDTOS
     * @return
     */
    private static List<String> getFullFundHoldCode(List<HkFundTxAcctDTO> hkFundTxAcctDTOS, FundTxAcctTypeEnum full, List<FundBasicInfoDTO> fundBasicInfoDTOList, List<QueryCustFundBalResponseDTO> queryCustFundBalResponseDTOS) {
        List<String> fullFundCodeList = hkFundTxAcctDTOS.stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && full.getCode().equals(m.getFundTxAccType()))
                .map(HkFundTxAcctDTO::getFundTxAcctNo)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fullFundCodeList)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(fundBasicInfoDTOList)) {
            return queryCustFundBalResponseDTOS.stream()
                    .filter(f -> fullFundCodeList.contains(f.getFundTxAcctNo()))
                    .map(QueryCustFundBalResponseDTO::getFundCode)
                    .collect(Collectors.toList());
        }
        List<String> piggyFundCodeList = fundBasicInfoDTOList.stream()
                .map(FundBasicInfoDTO::getFundCode)
                .collect(Collectors.toList());
        return queryCustFundBalResponseDTOS.stream()
                .filter(f -> !piggyFundCodeList.contains(f.getFundCode()) && fullFundCodeList.contains(f.getFundTxAcctNo()))
                .map(QueryCustFundBalResponseDTO::getFundCode)
                .collect(Collectors.toList());
    }
}
