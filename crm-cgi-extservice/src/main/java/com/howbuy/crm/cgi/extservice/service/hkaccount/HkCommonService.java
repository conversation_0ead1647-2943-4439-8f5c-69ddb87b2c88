/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.hkaccount;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.crm.cgi.common.cacheservice.CacheKeyPrefix;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.constants.MarkConstants;
import com.howbuy.crm.cgi.common.constants.OutReturnCodes;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.SystemTypeEnum;
import com.howbuy.crm.cgi.common.enums.UploadIdTypeDescEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.exception.SessionTimeOutException;
import com.howbuy.crm.cgi.common.utils.MainLogUtils;
import com.howbuy.crm.cgi.common.utils.NameUtils;
import com.howbuy.crm.cgi.extservice.common.constant.ExternalConstant;
import com.howbuy.crm.cgi.extservice.common.enums.ExamCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.enums.FileBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.HkIdTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.InvstTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.FundSearchBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.app.HkAcctAppAdvertisingEnum;
import com.howbuy.crm.cgi.extservice.common.utils.AppEncUtils;
import com.howbuy.crm.cgi.extservice.common.utils.ParamsUtils;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.common.utils.ResponseUtils;
import com.howbuy.crm.cgi.extservice.common.utils.file.DefaultDownLoadFile;
import com.howbuy.crm.cgi.extservice.common.utils.file.FileUploadUtils;
import com.howbuy.crm.cgi.extservice.request.account.AdvertisementRequest;
import com.howbuy.crm.cgi.extservice.request.account.IdTypeRequest;
import com.howbuy.crm.cgi.extservice.request.account.OpenTranslateRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.*;
import com.howbuy.crm.cgi.extservice.request.dtmsproduct.QueryProductContractRequest;
import com.howbuy.crm.cgi.extservice.vo.ProductReportVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.*;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.*;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.*;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.ContractFundCodeBySearchDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.TradeRecodeFundCodeBySearchDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.FilePreviewReqDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.ProductOpenAccConfigDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.AdMobileDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.EmailSuffixListDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.ExchangeAnnounDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkAppCheckUpdateDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.*;
import com.howbuy.crm.cgi.manager.outerservice.CrmResult;
import com.howbuy.crm.cgi.manager.outerservice.baidu.BaiduTranslateOuterService;
import com.howbuy.crm.cgi.manager.outerservice.cms.CmsOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmtrade.CrmCounterFileOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.CustContractSignOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.QueryFundTxAcctOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.QueryHwDealOrderOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.ProductOpenAccOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkAccCommonOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkOpenAcctRiskOuterService;
import com.howbuy.dfile.HFileService;
import crm.howbuy.base.utils.DesUtil;
import crm.howbuy.base.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/12/1 13:51
 * @since JDK 1.8
 */
@Slf4j
@Service("hkCommonService")
public class HkCommonService {

    @Value("${applet.open.split.name.array}")
    private String surNameList;

    /**
     * 香港开户,职业信息中业务性质
     */
    @Value("${applet.open.occupation.business.nature}")
    private String occupationBusinessNature;

    @Value("${COMMON_AREA}")
    private String commonArea;

    @Value("${ID_TYPE_DESC}")
    private String idTypeDesc;

    @Value("${HB_HK_BANK_INFO}")
    private String hbHkBankInfoList;

    @Value("${CMS_COUNTRY_LIST_URL}")
    private String cmsCountryListUrl;

    @Value("${ID_URL}")
    private String idUrl;

    @Value("${FILE_SAMPLE}")
    private String fileSample;

    @Value("${area.certificate.type.list}")
    private String areaCertificateTypeList;

    @Value("${open.acc.id.type.list}")
    private String openAccIdTypeList;

    @Value("${applet.open.cms.bank.logo.key}")
    private String hkOpenAccCmsBankLogoKey;

    @Value("${cms_city_list_url}")
    private String cmsCityListUrl;

    @Value("${cms_product_report_url}")
    private String cmsProductReportUrl;

    @Value("${app.cms.ad.config}")
    private String appCmsAdConfig;

    // @Value("${app.foreign.guide.pdf.url}")
    // private String foreignGuidePdfUrl;

    @Value("${app.purchase.fund.transfer.bank.info}")
    private String transferBankInfoJson;

    @Value("${applet.derivative.pdfconfig}")
    private String cmsDerivativeConfig;

    @Autowired
    private HkAccCommonOuterService hkAccCommonOuterService;

    @Autowired
    private BaiduTranslateOuterService baiduTranslateOuterService;

    @Autowired
    private ProductOpenAccOuterService productOpenAccOuterService;

    @Autowired
    private CmsOuterService cmsOuterService;

    @Autowired
    private HkCustInfoOuterService hkCustInfoOuterService;

    @Autowired
    private HkOpenAcctRiskOuterService hkOpenAcctRiskOuterService;

    @Resource
    private QueryFundBasicInfoOuterService queryFundBasicInfoOuterService;

    @Resource
    private QueryFundTxAcctOuterService queryFundTxAcctOuterService;

    @Resource
    private CrmCounterFileOuterService crmCounterFileOuterService;

    @Resource
    private QueryHwDealOrderOuterService queryHwDealOrderOuterService;


    @Resource
    private CustContractSignOuterService custContractSignOuterService;

    protected static CacheService cacheService = CacheServiceImpl.getInstance();

    /**
     * 省市区数据
     */
    private static final String provCityCoutryKey = CacheKeyPrefix.HK_ACCOUNT_COMMON_PREFIX + "provcitycountry";

    /**
     * 国家地区数据
     */
    private static final String countryKey = CacheKeyPrefix.HK_ACCOUNT_COMMON_PREFIX + "country";

    /**
     * 国家地区原始数据
     */
    private static final String priCountryKey = CacheKeyPrefix.HK_ACCOUNT_COMMON_PREFIX + "pricountry";

    /**
     * @param countryCode
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.hkaccount.IdTypeVO>
     * @description:(获取对应列表的证件数据)
     * @author: xufanchao
     * @date: 2023/12/1 11:23
     * @since JDK 1.8
     */
    public List<IdTypeVO> getIdTypeVOList(String countryCode, String bizCode) {
        JSONArray jsonArray = new JSONArray();
        if (StringUtils.isNotBlank(bizCode)
                && HkOpenAcctBizTypeEnum.HK_OPEN_ACCT_BIZ_OPEN_ID_TYPE.getBizType().equals(bizCode)) {
            jsonArray = JSON.parseArray(openAccIdTypeList);
        }
        // 兜底方案,没有展示所有的证件类型
        if (CollectionUtils.isEmpty(jsonArray)) {
            jsonArray = JSON.parseArray(areaCertificateTypeList);
        }
        List<IdTypeVO> idTypeVOList = new ArrayList<>();
        // 查找指定国家码对应的 ID 类型列表
        for (Object obj : jsonArray) {
            JSONObject jsonObject = (JSONObject) obj;
            String idAreaCode = jsonObject.getString("idAreaCode").toUpperCase();
            JSONArray voList = jsonObject.getJSONArray("voList");
            if (!commonArea.contains(countryCode)) {
                countryCode = "OTHER";
            }
            if (countryCode.equals(idAreaCode) || "other".equals(idAreaCode)) {
                for (Object voObj : voList) {
                    JSONObject voObject = (JSONObject) voObj;
                    IdTypeVO idTypeVO = new IdTypeVO();
                    idTypeVO.setIdType(voObject.getString("idType"));
                    idTypeVO.setIdTypeDesc(voObject.getString("idTypeDesc"));
                    idTypeVOList.add(idTypeVO);
                }
                if (countryCode.equals(idAreaCode)) {
                    break;
                }
            }
        }
        return idTypeVOList;
    }

    /**
     * @param queryProductContractRequest
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.ProductReportListVO
     * @description:(查询产品报告数据)
     * @author: xufanchao
     * @date: 2024/2/26 18:28
     * @since JDK 1.8
     */
    public ProductReportListVO getProductReportList(QueryProductContractRequest queryProductContractRequest) {
        ProductReportListVO productReportListVO = new ProductReportListVO();
        // 根据当前登录用户的香港客户号获取对应的一账通号
        String hboneNo = hkCustInfoOuterService.getBindHboneNo(RequestUtil.getParameter(Constants.HKCUSTNO));
        String encValue = ParamsUtils.getEncValue(hboneNo);
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("hboneNo", encValue);
        paramMap.put("fundCode", queryProductContractRequest.getFundCode());
        // 默认值 获取产品报告
        paramMap.put("type", "2");
        // 0-不过滤
        paramMap.put("authFilter", "0");
        try {
            long startTime = System.currentTimeMillis();
            String productReportList = HttpUtils.get(cmsProductReportUrl, paramMap);
            long endTime = System.currentTimeMillis();
            MainLogUtils.httpCallOut(cmsProductReportUrl, OutReturnCodes.HTTP_SUCCESS_CODE, endTime - startTime);
            JSONObject jsonObject = JSON.parseObject(productReportList).getJSONObject("body");
            List<ProductReportVO> productReportVOList = Lists.newArrayList();
            if (!Objects.isNull(jsonObject) && !Objects.isNull(jsonObject.get("dataArray"))) {
                JSONArray jsonArray = jsonObject.getJSONArray("dataArray");
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject object = jsonArray.getJSONObject(i);
                    JSONArray reportArray = object.getJSONArray("reportArray");
                    for (int j = 0; j < reportArray.size(); j++) {
                        ProductReportVO productReportVO = new ProductReportVO();
                        productReportVO.setDate(reportArray.getJSONObject(j).get("date").toString());
                        productReportVO.setTitle(reportArray.getJSONObject(j).get("title").toString());
                        productReportVO.setUrl(reportArray.getJSONObject(j).get("url").toString());
                        productReportVOList.add(productReportVO);
                    }
                }
            }

            productReportListVO.setProductReportList(productReportVOList);
        } catch (IOException e) {
            log.error("error in getCountryList", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
        return productReportListVO;
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.CmsPdfVO
     * @description:(查询cms配置的pdf地址)
     * @author: xufanchao
     * @date: 2024/11/7 13:59
     * @since JDK 1.8
     */
    public CmsPdfVO queryDerivativePdf() {
        CmsPdfVO cmsPdfVO = new CmsPdfVO();
        try {
            // 获取CMS配置的pdf地址
            String pdfUrl = cmsOuterService.getDerivativePdfUrl(cmsDerivativeConfig);
            long startTime = System.currentTimeMillis();
            String s = HttpUtils.get(pdfUrl);
            long endTime = System.currentTimeMillis();
            MainLogUtils.httpCallOut(pdfUrl, OutReturnCodes.HTTP_SUCCESS_CODE, endTime - startTime);
            byte[] bytes = s.getBytes();
            cmsPdfVO.setPdfByteBuffer(ByteBuffer.wrap(bytes));
            if (pdfUrl != null) {
                cmsPdfVO.setPdfUrl(pdfUrl);
                return cmsPdfVO;
            }
        } catch (IOException e) {
            log.error("error in queryDerivativePdf", e);
        }
        return cmsPdfVO;
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.RiskExamListVO
     * @description:(查询风险评测问卷的题目)
     * @author: xufanchao
     * @date: 2024/11/7 16:04
     * @since JDK 1.8
     */
    public RiskExamListVO queryExamList() {

        RiskExamListVO riskExamListVO = new RiskExamListVO();
        // 获取问卷题目接口数据
        HkOpenAcctExamInfoDTO hkOpenAcctExamInfoDTO = hkOpenAcctRiskOuterService.queryRiskQuestionnaireInfoByCategory(
                InvstTypeEnum.PERS.getKey(), ExamCategoryEnum.DERIVATIVE.getKey());
        riskExamListVO.setBaseQuestions(createExamList(hkOpenAcctExamInfoDTO));
        riskExamListVO.setExamId(hkOpenAcctExamInfoDTO.getExamId());
        // 获取当前提交过的分数及其答案数据
        String hkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        HkDerAnswerHisInfoDTO derAnswerHisInfoDTO = hkOpenAcctRiskOuterService.queryHisRiskAnswerList(hkCustNo,
                ExamCategoryEnum.DERIVATIVE.getKey());
        List<HkDerAnswerVO> hkDerAnswerVOS = HkDerAnswerVO.buildToDerAnswerList(derAnswerHisInfoDTO.getAnswerList());
        // 假设这里已经初始化了 hkDerAnswerVOS

        // 创建一个空的 Map 来存储结果
        Map<String, List<String>> answerMap = new HashMap<>();

        // 遍历每个 HkDerAnswerVO 对象
        for (HkDerAnswerVO answerVO : hkDerAnswerVOS) {
            // 获取 optionId 和 option 列表
            String optionId = answerVO.getOptionId();
            List<String> options = Arrays.asList(answerVO.getOption());
            // 检查 optionId 和 options 是否为 null
            if (optionId != null && options != null) {
                // 确保 options 列表不为空
                if (!options.isEmpty()) {
                    // 如果 answerMap 中已经存在该 optionId，则获取现有的列表并添加新的选项
                    answerMap.computeIfAbsent(optionId, k -> new ArrayList<>()).addAll(options);
                }
            }
        }
        String derivativeKnowledge = YesNoEnum.NO.getCode();
        HkOpenAcctRiskCalculateResultDTO hkOpenAcctRiskCalculateResultDTO = hkCustInfoOuterService
                .queryHkOpenAccKycInfoInfo(hkCustNo);
        if (null != hkOpenAcctRiskCalculateResultDTO) {
            derivativeKnowledge = hkOpenAcctRiskCalculateResultDTO.getDerivativeKnowledge();
        }
        riskExamListVO.setDerivativeKnowledge(derivativeKnowledge);
        if (answerMap.size() > 0) {
            HkCalculAnswerDTO hkCalculAnswerDTO = hkOpenAcctRiskOuterService
                    .calculateDerivativeAnswer(hkOpenAcctExamInfoDTO.getExamId(), answerMap);
            riskExamListVO
                    .setAnswerList(CalculateDerivativeAnswerVO.buildToAnswerList(hkCalculAnswerDTO.getDetailMap()));
        }
        riskExamListVO.setRiskToleranceDate(derAnswerHisInfoDTO.getRiskToleranceDate());
        riskExamListVO.setDerivativeKnowledgeScore(derAnswerHisInfoDTO.getDerivativeKnowledgeScore());

        return riskExamListVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.CalculateDerivativeAnswerVO
     * @description:(获取衍生品试算结果)
     * @author: xufanchao
     * @date: 2024/11/7 19:04
     * @since JDK 1.8
     */
    public CalculateDerivativeAnswerVO calculateDerivativeAnswer(HkCalculateDerAnswerRequest request) {
        CalculateDerivativeAnswerVO calculateDerivativeAnswerVO = new CalculateDerivativeAnswerVO();
        Map<String, List<String>> answerMap1 = request.getAnswerMap().stream()
                .collect(Collectors.toMap(AnswerRequest::getKey, AnswerRequest::getValue));
        HkCalculAnswerDTO hkCalculAnswerDTO = hkOpenAcctRiskOuterService.calculateDerivativeAnswer(request.getExamId(),
                answerMap1);
        calculateDerivativeAnswerVO.setDerivativeKnowledgeScore(hkCalculAnswerDTO.getDerivativeKnowledgeScore());
        calculateDerivativeAnswerVO.setDerivativeKnowledge(hkCalculAnswerDTO.getDerivativeKnowledge());
        calculateDerivativeAnswerVO
                .setAnswerVOList(CalculateDerivativeAnswerVO.buildToAnswerList(hkCalculAnswerDTO.getDetailMap()));
        return calculateDerivativeAnswerVO;

    }

    /**
     * @param hkOpenAcctExamInfoDTO
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.RiskExamListVO.QuestionInfoVO>
     * @description:(数据格式转换)
     * @author: xufanchao
     * @date: 2024/11/7 18:32
     * @since JDK 1.8
     */
    private List<RiskExamListVO.QuestionInfoVO> createExamList(HkOpenAcctExamInfoDTO hkOpenAcctExamInfoDTO) {
        List<RiskExamListVO.QuestionInfoVO> questionInfoVOList = new ArrayList<>();
        if (hkOpenAcctExamInfoDTO == null) {
            return questionInfoVOList;
        }
        List<HkOpenAcctExamInfoDTO.RiskQuestionDTO> questionList = hkOpenAcctExamInfoDTO.getQuestionList();
        for (HkOpenAcctExamInfoDTO.RiskQuestionDTO riskQuestionDTO : questionList) {
            RiskExamListVO.QuestionInfoVO questionInfoVO = new RiskExamListVO.QuestionInfoVO();
            questionInfoVO.setQuestionId(riskQuestionDTO.getQuestionId());
            questionInfoVO.setQuestion(riskQuestionDTO.getQuestion());
            questionInfoVO.setQuestionClassify(riskQuestionDTO.getQuestionClassify());
            questionInfoVO.setClassifyName(riskQuestionDTO.getClassifyName());
            questionInfoVO.setSortNum(riskQuestionDTO.getSortNum());
            questionInfoVO.setMultipleOption(riskQuestionDTO.getSupportMulti());

            List<HkOpenAcctExamInfoDTO.RiskOptionDTO> optionList = riskQuestionDTO.getOptionList();
            // 构建选项数据列表
            questionInfoVO.setOptions(optionList.stream().map(it -> {
                RiskExamListVO.OptionInfoVO optionInfoVO = new RiskExamListVO.OptionInfoVO();
                optionInfoVO.setOptionId(it.getOptionId());
                optionInfoVO.setOptionChar(it.getOptionChar());
                optionInfoVO.setOptionDesc(it.getOptionDesc());
                optionInfoVO.setDefaultLevel(it.getDefaultLevel());
                optionInfoVO.setLimitType(it.getLimitType());
                optionInfoVO.setLimitValue(it.getLimitValue());
                optionInfoVO.setScore(it.getScore());
                optionInfoVO.setDerivativeFlag(it.getDerivativeFlag());
                return optionInfoVO;
            }).collect(Collectors.toList()));

            questionInfoVOList.add(questionInfoVO);
        }
        return questionInfoVOList;
    }

    /**
     * @param
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @description: 获取城市信息的Map
     * @author: jinqing.rao
     * @date: 2024/1/11 9:30
     * @since JDK 1.8
     */
    public Map<String, String> getCountryInfoMap() {
        CountryListVO countryList = getCountryList();
        Map<String, String> countryMap = new HashMap<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(countryList.getCountryVOList())) {
            countryList.getCountryVOList().stream().forEach(f -> {
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(f)) {
                    f.forEach(f1 -> countryMap.put(f1.getCountryCode(), f1.getChineseName()));
                }
            });
        }
        return countryMap;
    }

    /**
     * @param
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @description: 递归获取城市信息MAp
     * @author: jinqing.rao
     * @date: 2024/1/11 9:30
     * @since JDK 1.8
     */
    public Map<String, String> getCityInfoMap() {
        ProvinceCityCoutryVO provinceCityCoutryVO = getCityListVO();
        List<CityListVO> cityListVO = provinceCityCoutryVO.getCityListVO();
        // 将cityListVO递归遍历，获取到dm为key,mc为名称的Map
        return recursiveMap(cityListVO);
    }

    private static Map<String, String> recursiveMap(List<CityListVO> cityListVO) {
        Map<String, String> map = new HashMap<>();
        for (CityListVO city : cityListVO) {
            String dm = city.getDm();
            String mc = city.getMc();
            map.put(dm, mc);
            List<CityListVO> dataList = city.getDataList();
            if (dataList != null) {
                map.putAll(recursiveMap(dataList));
            }
        }
        return map;
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.CountryListVO
     * @description:(获取国家的列表信息--按照常用以及拼音排序)
     * @author: xufanchao
     * @date: 2023/12/1 16:55
     * @since JDK 1.8
     */
    public CountryListVO getCountryList() {

        CountryListVO countryListVO = new CountryListVO();
        List<String> indexList = new ArrayList<>();
        List<List<CountryVO>> countryVOSList = new ArrayList<>();
        Map<String, List<CountryVO>> map = new LinkedHashMap<>();
        if (cacheService.exists(countryKey)) {
            return JSON.parseObject(cacheService.get(countryKey), CountryListVO.class);
        }
        try {
            long startTime = System.currentTimeMillis();
            String countryObjList = HttpUtils.get(cmsCountryListUrl);
            long endTime = System.currentTimeMillis();
            MainLogUtils.httpCallOut(cmsCountryListUrl, OutReturnCodes.HTTP_SUCCESS_CODE, endTime - startTime);
            JSONObject jsonObject = JSON.parseObject(countryObjList);
            JSONArray countryList = jsonObject.getJSONObject("body").getJSONArray("dataList");
            // 常用的国家 及类型 CN|HK|MO|TW 读nacos配置
            for (Object country : countryList) {
                JSONObject countryObject = (JSONObject) country;
                // 当是常用国家的时候,索引为common
                if (commonArea.contains(countryObject.get("shortCode").toString())) {
                    CountryVO countryVO = new CountryVO();
                    countryVO.setCountryCode(countryObject.get("shortCode").toString());
                    countryVO.setChineseName(countryObject.get("ChineseName").toString().replace(" ", ""));
                    countryVO.setEnglishName(countryObject.get("EnglishName").toString());
                    countryVO.setIndex("常用");
                    if (map.containsKey("常用")) {
                        List<CountryVO> countryVOList = new ArrayList<>(map.get("常用"));
                        countryVOList.add(countryVO);
                        map.put("常用", countryVOList);
                    } else {
                        map.put("常用", Arrays.asList(countryVO));
                    }
                }
                // 当不在常用国家列表里面时，按照拼音的首字母进行排序，放在对应的list中
                else {
                    CountryVO countryVO = new CountryVO();
                    countryVO.setCountryCode(countryObject.get("shortCode").toString());
                    countryVO.setChineseName(countryObject.get("ChineseName").toString().replace(" ", ""));
                    countryVO.setIndex(countryObject.get("firstPinyin").toString());
                    countryVO.setEnglishName(countryObject.get("EnglishName").toString());

                    if (map.containsKey(countryObject.get("firstPinyin").toString())) {
                        List<CountryVO> countryVOList = new ArrayList<>(
                                map.get(countryObject.get("firstPinyin").toString()));
                        countryVOList.add(countryVO);
                        map.put(countryObject.get("firstPinyin").toString(), countryVOList);
                    } else {
                        map.put(countryObject.get("firstPinyin").toString(), Arrays.asList(countryVO));
                    }

                }
            }
            List<CountryVO> countryVOList = map.get("常用");

            // 定义排序顺序
            List<String> sortOrder = Arrays.asList("CN", "HK", "MO", "TW");
            List<CountryVO> commonCountryVOList = new ArrayList<>();
            for (String s : sortOrder) {
                CountryVO countryVO = countryVOList.stream().filter(it -> it.getCountryCode().equals(s))
                        .collect(Collectors.toList()).get(0);
                commonCountryVOList.add(countryVO);
            }

            // 增加常用的特殊处理
            indexList.add("常用");
            countryVOSList.add(commonCountryVOList);
            for (Map.Entry<String, List<CountryVO>> entry : map.entrySet()) {
                if (!entry.getKey().equals("常用")) {
                    indexList.add(entry.getKey());
                    countryVOSList.add(entry.getValue());
                }
            }

            countryListVO.setIndexList(indexList);
            countryListVO.setCountryVOList(countryVOSList);
            log.info("map,{}", JSON.toJSONString(countryListVO));
        } catch (Exception e) {
            log.error("error in getCountryList", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
        // 后续值丢缓存里面，过期重新获取
        cacheService.put(countryKey, JSON.toJSONString(countryListVO));
        cacheService.expires(countryKey, 60 * 60 * 24);
        return countryListVO;
    }

    /**
     * @param idTypeRequest
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.IdTypeDescVO
     * @description:(获取证件类型文案)
     * @author: xufanchao
     * @date: 2023/12/1 18:10
     * @since JDK 1.8
     */
    public IdTypeDescVO getIdTypeDesc(IdTypeRequest idTypeRequest) {
        IdTypeDescVO idTypeDescVO = new IdTypeDescVO();
        try {
            JSONArray jsonArray = JSONArray.parseArray(idTypeDesc);
            Map<String, String> map = new HashMap<>();

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String idType = jsonObject.getString("idType");
                String idTypeDesc = jsonObject.getString("idTypeDesc");
                map.put(idType, idTypeDesc);
            }
            if (map.containsKey(idTypeRequest.getIdType())) {
                String desc = map.get(idTypeRequest.getIdType());
                idTypeDescVO.setIdTypeDesc("上传" + desc.trim());
            }
            if (Constants.ID_TYPE_VALUE.contains(idTypeRequest.getIdType())) {
                idTypeDescVO.setIsIdCard(YesNoEnum.YES.getCode());
            } else {
                idTypeDescVO.setIsIdCard(YesNoEnum.NO.getCode());
            }
            JSONArray idUrlbjects = JSONObject.parseArray(idUrl);
            for (int i = 0; i < idUrlbjects.size(); i++) {
                JSONObject jsonObject = idUrlbjects.getJSONObject(i);
                if (jsonObject.getString("idType").equals(idTypeRequest.getIdType())) {
                    idTypeDescVO.setIdUrl(jsonObject.get("idUrl").toString());
                }
            }
            List<String> desc = UploadIdTypeDescEnum.getDesc(idTypeRequest.getIdType());
            idTypeDescVO.setUpLoadIdCardDescList(desc);
            if (!idTypeRequest.getIdType().equals(HkIdTypeEnum.FOREIGN_PASSPORT.getKey())) {
                return idTypeDescVO;
            }
            JSONArray countryList;
            if (cacheService.exists(priCountryKey)) {
                countryList = cacheService.get(priCountryKey);
            } else {
                long startTime = System.currentTimeMillis();
                String countryObjList = HttpUtils.get(cmsCountryListUrl);
                long endTime = System.currentTimeMillis();
                MainLogUtils.httpCallOut(cmsCountryListUrl, OutReturnCodes.HTTP_SUCCESS_CODE, endTime - startTime);
                JSONObject jsonObject = JSON.parseObject(countryObjList);
                countryList = jsonObject.getJSONObject("body").getJSONArray("dataList");
                cacheService.put(priCountryKey, countryList);
                cacheService.expires(priCountryKey, 60 * 60 * 24);
            }
            for (Object country : countryList) {
                JSONObject countryObject = (JSONObject) country;
                if (idTypeRequest.getIdAreaCode().equals(countryObject.get("shortCode"))) {
                    idTypeDescVO.setIdTypeDesc("上传" + countryObject.get("ChineseName").toString().trim() + "护照");
                    return idTypeDescVO;
                }
            }

        } catch (Exception e) {
            log.error("error in getCountryList", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
        return idTypeDescVO;
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.CityListVO
     * @description:(获取省市区列表接口)
     * @author: xufanchao
     * @date: 2023/12/14 16:14
     * @since JDK 1.8
     */
    public ProvinceCityCoutryVO getCityListVO() {
        ProvinceCityCoutryVO provinceCityCoutryVO = new ProvinceCityCoutryVO();
        if (cacheService.exists(provCityCoutryKey)) {
            return JSON.parseObject(cacheService.get(provCityCoutryKey), ProvinceCityCoutryVO.class);
        }
        try {
            long startTime = System.currentTimeMillis();
            String cityList = HttpUtils.get(cmsCityListUrl);
            long endTime = System.currentTimeMillis();
            MainLogUtils.httpCallOut(cmsCityListUrl, OutReturnCodes.HTTP_SUCCESS_CODE, endTime - startTime);
            JSONObject JSONObject = JSON.parseObject(cityList).getJSONObject("body");
            JSONArray jsonArray = JSONObject.getJSONArray("dataList");
            List<CityListVO> cityListVOList = JSON.parseArray(jsonArray.toJSONString(), CityListVO.class);
            provinceCityCoutryVO.setCityListVO(cityListVOList);
            // 把省市区数据丢缓存存24小时
            cacheService.put(provCityCoutryKey, JSON.toJSONString(provinceCityCoutryVO));
            cacheService.expires(provCityCoutryKey, 60 * 60 * 24);
            return provinceCityCoutryVO;
        } catch (IOException e) {
            log.error("error in getCityListVO", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.SampleFileVO
     * @description:(获取示例文件)
     * @author: xufanchao
     * @date: 2024/1/2 09:59
     * @since JDK 1.8
     */
    public SampleFileVO getSampleFileVO() {
        // 获取基本的配置文件
        SampleFileVO sampleFileVO = JSON.parseObject(fileSample, SampleFileVO.class);
        // 调接口获取开户示例文件 条款及细则 ## 复杂产品警告声明
        CrmResult<ProductOpenAccConfigDTO> productOpenAccConfigDTOCrmResult = productOpenAccOuterService.queryList();
        String complesProductStatement = productOpenAccConfigDTOCrmResult.getData().getComplesProductStatement();
        String complesProductStatementPath = productOpenAccConfigDTOCrmResult.getData()
                .getComplesProductStatementPath();
        String termsAndConditionsPath = productOpenAccConfigDTOCrmResult.getData().getTermsAndConditionsPath();
        String termsAndConditions = productOpenAccConfigDTOCrmResult.getData().getTermsAndConditions();
        String personInformationStatement = productOpenAccConfigDTOCrmResult.getData().getPersonInformationStatement();
        String personInformationStatementPath = productOpenAccConfigDTOCrmResult.getData()
                .getPersonInformationStatementPath();
        try {
            complesProductStatement = URLEncoder.encode(complesProductStatement, "UTF-8").replaceAll("\\+", "%20");
            termsAndConditions = URLEncoder.encode(termsAndConditions, "UTF-8").replaceAll("\\+", "%20");
            personInformationStatement = URLEncoder.encode(personInformationStatement, "UTF-8").replaceAll("\\+",
                    "%20");
        } catch (Exception e) {
            log.error("getSampleFileVO>>>> 文件名称 URLEncoder 失败", e);
        }
        String complesfilePrewViewUrl = getFilePrewViewUrl(complesProductStatement, complesProductStatementPath);
        String termsAndConditionsPrewViewUrl = getFilePrewViewUrl(termsAndConditions, termsAndConditionsPath);
        String personInformationStatementPrewViewUrl = getFilePrewViewUrl(personInformationStatement,
                personInformationStatementPath);
        sampleFileVO.setComplesProductStatement(complesfilePrewViewUrl);
        sampleFileVO.setTermsAndConditions(termsAndConditionsPrewViewUrl);
        sampleFileVO.setPersonInformationStatement(personInformationStatementPrewViewUrl);
        return sampleFileVO;
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.EmailSuffixListVO
     * @description:(获取邮箱尾缀)
     * @author: xufanchao
     * @date: 2023/12/4 14:22
     * @since JDK 1.8
     */
    public EmailSuffixListVO getEmailSuffix() {
        EmailSuffixListVO emailSuffixListVO = new EmailSuffixListVO();
        EmailSuffixListDTO emailSuffix = hkAccCommonOuterService.getEmailSuffix();
        emailSuffixListVO.setEmailSuffixList(emailSuffix.getEmailSuffixList());
        return emailSuffixListVO;
    }

    /**
     * @param chineseList 中文汉字
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenTranslateVO
     * @description: 中文转拼音
     * @author: jinqing.rao
     * @date: 2023/12/6 13:36
     * @since JDK 1.8
     */
    public OpenTranslatePinYinVO translateToPinYin(List<OpenTranslateRequest.OpenTranslateInfo> chineseList) {
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        // 遍历翻译
        List<OpenTranslatePinYinVO.OpenTranslatePYInfo> openTranslatePinYinInfos = chineseList.stream().map(m -> {
            StringBuilder stringBuffer = new StringBuilder();
            if (StringUtils.isBlank(m.getChinese())) {
                return OpenTranslatePinYinVO.OpenTranslatePYInfo.builder().uuidKey(m.getUuidKey())
                        .pinyin(stringBuffer.toString()).build();
            }
            try {
                for (char c : m.getChinese().toCharArray()) {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        char f = pinyinArray[0].charAt(0);// 大写第一个字母
                        String pinyin = String.valueOf(f).toUpperCase().concat(pinyinArray[0].substring(1));// 首写大写
                        stringBuffer.append(pinyin);
                    } else {
                        stringBuffer.append(c);
                    }
                }
            } catch (Exception e) {
                log.warn("HkCommonService>>>translateToPinYin 中文转拼音异常 params:{}", m.getChinese(), e);
            }
            return OpenTranslatePinYinVO.OpenTranslatePYInfo.builder().uuidKey(m.getUuidKey())
                    .pinyin(stringBuffer.toString()).build();
        }).collect(Collectors.toList());
        return OpenTranslatePinYinVO.builder().translateList(openTranslatePinYinInfos).build();
    }

    /**
     * @param custName 中文名字
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.SplitNameVO
     * @description: 中文名字拆分
     * @author: jinqing.rao
     * @date: 2023/12/6 19:59
     * @since JDK 1.8
     */
    public SplitNameVO splitName(String custName) {
        List<String> splitNameList = JSON.parseArray(surNameList, String.class);
        Map<String, String> surNameMap = NameUtils.getSurName(splitNameList, custName);
        return new SplitNameVO(surNameMap.get(NameUtils.SUR_NAME), surNameMap.get(NameUtils.NAME));
    }

    /**
     * @param request
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.hkacc.AdMobileDTO>
     * @description:(获取广告位数据)
     * @author: xufanchao
     * @date: 2024/3/5 10:18
     * @since JDK 1.8
     */
    public List<AdMobileDTO> getAdMobileList(AdvertisementRequest request) {
        List<AdMobileDTO> adMobileList = hkAccCommonOuterService.getAdMobileList(request.getAppVersion(),
                request.getSystemType());
        return adMobileList;
    }

    /**
     * @param adMobileDTOList
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.AdMobileListVO
     * @description:(广告列表转换为对应的实体列表)
     * @author: xufanchao
     * @date: 2023/12/7 16:37
     * @since JDK 1.8
     */
    public AdMobileListVO transAdMobileDtoToVO(List<AdMobileDTO> adMobileDTOList) {
        AdMobileListVO adMobileListVO = new AdMobileListVO();
        List<AdMobileVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(adMobileDTOList)) {
            adMobileDTOList.forEach(it -> {
                AdMobileVO adMobileVO = new AdMobileVO();
                adMobileVO.setId(it.getId());
                adMobileVO.setCId(it.getCId());
                adMobileVO.setImgHeight(it.getImgHeight());
                adMobileVO.setImgWidth(it.getImgWidth());
                adMobileVO.setOnClick(it.getOnClick());
                adMobileVO.setAdTitle(it.getAdTitle());
                adMobileVO.setAdImg(it.getAdImg());
                list.add(adMobileVO);
            });
        }
        adMobileListVO.setList(list);
        return adMobileListVO;
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.HbHkBankAcctInfoVOList
     * @description:(返回好买香港账户信息)
     * @author: xufanchao
     * @date: 2023/12/14 19:48
     * @since JDK 1.8
     */
    public HbHkBankAcctInfoVOList gethbHkBankAcctInfo() {
        HbHkBankAcctInfoVOList hbHkBankAcctInfoVOList = new HbHkBankAcctInfoVOList();
        List<HbHkBankAcctInfoVO> hbHkBankAcctInfoVOS = JSON.parseArray(hbHkBankInfoList, HbHkBankAcctInfoVO.class);
        String swiftCode = hbHkBankAcctInfoVOS.get(0).getSwiftCode();
        // 获取CMS配置的银行卡图标
        Map<String, String> cmsConfigInfo = cmsOuterService.getCmsConfigInfo(hkOpenAccCmsBankLogoKey);
        // 未匹配,获取默认图片
        String url = cmsConfigInfo.get(swiftCode);
        if (StringUtils.isBlank(url)) {
            url = cmsConfigInfo.get("MRYHLOGO");
        }
        hbHkBankAcctInfoVOS.get(0).setDefaultLogo(url);
        hbHkBankAcctInfoVOList.setHbHkBankAcctInfoVOList(hbHkBankAcctInfoVOS);
        return hbHkBankAcctInfoVOList;
    }

    /**
     * @param request 需要翻译的请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.OpenTranslateForeignVO
     * @description: 翻译
     * @author: jinqing.rao
     * @date: 2023/12/21 16:31
     * @since JDK 1.8
     */
    public OpenTranslateForeignVO translateToEnglish(OpenTranslateRequest request) {
        List<BaiduTranslateForeignInfoDTO> translateInfos = request.getOpenTranslateInfoList().stream().map(m -> {

            return BaiduTranslateForeignInfoDTO.builder()
                    .uuidKey(m.getUuidKey())
                    .chineseLanguage(StringUtils.isBlank(m.getChinese()) ? m.getChinese() : m.getChinese().trim())
                    .build();
        }).collect(Collectors.toList());
        BaiduTranslateForeignRequestDTO foreignRequestDTO = BaiduTranslateForeignRequestDTO.builder()
                .from("auto")
                .to("en")
                .openTranslateInfoList(translateInfos)
                .build();
        BaiduTranslateForeignResponseDTO foreignResponseDTO = baiduTranslateOuterService.translate(foreignRequestDTO);

        List<OpenTranslateForeignVO.OpenTranslateForeignInfo> openTranslatePinYinInfos = foreignResponseDTO
                .getTranslateList().stream().map(m -> {
                    return OpenTranslateForeignVO.OpenTranslateForeignInfo.builder().uuidKey(m.getUuidKey())
                            .chineseLanguage(m.getChineseLanguage()).foreignLanguage(m.getForeignLanguage()).build();
                }).collect(Collectors.toList());
        return OpenTranslateForeignVO.builder().translateList(openTranslatePinYinInfos).build();
    }

    /**
     * @param bizType 业务类型
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctBizEnumVO
     * @description: 根据业务类型获取对应的枚举
     * @author: jinqing.rao
     * @date: 2023/12/19 19:13
     * @since JDK 1.8
     */
    public HkOpenAcctBizEnumVO queryBizEnumByType(String bizType) {

        if (HkOpenAcctBizTypeEnum.HK_OPEN_ACCT_BIZ_BUSINESS_TYPE.getBizType().equals(bizType)) {
            List<JSONObject> businessNatureEnumList = JSON.parseArray(occupationBusinessNature, JSONObject.class);
            List<HkOpenAcctBizEnumVO.BizEnumInfo> collected = businessNatureEnumList.stream()
                    .map(m -> HkOpenAcctBizEnumVO.BizEnumInfo.builder().bizCode(m.getString("enumCode"))
                            .bizDesc(m.getString("enumDesc")).build())
                    .collect(Collectors.toList());
            return HkOpenAcctBizEnumVO.builder().bizEnumInfoList(collected).build();
        }
        return HkOpenAcctBizEnumVO.builder().build();
    }

    /**
     * @param fileName
     * @param filePath
     * @return java.lang.String
     * @description:(获取开户文件的预览地址)
     * @author: xufanchao
     * @date: 2024/1/11 11:10
     * @since JDK 1.8
     */
    public String getFilePrewViewUrl(String fileName, String filePath) {
        FilePreviewReqDTO reqDTO = new FilePreviewReqDTO();
        reqDTO.setFileName(fileName);
        reqDTO.setFilePath(filePath);
        return productOpenAccOuterService.getpreviewurl(reqDTO).getData().getPreviewFileUrl();
    }

    public HkAppAnnouncementVO getAppAnnouncementNotice(HkAppAnnouncementRequest request) {
        HkAppAnnouncementVO hkAppAnnouncementVO = new HkAppAnnouncementVO();
        String productId = "145217193";
        List<ExchangeAnnounDTO> appAnnouncementNotice = hkAccCommonOuterService.getAppAnnouncementNotice(productId,
                request.getPosition());
        if (CollectionUtils.isEmpty(appAnnouncementNotice)) {
            return hkAppAnnouncementVO;
        }
        // 筛选状态为1的公告数据 状态的枚举是 0 为 未开始 1为正在进行中 -1为已过期
        List<ExchangeAnnounDTO> validAnnouncements = appAnnouncementNotice.stream()
                .filter(it -> null != it.getStatus() && it.getStatus().compareTo(1) == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validAnnouncements)) {
            return hkAppAnnouncementVO;
        }
        // validAnnouncements
        // 根据【顺序】字段排序，优先取第一条记录；若【顺序】一致，根据【起始时间】排序，优先取起始时间最近的第一条记录；若【顺序】、【起始时间】均一致，按记录入库时的id排序，优先取第一条记录。
        validAnnouncements.sort(Comparator.comparing(ExchangeAnnounDTO::getSeq)
                .thenComparing(ExchangeAnnounDTO::getStartTime).thenComparing(ExchangeAnnounDTO::getId));
        // validAnnouncements 根据位置分组,并且获取每个分组的第一条数据
        Map<String, List<ExchangeAnnounDTO>> announcementsMap = validAnnouncements.stream()
                .collect(Collectors.groupingBy(ExchangeAnnounDTO::getPosition));
        List<ExchangeAnnounDTO> firstAnnouncements = announcementsMap.values().stream().map(it -> it.get(0))
                .collect(Collectors.toList());
        // 转换成ExchangeAnnounVO
        List<HkAppAnnouncementVO.HkAppAnnouncementInfo> appAnnouncementInfos = firstAnnouncements.stream().map(it -> {
            HkAppAnnouncementVO.HkAppAnnouncementInfo hkAppAnnouncementInfo = new HkAppAnnouncementVO.HkAppAnnouncementInfo();
            hkAppAnnouncementInfo.setPosition(it.getPosition());
            hkAppAnnouncementInfo.setImportant(it.getImportant());
            hkAppAnnouncementInfo.setSeq(it.getSeq());
            hkAppAnnouncementInfo.setDesc(it.getDesc());
            hkAppAnnouncementInfo.setLink(it.getLink());
            hkAppAnnouncementInfo.setProductId(it.getProductId());
            return hkAppAnnouncementInfo;
        }).collect(Collectors.toList());
        hkAppAnnouncementVO.setAnnouncementList(appAnnouncementInfos);
        return hkAppAnnouncementVO;
    }

    /**
     * @param msg      加密参数
     * @param response 响应体
     * @return void
     * @description: 根据加密参数获取解密后的参数, 根据文件路劲获取文件流
     * @author: jinqing.rao
     * @date: 2024/2/27 18:01
     * @since JDK 1.8
     */
    public void downloadPdf(String msg, HttpServletResponse response) {
        // 默认对url解码下
        try {
            String decode = URLDecoder.decode(msg, "UTF-8");
            msg = decode;
        } catch (Exception e) {
            log.info("downloadPdf >>>> 解码异常,不做任何处理 msg:{}", msg);
        }
        String hkCustNo = null;
        // 根据URL,获取文件名,URL
        String[] split = msg.split("/");
        if (split == null || split.length != 4) {

            // 后续改造 msg 是加密参数,需要解密
            hkCustNo = getdecryptHkCustNoString(msg, hkCustNo);
        } else {
            // 证件类型 split[0] ,香港客户号 split[1],时间戳 split[2],文件名称 split[3]
            hkCustNo = split[1];
        }
        // 越权校验
        // String loginHkCustNo = getLoginHkCustNo();
        // if (StringUtils.isBlank(hkCustNo) || !loginHkCustNo.equals(hkCustNo)) {
        // throw new
        // BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_FILE_NOT_EXIST);
        // }
        // 获取文件格式
        String imageFormat = msg.substring(MarkConstants.SEPARATOR_DOT.length());
        // 相对路径
        String relativePath = split[0] + MarkConstants.SEPARATOR_SLASH + split[1] + MarkConstants.SEPARATOR_SLASH
                + split[2];
        ServletOutputStream outputStream = null;
        try {
            // 获取文件流
            byte[] bytes = HFileService.getInstance().read2Bytes(ExternalConstant.PAY_VOUCHER_TEMP_STORE_CONFIG,
                    relativePath, split[3]);

            // 设置文件名
            String fileName = split[3];
            // 获取文件格式后缀
            String type = fileName.substring(fileName.lastIndexOf(".") + 1);
            String contentTypeByFileExtension = ResponseUtils.getContentTypeByFileExtension(type);
            /// 设置文件格式
            response.setContentType(contentTypeByFileExtension);
            try {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } catch (Exception e) {
                fileName = "file" + type;
            }
            response.reset();
            // 设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename="
                    + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            // 输出文件流
            outputStream = response.getOutputStream();
            outputStream.write(bytes);
        } catch (Exception e) {
            throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_FILE_DOWNLOAD_ERROR);
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("HkCommonService>>>downloadPdf 关闭文件流异常", e);
                }
            }
        }
    }

    private static String getLoginHkCustNo() {
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        try {
            String decrypt = DesUtil.decrypt(loginHkCustNo, Constants.HK_APP_CUST_NO_KEY);
            loginHkCustNo = StringUtils.isNotEmpty(decrypt) ? decrypt : loginHkCustNo;
        } catch (Exception e) {
            log.info("downloadPdf >>>> 香港客户号解密失败 LoginHkCustNo : {}", loginHkCustNo);
        }
        return loginHkCustNo;
    }

    private static String getdecryptHkCustNoString(String msg, String hkCustNo) {
        try {
            String decryptJson = DesUtil.decrypt(msg, Constants.HK_APP_CUST_NO_KEY);
            JSONObject jsonObject = JSONObject.parseObject(decryptJson);
            if (null != jsonObject) {
                hkCustNo = jsonObject.getString(Constants.HKCUSTNO);
            }
        } catch (Exception e) {
            throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_FILE_NOT_EXIST);
        }
        return hkCustNo;
    }

    /**
     * @param request 请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkAcctAdvertisVO
     * @description: 海外App广告位查询接口
     * @author: jinqing.rao
     * @date: 2024/2/29 14:05
     * @since JDK 1.8
     */
    public HkAcctAdvertisVO getAppAdvertising(HkAcctAppAdvertisRequest request) {
        HkAcctAdvertisVO hkAcctAdvertisVO = new HkAcctAdvertisVO();
        String position = request.getPosition();
        HkAcctAppAdvertisingEnum advertisingEnum = HkAcctAppAdvertisingEnum.getEnumByCode(position);
        if (null == advertisingEnum) {
            throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_APP_ADVERTISING_POSITION_ERROR);
        }
        // 从用户的请求头中获取公共参数信息
        String appVersion = RequestUtil.getPublicParams(Constants.VERSION);
        String publicParams = RequestUtil.getPublicParams(Constants.PAR_PHONE_MODEL);
        String cId = SystemTypeEnum.getCode(publicParams);
        if (StringUtils.isBlank(cId)) {
            cId = SystemTypeEnum.IOS.getCode();
        }
        // 获取配置的cms广告配置
        JSONObject jsonObject = JSON.parseObject(appCmsAdConfig);
        JSONObject appCenterJson = jsonObject.getJSONObject(advertisingEnum.getValue());
        if (null == appCenterJson) {
            log.info("getAppAdvertising>>>>>app 获取广告配置信息 为空 position:{},hkCustNo:{}", position, request.getHkCustNo());
            return hkAcctAdvertisVO;
        }
        String key = appCenterJson.getString("key");
        String channelId = appCenterJson.getString("channelId");
        String adWidth = appCenterJson.getString("adWidth");
        String adHeight = appCenterJson.getString("adHeight");
        List<AdMobileDTO> adMobileList = hkAccCommonOuterService.getAdMobileList(appVersion, cId, key, channelId,
                adWidth, adHeight);
        if (CollectionUtils.isEmpty(adMobileList)) {
            return hkAcctAdvertisVO;
        }
        List<HkAcctAdvertisVO.AdvertisingInfo> collect = adMobileList.stream().map(m -> {
            HkAcctAdvertisVO.AdvertisingInfo advertisingInfo = new HkAcctAdvertisVO.AdvertisingInfo();
            advertisingInfo.setAdTitle(m.getAdTitle());
            advertisingInfo.setAdOrder(null == m.getAdOrder() ? "0" : m.getAdOrder().toString());
            advertisingInfo.setAdImg(m.getAdImg());
            advertisingInfo.setOnClick(m.getOnClick());
            advertisingInfo.setVerifyAccount(m.getExtendFieldOne());
            return advertisingInfo;
        }).collect(Collectors.toList());
        hkAcctAdvertisVO.setAdvertisings(collect);
        return hkAcctAdvertisVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkAppUpdateVO
     * @description: 检查App是否需要更新
     * @author: jinqing.rao
     * @date: 2024/2/29 15:49
     * @since JDK 1.8
     */
    public HkAppUpdateVO checkHkAppUpdate(HkAppUpdateRequest request) {
        HkAppCheckUpdateDTO hkAppCheckUpdateDTO = cmsOuterService.checkHkAppUpdate(request.getParPhoneModel(),
                request.getSubPhoneModel(), request.getVersion(), request.getProductId(), request.getChannelId());
        if (null == hkAppCheckUpdateDTO) {
            return HkAppUpdateVO.builder().build();
        }
        return HkAppUpdateVO.builder().fileSize(hkAppCheckUpdateDTO.getFileSize())
                .updateUrl(hkAppCheckUpdateDTO.getUpdateUrl())
                .updateDesc(hkAppCheckUpdateDTO.getUpdateDesc())
                .versionNum(hkAppCheckUpdateDTO.getVersionNum())
                .versionNeedUpdate(hkAppCheckUpdateDTO.getVersionNeedUpdate())
                .foreignGuide(hkAppCheckUpdateDTO.getUpdateUrl())
                .build();
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.ExchangeAnnounVO
     * @description:(获取公告位数据)
     * @author: xufanchao
     * @date: 2024/3/5 10:19
     * @since JDK 1.8
     */
    public ExchangeAnnounVO getExchangeAnnoun() {
        ExchangeAnnounVO exchangeAnnounVO = new ExchangeAnnounVO();
        ExchangeAnnounDTO exchangeAnnounDTO = hkAccCommonOuterService.getExchangeAnnounDTO();
        if (Objects.isNull(exchangeAnnounDTO)) {
            return null;
        }
        exchangeAnnounVO.setSeq(exchangeAnnounDTO.getSeq());
        exchangeAnnounVO.setLink(exchangeAnnounDTO.getLink());
        exchangeAnnounVO.setDesc(exchangeAnnounDTO.getDesc());
        exchangeAnnounVO.setPosition(exchangeAnnounDTO.getPosition());
        exchangeAnnounVO.setImportant(exchangeAnnounDTO.getImportant());
        exchangeAnnounVO.setProductId(exchangeAnnounDTO.getProductId());
        return exchangeAnnounVO;
    }

    /**
     * @param showAsset
     * @return void
     * @description:(保存资产状态)
     * @author: xufanchao
     * @date: 2024/3/5 10:20
     * @since JDK 1.8
     */
    public void saveAssetStatus(String showAsset) {
        try {
            String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
            // 接口测试数据提交
            hkAccCommonOuterService.saveAssetStatus(loginHkCustNo, showAsset);
        } catch (SessionTimeOutException e) {
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @param msg      加密字符串,包含文件地址,香港客户号,文件的业务类型,文件名称
     * @param response 文件流
     * @return void
     * @description: 根据加密请求参数, 下载文件
     * @author: jinqing.rao
     * @date: 2024/5/11 15:40
     * @since JDK 1.8
     */
    public void downloadFile(String msg, HttpServletResponse response) {
        // 解析加密文件,获取hkCustNo和文件流路径数据
        DefaultDownLoadFile downLoadFile = FileUploadUtils.downloadFile(msg);
        String hkCustNo = downLoadFile.getHkCustNo();
        // 越权校验
        String LoginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        if (StringUtils.isBlank(hkCustNo) || !LoginHkCustNo.equals(hkCustNo)) {
            log.error("downloadFile >>> 越权获取文件流,hkCustNo:{},LoginHkCustNo:{}", hkCustNo, LoginHkCustNo);
            throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_FILE_NOT_EXIST);
        }
        if(FileBizTypeEnum.CRM_PAYMENT_VOUCHER_FILE.equals(downLoadFile.getFileBizTypeEnum())){
            // crm-trade的文件，调接口
            crmCounterFileOuterService.docPreViewOnline(downLoadFile.getFileId(),null, response);
        }else {
            downLoadFile.downloadFile(response);
        }
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.HkOpenImageVO
     * @description: webdav文件流上传
     * @author: jinqing.rao
     * @date: 2024/4/29 16:57
     * @since JDK 1.8
     */
    public HkOpenImageVO uploadFile(HkAppUploadFileRequest request) {
        // 越权校验,比对请求中的香港客户号和登录的客户号
        String hkCustNo = AppEncUtils.decrypt(request.getHkCustNo());
        HkOpenImageVO hkOpenImageVO = new HkOpenImageVO();
        List<ImageVO> openImageVOList = new ArrayList<>();
        for (MultipartFile multipartFile : request.getFile()) {
            ImageVO imageVO = FileUploadUtils.uploadFile(multipartFile, request.getImageType(), hkCustNo);
            openImageVOList.add(imageVO);
        }
        hkOpenImageVO.setOpenImageVOList(openImageVOList);
        return hkOpenImageVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkRemittanceBankResponse
     * @description: 查询付款银行信息
     * @author: jinqing.rao
     * @date: 2024/8/5 13:30
     * @since JDK 1.8
     */
    public HkRemittanceBankVO queryRemittanceBankInfo(HkRemittanceBankRequest request) {
        HkRemittanceBankVO hkRemittanceBankVO = new HkRemittanceBankVO();
        if (StringUtils.isNotBlank(transferBankInfoJson)) {
            HkRemittanceBankVO.TransferBankInfo transferBankInfo = JSON.parseObject(transferBankInfoJson, HkRemittanceBankVO.TransferBankInfo.class);
            hkRemittanceBankVO.setTransferBankInfos(transferBankInfo);
        }
        return hkRemittanceBankVO;
    }

    /**
     * @param request
     * @return void
     * @description:(提交答案接口)
     * @author: xufanchao
     * @date: 2024/11/7 19:11
     * @since JDK 1.8
     */
    public void submitAnswer(HkCalculateDerAnswerRequest request) {
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        try {
            Map<String, List<String>> answerMap1 = request.getAnswerMap().stream()
                    .collect(Collectors.toMap(AnswerRequest::getKey, AnswerRequest::getValue));
            hkOpenAcctRiskOuterService.submitAnswer(request.getExamId(), loginHkCustNo, answerMap1);
        } catch (Exception e) {
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(),
                    ExceptionCodeEnum.SYSTEM_ERROR.getDescription());
        }
    }

    /**
     * @description: 通过基金编码或者基金名称,模糊查询基金信息
     * @param request 请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.FundSearchVO
     * @author: jinqing.rao
     * @date: 2025/3/31 10:59
     * @since JDK 1.8
     */
    public FundSearchVO queryFundBySearchValue(FundSearchRequest request) {
        String bizType = request.getBizType();
        if(StringUtils.isEmpty(bizType)){
            bizType = FundSearchBizTypeEnum.TRADE_RECORD.getCode();
        }
        // 类型校验
        FundSearchBizTypeEnum searchBizTypeEnum = FundSearchBizTypeEnum.getByCode(bizType);
        if(null == searchBizTypeEnum){
            throw new BusinessException(ExceptionCodeEnum.FUND_SEARCH_TYPE_ERROR);
        }

        switch (searchBizTypeEnum) {
            case TRADE_RECORD:
                return  queryTradeRecodeFundCodeByKeyword(request.getHkCustNo(), request.getSearchValue());
            case CONTRACT:
                return queryContractFundCodeByKeyword(request.getHkCustNo(), request.getSearchValue());
            default:
                break;
        }

        // 默认模糊查询前10条数据
        List<FundBasicInfoDTO> fundBasicInfoDTOS = queryFundBasicInfoOuterService
                .queryFundBySearchValue(request.getSearchValue(), 1, 10);
        if (CollectionUtils.isEmpty(fundBasicInfoDTOS)) {
            return FundSearchVO.builder().fundList(new ArrayList<>()).build();
        }
        List<FundSearchVO.FundInfo> fundInfos = fundBasicInfoDTOS.stream()
                .map(HkCommonService::getFundInfo)
                .collect(Collectors.toList());
        return FundSearchVO.builder().fundList(fundInfos).build();
    }

    /**
     * @description: 通过基金名称/基金简称/基金英文名称/基金编码  检索用户所所持有的基金产品信息
     * @param hkCustNo	香港客户号
     * @param searchKeyword 检索关键词
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.FundSearchVO
     * @author: jinqing.rao
     * @date: 2025/7/7 10:04
     * @since JDK 1.8
     */
    private FundSearchVO queryContractFundCodeByKeyword(String hkCustNo, String searchKeyword) {
        // 调用manager层接口查询基金列表
        List<ContractFundCodeBySearchDTO> contractFundCodeList = custContractSignOuterService.queryContractFundCodeBySearch(hkCustNo, searchKeyword);

        if(CollectionUtils.isEmpty(contractFundCodeList)){
            return FundSearchVO.builder().fundList(new ArrayList<>()).build();
        }
        List<FundSearchVO.FundInfo> fundList = contractFundCodeList.stream()
                .map(m -> FundSearchVO.FundInfo.builder()
                        .fundCode(m.getFundCode())
                        .fundName(m.getFundShortName())
                        .fundNameEn(m.getFundEnName())
                        .build())
                .collect(Collectors.toList());

        return FundSearchVO.builder().fundList(fundList).build();
    }

    /**
     * @param hkCustNo 香港客户号
     * @param searchKeyword 基金搜索关键字
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.FundSearchVO
     * @description: 交易记录基金模糊查询接口
     * @author: jinqing.rao
     * @date: 2025/6/30 10:00:00
     * @since JDK 1.8
     */
    public FundSearchVO queryTradeRecodeFundCodeByKeyword(String hkCustNo, String searchKeyword) {
        // 调用manager层接口查询基金列表
        List<TradeRecodeFundCodeBySearchDTO> orderFundCodeList = queryHwDealOrderOuterService.queryTradeRecodeFundCodeBySearch(hkCustNo, searchKeyword);

        if(CollectionUtils.isEmpty(orderFundCodeList)){
            return FundSearchVO.builder().fundList(new ArrayList<>()).build();
        }
        List<FundSearchVO.FundInfo> fundList = orderFundCodeList.stream()
                .map(m -> FundSearchVO.FundInfo.builder()
                        .fundCode(m.getFundCode())
                        .fundName(m.getFundShortName())
                        .fundNameEn(m.getFundEnName())
                        .build())
                .collect(Collectors.toList());

        return FundSearchVO.builder().fundList(fundList).build();
    }

    /**
     * @description: 类型转换
     * @param m
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.FundSearchVO.FundInfo
     * @author: jinqing.rao
     * @date: 2025/3/31 13:14
     * @since JDK 1.8
     */
    private static FundSearchVO.FundInfo getFundInfo(FundBasicInfoDTO m) {
        return FundSearchVO.FundInfo.builder()
                .fundCode(m.getFundCode())
                .fundName(m.getFundAbbr())
                .build();
    }

    /**
     * @description: 查询基金交易代码列表
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.FundTxCodeListVO
     * @author: jinqing.rao
     * @date: 2025/4/2 15:13
     * @since JDK 1.8
     */
    public QueryFundTxAcctNoListVO queryFundTxAcctNoList(FundTxCodeListRequest request) {
        QueryFundTxAcctNoListVO queryFundTxAcctNoListVO = new QueryFundTxAcctNoListVO();

        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = queryFundTxAcctOuterService.queryFundTxCodeList(request.getHkCustNo());
        if (CollectionUtils.isEmpty(hkFundTxAcctDTOS)) {
            return queryFundTxAcctNoListVO;
        }
        // 获取非全委的个数
        long nonFullCommissionCount = hkFundTxAcctDTOS.stream()
                .filter(m -> YesNoEnum.NO.getCode().equals(m.getFundTxAccType())).count();
        // 获取全委的个数
        long fullCommissionCount = hkFundTxAcctDTOS.stream()
                .filter(m -> YesNoEnum.YES.getCode().equals(m.getFundTxAccType())).count();

        String fullCommissionName = "全权委托账户";
        String nonFullCommissionName = "证券投资账户";
        AtomicInteger nonFullCommissionIndex = new AtomicInteger(1);
        AtomicInteger fullCommissionIndex = new AtomicInteger(1);
        List<QueryFundTxAcctNoListVO.FundTxAcctNoInfo> fundTxCodeInfos = hkFundTxAcctDTOS.stream().map(m -> {
            QueryFundTxAcctNoListVO.FundTxAcctNoInfo fundTxCodeInfo = new QueryFundTxAcctNoListVO.FundTxAcctNoInfo();
            fundTxCodeInfo.setFundTxCode(m.getFundTxAcctNo());
            fundTxCodeInfo.setFundTxCodeType(m.getFundTxAccType());
            String fundTxCodeName;
            if (YesNoEnum.YES.getCode().equals(m.getFundTxAccType())) {
                fundTxCodeName = getFundTxCodeName(fullCommissionCount, fullCommissionIndex, fullCommissionName);
            }else{
                fundTxCodeName = getFundTxCodeName(nonFullCommissionCount, nonFullCommissionIndex, nonFullCommissionName);
            }
            fundTxCodeInfo.setFundName(fundTxCodeName);
            return fundTxCodeInfo;
        }).collect(Collectors.toList());

        QueryFundTxAcctNoListVO listVO = new QueryFundTxAcctNoListVO();
        listVO.setFundTxCodeList(fundTxCodeInfos);
        return listVO;
    }

    /**
     * @description: 获取基金交易代码名称
     * @param fundTxCount 对应类型基金个数
     * @param fundTxIndex 对应类型基金索引
     * @param fundTxName  对应类型基金名称
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/4/2 17:20
     * @since JDK 1.8
     */
    private static String getFundTxCodeName(long fundTxCount, AtomicInteger fundTxIndex, String fundTxName) {
        if (fundTxCount == Constants.NUMBER_ONE) {
            return fundTxName;
        }
        if (fundTxIndex.get() < Constants.NUMBER_TEN) {
            return fundTxName + Constants.CONSTANT_ZERO + fundTxIndex.getAndIncrement();
        }
        return fundTxName + fundTxIndex.getAndIncrement();
    }
}