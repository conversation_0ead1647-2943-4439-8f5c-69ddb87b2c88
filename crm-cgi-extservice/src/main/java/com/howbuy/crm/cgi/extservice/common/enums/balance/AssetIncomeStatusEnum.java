package com.howbuy.crm.cgi.extservice.common.enums.balance;

import org.apache.commons.lang3.StringUtils;

/**
 * 资产收益状态枚举
 * 0-计算中；1-计算完成
 */
public enum AssetIncomeStatusEnum {
    /**
     * 计算中
     */
    CALCULATING("0", "计算中"),
    /**
     * 计算完成
     */
    CALCULATED("1", "计算完成");

    private final String code;
    private final String description;

    AssetIncomeStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过Code获取枚举
     * @param code 状态编码
     * @return AssetIncomeStatusEnum 枚举实例
     */
    public static AssetIncomeStatusEnum getEnumByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (AssetIncomeStatusEnum value : AssetIncomeStatusEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 通过Code获取描述
     * @param code 状态编码
     * @return 描述信息
     */
    public static String getDescriptionByCode(String code) {
        for (AssetIncomeStatusEnum value : AssetIncomeStatusEnum.values()) {
            if (value.code.equals(code)) {
                return value.description;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}