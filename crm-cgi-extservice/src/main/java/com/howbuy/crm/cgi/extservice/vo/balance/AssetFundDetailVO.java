package com.howbuy.crm.cgi.extservice.vo.balance;

/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 基金持仓详情实体类
 * @author: 陈杰文
 * @date: 2025-06-17 14:34:43
 * @since JDK 1.8
 */
@Setter
@Getter
public class AssetFundDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品名称
     */
    private String fundName;

    /**
     * 产品代码
     */
    private String fundCode;

    /**
     * 展示类型
     */
    private String showType;

    /**
     * 产品业务类型 1 海外公募   2 海外私募
     */
    private String productBusiType;

    /**
     * 产品业务子类型 1 阳光私募  2 私募股权
     */
    private String productBusiSubType;

    /**
     * 清算标识
     */
    private String crisisFlag;

    /**
     * 策略类型
     */
    private String strategyType;

    /**
     * 参考市值
     */
    private String currencyMarketValue;

    /**
     * 当前收益（当前币种）
     */
    private String currentAssetCurrency;

    /**
     * 费后当前收益（当前币种）
     */
    private String currentAssetCurrencyExFee;

    /**
     * 币种描述
     */
    private String currencyDesc;

    /**
     * 参考净值
     */
    private String nav;

    /**
     * 净值分红标识
     */
    private String navDivFlag;

    /**
     * 净值日期
     */
    private String navDate;

    /**
     * na flag 标识
     * 0：非NA  10201：NA
     */
    private String naFlag;

    /**
     * 收益计算状态
     */
    private String incomeCalStat;

    /**
     * 分期成立
     */
    private String stageEstablishFlag;

    /**
     * 总实缴金额
     */
    private String paidInAmt;

    /**
     * 待投金额（当前币种）
     */
    private String currencyUnPaidInAmt;

    /**
     * 持仓份额
     */
    private String totalBalanceVol;

    /**
     * 当前收益率
     */
    private String yieldRate;

    /**
     * 当前收益率(不含费)
     */
    private String yieldRateExFee;

    /**
     * 展示数据的正负
     */
    private String showDirection;

    /**
     * 是否存在明细
     */
    private String hasDetail;

    /**
     * 平衡因子
     */
    private String balanceFactor;

    /**
     * 平衡因子转换完成
     */
    private String convertFinish;

    /**
     * 平衡因子日期
     */
    private String balanceFactorDate;

    /**
     * 累计应收管理费
     */
    private String receivManageFee;

    /**
     * 累计应收业绩报酬
     */
    private String receivPreformFee;

    /**
     * 净购买金额(投资成本)(当前币种)
     */
    private String currencyNetBuyAmount;

    /**
     * 费后累计总回款(当前币种)
     */
    private String currencyTotalCollectionExFee;

    /**
     * 费后持仓市值
     */
    private String currencyMarketValueExFee;

    /**
     * 产品期限说明 示例：2+2+2年
     */
    private String productTerm;

    /**
     * 产品期限说明描述 示例：投资期+退出期+延长期
     */
    private String productTermDesc;

    /**
     * 产品详细信息列表
     */
    private List<FundItemVO> fundItemVOList;
} 