package com.howbuy.crm.cgi.extservice.controller.hk.statement;

/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.statement.SendStatementRequest;
import com.howbuy.crm.cgi.extservice.request.statement.StatementOrderQueryRequest;
import com.howbuy.crm.cgi.extservice.service.statement.StatementService;
import com.howbuy.crm.cgi.extservice.vo.statement.StatementOrderQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 结单控制器
 * @author: 陈杰文
 * @date: 2025-06-17 15:25:00
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hk/statement")
@Slf4j
public class StatementController {

    @Autowired
    private StatementService statementService;

    /**
     * @api {POST} /ext/hk/statement/order/query queryStatementOrder()
     * @apiVersion 1.0.0
     * @apiGroup StatementController
     * @apiName queryStatementOrder()
     * @apiDescription 日结订单查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} statementType 订单类型 必传 1-日结单、2-月结单、3-全委-日结单、4-全委-月结单
     * @apiParam (请求体) {String} fundTxAcctNo 基金交易账号 非必传
     * @apiParam (请求体) {String} hkCustNo 香港客户号 必传
     * @apiParam (请求体) {String} page 当前页 必传
     * @apiParam (请求体) {String} size 每页条数 必传 默认10条
     * @apiParamExample 请求体示例
     * {"statementType":"1","fundTxAcctNo":"HK123456","hkCustNo":"HK123456","page":"1","size":"10"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Integer} data.total 总条数
     * @apiSuccess (响应结果) {List} data.statementOrderList 结单列表数据
     * @apiSuccess (响应结果) {String} data.statementOrderList.fileName 文件名称 好买香港${结单类型}-${结单时间}，日期格式：YYYYMMDD，举例"好买香港日结单-20250428"
     * @apiSuccess (响应结果) {String} data.statementOrderList.previewUrl 文件预览地址
     * @apiSuccess (响应结果) {String} data.statementOrderList.fileBizType 文件业务类型
     * @apiSuccess (响应结果) {List} data.statementEmailList 结单邮箱列表
     * @apiSuccess (响应结果) {String} data.statementEmailList.emailMask 邮箱地址掩码
     * @apiSuccess (响应结果) {String} data.statementEmailList.emailDigest 邮箱地址摘要
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"A","data":{"total":10,"statementOrderList":[{"fileName":"好买香港日结单-20250428","previewUrl":"https://example.com/preview","fileBizType":"STATEMENT"}],"statementEmailList":[{"emailMask":"test***@example.com","emailDigest":"<EMAIL>"}]},"description":"操作成功","timestampServer":"2025-06-17 15:25:00"}
     */
    @PostMapping("/order/query")
    public CgiResponse<StatementOrderQueryVO> queryStatementOrder(@RequestBody StatementOrderQueryRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        
        // 调用服务层查询日结订单
        StatementOrderQueryVO result = statementService.queryStatementOrder(request);
        
        return CgiResponse.ok(result);
    }

    /**
     * @api {POST} /ext/hk/statement/sendstatement sendStatement()
     * @apiVersion 1.0.0
     * @apiGroup CommonController
     * @apiName sendEmail()
     * @apiDescription 发送邮箱接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} bizType 业务类型 必传 1.结单发送邮箱
     * @apiParam (请求体) {String} fundTxAcctNo 邮箱密文 非必传
     * @apiParam (请求体) {String} hkCustNo 邮箱摘要 必传
     * @apiParamExample 请求体示例
     * {"bizType":"1","fundTxAcctNo":"encrypted_email","hkCustNo":"email_digest"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"A","data":{},"description":"操作成功","timestampServer":"2025-06-17 16:42:18"}
     */
    @PostMapping("/sendstatement")
    public CgiResponse<Body> sendStatement(@RequestBody SendStatementRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);

        // 调用服务层发送邮箱
        statementService.sendStatement(request);

        return CgiResponse.ok(new Body());
    }
} 