package com.howbuy.crm.cgi.extservice.controller.hk.fund;

/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.fund.TradeCalendarRequest;
import com.howbuy.crm.cgi.extservice.service.fund.HkFundService;
import com.howbuy.crm.cgi.extservice.vo.fund.TradeCalendarVO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;

/**
 * @description: 香港基金控制器
 * @author: 陈杰文
 * @date: 2025-06-25 11:28:45
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hk/fund")
@Slf4j
public class HkFundController {

    @Resource
    private HkFundService hkFundService;

    /**
     * @api {POST} /ext/hk/fund/tradecalendar tradecalendar()
     * @apiVersion 1.0.0
     * @apiGroup HkFundController
     * @apiName tradecalendar()
     * @apiDescription 交易日历查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456789","fundCode":"F123456"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.buyCalendar 购买日历
     * @apiSuccess (响应结果) {String} data.buyCalendar.supportPrebook 是否支持购买预约交易，1-是，0-否
     * @apiSuccess (响应结果) {String} data.buyCalendar.prebookBuyEndDt 预约购买截止日期，格式：yyyy-MM-dd HH:mm
     * @apiSuccess (响应结果) {String} data.buyCalendar.prebookPayEndDt 预计打款截止时间，格式：yyyy-MM-dd HH:mm
     * @apiSuccess (响应结果) {String} data.buyCalendar.openStartDt 当前购买开放开始日期，格式：yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.buyCalendar.openEndDt 当前购买开放结束日期，格式：yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.buyCalendar.expectTradeDt 预计交易日期，格式：yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.buyCalendar.orderEndTm 下单结束时间
     * @apiSuccess (响应结果) {String} data.buyCalendar.paymentEndTm 打款结束时间
     * @apiSuccess (响应结果) {String} data.buyCalendar.preSubmitTaDt 预计上报日期
     * @apiSuccess (响应结果) {String} data.buyCalendar.preSubmitTaTm 预计上报时间
     * @apiSuccess (响应结果) {Object} data.redemptionCalendar 赎回日历
     * @apiSuccess (响应结果) {String} data.redemptionCalendar.supportPrebook 是否支持赎回预约交易，1-是，0-否
     * @apiSuccess (响应结果) {String} data.redemptionCalendar.prebookRedeemEndDt 预约赎回截止日期，格式：yyyy-MM-dd HH:mm
     * @apiSuccess (响应结果) {String} data.redemptionCalendar.availableVol 可赎回份额
     * @apiSuccess (响应结果) {String} data.redemptionCalendar.openStartDt 当前赎回开放开始日期，格式：yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.redemptionCalendar.openEndDt 当前赎回开放结束日期，格式：yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.redemptionCalendar.expectTradeDt 预计交易日期，格式：yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.redemptionCalendar.orderEndTm 下单结束时间
     * @apiSuccess (响应结果) {String} data.redemptionCalendar.preSubmitTaDt 预计上报日期
     * @apiSuccess (响应结果) {String} data.redemptionCalendar.preSubmitTaTm 预计上报时间
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"成功","data":{"buyCalendar":{"supportPrebook":"1","prebookBuyEndDt":"2025-06-30 15:00","prebookPayEndDt":"2025-06-30 17:00","openStartDt":"2025-06-25","openEndDt":"2025-06-30","expectTradeDt":"2025-07-01","orderEndTm":"15:00","paymentEndTm":"17:00","preSubmitTaDt":"2025-06-30","preSubmitTaTm":"15:00"},"redemptionCalendar":{"supportPrebook":"1","prebookRedeemEndDt":"2025-06-30 15:00","availableVol":"10000.00","openStartDt":"2025-06-25","openEndDt":"2025-06-30","orderEndTm":"15:00","preSubmitTaDt":"2025-06-30","preSubmitTaTm":"15:00"}},"timestampServer":"1640995200000"}
     */
    @PostMapping("/tradecalendar")
    public CgiResponse<TradeCalendarVO> tradecalendar(@RequestBody TradeCalendarRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        
        // 调用服务层查询交易日历
        TradeCalendarVO result = hkFundService.queryTradeCalendar(request);
        
        return CgiResponse.ok(result);
    }
} 