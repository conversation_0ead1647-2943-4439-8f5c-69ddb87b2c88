package com.howbuy.crm.cgi.extservice.service.balance;

import com.google.common.collect.Lists;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.crm.cgi.common.cacheservice.CacheKeyPrefix;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.CurrencyEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.utils.BigDecimalUtils;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.ShowTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.agreement.SupplementalAgreementSignStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.balance.HkCustBalanceDisPalyCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggySignStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.product.ProductBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.product.ProductSubBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.utils.balance.BalanceUtils;
import com.howbuy.crm.cgi.extservice.common.utils.product.ProductUtils;
import com.howbuy.crm.cgi.extservice.convert.balance.QueryPiggyAssetVOBuilder;
import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.*;
import com.howbuy.crm.cgi.extservice.request.balance.*;
import com.howbuy.crm.cgi.extservice.service.business.balance.FundTxAcctNoService;
import com.howbuy.crm.cgi.extservice.service.business.balance.PortfolioCalculator;
import com.howbuy.crm.cgi.extservice.vo.balance.*;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.agreement.SupplementalAgreementDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.BalanceBeanDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.CurrencyBalanceDetailDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.PiggyAgreementSignDetailDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.agreement.SupplementalAgreementOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkPiggyBankOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkTradeOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 香港账户余额服务实现类
 * @author: jinqing.rao
 * @date: 2025/6/17 9:45
 */
@Slf4j
@Service
public class HkCustBalanceService {

    /**
     * 资产状态的key
     */
    private static final String balanceKey = CacheKeyPrefix.ASSET_DISPLAY_KEY_PREFIX;

    @Resource
    private HkTradeOuterService hkTradeOuterService;

    @Resource
    private CacheService cacheService;

    @Resource
    private PortfolioCalculator portfolioCalculator;

    @Resource
    private HkPiggyBankOuterService hkPiggyBankOuterService;

    @Resource
    private SupplementalAgreementOuterService supplementalAgreementOuterService;

    @Resource
    protected QueryFundBasicInfoOuterService queryFundBasicInfoOuterService;

    @Resource
    private FundTxAcctNoService fundTxAcctNoService;

    @Resource(name = Constants.APP_BALANCE_QUERY_POOL)
    protected ThreadPoolTaskExecutor batchTradePoolExecutor;

    /**
     * @param request 查询总资产请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.balance.QueryTotalAssetV1VO
     * @description: 查询总资产接口
     * <AUTHOR>
     * @date 2025/6/17 9:45
     */
    public QueryTotalAssetV1VO queryTotalAssetV1(QueryTotalAssetV1Request request) {
        BalanceContent content = BalanceContent.builder()
                .hkCustNo(request.getHkCustNo())
                .disPlayCurrencyCode(request.getDisPlayCurrencyCode())
                .fundTxAcctNoDTOList(fundTxAcctNoService.queryFundTxAcctNo(request.getHkCustNo()))
                .build();

        // 获取所有在途订单(非资产在途订单)
        AllInTransitTradeBalancePortfolioDetail inTransitTradePortfolioDetail = portfolioCalculator.calculate(content, HkCustBalanceDisPalyCategoryEnum.ALL_IN_TRANSIT, AllInTransitTradeBalancePortfolioDetail.class);
        // 获取用户现金余额
        CashBalancePortfolioDetail cashBalancePortfolioDetail = portfolioCalculator.calculate(content, HkCustBalanceDisPalyCategoryEnum.CASH_BALANCE, CashBalancePortfolioDetail.class);
        // 获取所有总资产
        TotalBalancePortfolioDetail totalBalancePortfolioDetail = portfolioCalculator.calculate(content, HkCustBalanceDisPalyCategoryEnum.ALL_BALANCE, TotalBalancePortfolioDetail.class);
        // 获取签约储蓄罐基金
        PiggyAgreementSignDetailDTO hkCustPiggyAgreement = hkPiggyBankOuterService.getHkCustPiggyAgreement(request.getHkCustNo());
        // 参数转换
        QueryTotalAssetV1VO.QueryTotalAssetV1VOBuilder builder = buildQueryTotalAssetV1VO(request, totalBalancePortfolioDetail,
                inTransitTradePortfolioDetail,
                cashBalancePortfolioDetail,
                hkCustPiggyAgreement);
        return builder.build();
    }

    /**
     * @param request                       请求参数
     * @param totalBalancePortfolioDetail   总资产
     * @param inTransitTradePortfolioDetail 在途资产
     * @param cashBalancePortfolioDetail    现金资产
     * @param hkCustPiggyAgreement          储蓄罐签署状态
     * @return com.howbuy.crm.cgi.extservice.vo.balance.QueryTotalAssetV1VO
     * @description: 构建总资产VO
     * @author: jinqing.rao
     * @date: 2025/7/8 13:09
     * @since JDK 1.8
     */
    private QueryTotalAssetV1VO.QueryTotalAssetV1VOBuilder buildQueryTotalAssetV1VO(QueryTotalAssetV1Request request,
                                                                                    TotalBalancePortfolioDetail totalBalancePortfolioDetail,
                                                                                    AllInTransitTradeBalancePortfolioDetail inTransitTradePortfolioDetail,
                                                                                    CashBalancePortfolioDetail cashBalancePortfolioDetail,
                                                                                    PiggyAgreementSignDetailDTO hkCustPiggyAgreement) {
        BigDecimal totalAsset = BigDecimalUtils.add(totalBalancePortfolioDetail.getTotalAsset(), cashBalancePortfolioDetail.getTotalAsset());
        // 储蓄罐签署状态
        boolean signStatusFlag = PiggySignStatusEnum.SIGNED.getCode().equals(hkCustPiggyAgreement.getAgreementState());
        String signStatus = signStatusFlag ? PiggySignStatusEnum.SIGNED.getCode() : PiggySignStatusEnum.UNSIGNED.getCode();
        return QueryTotalAssetV1VO.builder()
                .totalAsset(BigDecimalUtils.bigDecimalToString(totalAsset, 2, RoundingMode.DOWN))
                .showAsset(buildShowAsset(request.getHkCustNo()))
                .inTransitTradeAmt(BigDecimalUtils.bigDecimalToString(totalBalancePortfolioDetail.getInTransitTradeAmt(), 2, RoundingMode.DOWN))
                .inTransitTradeCount(inTransitTradePortfolioDetail.getInTransitTradeCount())
                .inTransitTradeDealNo(inTransitTradePortfolioDetail.getInTransitTradeDealNo())
                .inTransitTradeCapitalCount(inTransitTradePortfolioDetail.getInTransitTradeCapitalCount())
                .inTransitTradeCapitalDealNo(inTransitTradePortfolioDetail.getInTransitTradeCapitalDealNo())
                .inTransitPayVoucherCount(inTransitTradePortfolioDetail.getInTransitPayVoucherCount())
                .inTransitPayVoucherNo(inTransitTradePortfolioDetail.getInTransitPayVoucherNo())
                .inTransitCashPayVoucherLimit(inTransitTradePortfolioDetail.getInTransitCashPayVoucherLimit())
                .inTransitDrawMoneyAppCount(inTransitTradePortfolioDetail.getInTransitDrawMoneyAppCount())
                .inTransitDrawMoneyAppNo(inTransitTradePortfolioDetail.getInTransitDrawMoneyAppNo())
                .inTransitDrawMoneyCapitalCount(inTransitTradePortfolioDetail.getInTransitDrawMoneyCapitalCount())
                .inTransitDrawMoneyCapitalNo(inTransitTradePortfolioDetail.getInTransitDrawMoneyCapitalNo())
                .fundAsset(BigDecimalUtils.bigDecimalToString(totalBalancePortfolioDetail.getFundAsset(), 2, RoundingMode.DOWN))
                .fundIncome(BigDecimalUtils.bigDecimalToString(totalBalancePortfolioDetail.getFundIncome(), 2, RoundingMode.HALF_UP))
                .fundIncomeCalStatus(totalBalancePortfolioDetail.getFundIncomeCalStatus())
                .piggyAsset(BigDecimalUtils.bigDecimalToString(totalBalancePortfolioDetail.getPiggyAsset(), 2, RoundingMode.DOWN))
                .piggyIncome(BigDecimalUtils.bigDecimalToString(totalBalancePortfolioDetail.getPiggyIncome(), 2, RoundingMode.HALF_UP))
                .piggyIncomeCalStatus(totalBalancePortfolioDetail.getPiggyIncomeCalStatus())
                .piggySignStatus(signStatus)
                .fullFundAsset(BigDecimalUtils.bigDecimalToString(totalBalancePortfolioDetail.getFullFundAsset(), 2, RoundingMode.DOWN))
                .fullFundIncome(BigDecimalUtils.bigDecimalToString(totalBalancePortfolioDetail.getFullFundIncome(), 2, RoundingMode.HALF_UP))
                .fullFundIncomeCalStatus(totalBalancePortfolioDetail.getFullFundIncomeCalStatus())
                .existsNaFund(totalBalancePortfolioDetail.getExistsNaFund())
                .cashBalanceAsset(BigDecimalUtils.bigDecimalToString(cashBalancePortfolioDetail.getTotalNonFullAsset(), 2, RoundingMode.DOWN))
                .systemDate(DateUtils.getCurrentDate(DateUtils.YYYYMMDD));
    }

    /**
     * @param hkCustNo 请求参数
     * @return void
     * @description: 初始化小眼睛状态
     * @author: jinqing.rao
     * @date: 2025/6/23 14:55
     * @since JDK 1.8
     */
    private String buildShowAsset(String hkCustNo) {
        // 历史逻辑重构
        // 1.根据香港客户号查询一账通号为空时，资产小眼睛按钮默认隐藏
        // 2.再判断缓存中是否存在对应切换的数据,有的话则覆盖
        String hboneNo = hkTradeOuterService.getHboneNo(hkCustNo);
        Object isShowCache = cacheService.get(balanceKey + hkCustNo);

        if (isShowCache != null) {
            return isShowCache.toString();
        }
        return hboneNo == null ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode();
    }

    /**
     * @param request 非全委托现金资产查询请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.balance.QueryNotFullCashBalanceAssetVO
     * @description: 非全委托现金资产查询接口
     * <AUTHOR>
     * @date 2025-06-17 14:13:43
     */
    public QueryNotFullCashBalanceAssetVO queryNotFullCashBalanceAsset(QueryNotFullCashBalanceAssetRequest request) {
        // 构建上下文
        BalanceContent content = BalanceContent.builder()
                .hkCustNo(request.getHkCustNo())
                .disPlayCurrencyCode(request.getDisPlayCurrencyCode())
                .fundTxAcctNoDTOList(fundTxAcctNoService.queryFundTxAcctNo(request.getHkCustNo()))
                .build();

        // 现金余额在途信息
        AllInTransitTradeBalancePortfolioDetail allInTransitTradeBalancePortfolioDetail = portfolioCalculator.calculate(content, HkCustBalanceDisPalyCategoryEnum.NON_FULL_FUND_BALANCE_IN_TRANSIT, AllInTransitTradeBalancePortfolioDetail.class);
        // 现金余额资产数据
        CashBalancePortfolioDetail cashBalancePortfolioDetail = portfolioCalculator.calculate(content, HkCustBalanceDisPalyCategoryEnum.CASH_BALANCE, CashBalancePortfolioDetail.class);
        // 非全委现金资产币种明细列表
        List<CurrencyBalanceDetailDTO> currencyBalanceList = cashBalancePortfolioDetail.getNonFullCurrencyBalanceList();
        List<CashBalanceDetailVO> currencyDetailList = this.convertCashBalanceDetailVO(currencyBalanceList);
        // 按照币种升序排序
        currencyDetailList = currencyDetailList.stream()
                .sorted(Comparator.comparing(CashBalanceDetailVO::getCurrencyCode))
                .collect(Collectors.toList());

        QueryNotFullCashBalanceAssetVO vo = new QueryNotFullCashBalanceAssetVO();
        vo.setShowAsset(this.buildShowAsset(request.getHkCustNo()));
        // 现金余额总资产
        vo.setCashBalanceAsset(BigDecimalUtils.bigDecimalToString(cashBalancePortfolioDetail.getTotalNonFullAsset(), 2, RoundingMode.DOWN));
        // 可用余额
        vo.setAvailableBalance(BigDecimalUtils.bigDecimalToString(cashBalancePortfolioDetail.getNonFullTotalAvailableBalance(), 2, RoundingMode.DOWN));
        // 冻结金额
        vo.setFreezeBalance(BigDecimalUtils.bigDecimalToString(cashBalancePortfolioDetail.getNonFullTotalFrozenAmt(), 2, RoundingMode.DOWN));

        // 在途打款凭证
        vo.setInTransitPayVoucherCount(String.valueOf(allInTransitTradeBalancePortfolioDetail.getInTransitPayVoucherCount()));
        vo.setInTransitPayVoucherNo(allInTransitTradeBalancePortfolioDetail.getInTransitPayVoucherNo());
        // 在途提款
        vo.setInTransitDrawMoneyAppCount(String.valueOf(allInTransitTradeBalancePortfolioDetail.getInTransitDrawMoneyAppCount()));
        vo.setInTransitDrawMoneyAppNo(allInTransitTradeBalancePortfolioDetail.getInTransitDrawMoneyAppNo());
        // 在途提取资金
        vo.setInTransitDrawMoneyCapitalCount(String.valueOf(allInTransitTradeBalancePortfolioDetail.getInTransitDrawMoneyCapitalCount()));
        vo.setInTransitDrawMoneyCapitalNo(allInTransitTradeBalancePortfolioDetail.getInTransitDrawMoneyCapitalNo());
        // 现金余额币种明细列表
        vo.setCashBalanceDetailList(currencyDetailList);
        return vo;
    }

    /**
     * @param request 查询现金余额明细请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.balance.QueryCashBalanceAssetDetailVO
     * @description: 查询现金余额明细接口
     * <AUTHOR>
     * @date 2025-06-17 14:22:53
     */
    public QueryCashBalanceAssetDetailVO queryCashBalanceAssetDetailV1(QueryCashBalanceAssetDetailV1Request request) {
        QueryCashBalanceAssetDetailVO vo = new QueryCashBalanceAssetDetailVO();
        // 构建上下文
        BalanceContent content = BalanceContent.builder()
                .hkCustNo(request.getHkCustNo())
                .fundTxAcctNoDTOList(fundTxAcctNoService.queryFundTxAcctNo(request.getHkCustNo()))
                .build();

        // 现金余额在途数据
        CashBalancePortfolioDetail cashBalancePortfolioDetail = portfolioCalculator.calculate(content, HkCustBalanceDisPalyCategoryEnum.CASH_BALANCE, CashBalancePortfolioDetail.class);

        List<CurrencyBalanceDetailDTO> currencyBalanceList = new ArrayList<>();
        // 全委现金余额币种明细列表
        List<CurrencyBalanceDetailDTO> fullCurrencyBalanceList = cashBalancePortfolioDetail.getFullCurrencyBalanceList();
        // 非全委现金余额币种明细列表
        List<CurrencyBalanceDetailDTO> nonFullCurrencyBalanceList = cashBalancePortfolioDetail.getNonFullCurrencyBalanceList();
        currencyBalanceList.addAll(CollectionUtils.isEmpty(fullCurrencyBalanceList) ? new ArrayList<>() : fullCurrencyBalanceList);
        currencyBalanceList.addAll(CollectionUtils.isEmpty(nonFullCurrencyBalanceList) ? new ArrayList<>() : nonFullCurrencyBalanceList);
        currencyBalanceList = currencyBalanceList.stream()
                .filter(v -> v.getFundTxAcctNo().equals(request.getFundTxAcctNo()))
                .collect(Collectors.toList());
        List<CashBalanceDetailVO> currencyDetailList = this.convertCashBalanceDetailVO(currencyBalanceList);
        // 按照币种升序排序
        currencyDetailList = currencyDetailList.stream()
                .sorted(Comparator.comparing(CashBalanceDetailVO::getCurrencyCode))
                .collect(Collectors.toList());
        vo.setCashBalanceDetailList(currencyDetailList);
        return vo;
    }

    /**
     * @param request 查询基金资产请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.balance.QueryFundAssetV1VO
     * @description: 查询基金资产接口
     * <AUTHOR>
     * @date 2025-06-17 14:34:43
     */
    public QueryFundAssetVO queryFundAssetV1(QueryFundAssetRequest request) {
        // 构建上下文
        BalanceContent content = BalanceContent.builder()
                .hkCustNo(request.getHkCustNo())
                .disPlayCurrencyCode(request.getDisPlayCurrencyCode())
                .fundTxAcctNoDTOList(fundTxAcctNoService.queryFundTxAcctNo(request.getHkCustNo()))
                .build();

        // 在途数据
        AllInTransitTradeBalancePortfolioDetail allInTransitTradeBalancePortfolioDetail = portfolioCalculator.calculate(content, HkCustBalanceDisPalyCategoryEnum.NON_FULL_FUND_BALANCE_IN_TRANSIT, AllInTransitTradeBalancePortfolioDetail.class);
        // 非全委基金持仓资产
        BalancePortfolioDetail portfolioDetail = portfolioCalculator.calculate(content, HkCustBalanceDisPalyCategoryEnum.NON_FULL_FUND_BALANCE, BalancePortfolioDetail.class);
        // 获取持仓明细列表
        List<BalanceBeanDTO> itemList = portfolioDetail.getItemList();
        // 获取客户所有未签署的补签协议数据列表
        List<SupplementalAgreementDTO> supplementalAgreementDTOS = getSupplementalAgreementDTOS(request.getHkCustNo(), itemList);

        // 根据基金代码分组
        Map<String, List<BalanceBeanDTO>> fundCodeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(itemList)) {
            // 根据基金代码分组
            fundCodeMap = itemList.stream()
                    .collect(Collectors.groupingBy(BalanceBeanDTO::getProductCode));
        }

        // 获取产品明细VO列表
        List<AssetFundDetailVO> nonFullAssetFundListVOS = this.getAssetFundListVOS(fundCodeMap);
        return getQueryFundAssetVO(request, portfolioDetail, itemList, allInTransitTradeBalancePortfolioDetail, supplementalAgreementDTOS, nonFullAssetFundListVOS);
    }

    /**
     * 获取补签协议列表（指定持仓产品列表中包含，并且不在储蓄罐产品列表范围内的数据）
     *
     * @param hkCustNo  香港客户号
     * @param itemList  持仓产品列表
     * @return List<SupplementalAgreementDTO>
     */
    private List<SupplementalAgreementDTO> getSupplementalAgreementDTOS(String hkCustNo, List<BalanceBeanDTO> itemList) {
        if (CollectionUtils.isEmpty(itemList) || StringUtils.isBlank(hkCustNo)) {
            return new ArrayList<>();
        }

        List<SupplementalAgreementDTO> supplementalAgreementDTOS = supplementalAgreementOuterService.querySupplementalAgreementList(hkCustNo,
                SupplementalAgreementSignStatusEnum.UNSIGNED.getCode(), null);
        if (CollectionUtils.isEmpty(supplementalAgreementDTOS)) {
            return new ArrayList<>();
        }

        // 持仓产品代码列表
        List<String> balanceFundCodes = itemList.stream()
                .map(BalanceBeanDTO::getProductCode)
                .collect(Collectors.toList());

        // 储蓄罐产品列表
        List<FundBasicInfoDTO> fundBasicInfoDTOList = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList();
        List<String> piggyFundCodes = CollectionUtils.isEmpty(fundBasicInfoDTOList) ? new ArrayList<>() :
                fundBasicInfoDTOList.stream()
                        .map(FundBasicInfoDTO::getFundCode)
                        .distinct()
                        .collect(Collectors.toList());

        // 过滤 持仓产品列表 & 非储蓄罐产品的补签协议列表
        supplementalAgreementDTOS = supplementalAgreementDTOS.stream()
                .filter(v -> balanceFundCodes.contains(v.getFundCode()) && !piggyFundCodes.contains(v.getFundCode()))
                .collect(Collectors.toList());
        return supplementalAgreementDTOS;
    }

    /**
     * 封装基金资产VO
     *
     * @param request
     * @param portfolioDetail
     * @param itemList
     * @param allInTransitTradeBalancePortfolioDetail
     * @param supplementalAgreementDTOS
     * @param nonFullAssetFundListVOS
     * @return
     */
    private QueryFundAssetVO getQueryFundAssetVO(QueryFundAssetRequest request, BalancePortfolioDetail portfolioDetail, List<BalanceBeanDTO> itemList, AllInTransitTradeBalancePortfolioDetail allInTransitTradeBalancePortfolioDetail, List<SupplementalAgreementDTO> supplementalAgreementDTOS, List<AssetFundDetailVO> nonFullAssetFundListVOS) {
        // 响应VO
        QueryFundAssetVO vo = new QueryFundAssetVO();
        // 是否显示资产
        vo.setShowAsset(this.buildShowAsset(request.getHkCustNo()));
        // 基金持仓资产
        vo.setFundAsset(BigDecimalUtils.bigDecimalToString(portfolioDetail.getTotalAsset(), 2, RoundingMode.DOWN));
        // 基金持仓收益
        vo.setFundIncome(BigDecimalUtils.bigDecimalToString(portfolioDetail.getTotalIncome(), 2, RoundingMode.HALF_UP));
        // 存在基金阳光私募产品
        vo.setExistsFundEquityFund(this.getExistOverseasFundFlag(itemList));
        // 存在基金私募股权产品
        vo.setExistsGqEquityFund(this.getExistOverseasGqFundFlag(itemList));
        // 基金持仓收益计算状态
        vo.setFundIncomeCalStatus(portfolioDetail.getIncomeStatus());
        // 基金股权总回款
        vo.setFundTotalEquityRecovery(BigDecimalUtils.bigDecimalToString(portfolioDetail.getTotalCashCollection(), 2, RoundingMode.HALF_UP));
        // 基金在途交易金额
        vo.setFundInTransitTradeAmt(BigDecimalUtils.bigDecimalToString(portfolioDetail.getInTransitTradeAmt(), 2, RoundingMode.HALF_UP));
        // 是否存在全委基金交易账号 todo 前端确认字段不用
//        vo.setExistsFullTxAcct(portfolioDetail.getExistsFullTxAcct());
        // 基金在途交易笔数
        vo.setFundInTransitTradeCount(allInTransitTradeBalancePortfolioDetail.getInTransitTradeCount());
        // 基金在途交易订单号
        vo.setFundInTransitTradeDealNo(allInTransitTradeBalancePortfolioDetail.getInTransitTradeDealNo());
        // 基金在途交易资金笔数
        vo.setFundInTransitTradeCapitalCount(allInTransitTradeBalancePortfolioDetail.getInTransitTradeCapitalCount());
        // 基金在途交易资金订单号
        vo.setFundInTransitTradeCapitalDealNo(allInTransitTradeBalancePortfolioDetail.getInTransitTradeCapitalDealNo());
        // 基金补签协议基金个数
        vo.setFundSupplementalAgreementFundCount(String.valueOf(supplementalAgreementDTOS.size()));
        // 基金补签协议基金代码（仅协议数量=1时返回）
        String supplementalAgreementFundCode = supplementalAgreementDTOS.size() == 1 ? supplementalAgreementDTOS.stream().map(SupplementalAgreementDTO::getFundCode).findFirst().orElse(null) : null;
        vo.setFundSupplementalAgreementFundCode(supplementalAgreementFundCode);
        // 持仓明细列表
        vo.setAssetFundDetailList(nonFullAssetFundListVOS);
        return vo;
    }

    /**
     * @param request 查询基金资产详情请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.balance.QueryFundAssetDetailV1VO
     * @description: 查询基金资产详情接口
     * <AUTHOR>
     * @date 2025-06-17 14:43:00
     */
    public QueryFundAssetDetailVO queryFundAssetDetailV1(QueryFundAssetDetailRequest request) {
        // TODO: 实现业务逻辑
        // 1. 调用dtms-order持仓查询接口，仅处理全委的资产、收益、收益计算状态以及基金持仓列表
        // 2. 调用dtms-order补签协议接口（入参支持香港客户号、基金代码），获取补签协议信息
        return new QueryFundAssetDetailVO();
    }

    /**
     * @param request 查询全委基金资产请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.balance.QueryFullFundAssetV1VO
     * @description: 查询全委基金资产接口
     * <AUTHOR>
     * @date 2025-06-17 15:00:00
     */
    public QueryFullFundAssetVO queryFullFundAssetV1(QueryFullFundAssetRequest request) {
        // 构建上下文
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = fundTxAcctNoService.queryFullFundTxAcctNo(request.getHkCustNo());
        // 获取当前需要查询的基金交易账号数据
        List<HkFundTxAcctDTO> queryHkFundTxAcctDTOS = getQueryFundTxAcctNo(request, hkFundTxAcctDTOS);
        BalanceContent content = BalanceContent.builder()
                .hkCustNo(request.getHkCustNo())
                .disPlayCurrencyCode(request.getDisPlayCurrencyCode())
                .fundTxAcctNoDTOList(queryHkFundTxAcctDTOS)
                .build();

        // 全委现金余额查询上下文
        BalanceContent cashContent = BalanceContent.builder()
                .hkCustNo(request.getHkCustNo())
                .disPlayCurrencyCode(request.getDisPlayCurrencyCode())
                .fundTxAcctNoDTOList(hkFundTxAcctDTOS)
                .build();

        // 在途数据
        AllInTransitTradeBalancePortfolioDetail allInTransitTradeBalancePortfolioDetail = portfolioCalculator.calculate(content, HkCustBalanceDisPalyCategoryEnum.FULL_IN_TRANSIT, AllInTransitTradeBalancePortfolioDetail.class);
        // 全委基金持仓资产
        FullBalancePortfolioDetail portfolioDetail = portfolioCalculator.calculate(content, HkCustBalanceDisPalyCategoryEnum.FULL_BALANCE, FullBalancePortfolioDetail.class);
        // 全委现金余额资产数据
        CashBalancePortfolioDetail cashBalancePortfolioDetail = portfolioCalculator.calculate(cashContent, HkCustBalanceDisPalyCategoryEnum.CASH_BALANCE, CashBalancePortfolioDetail.class);

        // 获取持仓明细列表
        List<BalanceBeanDTO> itemList = portfolioDetail.getItemList();
        Map<String, List<BalanceBeanDTO>> fundCodeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(itemList)) {
            // 根据基金代码分组
            fundCodeMap = itemList.stream()
                    .collect(Collectors.groupingBy(BalanceBeanDTO::getProductCode));
        }
        // 获取产品明细VO列表
        List<AssetFundDetailVO> fullAssetFundListVOS = this.getAssetFundListVOS(fundCodeMap);

        return getQueryFullFundAssetVO(request, cashBalancePortfolioDetail, portfolioDetail, itemList, allInTransitTradeBalancePortfolioDetail, hkFundTxAcctDTOS, fullAssetFundListVOS);
    }

    /**
     * 全委资产VO构建
     *
     * @param request
     * @param cashBalancePortfolioDetail
     * @param portfolioDetail
     * @param itemList
     * @param allInTransitTradeBalancePortfolioDetail
     * @param hkFundTxAcctDTOS
     * @param fullAssetFundListVOS
     * @return QueryFullFundAssetVO
     */
    private QueryFullFundAssetVO getQueryFullFundAssetVO(QueryFullFundAssetRequest request, CashBalancePortfolioDetail cashBalancePortfolioDetail, FullBalancePortfolioDetail portfolioDetail, List<BalanceBeanDTO> itemList, AllInTransitTradeBalancePortfolioDetail allInTransitTradeBalancePortfolioDetail, List<HkFundTxAcctDTO> hkFundTxAcctDTOS, List<AssetFundDetailVO> fullAssetFundListVOS) {
        // 响应VO
        QueryFullFundAssetVO vo = new QueryFullFundAssetVO();
        // 是否显示资产
        vo.setShowAsset(this.buildShowAsset(request.getHkCustNo()));
        // 全委现金余额总资产
        BigDecimal totalFullCashAsset = cashBalancePortfolioDetail.getTotalFullAsset();
        // 全委基金持仓总资产
        BigDecimal totalFullFundAsset = portfolioDetail.getTotalAsset();
        // 全委总资产
        BigDecimal totalAsset = BigDecimalUtils.add(totalFullCashAsset, totalFullFundAsset);
        vo.setFullFundAsset(BigDecimalUtils.bigDecimalToString(totalAsset, 2, RoundingMode.DOWN));
        // 基金持仓收益
        vo.setFullFundIncome(BigDecimalUtils.bigDecimalToString(portfolioDetail.getTotalIncome(), 2, RoundingMode.HALF_UP));
        // 全委基金持仓总资产
        vo.setFullHoldFundAsset(BigDecimalUtils.bigDecimalToString(totalFullFundAsset, 2, RoundingMode.DOWN));
        // 存在基金阳光私募产品
        vo.setExistsFundEquityFund(this.getExistOverseasFundFlag(itemList));
        // 存在基金私募股权产品
        vo.setExistsFullFundEquityFund(this.getExistOverseasGqFundFlag(itemList));
        // 基金持仓收益计算状态
        vo.setFullFundIncomeCalStatus(portfolioDetail.getIncomeStatus());
        // 基金股权总回款
        vo.setFullFundTotalEquityRecovery(BigDecimalUtils.bigDecimalToString(portfolioDetail.getTotalCashCollection(), 2, RoundingMode.HALF_UP));
        // 基金在途交易金额
        vo.setFullFundInTransitTradeAmt(BigDecimalUtils.bigDecimalToString(portfolioDetail.getInTransitTradeAmt(), 2, RoundingMode.HALF_UP));
        // 基金在途交易笔数
        vo.setFullFundInTransitTradeCount(allInTransitTradeBalancePortfolioDetail.getInTransitTradeCount());
        // 基金在途交易订单号
        vo.setFullFundInTransitTradeDealNo(allInTransitTradeBalancePortfolioDetail.getInTransitTradeDealNo());
        // 基金在途交易资金笔数
        vo.setFullFundInTransitTradeCapitalCount(allInTransitTradeBalancePortfolioDetail.getInTransitTradeCapitalCount());
        // 基金在途交易资金订单号
        vo.setFullFundInTransitTradeCapitalDealNo(allInTransitTradeBalancePortfolioDetail.getInTransitTradeCapitalDealNo());
        // 全委现金余额资产
        vo.setFullCashBalanceAsset(BigDecimalUtils.bigDecimalToString(totalFullCashAsset, 2, RoundingMode.DOWN));
        // 全委可用余额
        vo.setFullAvailableBalance(BigDecimalUtils.bigDecimalToString(cashBalancePortfolioDetail.getFullTotalAvailableBalance(), 2, RoundingMode.DOWN));
        // 全委冻结余额
        vo.setFullFreezeBalance(BigDecimalUtils.bigDecimalToString(cashBalancePortfolioDetail.getFullTotalFrozenAmt(), 2, RoundingMode.DOWN));

        // 基金交易账号列表
        vo.setFundTxAcctList(this.buildFundTxAcctVOList(hkFundTxAcctDTOS));
        // 持仓明细列表
        vo.setAssetFundDetailList(fullAssetFundListVOS);
        return vo;
    }

    /**
     * 获取基金交易账号列表
     * 1.若入参基金交易账号存在，则过滤出列表中与之相同的数据返回
     * 2.若入参基金交易账号不存在，则遍历全部数据，根据创建时间升序返回第一个有持仓的数据
     *
     * @param request          入参
     * @param hkFundTxAcctDTOS 基金交易账号列表
     * @return List<HkFundTxAcctDTO>
     */
    private List<HkFundTxAcctDTO> getQueryFundTxAcctNo(QueryFullFundAssetRequest request, List<HkFundTxAcctDTO> hkFundTxAcctDTOS) {
        if (CollectionUtils.isEmpty(hkFundTxAcctDTOS)) {
            return new ArrayList<>();
        }

        // 入参基金交易账号存在 则过滤该账号数据
        if (StringUtils.isNotBlank(request.getFundTxAcctNo())) {
            return hkFundTxAcctDTOS.stream()
                    .filter(m -> StringUtils.equals(m.getFundTxAcctNo(), request.getFundTxAcctNo()))
                    .collect(Collectors.toList());
        }

        // 入参不存在，则遍历全部数据，根据创建时间升序返回第一个有持仓的数据
        List<HkFundTxAcctDTO> fundTxAcctDTOS = hkFundTxAcctDTOS.stream()
                .filter(v -> YesNoEnum.YES.getCode().equals(v.getHasBalance()))
                .sorted(Comparator.comparing(HkFundTxAcctDTO::getCreateTimestamp))
                .map(Lists::newArrayList)
                .findFirst()
                .orElse(null);

        if (CollectionUtils.isEmpty(fundTxAcctDTOS)) {
            return hkFundTxAcctDTOS.stream()
                    .min(Comparator.comparing(HkFundTxAcctDTO::getCreateTimestamp))
                    .map(Lists::newArrayList)
                    .orElse(null);
        }

        return fundTxAcctDTOS;
    }

    /**
     * @param request 查询现金提取页面请求参数
     * @return QueryCashDrawMoneyPageVO
     * @description: 查询现金提取页面接口
     * @author: 陈杰文
     * @date: 2025-06-17 17:46:35
     */
    public QueryCashDrawMoneyPageVO queryCashDrawMoneyPage(QueryCashDrawMoneyPageRequest request) {
        // TODO: 实现业务逻辑
        // 1. 参数必填校验（已在控制器层完成）
        // 2. 调用账户的银行卡列表查询接口
        // 3. 调用dtms-order现金余额接口，仅查询非全委的数据
        return new QueryCashDrawMoneyPageVO();
    }

    /**
     * @param request 现金提取提交请求参数
     * @description: 现金提取提交接口
     * @author: 陈杰文
     * @date: 2025-06-17 17:46:35
     */
    public void submitCashDrawMoney(CashDrawMoneySubmitRequest request) {
        // TODO: 实现业务逻辑
        // 1. 参数必填校验（已在控制器层完成）
        // 2. 调用dtms-order获取非全委基金交易账号
        // 3. 非全委基金交易账号及入参调用资金的现金提取提交接口
    }

    /**
     * @param request 现金提取记录列表请求参数
     * @return QueryCashDrawMoneyRecordListVO
     * @description: 现金提取记录列表接口
     * @author: 陈杰文
     * @date: 2025-06-17 17:46:35
     */
    public QueryCashDrawMoneyRecordListVO queryCashDrawMoneyRecordList(QueryCashDrawMoneyRecordListRequest request) {
        // TODO: 实现业务逻辑
        // 1. 参数必填校验（已在控制器层完成）
        // 2. 调用dtms-order获取非全委基金交易账号
        // 3. 根据非全委基金交易账号及入参调用资金的现金提取记录列表接口
        return new QueryCashDrawMoneyRecordListVO();
    }

    /**
     * @param request 现金提取记录详情请求参数
     * @return QueryCashDrawMoneyRecordDetailVO
     * @description: 现金提取记录详情接口
     * @author: 陈杰文
     * @date: 2025-06-17 17:46:35
     */
    public QueryCashDrawMoneyRecordDetailVO queryCashDrawMoneyRecordDetail(QueryCashDrawMoneyRecordDetailRequest request) {
        // TODO: 实现业务逻辑
        // 1. 参数必填校验（已在控制器层完成）
        // 2. 调用资金的现金提取记录详情接口
        return new QueryCashDrawMoneyRecordDetailVO();
    }

    /**
     * @param request 资金明细查询请求参数
     * @return QueryFinancialDetailListVO
     * @description: 资金明细查询接口
     * @author: 陈杰文
     * @date: 2025-06-17 18:07:37
     */
    public QueryFinancialDetailListVO queryFinancialDetailList(QueryFinancialDetailListRequest request) {
        // TODO: 实现业务逻辑
        // 1. 参数必填校验（已在控制器层完成）
        // 2. 基金交易账号入参为空时调用dtms-order获取非全委基金交易账号
        // 3. 根据基金交易账号调用资金的资金明细查询接口
        return new QueryFinancialDetailListVO();
    }

    /**
     * @param currencyBalanceList 币种明细
     * @return CashBalanceDetailVO
     * @description: 现金余额币种明细列表转换
     * @author: 陈杰文
     * @date: 2025-07-09 16:07:21
     */
    private List<CashBalanceDetailVO> convertCashBalanceDetailVO(List<CurrencyBalanceDetailDTO> currencyBalanceList) {
        if (CollectionUtils.isEmpty(currencyBalanceList)) {
            return new ArrayList<>();
        }

        List<CashBalanceDetailVO> cashBalanceDetailVOS = new ArrayList<>();
        for (CurrencyBalanceDetailDTO dto : currencyBalanceList) {
            CashBalanceDetailVO vo = new CashBalanceDetailVO();
            vo.setCurrencyCode(dto.getCurCode());
            vo.setCashBalanceAsset(BigDecimalUtils.bigDecimalToString(dto.getExchangeCashBalance(), 2, RoundingMode.DOWN));
            vo.setAvailableBalance(BigDecimalUtils.bigDecimalToString(dto.getExchangeAvailableBalance(), 2, RoundingMode.DOWN));
            vo.setFreezeBalance(BigDecimalUtils.bigDecimalToString(dto.getExchangeFrozenAmt(), 2, RoundingMode.DOWN));
            cashBalanceDetailVOS.add(vo);
        }
        return cashBalanceDetailVOS;
    }

    /**
     * @param fundCodeMap 持仓数据
     * @return QueryFinancialDetailListVO
     * @description: 基金持仓明细列表封装
     * @author: 陈杰文
     * @date: 2025-07-04 14:07:21
     */
    private List<AssetFundDetailVO> getAssetFundListVOS(Map<String, List<BalanceBeanDTO>> fundCodeMap) {
        if (MapUtils.isEmpty(fundCodeMap)) {
            return Collections.emptyList();
        }

        // 持仓明细VO列表
        List<AssetFundDetailVO> assetFundDetailVOS = new ArrayList<>();
        fundCodeMap.forEach((fundCode, balanceBeanDTOS) -> {
            BalanceBeanDTO balanceBeanDTO = balanceBeanDTOS.get(0);
            String productName = balanceBeanDTO.getProductName();
            AssetFundDetailVO assetFundDetailVO = new AssetFundDetailVO();
            assetFundDetailVO.setFundName(productName);
            assetFundDetailVO.setFundCode(fundCode);
            // 产品业务大类
            ProductBizTypeEnum productBizTypeEnum = ProductUtils.getProductType(balanceBeanDTO.getHkSaleFlag(), balanceBeanDTO.getSfhwcxg(), balanceBeanDTO.getProductType(), balanceBeanDTO.getProductSubType());
            assetFundDetailVO.setProductBusiType(null != productBizTypeEnum ? productBizTypeEnum.getCode() : null);
            // 产品业务子类
            ProductSubBizTypeEnum productSubBizTypeEnum = ProductUtils.getProductSubType(balanceBeanDTO.getHkSaleFlag(), balanceBeanDTO.getSfhwcxg(), balanceBeanDTO.getProductType(), balanceBeanDTO.getProductSubType());
            assetFundDetailVO.setProductBusiSubType(null != productSubBizTypeEnum ? productSubBizTypeEnum.getCode() : null);
            // showType todo 字段前端是否使用确认
            // 是否分期成立 1-是;0-否
            String stageEstablishFlag = balanceBeanDTO.getStageEstablishFlag();
            // 是否千禧年产品 1-是;0-否
            String qianXiFlag = balanceBeanDTO.getQianXiFlag();
            if (balanceBeanDTO.getQianXiFlag().equals(YesNoEnum.YES.getCode())) {
                assetFundDetailVO.setShowType(ShowTypeEnum.MILLENNIUM.getCode());
            }
            // 清算标识 todo 取值逻辑确认
            assetFundDetailVO.setCrisisFlag(balanceBeanDTO.getCrisisFlag());
            // 策略类型 多策略、股票型、股权型、CTA、另类、固收与中性
            assetFundDetailVO.setStrategyType(balanceBeanDTO.getAssetStrategyFirstType());
            // 参考市值（汇总）
            BigDecimal currencyMarketValueSum = calcSumValueFunction(balanceBeanDTOS, BalanceBeanDTO::getCurrencyMarketValue);
            assetFundDetailVO.setCurrencyMarketValue(BigDecimalUtils.bigDecimalToString(currencyMarketValueSum, 2, RoundingMode.DOWN));
            // 费后持仓市值（汇总）
            BigDecimal currencyMarketValueExFeeSum = calcSumValueFunction(balanceBeanDTOS, BalanceBeanDTO::getCurrencyMarketValueExFee);
            assetFundDetailVO.setCurrencyMarketValueExFee(BigDecimalUtils.bigDecimalToString(currencyMarketValueExFeeSum, 2, RoundingMode.DOWN));
            // 当前收益（汇总）
            BigDecimal currentAssetCurrencySum = calcSumValueFunction(balanceBeanDTOS, BalanceBeanDTO::getCurrentAssetCurrency);
            assetFundDetailVO.setCurrentAssetCurrency(BigDecimalUtils.bigDecimalToString(currentAssetCurrencySum, 2, RoundingMode.DOWN));
            // 费后当前收益（汇总）
            BigDecimal currentAssetCurrencyExFeeSum = calcSumValueFunction(balanceBeanDTOS, BalanceBeanDTO::getCurrentAssetCurrency);
            assetFundDetailVO.setCurrentAssetCurrencyExFee(BigDecimalUtils.bigDecimalToString(currentAssetCurrencyExFeeSum, 2, RoundingMode.DOWN));
            // 持仓份额（汇总）
            BigDecimal totalBalanceVolSum = calcSumValueFunction(balanceBeanDTOS, BalanceBeanDTO::getBalanceVol);
            assetFundDetailVO.setTotalBalanceVol(BigDecimalUtils.bigDecimalToString(totalBalanceVolSum, 2, RoundingMode.DOWN));
            // 股权产品总回款（汇总）
            BigDecimal accumCollectionSum = calcSumValueFunction(balanceBeanDTOS, BalanceBeanDTO::getAccumCollection);
            assetFundDetailVO.setCurrencyTotalCollectionExFee(BigDecimalUtils.bigDecimalToString(accumCollectionSum, 2, RoundingMode.HALF_UP));
            // 币种描述
            assetFundDetailVO.setCurrencyDesc(CurrencyEnum.getDescription(balanceBeanDTO.getCurrency()));
            // 净值分红标识
            assetFundDetailVO.setNavDivFlag(balanceBeanDTO.getNavDivFlag());
            // 净值
            assetFundDetailVO.setNav(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getNav(), 4, RoundingMode.DOWN));
            // 净值日期（yyyy-MM-dd or MM-dd）
            assetFundDetailVO.setNavDate(DateUtils.formatIfNotCurrentYear(balanceBeanDTO.getNavDt()));
            // na标识
            assetFundDetailVO.setNaFlag(balanceBeanDTO.getSxz());
            // 收益计算状态（取母基金下所有份额明细判断）
            String incomeCalStat = BalanceUtils.calculateIncomeCalStat(balanceBeanDTOS);
            assetFundDetailVO.setIncomeCalStat(incomeCalStat);
            // 分期成立
            assetFundDetailVO.setStageEstablishFlag(balanceBeanDTO.getStageEstablishFlag());
            // 总实缴金额
            assetFundDetailVO.setPaidInAmt(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getPaidInAmt(), 2, RoundingMode.DOWN));
            // 待投金额
            assetFundDetailVO.setCurrencyUnPaidInAmt(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getCurrencyUnPaidInAmt(), 2, RoundingMode.DOWN));
            // 持仓收益率
            assetFundDetailVO.setYieldRate(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getYieldRate(), 2, RoundingMode.HALF_UP));
            // 费后持仓收益率
            assetFundDetailVO.setYieldRateExFee(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getYieldRate(), 2, RoundingMode.HALF_UP));
            // 收益率正负符号
            assetFundDetailVO.setShowDirection(getShowDirection(balanceBeanDTO.getCurrentAssetCurrency()));
            // 是否存在明细
            assetFundDetailVO.setHasDetail(balanceBeanDTOS.size() > 1 ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
            // 平衡因子
            assetFundDetailVO.setBalanceFactor(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getBalanceFactor(), 2, RoundingMode.DOWN));
            // 平衡因子转换完成
            assetFundDetailVO.setConvertFinish(balanceBeanDTO.getConvertFinish());
            // 平衡因子日期
            assetFundDetailVO.setBalanceFactorDate(balanceBeanDTO.getBalanceFactorDate());
            // 累计应收管理费
            assetFundDetailVO.setReceivManageFee(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getReceivManageFee(), 2, RoundingMode.HALF_UP));
            // 累计应收业绩报酬
            assetFundDetailVO.setReceivPreformFee(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getReceivPreformFee(), 2, RoundingMode.HALF_UP));
            // 初始投资成本
            assetFundDetailVO.setCurrencyNetBuyAmount(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getCurrencyNetBuyAmount(), 2, RoundingMode.HALF_UP));
            // 股权产品期限&说明
            assetFundDetailVO.setProductTerm(balanceBeanDTO.getCxqxStr());
            assetFundDetailVO.setProductTermDesc(balanceBeanDTO.getCpqxsm());

            assetFundDetailVO.setFundItemVOList(this.buildFundItemVOList(balanceBeanDTOS));
            assetFundDetailVOS.add(assetFundDetailVO);
        });

        log.info("基金持仓产品列表数量:{}", assetFundDetailVOS.size());
        return assetFundDetailVOS;
    }

    /**
     * 构建基金持仓明细列表
     *
     * @param balanceBeanDTOS 基金持仓明细列表
     * @return 基金持仓明细列表
     */
    private List<FundItemVO> buildFundItemVOList(List<BalanceBeanDTO> balanceBeanDTOS) {
        if (CollectionUtils.isEmpty(balanceBeanDTOS)) {
            return new ArrayList<>();
        }

        if (balanceBeanDTOS.size() == 1) {
            return new ArrayList<>();
        }

        List<FundItemVO> fundItemVOS = new ArrayList<>();
        for (BalanceBeanDTO balanceBeanDTO : balanceBeanDTOS) {
            FundItemVO fundItemVO = new FundItemVO();
            // 产品代码
            fundItemVO.setProductCode(balanceBeanDTO.getProductCode());
            // 产品名称
            fundItemVO.setProductName(balanceBeanDTO.getProductName());
            // 参考市值
            fundItemVO.setCurrencyMarketValue(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getCurrencyMarketValue(), 2, RoundingMode.DOWN));
            // 费后持仓市值
            fundItemVO.setCurrencyMarketValueExFee(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getCurrencyMarketValueExFee(), 2, RoundingMode.DOWN));
            // 净值
            fundItemVO.setNav(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getNav(), 4, RoundingMode.DOWN));
            // 净值日期（yyyy-MM-dd or MM-dd）
            fundItemVO.setNavDate(DateUtils.formatIfNotCurrentYear(balanceBeanDTO.getNavDt()));
            // 持仓份额
            fundItemVO.setTotalBalanceVol(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getBalanceVol(), 2, RoundingMode.DOWN));
            // 当前收益（当前币种）
            fundItemVO.setCurrentAssetCurrency(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getCurrentAssetCurrency(), 2, RoundingMode.DOWN));
            // 费后当前收益（当前币种）
            fundItemVO.setCurrentAssetCurrencyExFee(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getCurrentAssetCurrency(), 2, RoundingMode.DOWN));
            // 币种描述
            fundItemVO.setCurrencyDesc(CurrencyEnum.getDescription(balanceBeanDTO.getCurrency()));
            // 当前收益率
            fundItemVO.setYieldRate(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getYieldRate(), 2, RoundingMode.HALF_UP));
            // 费后当前收益率
            fundItemVO.setYieldRateExFee(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getYieldRate(), 2, RoundingMode.HALF_UP));
            // 分红标志
            fundItemVO.setNavDivFlag(balanceBeanDTO.getNavDivFlag());
            // 累计应收管理费
            fundItemVO.setReceivManageFee(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getReceivManageFee(), 2, RoundingMode.HALF_UP));
            // 累计应收业绩报酬
            fundItemVO.setReceivPreformFee(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getReceivPreformFee(), 2, RoundingMode.HALF_UP));
            // 初始投资成本
            fundItemVO.setCurrencyNetBuyAmount(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getCurrencyNetBuyAmount(), 2, RoundingMode.HALF_UP));
            // 平衡因子
            fundItemVO.setBalanceFactor(BigDecimalUtils.bigDecimalToString(balanceBeanDTO.getBalanceFactor(), 2, RoundingMode.DOWN));
            // 收益计算状态
            String incomeCalStat = BalanceUtils.calculateIncomeCalStat(Lists.newArrayList(balanceBeanDTO));
            fundItemVO.setIncomeCalStat(incomeCalStat);
            // 清算标识
            fundItemVO.setCrisisFlag(balanceBeanDTO.getCrisisFlag());
            // 平衡因子转换完成
            fundItemVO.setConvertFinish(balanceBeanDTO.getConvertFinish());
            // 平衡因子日期
            fundItemVO.setBalanceFactorDate(balanceBeanDTO.getBalanceFactorDate());
            fundItemVOS.add(fundItemVO);
        }
        return fundItemVOS;
    }

    /**
     * 针对基金持仓明细具体指标字段求和计算器
     *
     * @param list     基金持仓明细列表
     * @param function 取值函数
     * @return 求和结果
     */
    private BigDecimal calcSumValueFunction(List<BalanceBeanDTO> list, Function<BalanceBeanDTO, BigDecimal> function) {
        if (CollectionUtils.isEmpty(list) || function == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal sumVal = list.stream()
                .map(function)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        log.info("calcSumValue 计算指标:{}， 结果:{}", function, sumVal);
        return sumVal;
    }

    /**
     * 获取持仓产品列表显示方向 1-正值 0-等于 -1-负值
     *
     * @param assetCurrency 收益率值
     * @return String 显示方向
     */
    private static String getShowDirection(BigDecimal assetCurrency) {
        if (assetCurrency != null && assetCurrency.compareTo(BigDecimal.ZERO) > 0) {
            return Constants.ONE;
        } else if (assetCurrency != null && assetCurrency.compareTo(BigDecimal.ZERO) == 0) {
            return Constants.ZERO;
        } else {
            return Constants.MINUS_ONE;
        }
    }

    /**
     * 构建持仓产品明细是否存在海外公募 or 海外私募
     *
     * @param balanceList 持仓明细列表
     * @return String 是否存在海外公募或海外私募，1-表示存在，0-表示不存在
     */
    private String getExistOverseasFundFlag(List<BalanceBeanDTO> balanceList) {
        if (CollectionUtils.isEmpty(balanceList)) {
            return YesNoEnum.NO.getCode();
        }

        // 遍历持仓明细列表，判断是否存在海外公募产品或海外私募（阳光私募）
        for (BalanceBeanDTO balanceBeanDTO : balanceList) {
            // 判断产品业务类型
            ProductBizTypeEnum productBizTypeEnum = ProductUtils.getProductType(balanceBeanDTO.getHkSaleFlag(),
                    balanceBeanDTO.getSfhwcxg(), balanceBeanDTO.getProductType(), balanceBeanDTO.getProductSubType());

            // 如果存在海外公募产品，直接返回YES
            if (ProductBizTypeEnum.OVERSEAS_PUBLIC.equals(productBizTypeEnum)) {
                return YesNoEnum.YES.getCode();
            }

            // 如果是海外私募产品，进一步判断产品业务子类型
            if (ProductBizTypeEnum.OVERSEAS_PRIVATE.equals(productBizTypeEnum)) {
                ProductSubBizTypeEnum productSubBizTypeEnum = ProductUtils.getProductSubType(balanceBeanDTO.getHkSaleFlag(),
                        balanceBeanDTO.getSfhwcxg(), balanceBeanDTO.getProductType(), balanceBeanDTO.getProductSubType());

                // 如果是阳光私募，返回YES
                if (ProductSubBizTypeEnum.SUNSHINE_PRIVATE.equals(productSubBizTypeEnum)) {
                    return YesNoEnum.YES.getCode();
                }
            }
        }

        // 如果遍历完所有持仓都没有找到海外公募或海外私募（阳光私募），返回0
        return YesNoEnum.NO.getCode();
    }

    /**
     * 构建持仓产品明细是否存在私募股权产品
     *
     * @param balanceList 持仓明细列表
     * @return String 是否存在海外私募股权产品，1-表示存在，0-表示不存在
     */
    private String getExistOverseasGqFundFlag(List<BalanceBeanDTO> balanceList) {
        if (CollectionUtils.isEmpty(balanceList)) {
            return YesNoEnum.NO.getCode();
        }

        // 遍历持仓明细列表，判断是否存在私募股权产品
        for (BalanceBeanDTO balanceBeanDTO : balanceList) {
            // 判断产品业务类型
            ProductBizTypeEnum productBizTypeEnum = ProductUtils.getProductType(balanceBeanDTO.getHkSaleFlag(),
                    balanceBeanDTO.getSfhwcxg(), balanceBeanDTO.getProductType(), balanceBeanDTO.getProductSubType());

            // 如果是海外私募产品，进一步判断产品业务子类型
            if (ProductBizTypeEnum.OVERSEAS_PRIVATE.equals(productBizTypeEnum)) {
                ProductSubBizTypeEnum productSubBizTypeEnum = ProductUtils.getProductSubType(balanceBeanDTO.getHkSaleFlag(),
                        balanceBeanDTO.getSfhwcxg(), balanceBeanDTO.getProductType(), balanceBeanDTO.getProductSubType());

                // 如果是私募股权产品，返回YES
                if (ProductSubBizTypeEnum.PRIVATE_EQUITY.equals(productSubBizTypeEnum)) {
                    return YesNoEnum.YES.getCode();
                }
            }
        }

        // 如果遍历完所有持仓都没有找到私募股权产品，返回0
        return YesNoEnum.NO.getCode();
    }

    /**
     * @param hkFundTxAcctDTOS 基金交易账号DTO
     * @return FundTxAcctVO
     * @description: 转换基金交易账号VO
     * @author: 陈杰文
     * @date: 2025-07-11 10:42:21
     */
    private List<FundTxAcctVO> buildFundTxAcctVOList(List<HkFundTxAcctDTO> hkFundTxAcctDTOS) {
        if (CollectionUtils.isEmpty(hkFundTxAcctDTOS)) {
            return new ArrayList<>();
        }

        List<FundTxAcctVO> fundTxAcctVOList = new ArrayList<>();
        for (int i = 0; i < hkFundTxAcctDTOS.size(); i++) {
            FundTxAcctVO fundTxAcctVO = new FundTxAcctVO();
            fundTxAcctVO.setFundTxAcctNo(hkFundTxAcctDTOS.get(i).getFundTxAcctNo());
            // 基金交易账号名称：全权委托账户 + 遍历序号（01、02、03...）
            String accountIndex = String.format("%02d", i + 1);
            fundTxAcctVO.setFundTxAcctName("全权委托账户" + accountIndex);
            fundTxAcctVOList.add(fundTxAcctVO);
        }

        return fundTxAcctVOList;
    }

    /**
     * @param request 查询储蓄罐资产请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.balance.QueryPiggyAssetVO
     * @description: 查询储蓄罐资产接口
     * <AUTHOR>
     * @date 2025-06-17 19:00:00
     */
    public QueryPiggyAssetVO queryPiggyAssetV1(QueryPiggyAssetRequest request) {

        // 构建上下文
        BalanceContent content = BalanceContent.builder()
                .hkCustNo(request.getHkCustNo())
                .disPlayCurrencyCode(request.getDisPlayCurrencyCode())
                .fundTxAcctNoDTOList(fundTxAcctNoService.queryFundTxAcctNo(request.getHkCustNo()))
                .build();

        // 在途数据
        AllInTransitTradeBalancePortfolioDetail allInTransitTradeBalancePortfolioDetail = portfolioCalculator.calculate(content, HkCustBalanceDisPalyCategoryEnum.NON_FULL_PIGGY_IN_TRANSIT, AllInTransitTradeBalancePortfolioDetail.class);
        // 基金数据
        PiggyBalancePortfolio portfolioDetail = portfolioCalculator.calculate(content, HkCustBalanceDisPalyCategoryEnum.NON_FULL_PIGGY, PiggyBalancePortfolio.class);
        // 获取签约储蓄罐基金
        PiggyAgreementSignDetailDTO hkCustPiggyAgreement = hkPiggyBankOuterService.getHkCustPiggyAgreement(request.getHkCustNo());

        // 获取明细
        List<BalanceBeanDTO> itemList = portfolioDetail.getItemList();

        // 根据基金编码分组，支持多储蓄罐基金
        Map<String, List<BalanceBeanDTO>> fundCodeMap = itemList.stream().collect(Collectors.groupingBy(BalanceBeanDTO::getProductCode));
        // 获取产品维度信息
        List<PiggyAssetFundListVO> piggyAssetFundListVOS = getPiggyAssetFundListVOS(fundCodeMap);

        return new QueryPiggyAssetVOBuilder()
                .setShowAsset(buildShowAsset(request.getHkCustNo()))
                .setPiggyPortfolio(portfolioDetail)
                .setInTransitDetail(allInTransitTradeBalancePortfolioDetail)
                .setPiggySignInfo(hkCustPiggyAgreement)
                .setPiggyAssetFundList(piggyAssetFundListVOS)
                .build();
    }

    /**
     * @param fundCodeMap 持仓数据
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.balance.PiggyAssetFundListVO>
     * @description: 获取基金持仓列表参数信息
     * @author: jinqing.rao
     * @date: 2025/6/30 14:13
     * @since JDK 1.8
     */
    private static List<PiggyAssetFundListVO> getPiggyAssetFundListVOS(Map<String, List<BalanceBeanDTO>> fundCodeMap) {
        return fundCodeMap.keySet().stream().map(fundCode -> {
            List<BalanceBeanDTO> balanceBeanDTOS = fundCodeMap.get(fundCode);
            String productName = balanceBeanDTOS.get(0).getProductName();
            PiggyAssetFundListVO piggyAssetFundListVO = new PiggyAssetFundListVO();
            piggyAssetFundListVO.setFundName(productName);
            piggyAssetFundListVO.setFundCode(fundCode);
            piggyAssetFundListVO.setCurrencyMarketValue(BigDecimalUtils.bigDecimalToString(balanceBeanDTOS.get(0).getCurrencyMarketValue(), 2, RoundingMode.DOWN));
            piggyAssetFundListVO.setCurrentAssetCurrencyExFee(BigDecimalUtils.bigDecimalToString(balanceBeanDTOS.get(0).getCurrentAssetCurrency(), 2, RoundingMode.DOWN));
            piggyAssetFundListVO.setCurrencyDesc(CurrencyEnum.getDescription(balanceBeanDTOS.get(0).getCurrency()));
            piggyAssetFundListVO.setNav(BigDecimalUtils.bigDecimalToString(balanceBeanDTOS.get(0).getNav(), 4, RoundingMode.DOWN));
            piggyAssetFundListVO.setNavDate(DateUtils.formatDateStr(balanceBeanDTOS.get(0).getNavDt(), DateUtils.YYYY_MM_DD, DateUtils.YYYYMMDD));
            piggyAssetFundListVO.setIncomeCalStat(balanceBeanDTOS.get(0).getIncomeCalStat());
            piggyAssetFundListVO.setYieldRateExFee(BigDecimalUtils.bigDecimalToString(balanceBeanDTOS.get(0).getYieldRate(), 4, RoundingMode.DOWN));
            if (balanceBeanDTOS.get(0).getCurrentAssetCurrency() != null && balanceBeanDTOS.get(0).getCurrentAssetCurrency().compareTo(BigDecimal.ZERO) > 0) {
                piggyAssetFundListVO.setShowDirection(Constants.ONE);
            } else if (balanceBeanDTOS.get(0).getCurrentAssetCurrency() != null && balanceBeanDTOS.get(0).getCurrentAssetCurrency().compareTo(BigDecimal.ZERO) == 0) {
                piggyAssetFundListVO.setShowDirection(Constants.ZERO);
            } else {
                piggyAssetFundListVO.setShowDirection(Constants.MINUS_ONE);
            }
            return piggyAssetFundListVO;
        }).collect(Collectors.toList());
    }
}