/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkaccount;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.account.*;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAcctAppPersonalCenterRequest;
import com.howbuy.crm.cgi.extservice.service.hkaccount.CustInfoService;
import com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct.OpenAcctService;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.*;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkAcctAppPersonalCenterVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @description: (客户信息)
 * @date 2023/5/18 14:01
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/cust")
@Slf4j
public class HkCustController {

    @Autowired
    private CustInfoService custInfoService;

    @Autowired
    private OpenAcctService openAcctService;


    /**
     * @api {POST} /ext/hkaccount/cust/getpersonalcenterinfo getPersonalCenterInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkCustController
     * @apiName getPersonalCenterInfo()
     * @apiDescription 查询客户个人中心信息
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱地址掩码
     * @apiSuccess (响应结果) {String} data.emailDigest 邮箱地址密文
     * @apiSuccess (响应结果) {String} data.riskToleranceLevel 客户风险等级      风险等级      0-保守型（最低）      1-保守型      2-稳健型      3-平衡型      4-成长型      5-进取型
     * @apiSuccess (响应结果) {String} data.bindCardCount 银行卡数量
     * @apiSuccess (响应结果) {String} data.custLoginPasswdType 登录密码设置标识	 0正常状态 1重置状态 2-未设置
     * @apiSuccess (响应结果) {String} data.custTxPasswdType 交易密码设置标识	 0正常状态 1重置状态 2-未设置
     * @apiSuccess (响应结果) {String} data.realNameCompletedFlag 实名信息是否完整标识	  0-不完整；1-完整
     * @apiSuccess (响应结果) {String} data.riskToleranceExpire 客户风测过期标识 1：是 0：否
     * @apiSuccess (响应结果) {String} data.openDepositsStatus 开户入金状态      01-去开户；02-继续开户；03-查看开户进度；04-修改开户资料；05-去入金；06-查看入金进度；07- 修改入金资料；08-隐藏开户入金区域
     * @apiSuccess (响应结果) {String} data.openAcctStepFlag 开户步骤标识      01-证件信息页；02-个人信息页；03-职业信息页；04-声明信息页；05-投资经验页；06-银行卡页；07-电子签名页
     * @apiSuccess (响应结果) {String} data.hboneBindStatus 一账通绑定状态 0-未绑定 1-已绑定
     * @apiSuccess (响应结果) {String} data.custState 客户状态0-正常 1-注销 2-休眠 3-注册 4-开户申请成功
     * @apiSuccess (响应结果) {String} data.loginActivate 登录是否激活  1:是   0：否
     * @apiSuccess (响应结果) {String} data.transactionActivation 交易是否激活  1:是  0：否
     * @apiSuccess (响应结果) {String} data.investorType NORMAL:普通(去认证) PRO:专业
     * @apiSuccess (响应结果) {String} timestampServer 业务耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"eXlajRRsIB","data":{"riskToleranceLevel":"28X","custLoginPasswdType":"fB0pOizq","bindCardCount":"Hsi6tbPYb3","hkCustNo":"hE","emailDigest":"gYAg6","openDepositsStatus":"IwbCoU","openAcctStepFlag":"ar5IiRyJ7","custTxPasswdType":"3","riskToleranceExpire":"ugxWJmdzV","realNameCompletedFlag":"rkbbtBEZN","mobileDigest":"QHD2uSKW","emailMask":"Zb","mobileMask":"DyF8hle"},"description":"TWv3bMH","timestampServer":"5MdfBs"}
     */
    @PostMapping("/getpersonalcenterinfo")
    public CgiResponse<PersonalCenterInfoVO> getPersonalCenterInfo(@RequestBody HkCustPersonalCenterRequest request) {
        PersonalCenterInfoVO personalCenterInfoVO = custInfoService.getPersonalCenterInfo();
        return CgiResponse.ok(personalCenterInfoVO);
    }


    /**
     * @api {POST} /ext/hkaccount/cust/app/getpersonalcenterinfo getAppPersonalCenterInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkCustController
     * @apiName getAppPersonalCenterInfo()
     * @apiDescription App个人中心接口
     * @apiParam (请求体) {String} hkCustNo
     * @apiParamExample 请求体示例
     * {"hkCustNo":"z"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.mobileAreaCode 手机号地区码
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱地址掩码
     * @apiSuccess (响应结果) {String} data.emailDigest 邮箱地址密文
     * @apiSuccess (响应结果) {String} data.riskToleranceLevel 客户风险等级      风险等级      0-保守型（最低）      1-保守型      2-稳健型      3-平衡型      4-成长型      5-进取型
     * @apiSuccess (响应结果) {String} data.bindCardCount 银行卡数量
     * @apiSuccess (响应结果) {String} data.riskToleranceExpire 客户风测过期标识 1：是 0：否
     * @apiSuccess (响应结果) {String} data.custState 客户状态0-正常 1-注销 2-休眠 3-注册 4-开户申请成功
     * @apiSuccess (响应结果) {String} data.investorType NORMAL:普通(去认证) PRO:专业
     * @apiSuccess (响应结果) {String} data.hboneBindStatus 一账通绑定状态 0-未绑定 1-已绑定
     * @apiSuccess (响应结果) {String} data.openDepositsStatus 开户入金状态      01-去开户；02-继续开户；03-查看开户进度；04-修改开户资料；05-去入金；06-查看入金进度；07- 修改入金资料；08-隐藏开户入金区域
     * @apiSuccess (响应结果) {String} data.openAcctStep 开户订单信息,填写到具体的步骤,开户步骤标识：01-证件信息页；02-个人信息页；03-职业信息页；04-声明信息页；05-投资经验页；06-银行卡页；07-电子签名页      该字段通过循环查询缓存,判断最大不步骤页
     * @apiSuccess (响应结果) {String} data.openAcctStepProgress 开户的具体进度
     * @apiSuccess (响应结果) {String} data.openType 开户方式 0-线下 1-线上
     * @apiSuccess (响应结果) {String} data.custTxPasswdType 交易密码设置标识 0正常状态 1重置状态 2-未设置(包含为空的情况)
     * @apiSuccess (响应结果) {String} data.custLoginPasswdType 登录密码设置标识	 0正常状态 1重置状态 2-未设置
     * @apiSuccess (响应结果) {String} data.idType 客户证件类型
     * @apiSuccess (响应结果) {String} data.idTypeDesc 证件类型描述
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.birthday 生日
     * @apiSuccess (响应结果) {String} data.custName 姓名
     * @apiSuccess (响应结果) {String} data.loginActivate 登录是否激活  1:是   0：否
     * @apiSuccess (响应结果) {String} data.transactionActivation 交易是否激活  1:是  0：否
     * @apiSuccess (响应结果) {String} data.investorAuditStatus 专业投资者 1:是 0:否
     * @apiSuccess (响应结果) {String} data.investorAssetsEffectiveStatus 专业投资者 资产有效时间，1 :是 0：否
     * @apiSuccess (响应结果) {String} data.piggyBankSignStatus 是否签署海外储蓄罐
     * @apiSuccess (响应结果) {String} data.derivativeKnowledge 是否具有衍生工具知识 0-无 1-有
     * @apiSuccess (响应结果) {String} data.hasOverseasReport 是否有海外报告 1 是 0 否
     * @apiSuccess (响应结果) {String} data.overseasReportLatestDt 海外报告最新时间
     * @apiSuccess (响应结果) {String} data.gender 性别 0-女，1-男
     * @apiSuccess (响应结果) {String} data.nickname 昵称
     * @apiSuccess (响应结果) {String} data.hkCustNoPlaintext 香港客户号明文
     * @apiSuccess (响应结果) {String} data.paperSignUploaded 是否上传纸质签名 1是 0否
     * @apiSuccess (响应结果) {String} data.paperSignOrderNo 纸质签名订单号
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "OY",
     *   "data": {
     *     "birthday": "80tGLYas",
     *     "loginActivate": "MOqHes",
     *     "derivativeKnowledge": "CgaLRh",
     *     "transactionActivation": "x0LDtdQ",
     *     "openDepositsStatus": "J0Oq89dLpk",
     *     "openAcctStep": "SIe6",
     *     "openType": "r",
     *     "investorAssetsEffectiveStatus": "NUbRTX7",
     *     "mobileAreaCode": "v3JPe74",
     *     "custTxPasswdType": "f",
     *     "investorAuditStatus": "UwM4",
     *     "hboneBindStatus": "UFnT",
     *     "mobileDigest": "G42R",
     *     "hboneNo": "Xs",
     *     "riskToleranceLevel": "sN",
     *     "custLoginPasswdType": "N8",
     *     "idType": "2wf",
     *     "bindCardCount": "cuF",
     *     "hkCustNo": "AThkJdI",
     *     "openAcctStepProgress": "YtwV",
     *     "emailDigest": "w9Wc",
     *     "custName": "uE",
     *     "hasOverseasReport": "q",
     *     "riskToleranceExpire": "aIvskvf5T",
     *     "emailMask": "ylmi04XWd",
     *     "overseasReportLatestDt": "MpYI",
     *     "custState": "ca0HJdW",
     *     "idTypeDesc": "BvPU",
     *     "piggyBankSignStatus": "cCq",
     *     "mobileMask": "QM5H",
     *     "investorType": "JV"
     *   },
     *   "description": "2PIjV",
     *   "timestampServer": "Gtv5euLZb"
     * }
     */
    @PostMapping("/app/getpersonalcenterinfo")
    public CgiResponse<HkAcctAppPersonalCenterVO> getAppPersonalCenterInfo(@RequestBody HkAcctAppPersonalCenterRequest request) {
        BasicDataTypeValidator.validator(request);
        HkAcctAppPersonalCenterVO personalCenterInfoVO = custInfoService.getAppPersonalCenterInfo(request);
        return CgiResponse.appOk(personalCenterInfoVO);
    }


    /**
     * @api {POST} /hkaccount/cust/getcustrealnameinfo 查询客户实名信息
     * @apiVersion 1.0.0
     * @apiGroup CustController
     * @apiName getCustRealNameInfo()
     * @apiDescription 查询客户实名信息
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.custName 客户名称
     * @apiSuccess (响应结果) {String} data.custEnName 客户英文名
     * @apiSuccess (响应结果) {String} data.invstType 投资者类型0-机构,1-个人,2-产品户
     * @apiSuccess (响应结果) {String} data.idType 证件类型      <p>      大陆身份证-0      香港身份证-D      澳门身份证-E      台湾身份证-F      中国护照-1      外国护照-6      港澳通行证-4      台胞证-A      港澳台居民居住证-C      其他证件-7
     * @apiSuccess (响应结果) {String} data.idTypeDesc 证件类型描述
     * @apiSuccess (响应结果) {String} data.idNoDigest 证件号码摘要
     * @apiSuccess (响应结果) {String} data.idNoMask 证件号码掩码
     * @apiSuccess (响应结果) {String} data.idValidityEnd 证件有效期截止日 yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.idAlwaysValidFlag 证件是否长期有效0-否 1-是
     * @apiSuccess (响应结果) {String} data.idImageUploadStatus 证件图片上传状态0-未上传 1-已上传
     * @apiSuccess (响应结果) {String} data.nationality 国籍
     * @apiSuccess (响应结果) {String} data.nationalityDesc 国籍描述
     * @apiSuccess (响应结果) {String} data.birthday 出生日期 yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.gender 性别 0-女，1-男
     * @apiSuccess (响应结果) {String} data.residentialAddressDesc 现居地址描述
     * @apiSuccess (响应结果) {String} data.mailingAddressDesc 通信地址描述
     * @apiSuccess (响应结果) {String} data.emplStatusDesc 就业状况
     * @apiSuccess (响应结果) {String} data.emplStatus 就业状况描述
     * @apiSuccess (响应结果) {String} data.incLevel 年收入
     * @apiSuccess (响应结果) {String} data.incLevelDesc 年收入描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"raE9hBrym","data":{"birthday":"Bzuzok9","idType":"zke","gender":"jlqJbj","idAlwaysValidFlag":"kYr","description":"lf9F","idNoDigest":"C6vXNypS","custName":"LIo","idNoMask":"2dKuVW0l","returnCode":"WWhejCzwu","idImageUploadStatus":"4wc","nationality":"saF","idValidityEnd":"vNGhU48gt","invstType":"kmSU","nationalityDesc":"3m75wVtyEf"},"description":"5uiWmb7y","timestampServer":"NzsgQz2w7C"}
     */
    @PostMapping("/getcustrealnameinfo")
    public CgiResponse<CustRealNameInfoVO> getCustRealNameInfo(@RequestBody(required = false)HkCustPersonalCenterRequest request) {
        CustRealNameInfoVO custRealNameInfo = custInfoService.getCustRealNameInfoNewWay();
        return CgiResponse.ok(custRealNameInfo);
    }


    /**
     * @api {POST} /hkaccount/cust/getbankcardlist 查询银行卡列表
     * @apiVersion 1.0.0
     * @apiGroup CustController
     * @apiName getBankCardList()
     * @apiDescription 查询银行卡列表
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.bankCardInfoVOList
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.hkCpAcctNo 香港资金账号
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankCode 银行代码
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankName 银行名称 银行中文名称为空取银行名称，否则取银行中文名称
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankRegionCode 联行号
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankAcctName 银行账户名称
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankAcctDigest 摘要-银行账号
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankAcctMask 掩码-银行账号
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankAcctStatus 银行账户状态0-正常 1-注销
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"1xqGehi3B","data":{"returnCode":"tjqfE","description":"7N3to","bankCardInfoVOList":[{"bankRegionCode":"SnNxAZ5","hkCpAcctNo":"kN","bankCode":"nT","bankAcctName":"TEROYQYc","bankAcctMask":"ihv2a","bankName":"lEoNDXa4V","bankAcctDigest":"qjf3fqxDP4","bankAcctStatus":"3Al"}]},"description":"UJIBPJrAx","timestampServer":"z3bMBoP4"}
     */
    @PostMapping("/getbankcardlist")
    public CgiResponse<BankCardListVO> getBankCardList() {
        BankCardListVO bankCardListVO = custInfoService.getBankCardList();
        return CgiResponse.ok(bankCardListVO);
    }


    /**
     * @api {POST} /ext/hkaccount/cust/getopencustfilelist 获取开户文件列表数据 (个人中心-开户文件查询)
     * @apiVersion 1.0.0
     * @apiGroup OpenAcctController
     * @apiName getOpenCustFileList
     * @apiDescription 获取开户文件列表数据 (个人中心-开户文件查询)
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.ebrokerID ebrokerID
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱验码
     * @apiSuccess (响应结果) {Array} data.openFileVOList 开户文件列表
     * @apiSuccess (响应结果) {String} data.openFileVOList.filaName 文件名称
     * @apiSuccess (响应结果) {String} data.openFileVOList.fileUrl 文件URL
     * @apiSuccess (响应结果) {String} data.openFileVOList.fileType 文件类型
     * @apiSuccess (响应结果) {String} data.openFileVOList.fileTypeName 文件类型名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"6ViLQg81","data":{"emailMask":"suxubxSQdj","openFileVOList":[{"fileUrl":"y5d","filaName":"nuK0"}],"ebrokerID":"3hA0RKGGYY"},"description":"KSnf","timestampServer":"Wh"}
     */
    @PostMapping("/getopencustfilelist")
    @ResponseBody
    public CgiResponse<OpenCustFileVO> getOpenCustFileList() {
        return CgiResponse.ok(openAcctService.getOpenCustFileVO());
    }

    /**
     * @api {POST} /hkaccount/cust/getbankcardplaintext 查询银行卡明文
     * @apiVersion 1.0.0
     * @apiGroup CustController
     * @apiName getBankCardPlaintext()
     * @apiDescription 查询银行卡明文
     * @apiParam (请求体) {String} tradePassword 交易密码 必须
     * @apiParam (请求体) {String} hkCpAcctNo 香港资金账号 必须
     * @apiParamExample 请求体示例
     * {"hkCpAcctNo":"b4MlpWe","tradePassword":"udpRZnos"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hkCpAcctNo 香港资金账号
     * @apiSuccess (响应结果) {String} data.bankAcct 银行账号
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"2NP","data":{"hkCpAcctNo":"G","returnCode":"HOmTYJjWjE","bankAcct":"Gb","description":"uSA"},"description":"DigM9oon","timestampServer":"X4E"}
     */
    @PostMapping("/getbankcardplaintext")
    public CgiResponse<BankCardPlaintextVO> getBankCardPlaintext(@RequestBody BankCardPlaintextRequest request) {
        BankCardPlaintextVO bankCardPlaintextVO = custInfoService.getBankCardPlaintext(request);
        return CgiResponse.ok(bankCardPlaintextVO);
    }

    /**
     * @api {POST} /hkaccount/cust/getcustinfo 查询客户信息
     * @apiVersion 1.0.0
     * @apiGroup CustController
     * @apiName getCustInfo()
     * @apiDescription 查询客户信息
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.invstType 客户类型，0-机构,1-个人,2-产品户
     * @apiSuccess (响应结果) {String} data.idType 证件类型           大陆身份证-0      香港身份证-D      澳门身份证-E      台湾身份证-F      中国护照-1      外国护照-6      港澳通行证-4      台胞证-A      港澳台居民居住证-C      其他证件-7
     * @apiSuccess (响应结果) {String} data.idTypeDesc 证件类型描述
     * @apiSuccess (响应结果) {String} data.idNoDigest 证件号摘要
     * @apiSuccess (响应结果) {String} data.idNoMask 证件号掩码
     * @apiSuccess (响应结果) {String} data.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.mobileVerifyStatus 手机号验证状态 0:未验证 1:已验证
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱地址掩码
     * @apiSuccess (响应结果) {String} data.emailDigest 邮箱地址密文
     * @apiSuccess (响应结果) {String} data.emailVerifyStatus 邮箱验证状态0:未验证 1:已验证
     * @apiSuccess (响应结果) {String} data.custLoginPasswdType 登录密码设置标识	 0正常状态 1重置状态 2-未设置
     * @apiSuccess (响应结果) {String} data.custTxPasswdType 交易密码设置标识	 0正常状态 1重置状态 2-未设置
     * @apiSuccess (响应结果) {String} data.idValidityEnd 证件有效期截止日 yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.idAlwaysValidFlag 证件是否长期有效0-否 1-是
     * @apiSuccess (响应结果) {String} data.idImageUploadStatus 证件图片上传状态0-未上传 1-已上传
     * @apiSuccess (响应结果) {String} data.nationality 国籍       NATIONALITY_MAP.put("CN", "中国内地");              NATIONALITY_MAP.put("AD", "安道尔");              NATIONALITY_MAP.put("AE", "阿联酋");              NATIONALITY_MAP.put("AF", "阿富汗");              NATIONALITY_MAP.put("AG", "安提瓜和巴布达");              NATIONALITY_MAP.put("AI", "安圭拉");              NATIONALITY_MAP.put("AL", "阿尔巴尼亚");              NATIONALITY_MAP.put("AM", "亚美尼亚");              NATIONALITY_MAP.put("AO", "安哥拉");              NATIONALITY_MAP.put("AQ", "南极洲");              NATIONALITY_MAP.put("AR", "阿根廷");              NATIONALITY_MAP.put("AS", "美属萨摩亚");              NATIONALITY_MAP.put("AT", "奥地利");              NATIONALITY_MAP.put("AU", "澳大利亚");              NATIONALITY_MAP.put("AW", "阿鲁巴");              NATIONALITY_MAP.put("AX", "奥兰群岛");              NATIONALITY_MAP.put("AZ", "阿塞拜疆");              NATIONALITY_MAP.put("BA", "波黑");              NATIONALITY_MAP.put("BB", "巴巴多斯");              NATIONALITY_MAP.put("BD", "孟加拉");              NATIONALITY_MAP.put("BE", "比利时");              NATIONALITY_MAP.put("BF", "布基纳法索");              NATIONALITY_MAP.put("BG", "保加利亚");              NATIONALITY_MAP.put("BH", "巴林");              NATIONALITY_MAP.put("BI", "布隆迪");              NATIONALITY_MAP.put("BJ", "贝宁");              NATIONALITY_MAP.put("BL", "圣巴泰勒米岛");              NATIONALITY_MAP.put("BM", "百慕大");              NATIONALITY_MAP.put("BN", "文莱");              NATIONALITY_MAP.put("BO", "玻利维亚");              NATIONALITY_MAP.put("BQ", "荷兰加勒比区");              NATIONALITY_MAP.put("BR", "巴西");              NATIONALITY_MAP.put("BS", "巴哈马");              NATIONALITY_MAP.put("BT", "不丹");              NATIONALITY_MAP.put("BV", "布韦岛");              NATIONALITY_MAP.put("BW", "博茨瓦纳");              NATIONALITY_MAP.put("BY", "白俄罗斯");              NATIONALITY_MAP.put("BZ", "伯利兹");              NATIONALITY_MAP.put("CA", "加拿大");              NATIONALITY_MAP.put("CC", "科科斯群岛");              NATIONALITY_MAP.put("CD", "刚果（金）");              NATIONALITY_MAP.put("CF", "中非");              NATIONALITY_MAP.put("CG", "刚果（布）");              NATIONALITY_MAP.put("CH", "瑞士");              NATIONALITY_MAP.put("CI", "科特迪瓦");              NATIONALITY_MAP.put("CK", "库克群岛");              NATIONALITY_MAP.put("CL", "智利");              NATIONALITY_MAP.put("CM", "喀麦隆");              NATIONALITY_MAP.put("HK", "中国香港");              NATIONALITY_MAP.put("MO", "中国澳门");              NATIONALITY_MAP.put("TW", "中国台湾");              NATIONALITY_MAP.put("CO", "哥伦比亚");              NATIONALITY_MAP.put("CR", "哥斯达黎加");              NATIONALITY_MAP.put("CU", "古巴");              NATIONALITY_MAP.put("CV", "佛得角");              NATIONALITY_MAP.put("CX", "圣诞岛");              NATIONALITY_MAP.put("CY", "塞浦路斯");              NATIONALITY_MAP.put("CZ", "捷克");              NATIONALITY_MAP.put("DE", "德国");              NATIONALITY_MAP.put("DJ", "吉布提");              NATIONALITY_MAP.put("DK", "丹麦");              NATIONALITY_MAP.put("DM", "多米尼克");              NATIONALITY_MAP.put("DO", "多米尼加");              NATIONALITY_MAP.put("DZ", "阿尔及利亚");              NATIONALITY_MAP.put("EC", "厄瓜多尔");              NATIONALITY_MAP.put("EE", "爱沙尼亚");              NATIONALITY_MAP.put("EG", "埃及");              NATIONALITY_MAP.put("EH", "西撒哈拉");              NATIONALITY_MAP.put("ER", "厄立特里亚");              NATIONALITY_MAP.put("ES", "西班牙");              NATIONALITY_MAP.put("ET", "埃塞俄比亚");              NATIONALITY_MAP.put("FI", "芬兰");              NATIONALITY_MAP.put("FJ", "斐济群岛");              NATIONALITY_MAP.put("FK", "马尔维纳斯群岛（福克兰）");              NATIONALITY_MAP.put("FM", "密克罗尼西亚联邦");              NATIONALITY_MAP.put("FO", "法罗群岛");              NATIONALITY_MAP.put("FR", "法国");              NATIONALITY_MAP.put("GA", "加蓬");              NATIONALITY_MAP.put("GB", "英国");              NATIONALITY_MAP.put("GD", "格林纳达");              NATIONALITY_MAP.put("GE", "格鲁吉亚");              NATIONALITY_MAP.put("GF", "法属圭亚那");              NATIONALITY_MAP.put("GG", "根西岛");              NATIONALITY_MAP.put("GH", "加纳");              NATIONALITY_MAP.put("GI", "直布罗陀");              NATIONALITY_MAP.put("GL", "格陵兰");              NATIONALITY_MAP.put("GM", "冈比亚");              NATIONALITY_MAP.put("GN", "几内亚");              NATIONALITY_MAP.put("GP", "瓜德罗普");              NATIONALITY_MAP.put("GQ", "赤道几内亚");              NATIONALITY_MAP.put("GR", "希腊");              NATIONALITY_MAP.put("GS", "南乔治亚岛和南桑威奇群岛");              NATIONALITY_MAP.put("GT", "危地马拉");              NATIONALITY_MAP.put("GU", "关岛");              NATIONALITY_MAP.put("GW", "几内亚比绍");              NATIONALITY_MAP.put("GY", "圭亚那");              NATIONALITY_MAP.put("HM", "赫德岛和麦克唐纳群岛");              NATIONALITY_MAP.put("HN", "洪都拉斯");              NATIONALITY_MAP.put("HR", "克罗地亚");              NATIONALITY_MAP.put("HT", "海地");              NATIONALITY_MAP.put("HU", "匈牙利");              NATIONALITY_MAP.put("ID", "印尼");              NATIONALITY_MAP.put("IE", "爱尔兰");              NATIONALITY_MAP.put("IL", "以色列");              NATIONALITY_MAP.put("IM", "马恩岛");              NATIONALITY_MAP.put("IN", "印度");              NATIONALITY_MAP.put("IO", "英属印度洋领地");              NATIONALITY_MAP.put("IQ", "伊拉克");              NATIONALITY_MAP.put("IR", "伊朗");              NATIONALITY_MAP.put("IS", "冰岛");              NATIONALITY_MAP.put("IT", "意大利");              NATIONALITY_MAP.put("JE", "泽西岛");              NATIONALITY_MAP.put("JM", "牙买加");              NATIONALITY_MAP.put("JO", "约旦");              NATIONALITY_MAP.put("JP", "日本");              NATIONALITY_MAP.put("KE", "肯尼亚");              NATIONALITY_MAP.put("KG", "吉尔吉斯斯坦");              NATIONALITY_MAP.put("KH", "柬埔寨");              NATIONALITY_MAP.put("KI", "基里巴斯");              NATIONALITY_MAP.put("KM", "科摩罗");              NATIONALITY_MAP.put("KN", "圣基茨和尼维斯");              NATIONALITY_MAP.put("KP", "朝鲜");              NATIONALITY_MAP.put("KR", "韩国");              NATIONALITY_MAP.put("KW", "科威特");              NATIONALITY_MAP.put("KY", "开曼群岛");              NATIONALITY_MAP.put("KZ", "哈萨克斯坦");              NATIONALITY_MAP.put("LA", "老挝");              NATIONALITY_MAP.put("LB", "黎巴嫩");              NATIONALITY_MAP.put("LC", "圣卢西亚");              NATIONALITY_MAP.put("LI", "列支敦士登");              NATIONALITY_MAP.put("LK", "斯里兰卡");              NATIONALITY_MAP.put("LR", "利比里亚");              NATIONALITY_MAP.put("LS", "莱索托");              NATIONALITY_MAP.put("LT", "立陶宛");              NATIONALITY_MAP.put("LU", "卢森堡");              NATIONALITY_MAP.put("LV", "拉脱维亚");              NATIONALITY_MAP.put("LY", "利比亚");              NATIONALITY_MAP.put("MA", "摩洛哥");              NATIONALITY_MAP.put("MC", "摩纳哥");              NATIONALITY_MAP.put("MD", "摩尔多瓦");              NATIONALITY_MAP.put("ME", "黑山");              NATIONALITY_MAP.put("MF", "法属圣马丁");              NATIONALITY_MAP.put("MG", "马达加斯加");              NATIONALITY_MAP.put("MH", "马绍尔群岛");              NATIONALITY_MAP.put("MK", "马其顿");              NATIONALITY_MAP.put("ML", "马里");              NATIONALITY_MAP.put("MM", "缅甸");              NATIONALITY_MAP.put("MN", "蒙古");              NATIONALITY_MAP.put("MP", "北马里亚纳群岛");              NATIONALITY_MAP.put("MQ", "马提尼克");              NATIONALITY_MAP.put("MR", "毛里塔尼亚");              NATIONALITY_MAP.put("MS", "蒙塞拉特岛");              NATIONALITY_MAP.put("MT", "马耳他");              NATIONALITY_MAP.put("MU", "毛里求斯");              NATIONALITY_MAP.put("MV", "马尔代夫");              NATIONALITY_MAP.put("MW", "马拉维");              NATIONALITY_MAP.put("MX", "墨西哥");              NATIONALITY_MAP.put("MY", "马来西亚");              NATIONALITY_MAP.put("MZ", "莫桑比克");              NATIONALITY_MAP.put("NA", "纳米比亚");              NATIONALITY_MAP.put("NC", "新喀里多尼亚");              NATIONALITY_MAP.put("NE", "尼日尔");              NATIONALITY_MAP.put("NF", "诺福克岛");              NATIONALITY_MAP.put("NG", "尼日利亚");              NATIONALITY_MAP.put("NI", "尼加拉瓜");              NATIONALITY_MAP.put("NL", "荷兰");              NATIONALITY_MAP.put("NO", "挪威");              NATIONALITY_MAP.put("NP", "尼泊尔");              NATIONALITY_MAP.put("NR", "瑙鲁");              NATIONALITY_MAP.put("NU", "纽埃");              NATIONALITY_MAP.put("NZ", "新西兰");              NATIONALITY_MAP.put("OM", "阿曼");              NATIONALITY_MAP.put("PA", "巴拿马");              NATIONALITY_MAP.put("PE", "秘鲁");              NATIONALITY_MAP.put("PF", "法属波利尼西亚");              NATIONALITY_MAP.put("PG", "巴布亚新几内亚");              NATIONALITY_MAP.put("PH", "菲律宾");              NATIONALITY_MAP.put("PK", "巴基斯坦");              NATIONALITY_MAP.put("PL", "波兰");              NATIONALITY_MAP.put("PM", "圣皮埃尔和密克隆");              NATIONALITY_MAP.put("PN", "皮特凯恩群岛");              NATIONALITY_MAP.put("PR", "波多黎各");              NATIONALITY_MAP.put("PS", "巴勒斯坦");              NATIONALITY_MAP.put("PT", "葡萄牙");              NATIONALITY_MAP.put("PW", "帕劳");              NATIONALITY_MAP.put("PY", "巴拉圭");              NATIONALITY_MAP.put("QA", "卡塔尔");              NATIONALITY_MAP.put("RE", "留尼汪");              NATIONALITY_MAP.put("RO", "罗马尼亚");              NATIONALITY_MAP.put("RS", "塞尔维亚");              NATIONALITY_MAP.put("RU", "俄罗斯");              NATIONALITY_MAP.put("RW", "卢旺达");              NATIONALITY_MAP.put("SA", "沙特阿拉伯");              NATIONALITY_MAP.put("SB", "所罗门群岛");              NATIONALITY_MAP.put("SC", "塞舌尔");              NATIONALITY_MAP.put("SD", "苏丹");              NATIONALITY_MAP.put("SE", "瑞典");              NATIONALITY_MAP.put("SG", "新加坡");              NATIONALITY_MAP.put("SH", "圣赫勒拿");              NATIONALITY_MAP.put("SI", "斯洛文尼亚");              NATIONALITY_MAP.put("SJ", "斯瓦尔巴群岛和扬马延岛");              NATIONALITY_MAP.put("SK", "斯洛伐克");              NATIONALITY_MAP.put("SL", "塞拉利昂");              NATIONALITY_MAP.put("SM", "圣马力诺");              NATIONALITY_MAP.put("SN", "塞内加尔");              NATIONALITY_MAP.put("SO", "索马里");              NATIONALITY_MAP.put("SR", "苏里南");              NATIONALITY_MAP.put("SS", "南苏丹");              NATIONALITY_MAP.put("ST", "圣多美和普林西比");              NATIONALITY_MAP.put("SV", "萨尔瓦多");              NATIONALITY_MAP.put("SY", "叙利亚");              NATIONALITY_MAP.put("SZ", "斯威士兰");              NATIONALITY_MAP.put("TC", "特克斯和凯科斯群岛");              NATIONALITY_MAP.put("TD", "乍得");              NATIONALITY_MAP.put("TF", "法属南部领地");              NATIONALITY_MAP.put("TG", "多哥");              NATIONALITY_MAP.put("TH", "泰国");              NATIONALITY_MAP.put("TJ", "塔吉克斯坦");              NATIONALITY_MAP.put("TK", "托克劳");              NATIONALITY_MAP.put("TL", "东帝汶");              NATIONALITY_MAP.put("TM", "土库曼斯坦");              NATIONALITY_MAP.put("TN", "突尼斯");              NATIONALITY_MAP.put("TO", "汤加");              NATIONALITY_MAP.put("TR", "土耳其");              NATIONALITY_MAP.put("TT", "特立尼达和多巴哥");              NATIONALITY_MAP.put("TV", "图瓦卢");              NATIONALITY_MAP.put("TZ", "坦桑尼亚");              NATIONALITY_MAP.put("UA", "乌克兰");              NATIONALITY_MAP.put("UG", "乌干达");              NATIONALITY_MAP.put("UM", "美国本土外小岛屿");              NATIONALITY_MAP.put("US", "美国");              NATIONALITY_MAP.put("UY", "乌拉圭");              NATIONALITY_MAP.put("UZ", "乌兹别克斯坦");              NATIONALITY_MAP.put("VA", "梵蒂冈");              NATIONALITY_MAP.put("VC", "圣文森特和格林纳丁斯");              NATIONALITY_MAP.put("VE", "委内瑞拉");              NATIONALITY_MAP.put("VG", "英属维尔京群岛");              NATIONALITY_MAP.put("VI", "美属维尔京群岛");              NATIONALITY_MAP.put("VN", "越南");              NATIONALITY_MAP.put("VU", "瓦努阿图");              NATIONALITY_MAP.put("WF", "瓦利斯和富图纳");              NATIONALITY_MAP.put("WS", "萨摩亚");              NATIONALITY_MAP.put("YE", "也门");              NATIONALITY_MAP.put("YT", "马约特");              NATIONALITY_MAP.put("ZA", "南非");              NATIONALITY_MAP.put("ZM", "赞比亚");              NATIONALITY_MAP.put("ZW", "津巴布韦");
     * @apiSuccess (响应结果) {String} data.birthday 出生日期 yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.gender 性别 0-女，1-男
     * @apiSuccess (响应结果) {String} data.riskToleranceLevel 客户风险等级      风险等级      0-保守型（最低）      1-保守型      2-稳健型      3-平衡型      4-成长型      5-进取型
     * @apiSuccess (响应结果) {String} data.custState 客户状态 0-正常 1-注销 2-休眠
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"gUf2","data":{"birthday":"kJM8i01O1","gender":"WCnR","description":"SfxPTUD","mobileVerifyStatus":"5N","custTxPasswdType":"RFWv","returnCode":"ZfLB","idImageUploadStatus":"yxqZ2M","idValidityEnd":"2yU5SijMbG","invstType":"ct","mobileDigest":"E","hboneNo":"w","idType":"1Z","custLoginPasswdType":"pBmIokK","riskToleranceLevel":"SjGkfxZr7","idAlwaysValidFlag":"g","hkCustNo":"W","idNoDigest":"z2XNBh","emailDigest":"58X","emailVerifyStatus":"glacN","custName":"8SChTj5","idNoMask":"jtBi3Kw","nationality":"Y","emailMask":"ht","custState":"L2","mobileMask":"hjx"},"description":"4k59dcRME","timestampServer":"ZxM"}
     */
    @PostMapping("/getcustinfo")
    public CgiResponse<CustInfoVO> getCustInfo() {
        CustInfoVO vo = custInfoService.getCustInfo();
        return CgiResponse.ok(vo);
    }

    /**
     * @api {POST} /hkaccount/cust/getcustinfonotlogin 查询客户信息（未登录）
     * @apiVersion 1.0.0
     * @apiGroup CustController
     * @apiName getCustInfoNotLogin()
     * @apiParam (请求体) {String} mobile 手机号  优先级1 手机号
     * @apiParam (请求体) {String} mobileAreaCode 手机区号  优先级1 一期不用该字段
     * @apiParam (请求体) {String} email 邮箱 优先级2 邮箱
     * @apiParamExample 请求体示例
     * {"mobileAreaCode":"B","mobile":"3zwThxwa","email":"T"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.invstType 客户类型，0-机构,1-个人,2-产品户
     * @apiSuccess (响应结果) {String} data.idType 证件类型           大陆身份证-0      香港身份证-D      澳门身份证-E      台湾身份证-F      中国护照-1      外国护照-6      港澳通行证-4      台胞证-A      港澳台居民居住证-C      其他证件-7
     * @apiSuccess (响应结果) {String} data.idTypeDesc 证件类型描述
     * @apiSuccess (响应结果) {String} data.idNoDigest 证件号摘要
     * @apiSuccess (响应结果) {String} data.idNoMask 证件号掩码
     * @apiSuccess (响应结果) {String} data.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.mobileVerifyStatus 手机号验证状态 0:未验证 1:已验证
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱地址掩码
     * @apiSuccess (响应结果) {String} data.emailDigest 邮箱地址密文
     * @apiSuccess (响应结果) {String} data.emailVerifyStatus 邮箱验证状态0:未验证 1:已验证
     * @apiSuccess (响应结果) {String} data.birthday 出生日期 yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"NvIb2FOK","data":{"returnCode":"Mm96x1","idType":"i","hkCustNo":"yH6I","description":"BnApg","idTypeDesc":"j0Yly"},"description":"Z","timestampServer":"O"}
     */
    @PostMapping("/getcustinfonotlogin")
    public CgiResponse<CustInfoNotLoginVO> getCustInfoNotLogin(@RequestBody CustInfoNotLoginRequest request) {
        CustInfoNotLoginVO vo = custInfoService.getCustInfoNotLogin(request);
        return CgiResponse.ok(vo);
    }


    /**
     * @api {POST} /ext/hkaccount/cust/validatecustinfo 校验客户信息
     * @apiVersion 1.0.0
     * @apiGroup CustController
     * @apiName validateCustInfo()
     * @apiDescription 校验客户信息
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} areaCode 地区码 手机号非空时必须
     * @apiParam (请求体) {String} mobile 手机号 手机号,邮箱,证件号 三选一必须
     * @apiParam (请求体) {String} email 邮箱地址 手机号,邮箱,证件号 三选一必须
     * @apiParam (请求体) {String} idType 证件类型  证件号非空时必须
     * @apiParam (请求体) {String} idNo 证件号码 手机号,邮箱,证件号 三选一必须
     * @apiParamExample 请求体示例
     * {"areaCode":"aUxTVId7H","idType":"NTM","mobile":"DG4W5h86Ni","idNo":"nmD1c","email":"Si8"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"ZnqUN","data":{"returnCode":"tsagTD","description":"h9j"},"description":"1xSk1","timestampServer":"0NdqLzzDTg"}
     */
    @PostMapping("/validatecustinfo")
    public CgiResponse<Body> validateCustInfo(@RequestBody ValidateCustInfoRequest request) {
        custInfoService.validateCustInfo(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /ext/hkaccount/cust/getpersonalsimpleinfo getPersonalSimpleInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkCustController
     * @apiName getPersonalSimpleInfo()
     * @apiDescription 查询个人客户简单信息
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {Object} requestBody
     * @apiParamExample 请求体示例
     * null
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.invstType 客户类型，0-机构,1-个人,2-产品户
     * @apiSuccess (响应结果) {String} data.custName 客户姓名(中文)
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.mobileAreaCode 手机号地区码
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱地址掩码
     * @apiSuccess (响应结果) {String} data.emailDigest 邮箱地址密文
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"iTSctJO","data":{"mobileAreaCode":"REGceB","invstType":"p","hkCustNo":"f0v18L","mobileDigest":"nev44","emailMask":"S4A7ohs","emailDigest":"Dbg3S","custName":"EJ2","mobileMask":"R"},"description":"DXh9w0","timestampServer":"KAc"}
     */
    @PostMapping("/getpersonalsimpleinfo")
    public CgiResponse<PersonalSimpleInfoVO> getPersonalSimpleInfo(@RequestBody HkCustPersonalCenterRequest request) {
        PersonalSimpleInfoVO personalSimpleInfo = custInfoService.getPersonalSimpleInfo();
        return CgiResponse.ok(personalSimpleInfo);
    }
}