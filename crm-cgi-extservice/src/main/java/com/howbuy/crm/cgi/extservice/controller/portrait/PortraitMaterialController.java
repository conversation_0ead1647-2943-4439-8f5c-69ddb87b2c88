package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.*;
import com.howbuy.crm.cgi.extservice.service.portrait.material.PortraitMaterialSearchHisService;
import com.howbuy.crm.cgi.extservice.service.portrait.material.PortraitMaterialService;
import com.howbuy.crm.cgi.extservice.vo.portrait.material.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 客户画像素材控制器
 * @Date 2024/9/2 16:15
 */
@Slf4j
@RestController
@RequestMapping("/portrait/material")
@Validated
public class PortraitMaterialController {

    @Autowired
    private PortraitMaterialService portraitMaterialService;
    @Autowired
    private PortraitMaterialSearchHisService portraitMaterialSearchHisService;

    /**
     * @api {POST} /ext/portrait/material/searchreport searchreport()
     * @apiVersion 1.0.0
     * @apiGroup PortraitMaterialController
     * @apiName searchReport()
     * @apiDescription 客户画像-素材产品报告搜索接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} fundCode 产品代码
     * @apiParam (请求参数) {String} hboneNo 一账通号
     * @apiParam (请求参数) {String} conscode 投顾号
     * @apiParamExample 请求参数示例
     * fundCode=f6JCR1xEa
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.total 总数量
     * @apiSuccess (响应结果) {Array} data.dataList 产品报告列表
     * @apiSuccess (响应结果) {String} data.dataList.materialId 报告ID
     * @apiSuccess (响应结果) {String} data.dataList.materialSendType 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
     * @apiSuccess (响应结果) {String} data.dataList.reportDate 报告日期
     * @apiSuccess (响应结果) {String} data.dataList.title 报告标题
     * @apiSuccess (响应结果) {String} data.dataList.sendNum 发送次数
     * @apiSuccess (响应结果) {String} data.dataList.redirectUrl 报告url
     * @apiSuccess (响应结果) {String} data.dataList.reportType 报告类型
     * @apiSuccess (响应结果) {String} data.dataList.reportTypeName 报告类型名称
     * @apiSuccess (响应结果) {String} data.dataList.reportSource 报告来源(1:CMS报告；2:参数中心报告)
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"bWCAyu","data":{"total":"xiFouT","dataList":[{"reportType":"Jsq3f","redirectUrl":"MHc","reportDate":"QcaAR1hL","title":"MkEt","reportTypeName":"huyOs"}]},"description":"JS7Rp9fC","timestampServer":"oAxrT"}
     */
    @PostMapping("/searchreport")
    public CgiResponse<PortraitMetarialReportVO> searchReport(
            @RequestBody PortraitMetarialSearchReportRequest request
    ) {
        return CgiResponse.appOk(portraitMaterialService.searchReport(request));
    }

    /**
     * @api {POST} /ext/portrait/material/addrecord addrecord()
     * @apiVersion 1.0.0
     * @apiGroup PortraitMaterialController
     * @apiName addRecord()
     * @apiDescription 客户画像-新增素材发送记录接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} hboneNo 一账通号
     * @apiParam (请求参数) {String} conscode 投顾号
     * @apiParam (请求参数) {String} contentId 内容地址
     * @apiParam (请求参数) {String} materialId 素材ID
     * @apiParam (请求参数) {String} materialType 素材类型（1-素材 2-资配报告 3-持仓报告）
     * @apiParamExample 请求参数示例
     * {
     *     "hboneNo": "HB123456",
     *     "conscode": "TA123456",
     *     "contentId": "CONTENT001",
     *     "materialId": "MATERIAL001",
     *     "materialType": "1"
     * }
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.result 操作结果 1-成功 0-失败
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *     "code": "0000",
     *     "description": "成功",
     *     "data": {
     *         "result": "1"
     *     },
     *     "timestampServer": "1710831600000"
     * }
     */
    @PostMapping("/addrecord")
    public CgiResponse<AddMetarialSendRecordVO> addRecord(
            @RequestBody AddMetarialSendRecordRequest request
    ) {
        return CgiResponse.appOk(portraitMaterialService.addSendRecord(request));
    }

    /**
     * @api {POST} /ext/portrait/material/asset/info assetpdfinfo()
     * @apiVersion 1.0.0
     * @apiGroup PortraitMaterialController
     * @apiName assetPdfInfo()
     * @apiDescription 查询资配报告文件接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} assetId 报告id
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParamExample 请求体示例
     * {"assetId":"D","hboneNo":"UPh"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.id 报告id
     * @apiSuccess (响应结果) {String} data.fileName pdf文件名称
     * @apiSuccess (响应结果) {String} data.filebase64 文件字节base64字符串
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"yQdmOn5","data":{"fileName":"l","id":"7QNU","filebase64":"ljS79ghP2B"},"description":"0zrfupB","timestampServer":"VEee4IR96"}
     */
    @PostMapping("/asset/info")
    public CgiResponse<PortraitMetarialAssetInfoVO> assetPdfInfo(
            @RequestBody PortraitMetarialAssetInfoRequest request
    ) {
        return CgiResponse.appOk(portraitMaterialService.getAssetPdfInfo(request));
    }

    /**
     * @api {POST} /ext/portrait/material/send send()
     * @apiVersion 1.0.0
     * @apiGroup PortraitMaterialController
     * @apiName send()
     * @apiDescription 客户画像-获取发送素材文件流接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} hboneNo 一账通号
     * @apiParam (请求参数) {String} reportType pdf报告类型 1-资配报告 2-产品报告
     * @apiParam (请求参数) {String} name 报告名称
     * @apiParam (请求参数) {String} assetId 资配报告id
     * @apiParam (请求参数) {String} reportUrl 素材报告url
     * @apiParamExample 请求参数示例
     * contentId=iZC&reportUrl=I
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.mediaId 文件上传企微媒体id（媒体文件上传后获取的唯一标识，3天内有效）
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"j5Ww7c","data":{"byteArr":"AxP6"},"description":"zH","timestampServer":"JEkFO7p"}
     */
    @PostMapping("/send")
    public CgiResponse<SendMetarialVO> send(
            @RequestBody SendMetarialRequest request
    ) {
        return CgiResponse.appOk(portraitMaterialService.uploadReport2Wechat(request));
    }

    /**
     * @api {POST} /ext/portrait/material/history/delete deleteHistory()
     * @apiVersion 1.0.0
     * @apiGroup PortraitMaterialController
     * @apiName deleteHistory()
     * @apiDescription 素材库对话检索历史删除接口
     *
     * @apiParam (请求体) {String} conscode 投顾编号（必填）
     * @apiParam (请求体) {String} hboneNo 一账通号
     *
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     *
     * @apiParamExample 请求体示例
     * {
     *   "conscode": "TA123456",
     *   "hboneNo": "HB123456789",
     * }
     *
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {},
     *   "timestampServer": "1677196800000"
     * }
     */
    @PostMapping("/history/delete")
    public CgiResponse<PortraitMaterialHistoryDeleteVO> deleteHistory(@RequestBody PortraitMaterialHistoryDeleteRequest request) {
        log.info("素材库对话检索历史删除请求参数：{}", request);
        return CgiResponse.appOk(portraitMaterialSearchHisService.deleteHistory(request));
    }

    /**
     * @api {POST} /ext/portrait/material/history/query queryHistory()
     * @apiVersion 1.0.0
     * @apiGroup PortraitMaterialController
     * @apiName queryHistory()
     * @apiDescription 素材库对话检索历史查询接口
     *
     * @apiParam (请求体) {String} conscode 投顾编号（必填）
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {Integer} page 分页页码
     * @apiParam (请求体) {Integer} size 每页数量
     *
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.total 数据总量
     * @apiSuccess (响应结果) {Array} data.dataList 数据列表
     * @apiSuccess (响应结果) {String} data.dataList.id 记录id
     * @apiSuccess (响应结果) {String} data.dataList.content 检索内容
     * @apiSuccess (响应结果) {String} data.dataList.searchParam 检索参数
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     *
     * @apiParamExample 请求体示例
     * {
     *   "conscode": "TA123456",
     *   "hboneNo": "HB123456789",
     *   "page": 1,
     *   "size": 20
     * }
     *
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "total": "10",
     *     "dataList": [{
     *       "id": "123",
     *       "content": "基金投资",
     *       "searchParam": "keyword=基金投资"
     *     }]
     *   },
     *   "timestampServer": "1677196800000"
     * }
     */
    @PostMapping("/history/query")
    public CgiResponse<PortraitMaterialHistoryQueryVO> queryHistory(@RequestBody PortraitMaterialHistoryQueryRequest request) {
        log.info("素材库对话检索历史查询请求参数：{}", request);
        return CgiResponse.appOk(portraitMaterialSearchHisService.queryHistory(request));
    }

    /**
     * @api {POST} /ext/portrait/material/history/add addHistory()
     * @apiVersion 1.0.0
     * @apiGroup PortraitMaterialController
     * @apiName addHistory()
     * @apiDescription 素材库对话检索历史新增接口
     *
     * @apiParam (请求体) {String} conscode 投顾编号（必填）
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {String} content 检索内容（必填）
     * @apiParam (请求体) {String} searchParam 检索参数（必填）
     *
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     *
     * @apiParamExample 请求体示例
     * {
     *   "conscode": "TA123456",
     *   "hboneNo": "HB123456789",
     *   "content": "基金投资",
     *   "searchParam": "keyword=基金投资"
     * }
     *
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {},
     *   "timestampServer": "1677196800000"
     * }
     */
    @PostMapping("/history/add")
    public CgiResponse<PortraitMaterialHistoryAddVO> addHistory(@RequestBody PortraitMaterialHistoryAddRequest request) {
        log.info("素材库对话检索历史新增请求参数：{}", request);
        return CgiResponse.appOk(portraitMaterialSearchHisService.addHistory(request));
    }

    /**
     * @api {POST} /ext/portrait/material/pool getMaterialPool()
     * @apiVersion 1.0.0
     * @apiGroup PortraitMaterialController
     * @apiName getMaterialPool()
     * @apiDescription 智能素材库-推荐池列表查询接口
     *
     * @apiParam (请求体) {String} tab 为您推荐tab页（0-全部 1-A基础 2-B投教 3-市场资讯 4-金融产品）
     * @apiParam (请求体) {String} conscode 投顾编号（必填）
     * @apiParam (请求体) {String} hboneNo 一账通号（必填）
     * @apiParam (请求体) {Integer} page 分页页码
     * @apiParam (请求体) {Integer} size 每页数量
     *
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.searchPool 检索池类型 0-全池 1-推荐池
     * @apiSuccess (响应结果) {String} data.total 总数量
     * @apiSuccess (响应结果) {Array} data.dataList 推荐列表
     * @apiSuccess (响应结果) {String} data.dataList.materialId 素材id
     * @apiSuccess (响应结果) {String} data.dataList.title 素材标题
     * @apiSuccess (响应结果) {String} data.dataList.link 素材链接
     * @apiSuccess (响应结果) {String} data.dataList.label 素材分类（1-A基础 2-B投教 3-C市场资讯 4-D金融产品）
     * @apiSuccess (响应结果) {String} data.dataList.column 专栏名称
     * @apiSuccess (响应结果) {String} data.dataList.newestTag 最新标签（1-是 0-否）
     * @apiSuccess (响应结果) {Array} data.dataList.productTag 产品标签列表
     * @apiSuccess (响应结果) {Array} data.dataList.keywordList 关键词列表
     * @apiSuccess (响应结果) {String} data.dataList.sendNum 素材发送次数
     * @apiSuccess (响应结果) {String} data.dataList.materialSendType 素材发送类型（1-素材 2-产品报告 2-资配报告）
     * @apiSuccess (响应结果) {String} data.dataList.fundCode 产品代码
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     *
     * @apiParamExample 请求体示例
     * {
     *   "tab": "0",
     *   "conscode": "TA123456",
     *   "hboneNo": "HB123456789",
     *   "page": 1,
     *   "size": 20
     * }
     *
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "total": "10",
     *     "custName": "张三",
     *     "searchPool": "1",
     *     "dataList": [{
     *       "materialId": "123",
     *       "title": "投资入门基础知识",
     *       "link": "/article/123",
     *       "label": "1",
     *       "column": "投资基础",
     *       "newestTag": "1",
     *       "productTag": ["基金","股票"],
     *       "keywordList": ["投资","理财"],
     *       "sendNum": "10",
     *       "materialType": "1",
     *       "fundCode": "000001"
     *     }]
     *   },
     *   "timestampServer": "1677196800000"
     * }
     */
    @PostMapping("/pool")
    public CgiResponse<PortraitMaterialPoolVO> getMaterialPool(@RequestBody PortraitMaterialPoolRequest request) {
        log.info("智能素材库-推荐池列表查询请求参数：{}", request);
        PortraitMaterialPoolVO materialPool = portraitMaterialService.getMaterialPool(request);
        return CgiResponse.appOk(materialPool);
    }

}
