package com.howbuy.crm.cgi.extservice.vo.balance;

/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 资产详情信息实体类
 * @author: 陈杰文
 * @date: 2025-06-17 14:43:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class AssetDetailInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 子基金名称
     */
    private String subFundName;

    /**
     * 子基金代码
     */
    private String subFundCode;

    /**
     * 参考市值
     */
    private String currencyMarketValue;

    /**
     * 币种描述 仅参考市值用
     */
    private String currencyDesc;

    /**
     * 币种 其余的市值 用
     */
    private String currency;

    /**
     * 参考净值
     */
    private String nav;

    /**
     * 净值日期 YYYY-MM-DD或者MM-DD
     */
    private String navDate;

    /**
     * 总实缴金额
     */
    private String paidInAmt;

    /**
     * 待投金额（当前币种）
     */
    private String currencyUnPaidInAmt;

    /**
     * 持仓份额
     */
    private String totalBalanceVol;

    /**
     * 当前收益率
     */
    private String dayIncomeRate;

    /**
     * 累计收益
     */
    private String accumIncome;

    /**
     * 最新收益
     */
    private String latestIncome;

    /**
     * 最新收益率
     */
    private String latestIncomeRate;

    /**
     * 单位成本
     */
    private String unitCost;

    /**
     * 分红提醒标识 0:无需分红提醒;1:提醒收益偏差;2:提醒即将分红
     */
    private String navDivFlag;

    /**
     * 清算标识 0-否; 1-是
     */
    private String crisisFlag;

    /**
     * 千禧年产品标识 0-否; 1-是
     */
    private String qianXiFlag;

    /**
     * 是否分期成立 0-否; 1-是
     */
    private String stageEstablishFlag;

    /**
     * 是否NA产品
     */
    private String naFlag;

    /**
     * 累计应收管理费
     */
    private String receivManageFee;

    /**
     * 累计应收业绩报酬
     */
    private String receivPreformFee;

    /**
     * 产品费后参考市值
     */
    private String currencyMarketValueExFee;

    /**
     * 产品费后持仓收益
     */
    private String currentAssetCurrency;

    /**
     * 产品费后持仓收益率
     */
    private String yieldRate;

    /**
     * 产品费后持仓收益率 保留几位小数
     */
    private String yieldRateStr;

    /**
     * 平衡因子
     */
    private String balanceFactor;

    /**
     * 平衡因子转换完成 1-是 0-否
     */
    private String convertFinish;

    /**
     * 平衡因子日期
     */
    private String balanceFactorDate;

    /**
     * 收益计算状态 0-计算中;1-计算成功
     */
    private String incomeCalStat;

    /**
     * 是否可卖出 1-可交易 2-不可交易 3-按钮不展示
     */
    private String isCanSell;
} 