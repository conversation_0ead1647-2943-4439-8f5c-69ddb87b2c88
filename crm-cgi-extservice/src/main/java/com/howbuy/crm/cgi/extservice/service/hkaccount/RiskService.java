package com.howbuy.crm.cgi.extservice.service.hkaccount;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctInvstTypeEnum;
import com.howbuy.crm.cgi.extservice.common.utils.IdentityUtils;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.convert.hkopen.OpenAcctConvert;
import com.howbuy.crm.cgi.extservice.request.account.RiskQuestionRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAppAccountBaseRequest;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkCustFundHoldRiskLevelVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.enums.OrderStatusEnum;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.FundHoldingsOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.QueryHwDealOrderOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkopen.OpenAcctCatchService;
import com.howbuy.crm.cgi.extservice.validator.hkopen.HkOpenAcctValidator;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.RiskToleranceLevelVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.RiskQuestionnaireVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.*;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkOpenAcctRiskOuterService;
import com.howbuy.dtms.common.enums.AckStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 风险测算服务
 * @date 2023/12/11 17:09
 * @since JDK 1.8
 */
@Service
public class RiskService {

    private static final String PERSONAL_CENTER_RISK_EVALUATION = "1";

    private static final String HK_OPEN_ACCOUNT_EXPERIENCE_RISK_EVALUATION = "0";


    @Resource
    private HkOpenAcctRiskOuterService hkOpenAcctRiskOuterService;

    @Resource
    private HkCustInfoOuterService hkCustInfoOuterService;

    @Resource
    private OpenAcctCatchService openAcctCatchService;

    @Resource
    private FundHoldingsOuterService fundHoldingsOuterService;

    @Resource
    private QueryHwDealOrderOuterService queryHwDealOrderOuterService;

    @Resource
    private QueryFundBasicInfoOuterService queryFundBasicInfoOuterService;


    /**
     * @param request 请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.RiskToleranceLevelVO
     * @description: 风险测算结果, 账户中心不会保存测算结果信息
     * @author: jinqing.rao
     * @date: 2023/12/12 10:01
     * @since JDK 1.8
     */
    public RiskToleranceLevelVO queryAssessmentRisk(RiskQuestionRequest request) {
        //校验参数
        HkOpenAcctValidator.validatorListType(request.getAnswerDTOList());
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        // 获取用户信息
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfo(loginHkCustNo);
        List<FundBasicInfoDTO> fundBasicInfoDTOS = new ArrayList<>();

        //获取用户生日信息
        String custBirthDay = getCustBirthDay(loginHkCustNo,hkCustInfoDTO);
        //调用账户中心接口,测算风险等级
        HkOpenAcctRiskQuestionDTO hkOpenAcctRiskQuestionDTO = OpenAcctConvert.toHkOpenAcctRiskQuestionDTO(request, loginHkCustNo, custBirthDay,fundBasicInfoDTOS);
        HkOpenAcctRiskCalculateResultDTO resultDTO = hkOpenAcctRiskOuterService.calculateRiskLevel(hkOpenAcctRiskQuestionDTO);
        if(YesNoEnum.YES.getCode().equals(request.getRiskWarning())){
            fundBasicInfoDTOS = getFundAndMainFundBasicInfoDTOS(loginHkCustNo);
            //保存临时的测算结果,用于前端的风险不匹配弹框数据回显
            openAcctCatchService.saveTemporaryRiskAssessmentRisk(loginHkCustNo, resultDTO);
            //判断是否存在大于用户当前风险等级的基金产品
            checkRiskLevel(resultDTO.getLevelValue(), fundBasicInfoDTOS);
        }
        //保存缓存,在经验填写页保存时,会调用该方法,获取分险答案信息
        openAcctCatchService.saveHkOpenAcctRiskAssessmentRisk(loginHkCustNo, resultDTO);
        //保存答案
        openAcctCatchService.saveHkOpenAcctRiskAssessmentRiskAnswer(loginHkCustNo, hkOpenAcctRiskQuestionDTO);
        return OpenAcctConvert.toRiskToleranceLevelVO(resultDTO);
    }

    /**
     * @param request 风险测算题目
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.RiskToleranceLevelVO
     * @description: 保存风险测算结果 并且账户中心会保存测算的结果信息
     * @author: jinqing.rao
     * @date: 2023/12/12 9:52
     * @since JDK 1.8
     */
    public RiskToleranceLevelVO saveAssessmentRisk(RiskQuestionRequest request) {
        //校验参数
        HkOpenAcctValidator.validatorListType(request.getAnswerDTOList());
        //获取用户的香港客户号
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        List<FundBasicInfoDTO> fundBasicInfoDTOS = getFundAndMainFundBasicInfoDTOS(loginHkCustNo);
        // 获取用户信息
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfo(loginHkCustNo);
        //计算用户的试算的结果分险等级，调用账户中心接口,测算风险等级
        HkOpenAcctRiskQuestionDTO hkOpenAcctRiskQuestionDTO = OpenAcctConvert.toHkOpenAcctRiskQuestionDTO(request, loginHkCustNo, hkCustInfoDTO.getBirthday(),fundBasicInfoDTOS);
        HkOpenAcctRiskCalculateResultDTO calculateResultDTO = hkOpenAcctRiskOuterService.calculateRiskLevel(hkOpenAcctRiskQuestionDTO);
        //保存临时的测算结果,用于前端的风险不匹配弹框数据回显
        openAcctCatchService.saveTemporaryRiskAssessmentRisk(loginHkCustNo, calculateResultDTO);
        boolean mismatchResult  = false;
        if(CollectionUtils.isNotEmpty(fundBasicInfoDTOS)){
            mismatchResult = fundBasicInfoDTOS.stream().anyMatch(f -> compareRiskLevel(f, calculateResultDTO.getLevelValue()));
        }
        //校验用户的风险合适性校验,这里是个开关,如果是YES,则需要做风险不匹配校验，前端跳转展示页面，然后可以选择2次提交
        if(StringUtils.isBlank(request.getRiskWarning()) || YesNoEnum.YES.getCode().equals(request.getRiskWarning())){
            //判断是否存在大于用户当前风险等级的基金产品
            if(mismatchResult){
                throw new BusinessException(ExceptionCodeEnum.RISK_ASSESSMENT_ERROR);
            }
        }
        List<FundBasicInfoDTO> mismatchFundList = null;
        if(mismatchResult){
            mismatchFundList = new ArrayList<>(fundBasicInfoDTOS);
        }
        //调用账户中心接口,测算风险等级,并且账户中心会保存测算的结果信息
        HkOpenAcctRiskCalculateResultDTO resultDTO = hkOpenAcctRiskOuterService.saveAssessmentRisk(OpenAcctConvert.toHkOpenAcctRiskQuestionDTO(request, loginHkCustNo, null,mismatchFundList));
        //保存缓存,在经验填写页保存时,会调用该方法,获取分险答案信息
        openAcctCatchService.saveHkOpenAcctRiskAssessmentRisk(loginHkCustNo, resultDTO);
        return OpenAcctConvert.toRiskToleranceLevelVO(resultDTO);
    }

    /**
     * @description: 分险合适性检查,用户的持仓或者在途基金，对应的分险等级是否存在比用户当前的风险等级大
     * @param loginHkCustNo	 香港客户号
     * @return void
     * @author: jinqing.rao
     * @date: 2024/8/12 15:04
     * @since JDK 1.8
     */
    private List<FundBasicInfoDTO> riskAssessmentCheck(String loginHkCustNo) {
        //查询用户的持仓信息
        List<String> holdFundCodeList = fundHoldingsOuterService.queryCustFundHoldFundByHkCustNo(loginHkCustNo);
        //查询在途订单
        List<String> orderStatusList = Arrays.asList(OrderStatusEnum.APPLY_SUCCESS.getCode());
        List<String> ackStatusList = Arrays.asList(AckStatusEnum.UN_CONFIRM.getCode());
        List<String> orderFundCodeList = queryHwDealOrderOuterService.queryOrderFundCodeByDealStatusAndAckStatus(loginHkCustNo,orderStatusList,ackStatusList);
        //合并Code,调用参数系统获取基金的风险等级信息
        List<String> fundCodeList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(holdFundCodeList)){
            fundCodeList.addAll(holdFundCodeList);
        }
        if(CollectionUtils.isNotEmpty(orderFundCodeList)){
            fundCodeList.addAll(orderFundCodeList);
        }
        List<FundBasicInfoDTO> fundBasicInfoDTOS = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(fundCodeList)){
            List<String> fundCodes = fundCodeList.stream().distinct().collect(Collectors.toList());
            fundBasicInfoDTOS = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList(fundCodes);
        }
        return fundBasicInfoDTOS;
    }

    private void checkRiskLevel(String level, List<FundBasicInfoDTO> fundBasicInfoDTOS) {
        if(CollectionUtils.isNotEmpty(fundBasicInfoDTOS)){
            boolean present = fundBasicInfoDTOS.stream().anyMatch(f -> compareRiskLevel(f, level));
            if(present){
                 throw new BusinessException(ExceptionCodeEnum.RISK_ASSESSMENT_ERROR);
            }
        }
    }

    private boolean compareRiskLevel(FundBasicInfoDTO fundBasicInfoDTO, String level) {
        if(null ==  fundBasicInfoDTO){
            return false;
        }
        if(StringUtils.isBlank(fundBasicInfoDTO.getFundRiskLevel())){
            return false;
        }
        //基金对应的分险等级不为空,用户的风险等级是空
        if(StringUtils.isBlank(level)){
            return true;
        }
        return fundBasicInfoDTO.getFundRiskLevel().compareTo(level) > 0;
    }
    /**
     * @param sourceType
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.RiskQuestionnaireVO
     * @description: 分险测评获取测评问卷题目信息
     * @author: jinqing.rao
     * @date: 2023/12/12 10:45
     * @since JDK 1.8
     */
    public RiskQuestionnaireVO queryRiskQuestionnaireInfo(String sourceType) {
        //获取用户的年龄,部分选项，涉及到年龄的校验,取值逻辑:优先获取用户开户缓存的年龄,没有的话获取账户中心的
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(loginHkCustNo);
        int age = getCustAge(loginHkCustNo,hkCustInfo);
        //获取风险测评的题目信息
        HkOpenAcctExamInfoDTO hkOpenAcctExamInfo = hkOpenAcctRiskOuterService.queryRiskQuestionnaireInfo(HkOpenAcctInvstTypeEnum.INDIVIDUAL.getValue());
        //开户过程中经验填写页的风险测评,需要返回问卷题目信息,以及用户历史的测算选着答案
        HkOpenAcctRiskQuestionDTO hkOpenAcctRiskQuestionDTO = null;
        if (HK_OPEN_ACCOUNT_EXPERIENCE_RISK_EVALUATION.equals(sourceType)) {
            hkOpenAcctRiskQuestionDTO = openAcctCatchService.getHkOpenAcctRiskAssessmentRiskHistoryAnswer(loginHkCustNo);

        }
        return OpenAcctConvert.toRiskQuestionnaireVO(hkOpenAcctExamInfo, age, hkOpenAcctRiskQuestionDTO);
    }

    private int getCustAge(String loginHkCustNo,HkCustInfoDTO hkCustInfo) {
        String birthday = getCustBirthDay(loginHkCustNo,hkCustInfo);
        return IdentityUtils.calculateAge(birthday);
    }

    private String getCustBirthDay(String loginHkCustNo,HkCustInfoDTO hkCustInfo) {
        String birthday = null;
        //从缓存获取
        HkOpenAcctCustInfoDTO acctCustInfoDTO = openAcctCatchService.getHkOpenAcctCustInfoDTO(loginHkCustNo);
        if (null != acctCustInfoDTO) {
            birthday = acctCustInfoDTO.getBirthday();
        }
        // 从账户中心获取
        if (StringUtils.isBlank(birthday)) {
            birthday = hkCustInfo.getBirthday();
        }
        // 新用户，检查是否有开户信息的生日日期
        birthday = getBirthdayFromOpenAccOrder(loginHkCustNo, birthday);

        if (StringUtils.isBlank(birthday)) {
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "用户生日信错误,获取问卷列表失败");
        }
        return birthday;
    }

    /**
     * 从开户订单上获取生日信息
     * @param loginHkCustNo
     * @param birthday
     * @return
     */
    private String getBirthdayFromOpenAccOrder(String loginHkCustNo, String birthday) {
        if(StringUtils.isBlank(birthday)){
            HkOpenAccOrderInfoDTO orderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(loginHkCustNo);
            HkOpenAcctCustInfoDTO accountCustInfo = orderInfoDTO.getAccountCustInfo();
            if(null != accountCustInfo){
                birthday = accountCustInfo.getBirthday();
            }
        }
        return birthday;
    }

    /**
     * @param sourceType
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.RiskToleranceLevelVO
     * @description: 获取分险测评的结果, 如果是开户填写页的直接查询缓存, 个人中心的入口直接查询账户中心获取结果
     * @author: jinqing.rao
     * @date: 2023/12/14 10:24
     * @since JDK 1.8
     */
    public RiskToleranceLevelVO queryCalculateResult(String sourceType) {
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        //参数校验
        HkOpenAcctValidator.validatorStringType(sourceType);
        if (HK_OPEN_ACCOUNT_EXPERIENCE_RISK_EVALUATION.equals(sourceType)) {
            //从缓存获取风险测评结果
            HkOpenAcctRiskCalculateResultDTO acctRiskCalculateResultDTO = openAcctCatchService.getHkOpenAcctRiskAssessmentRiskResult(loginHkCustNo);
            if(null == acctRiskCalculateResultDTO){
                HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(loginHkCustNo);
                acctRiskCalculateResultDTO = hkOpenAccOrderInfoDTO.getRiskCalculateResultDTO();
            }
            return OpenAcctConvert.toRiskToleranceLevelVO(acctRiskCalculateResultDTO);
        }
        //调用账户中心接口获取开户信息
        HkOpenAcctRiskCalculateResultDTO acctRiskCalculateResultDTO = hkCustInfoOuterService.queryHkOpenAccKycInfoInfo(loginHkCustNo);
        return OpenAcctConvert.toRiskToleranceLevelVO(acctRiskCalculateResultDTO);
    }

    /**
     * @description: 查询用户的基金持仓风险等级
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkCustFundHoldRiskLevelVO
     * @author: jinqing.rao
     * @date: 2024/8/12 16:06
     * @since JDK 1.8
     */
    public HkCustFundHoldRiskLevelVO queryFundHoldRiskLevel(HkAppAccountBaseRequest request) {
        String riskLevel = null;
        //获取临时的试算结果
        HkOpenAcctRiskCalculateResultDTO riskCalculateResultDTO = openAcctCatchService.getTemporaryRiskAssessmentRisk(request.getHkCustNo());
        //是兼容处理，缓存是空的特殊情况

        if(null == riskCalculateResultDTO ||StringUtils.isBlank(riskCalculateResultDTO.getLevelValue())){
            HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(request.getHkCustNo());
            riskLevel = hkCustInfo.getRiskToleranceLevel();
        }else{
            riskLevel  = riskCalculateResultDTO.getLevelValue();
        }
        //查询用户持仓的基金以及对应的母基金的代码名称和风险等级
        List<FundBasicInfoDTO> fundBasicInfoDTOS = getFundAndMainFundBasicInfoDTOS(request.getHkCustNo());
        HkCustFundHoldRiskLevelVO response = new HkCustFundHoldRiskLevelVO();
        response.setCustRiskLevel(riskLevel);
        response.setFundRiskLevelInfoList(OpenAcctConvert.toFundHoldRiskLevelVOList(fundBasicInfoDTOS,riskLevel));
        return response;
    }

    private List<FundBasicInfoDTO> getFundAndMainFundBasicInfoDTOS(String hkCustNo) {
        List<FundBasicInfoDTO> fundBasicInfoDTOS = riskAssessmentCheck(hkCustNo);
        //筛选含有母基金的基金，并且获取母基金信息
        List<FundBasicInfoDTO> motherFundList = fundBasicInfoDTOS.stream().filter(RiskService::hasMotherFund).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(motherFundList)){
            // 去重获母基金Code
            List<String> motherFundCodes = motherFundList.stream()
                    .map(FundBasicInfoDTO::getMainFundCode)
                    .distinct()
                    .collect(Collectors.toList());
            fundBasicInfoDTOS.addAll(queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList(motherFundCodes));
        }
        //fundBasicInfoDTOS 根据基金代码去重,
        fundBasicInfoDTOS = fundBasicInfoDTOS.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(FundBasicInfoDTO::getFundCode, Function.identity(), (existing, replacement) -> existing),
                        map -> new ArrayList<>(map.values())
                ));
        return fundBasicInfoDTOS;
    }

    /**
     * @description: 筛选出母子基金
     * @param fundBasicInfoDTO
     * @return boolean
     * @author: jinqing.rao
     * @date: 2024/10/17 10:08
     * @since JDK 1.8
     */
    private static boolean hasMotherFund(FundBasicInfoDTO fundBasicInfoDTO) {
        if(StringUtils.isBlank(fundBasicInfoDTO.getIsMotherChildFund())){
            return false;
        }
        if(YesNoEnum.NO.getCode().equals(fundBasicInfoDTO.getIsMotherChildFund())){
            return false;
        }
        return !fundBasicInfoDTO.getFundCode().equals(fundBasicInfoDTO.getMainFundCode());
    }
}
