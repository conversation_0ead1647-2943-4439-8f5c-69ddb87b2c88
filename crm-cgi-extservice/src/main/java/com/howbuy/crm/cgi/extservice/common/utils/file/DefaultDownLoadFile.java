/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils.file;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.enums.FileBizTypeEnum;
import crm.howbuy.base.utils.DesUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/11 10:51
 * @since JDK 1.8
 */
public class DefaultDownLoadFile extends AbstractDownLoadFile {
    private static final Logger log = LoggerFactory.getLogger(DefaultDownLoadFile.class);

    private final String hkCustNo;


    public DefaultDownLoadFile(String encryptMsg){
        //解析加密参数,获取文件的下载链接和文件名
        String decryptData = DesUtil.decrypt(encryptMsg, Constants.HK_APP_CUST_NO_KEY);
        JSONObject jsonObject = JSON.parseObject(decryptData);
        if(null == jsonObject){
            throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_FILE_NOT_EXIST);
        }
        this.fileId = jsonObject.getString(FILE_ID);
        this.fileBizType = jsonObject.getString(FILE_BIZ_TYPE);
        this.fileBizTypeEnum = FileBizTypeEnum.getFileBizTypeEnumByCode(this.fileBizType);
        this.fileName = jsonObject.getString(FILE_NAME);
        this.hkCustNo = jsonObject.getString(Constants.HKCUSTNO);

        if(FileBizTypeEnum.CRM_PAYMENT_VOUCHER_FILE.equals(this.fileBizTypeEnum)){
            //路径校验
            if(StringUtils.isAnyBlank(this.fileId,this.fileName,this.hkCustNo,this.fileBizType)){
                log.error("DefaultDownLoadFile 业务类型：>>>"+ FileBizTypeEnum.CRM_PAYMENT_VOUCHER_FILE.getCode() +" 加密参数中,没有必要字段数据,fileId : {},fileName:{},hkCustNo:{},fileBizType:{}",this.filePath,this.fileName,this.hkCustNo,this.fileBizType);
                throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_FILE_NOT_EXIST);
            }
            return;
        }
        String url = jsonObject.getString(URL);
        this.filePath = url.replace(this.fileName,"");

        //路径校验
        if(StringUtils.isAnyBlank(this.filePath,this.fileName,this.hkCustNo,this.fileBizType)){
            log.error("DefaultDownLoadFile >>> 加密参数中,没有必要字段数据,filePath : {},fileName:{},hkCustNo:{},fileBizType:{}",this.filePath,this.fileName,this.hkCustNo,this.fileBizType);
            throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_FILE_NOT_EXIST);
        }
    }
    @Override
    protected String initFileName() {
        return this.fileName;
    }

    @Override
    protected String initFilePath() {
        return this.filePath;
    }

    public String getHkCustNo() {
        return this.hkCustNo;
    }

    public FileBizTypeEnum getFileBizTypeEnum() {
        return this.fileBizTypeEnum;
    }

    public String getFileId() {
        return this.fileId;
    }
}
