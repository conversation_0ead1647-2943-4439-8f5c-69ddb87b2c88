/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.agreement;

import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.agreement.SupplementalAgreementSignStatusEnum;
import com.howbuy.crm.cgi.extservice.request.agreement.SupplementalAgreementDetailRequest;
import com.howbuy.crm.cgi.extservice.request.agreement.SupplementalAgreementListRequest;
import com.howbuy.crm.cgi.extservice.request.agreement.SupplementalAgreementSubmitRequest;
import com.howbuy.crm.cgi.extservice.service.business.holdfund.HkCustHoldFundService;
import com.howbuy.crm.cgi.extservice.vo.agreement.SupplementalAgreementDetailVO;
import com.howbuy.crm.cgi.extservice.vo.agreement.SupplementalAgreementListVO;
import com.howbuy.crm.cgi.extservice.vo.agreement.SupplementalAgreementSubmitVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.agreement.SupplementalAgreementDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.agreement.SupplementalAgreementOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 补充协议服务
 * @date 2025/3/6 18:20
 * @since JDK 1.8
 */
@Service
@Slf4j
public class SupplementalAgreementService {

    @Resource
    private SupplementalAgreementOuterService supplementalAgreementOuterService;

    @Resource
    private QueryFundBasicInfoOuterService queryFundBasicInfoOuterService;


    @Resource
    private HkCustInfoOuterService hkCustInfoOuterService;
    @Resource
    private HkCustHoldFundService hkCustHoldFundService;


    /**
     * @param request 请求参数
     * @return SupplementalAgreementListVO
     * @description: 查询补充协议列表
     */
    public SupplementalAgreementListVO querySupplementalAgreementList(SupplementalAgreementListRequest request) {
        SupplementalAgreementListVO supplementalAgreementListVO = new SupplementalAgreementListVO();

        // 1. 查询未签署的补充协议列表
        List<SupplementalAgreementDTO> supplementalAgreementDTOS = supplementalAgreementOuterService.querySupplementalAgreementList(
                request.getHkCustNo(),
                SupplementalAgreementSignStatusEnum.UNSIGNED.getCode(),
                null
        );
        // 不同渠道来源对应的持仓基金信息
        List<String> fundCodeList = hkCustHoldFundService.getFundCodeListByChannelCode(request.getChannelCode(), request.getHkCustNo());

        if (CollectionUtils.isEmpty(supplementalAgreementDTOS)) {
            return supplementalAgreementListVO;
        }
        if(CollectionUtils.isNotEmpty(fundCodeList)){
            supplementalAgreementDTOS = supplementalAgreementDTOS.stream()
                    .filter(supplementalAgreementDTO -> fundCodeList.contains(supplementalAgreementDTO.getFundCode()))
                    .collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(supplementalAgreementDTOS)){
            return supplementalAgreementListVO;
        }
        // 获取基金信息
        Map<String, FundBasicInfoDTO> fundInfoMap = getFundBasicInfoDTOMap(supplementalAgreementDTOS);

        // 4. 根据基金编码分组，返回基金编码和基金名称
        List<SupplementalAgreementListVO.FundAgreementVO> fundAgreementList = getFundAgreementVOS(supplementalAgreementDTOS, fundInfoMap);

        supplementalAgreementListVO.setFundAgreementList(fundAgreementList);
        return supplementalAgreementListVO;
    }
    /**
     * @param supplementalAgreementDTOS
     * @param fundInfoMap
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.agreement.SupplementalAgreementListVO.FundAgreementVO>
     * @description: 参数组合
     * @author: jinqing.rao
     * @date: 2025/3/6 19:13
     * @since JDK 1.8
     */
    private static List<SupplementalAgreementListVO.FundAgreementVO> getFundAgreementVOS(List<SupplementalAgreementDTO> supplementalAgreementDTOS, Map<String, FundBasicInfoDTO> fundInfoMap) {
        List<SupplementalAgreementListVO.FundAgreementVO> fundAgreementList = new ArrayList<>();
        Map<String, List<SupplementalAgreementDTO>> fundAgreementMap = supplementalAgreementDTOS.stream()
                .filter(f -> DateUtils.isAfterCurrentDateTime(f.getAgreementSignEndDt(), DateUtils.YYYYMMDDHHMMSS))
                .collect(Collectors.groupingBy(SupplementalAgreementDTO::getFundCode));

        fundAgreementMap.forEach((fundCode, agreements) -> {
            SupplementalAgreementListVO.FundAgreementVO vo = new SupplementalAgreementListVO.FundAgreementVO();
            vo.setFundCode(fundCode);
            // 设置基金名称
            FundBasicInfoDTO fundInfo = fundInfoMap.get(fundCode);
            if (fundInfo != null) {
                vo.setFundAddr(fundInfo.getFundAbbr());
            }

            fundAgreementList.add(vo);
        });
        return fundAgreementList;
    }

    /**
     * @param supplementalAgreementDTOS
     * @return java.util.Map<java.lang.String, com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO>
     * @description: 获取基金信息
     * @author: jinqing.rao
     * @date: 2025/3/6 19:12
     * @since JDK 1.8
     */
    private Map<String, FundBasicInfoDTO> getFundBasicInfoDTOMap(List<SupplementalAgreementDTO> supplementalAgreementDTOS) {
        // 2. 获取基金编码集合
        List<String> fundCodes = supplementalAgreementDTOS.stream()
                .map(SupplementalAgreementDTO::getFundCode)
                .distinct()
                .collect(Collectors.toList());

        // 3. 查询基金信息
        Map<String, FundBasicInfoDTO> fundInfoMap = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList(fundCodes)
                .stream()
                .collect(Collectors.toMap(FundBasicInfoDTO::getFundCode, Function.identity(), (k1, k2) -> k1));
        return fundInfoMap;
    }

    /**
     * @param request 请求参数
     * @return SupplementalAgreementDetailVO
     * @description: 查询补充协议详情
     */
    public SupplementalAgreementDetailVO querySupplementalAgreementDetail(SupplementalAgreementDetailRequest request) {
        // 查询补充协议详情
        List<SupplementalAgreementDTO> supplementalAgreementDTOS = supplementalAgreementOuterService.querySupplementalAgreementDetail(
                request.getHkCustNo(),
                request.getFundCode(),
                SupplementalAgreementSignStatusEnum.UNSIGNED.getCode()
        );

        if (CollectionUtils.isEmpty(supplementalAgreementDTOS)) {
            return new SupplementalAgreementDetailVO();
        }
        SupplementalAgreementDetailVO supplementalAgreementDetailVO = new SupplementalAgreementDetailVO();
        List<SupplementalAgreementDetailVO.SupplementalAgreementDetailDtl> collect = getSupplementalAgreementDetailDtls(supplementalAgreementDTOS);

        supplementalAgreementDetailVO.setSupplementalAgreementDetailDtlList(collect);
        if (CollectionUtils.isNotEmpty(collect)) {
            supplementalAgreementDetailVO.setFundCode(collect.get(0).getFundCode());
            supplementalAgreementDetailVO.setFundName(collect.get(0).getFundName());
        }
        return supplementalAgreementDetailVO;
    }

    /**
     * @param supplementalAgreementDTOS
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.agreement.SupplementalAgreementDetailVO.SupplementalAgreementDetailDtl>
     * @description: 获取详情列表
     * @author: jinqing.rao
     * @date: 2025/3/6 20:01
     * @since JDK 1.8
     */
    private static List<SupplementalAgreementDetailVO.SupplementalAgreementDetailDtl> getSupplementalAgreementDetailDtls(List<SupplementalAgreementDTO> supplementalAgreementDTOS) {
        return supplementalAgreementDTOS.stream()
                .filter(f -> DateUtils.isAfterCurrentDateTime(f.getAgreementSignEndDt(), DateUtils.YYYYMMDDHHMMSS))
                // 按签订截止时间倒序排序
                .sorted((a, b) -> b.getAgreementSignEndDt().compareTo(a.getAgreementSignEndDt()))
                .map(agreementDTO -> {
                    // 转换返回数据
                    SupplementalAgreementDetailVO.SupplementalAgreementDetailDtl detailVO = new SupplementalAgreementDetailVO.SupplementalAgreementDetailDtl();
                    detailVO.setAgreementSignEndDt(DateUtils.formatDateStr(agreementDTO.getAgreementSignEndDt(), DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.YYYYMMDDHHMMSS));
                    detailVO.setAgreementName(agreementDTO.getAgreementName());
                    detailVO.setFundCode(agreementDTO.getFundCode());
                    detailVO.setFundName(agreementDTO.getFundName());
                    detailVO.setAgreementDescription(agreementDTO.getAgreementDescription());
                    detailVO.setAgreementUrl(agreementDTO.getAgreementUrl());
                    detailVO.setAgreementId(agreementDTO.getAgreementId());
                    return detailVO;
                }).collect(Collectors.toList());
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.agreement.SupplementalAgreementSubmitVO
     * @description: 用户补充协议签约
     * @author: jinqing.rao
     * @date: 2025/3/7 13:47
     * @since JDK 1.8
     */
    public SupplementalAgreementSubmitVO submitSupplementalAgreement(SupplementalAgreementSubmitRequest request) {
        SupplementalAgreementSubmitVO supplementalAgreementSubmitVO = new SupplementalAgreementSubmitVO();
        // 验证交易密码
        hkCustInfoOuterService.validateHkTradePassword(request.getHkCustNo(), request.getTxPassword());
        // 提交补充协议的签约基金
        supplementalAgreementOuterService.submitSupplementalAgreement(request.getHkCustNo(), request.getFundCode(), request.getAgreementIdList());

        return supplementalAgreementSubmitVO;
    }
}
