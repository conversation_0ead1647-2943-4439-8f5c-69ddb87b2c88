package com.howbuy.crm.cgi.extservice.service.hkaccount;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.cc.message.SendMsgResult;
import com.howbuy.crm.cgi.common.cacheservice.CacheKeyPrefix;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.*;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.utils.ExceptionUtil;
import com.howbuy.crm.cgi.extservice.common.utils.ParamsValidator;
import com.howbuy.crm.cgi.extservice.common.utils.RandomCodeUtil;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.request.account.GetEmailDigestCodeByTypeRequest;
import com.howbuy.crm.cgi.extservice.request.account.MobileCodeRequset;
import com.howbuy.crm.cgi.extservice.request.account.MobileDigestCodeRequest;
import com.howbuy.crm.cgi.manager.outerservice.encrypt.HwAuthEncryptOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkopen.OpenAcctCatchService;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.CustInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoPlaintextDTO;
import com.howbuy.crm.cgi.manager.outerservice.cc.SmsVerifyCodeOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkPasswordOuterService;
import com.howbuy.crm.cgi.manager.service.verifycode.SmsVerifyCodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 验证码服务
 * @date 2023/6/7 16:14
 * @since JDK 1.8
 */
@Slf4j
@Service("verifyCodeService")
public class VerifyCodeService {

    @Autowired
    private SmsVerifyCodeService smsVerifyCodeService;

    @Autowired
    private SmsVerifyCodeOuterService smsVerifyCodeOuterService;

    @Autowired
    private CustInfoService custInfoService;

    @Autowired
    private HkPasswordOuterService hkPasswordOuterService;

    @Autowired
    private HkCustInfoOuterService hkCustInfoOuterService;

    @Autowired
    private OpenAcctCatchService openAcctCatchService;

    @Autowired
    private HwAuthEncryptOuterService hwAuthEncryptOuterService;

    /**
     * 验证码有效时间，秒
     */
    @Value("${sms.verifyCode.validTime:600}")
    private int validTime;

    @Value("${active.env}")
    private String env;

    /**
     * @description: 发送登陆短信验证码
     * @param: [mobile, messageTempleteEnum]
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO
     * @author: shaoyang.li
     * @date: 2023/6/7 16:51
     * @since JDK 1.8
     */
    public SendVerifyCodeVO getLoginMsgVerifyCode(String mobile) {
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        String verifyCode = null;
        verifyCode = initTestVerifyCode(verifyCode);
        smsVerifyCodeService.sendSmsVerifyCode(mobile, MessageTempleteEnum.LOGIN_MOBILE_VERIFYCODE,
                verifyCode, JudgeRulesEnum.USER_VERFYCODE_LOGIN_RULE);
        sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
        return sendVerifyCodeVO;
    }

    /**
     * @param mobile
     * @param verifyCodeType
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO
     * @description:(获取语音验证码)
     * @author: xufanchao
     * @date: 2024/11/8 15:02
     * @since JDK 1.8
     */
    public SendVerifyCodeVO getVoiceMsgVerifyCode(String mobile, String verifyCodeType) {
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        String verifyCode = RandomCodeUtil.createVerifyCode();
        HkCustInfoDTO custInfo = hkCustInfoOuterService.getHkCustInfo(RequestUtil.getParameter(Constants.HKCUSTNO));
        try {
            if (mobile.equals(custInfo.getMobileDigest())) {
                String realMobile = hkCustInfoOuterService.getMobile(RequestUtil.getParameter(Constants.HKCUSTNO));
                String encryptString = hwAuthEncryptOuterService.getEncryptString(custInfo.getMobileAreaCode() + realMobile);
                SendMsgResult sendMsgResult = smsVerifyCodeService.sendVoiceMsg(encryptString, verifyCode, JudgeRulesEnum.USER_VERFYCODE_LOGIN_RULE);
                if (null != sendMsgResult) {
                    String verifyCodeKey = CacheKeyPrefix.VERIFY_CODE_KEY_PREFIX + encryptString + VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(verifyCodeType).getCode();
                    CacheServiceImpl.getInstance().put(validTime, verifyCodeKey, verifyCode);
                }
                sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
            }
        } catch (Exception e) {
            log.error("SendVoiceMsg is error:", e);
            sendVerifyCodeVO.setSendState(YesNoEnum.NO.getCode());
        }
        return sendVerifyCodeVO;
    }

    /**
     * @description:(语音验证码的验证接口)
     * @param mobile
     * @param verifyCode
     * @return boolean
     * @author: xufanchao
     * @date: 2024/11/8 15:21
     * @since JDK 1.8
     */
    public void verifyvoiceMobileVerifyCode(String mobile, String verifyCode) {
        String verifyCodeKey = CacheKeyPrefix.VERIFY_CODE_KEY_PREFIX + mobile;
        if (CacheServiceImpl.getInstance().exists(verifyCodeKey)){
            String cacheVerifyCode = CacheServiceImpl.getInstance().get(verifyCodeKey).toString();
            if (!StringUtils.equals(cacheVerifyCode, verifyCode)){
                ExceptionUtil.throwBusinessException(ExceptionCodeEnum.VOICE_MSG_ERROR);
            }
        }else {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.VOICE_MSG_EXPIRED_ERROR);
        }
    }

    /**
     * @description: 重置登录密码手机验证码发送
     * @param: [mobile]
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO
     * @author: shaoyang.li
     * @date: 2023/6/9 16:10
     * @since JDK 1.8
     */
    public SendVerifyCodeVO getResetLoginPasswordMobileVerifyCode(String mobile) {
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        smsVerifyCodeService.sendSmsVerifyCode(mobile, MessageTempleteEnum.RESET_LOGIN_PASSWORD_MOBILE_VERIFYCODE,
                null, JudgeRulesEnum.RESET_PASSWORD_VERIFYCODE_RULE);
        sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
        return sendVerifyCodeVO;
    }

    /**
     * @description: 重置交易密码手机验证码发送
     * @param: [mobile]
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO
     * @author: shaoyang.li
     * @date: 2023/6/9 16:09
     * @since JDK 1.8
     */
    public SendVerifyCodeVO getResetTxPasswordMobileVerifyCode(String mobile) {
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        // 校验是否登录、校验手机号码是否一致
        String hkCustNo = getLoginHkCustNo();
        HkCustInfoDTO hkCustInfoDTO = custInfoService.getHkCustInfo(hkCustNo);
        if (!Objects.equals(DigestUtil.digest(mobile), hkCustInfoDTO.getMobileDigest())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.MOBILE_DIFF);
        }
        smsVerifyCodeService.sendSmsVerifyCode(mobile, MessageTempleteEnum.RESET_TRADE_PASSWORD_MOBILE_VERIFYCODE,
                null, JudgeRulesEnum.RESET_PASSWORD_VERIFYCODE_RULE);
        sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
        return sendVerifyCodeVO;
    }

    /**
     * @description: 重置登录密码邮箱验证码发送
     * @param: [email]
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO
     * @author: shaoyang.li
     * @date: 2023/6/9 16:09
     * @since JDK 1.8
     */
    public SendVerifyCodeVO getResetLoginPasswordEmailVerifyCode(String email) {
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        smsVerifyCodeService.sendEmailVerifyCode(email, MessageTempleteEnum.RESET_LOGIN_PASSWORD_EMAIL_VERIFYCODE,
                RandomCodeUtil.createVerifyCode(), JudgeRulesEnum.EMAIL_VERIFYCODE_RULE);
        sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
        return sendVerifyCodeVO;
    }

    /**
     * @description: 重置交易密码邮箱验证码发送
     * @param: [email]
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO
     * @author: shaoyang.li
     * @date: 2023/6/9 16:09
     * @since JDK 1.8
     */
    public SendVerifyCodeVO getResetTxPasswordEmailVerifyCode(String email) {
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        // 校验是否登录、校验邮箱是否一致
        String hkCustNo = getLoginHkCustNo();
        HkCustInfoDTO hkCustInfoDTO = custInfoService.getHkCustInfo(hkCustNo);
        if (!Objects.equals(DigestUtil.digest(email), hkCustInfoDTO.getEmailDigest())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.EMAIL_DIFF);
        }
        smsVerifyCodeService.sendEmailVerifyCode(email, MessageTempleteEnum.RESET_TRADE_PASSWORD_EMAIL_VERIFYCODE,
                RandomCodeUtil.createVerifyCode(), JudgeRulesEnum.EMAIL_VERIFYCODE_RULE);
        sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
        return sendVerifyCodeVO;
    }

    /**
     * @description: 手机号验证验证码发送
     * @param: [mobile]
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO
     * @author: shaoyang.li
     * @date: 2023/6/13 10:31
     * @since JDK 1.8
     */
    public SendVerifyCodeVO getMobileVerifyMsgVerifyCode(String mobile) {
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        smsVerifyCodeService.sendSmsVerifyCode(mobile, MessageTempleteEnum.MOBILE_VERIFY_VERIFYCODE,
                null, JudgeRulesEnum.MOBILE_VERIFY_VERIFYCODE_RULE);
        sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
        return sendVerifyCodeVO;
    }

    /**
     * @description:(根据验证码类型发送手机验证码)
     * @param req
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO
     * @author: xufanchao
     * @date: 2023/12/12 17:52
     * @since JDK 1.8
     */
    public SendVerifyCodeVO getMobileVerifyCodeByType(MobileCodeRequset req) {
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        // 1.参数校验
        ParamsValidator.validateParams(req, "mobile", "verifyCodeType");
        // 2. 获取登陆会话信息
        String hkCustNo = getLoginHkCustNo();
        HkCustInfoDTO hkCustInfoByBindHboneNo = hkCustInfoOuterService.getHkCustInfoByBindHboneNo(hkCustNo);
        // 对交易密码做做操作的时候比较一下类型
        if ( (req.getVerifyCodeType().equals(VerifyCodeTypeEnum.RESET_TRADE_PASSWORD_MOBILE_VERIFY_CODE.getCode()) || req.getVerifyCodeType().equals(VerifyCodeTypeEnum.SET_TRADE_PASSWORD_MOBILE_VERIFY_CODE.getCode())) &&
                !Objects.equals(DigestUtil.digest(req.getMobile()), hkCustInfoByBindHboneNo.getMobileDigest())) {
            throw new BusinessException(ExceptionCodeEnum.MOBILE_DIFF);
        }
        getMobileVerifyCodeByType(req, hkCustNo);
        sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
        return sendVerifyCodeVO;
    }

    /**
     * @description:(调用手机验证码方法)
     * @param req
     * @param hkCustNo
     * @return void
     * @author: xufanchao
     * @date: 2024/1/10 17:21
     * @since JDK 1.8
     */
    private void getMobileVerifyCodeByType(MobileCodeRequset req, String hkCustNo) {
        // 判断传入的手机号验证类型，香港手机号 和一账通手机号验证操作需要 传入类型和定义的验证码
        if (req.getVerifyCodeType().equals(VerifyCodeTypeEnum.BIND_HK_MOBILE_VERIFY_CODE.getCode()) || req.getVerifyCodeType().equals(VerifyCodeTypeEnum.UNBIND_HK_MOBILE_VERIFY_CODE.getCode()) ||
                req.getVerifyCodeType().equals(VerifyCodeTypeEnum.UNBIND_HBONE_MOBILE_VERIFY_CODE.getCode()) || req.getVerifyCodeType().equals(VerifyCodeTypeEnum.BIND_HBONE_MOBILE_VERIFY_CODE.getCode()) ||
                req.getVerifyCodeType().equals(VerifyCodeTypeEnum.EDIT_MOBILE_VERIFY_CODE.getCode()) || req.getVerifyCodeType().equals(VerifyCodeTypeEnum.BIND_MOBILE_VERIFY_CODE.getCode())
                || req.getVerifyCodeType().equals(VerifyCodeTypeEnum.SET_TRADE_PASSWORD_MOBILE_VERIFY_CODE.getCode()) || req.getVerifyCodeType().equals(VerifyCodeTypeEnum.MOBILE_WRITE_OPEN_FILE_CODE.getCode())) {
            //历史问题处理,统一手机号的验证码缓存key
            String verifyCodeKey = CacheKeyPrefix.VERIFY_CODE_KEY_PREFIX + req.getMobile() + VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(req.getVerifyCodeType()).getCode();
            if (CacheServiceImpl.getInstance().exists(verifyCodeKey)) {
                String code = CacheServiceImpl.getInstance().get(verifyCodeKey);
                smsVerifyCodeService.sendSmsVerifyCode(req.getMobile(), VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(req.getVerifyCodeType()),
                        code, JudgeRulesEnum.MOBILE_VERIFY_VERIFYCODE_RULE);
            } else {
                String verifyCode = RandomCodeUtil.createVerifyCode();
                smsVerifyCodeService.sendSmsVerifyCode(req.getMobile(), VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(req.getVerifyCodeType()),
                        verifyCode, JudgeRulesEnum.MOBILE_VERIFY_VERIFYCODE_RULE);
                CacheServiceImpl.getInstance().put(validTime, verifyCodeKey, verifyCode);
            }
        } else {
            smsVerifyCodeService.sendSmsVerifyCode(req.getMobile(), VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(req.getVerifyCodeType()),
                    null, JudgeRulesEnum.MOBILE_VERIFY_VERIFYCODE_RULE);
        }
    }


    /**
     * @description:(通过手机号摘要和验证码类型获取手机验证码)
     * @param req
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO
     * @author: xufanchao
     * @date: 2023/12/18 15:34
     * @since JDK 1.8
     */
    public SendVerifyCodeVO getMobileDigestVerifyCodeByType(MobileDigestCodeRequest req) {
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        // 1.参数校验
        ParamsValidator.validateParams(req, "mobileDigest", "verifyCodeType");
        String hkCustNo = getLoginHkCustNo();
        // 当验证码的类型不为好买手机号的时候
        if (!(req.getVerifyCodeType().equals(VerifyCodeTypeEnum.UNBIND_HBONE_MOBILE_VERIFY_CODE.getCode()) || req.getVerifyCodeType().equals(VerifyCodeTypeEnum.BIND_HBONE_MOBILE_VERIFY_CODE.getCode()))) {
            CustInfoVO custInfo = custInfoService.getCustInfo();
            if (custInfo != null && custInfo.getMobileDigest().equals(req.getMobileDigest())) {
                String mobile = hkCustInfoOuterService.getMobile(hkCustNo);
                return setMobileVerifyCode(req, sendVerifyCodeVO, hkCustNo, mobile);
            } else {
                ExceptionUtil.throwBusinessException(ExceptionCodeEnum.MOBILE_DIFF);
            }
        } else {
            // 当验证码类型为好买的时候 比较好买的手机号摘要
            HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfoByBindHboneNo(hkCustNo);
            String hbMobile = hkCustInfoOuterService.getHbMobile(hkCustNo);
            if (Objects.equals(hkCustInfoDTO.getHbMobileDigest(), req.getMobileDigest())) {
                return setMobileVerifyCode(req, sendVerifyCodeVO, hkCustNo, hbMobile);

            }else {
                ExceptionUtil.throwBusinessException(ExceptionCodeEnum.MOBILE_DIFF);
            }
        }
        return sendVerifyCodeVO;
    }

    /**
     * 发送验证码
     * @param req
     * @param sendVerifyCodeVO
     * @param hkCustNo
     * @param hbMobile
     * @return
     */
    private SendVerifyCodeVO setMobileVerifyCode(MobileDigestCodeRequest req, SendVerifyCodeVO sendVerifyCodeVO, String hkCustNo, String hbMobile) {
        // 历史问题修复,统一手机验证码的缓存key
        String  verifyCodeKey = CacheKeyPrefix.VERIFY_CODE_KEY_PREFIX + hbMobile + VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(req.getVerifyCodeType()).getCode();
        if (CacheServiceImpl.getInstance().exists(verifyCodeKey)) {
            String code = CacheServiceImpl.getInstance().get(verifyCodeKey);
            smsVerifyCodeService.sendSmsVerifyCode(hbMobile, VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(req.getVerifyCodeType()),
                    code, JudgeRulesEnum.MOBILE_VERIFY_VERIFYCODE_RULE);
            sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
            return sendVerifyCodeVO;
        }else {
            String verifyCode = RandomCodeUtil.createVerifyCode();
            verifyCode = initTestVerifyCode(verifyCode);
            smsVerifyCodeService.sendSmsVerifyCode(hbMobile, VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(req.getVerifyCodeType()),
                    verifyCode, JudgeRulesEnum.MOBILE_VERIFY_VERIFYCODE_RULE);
            sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
            CacheServiceImpl.getInstance().put(validTime, verifyCodeKey, verifyCode);
            return sendVerifyCodeVO;
        }
    }


    /**
     * @description:(根据邮箱号的摘要获取对应的验证码)
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO
     * @author: xufanchao
     * @date: 2023/12/18 16:44
     * @since JDK 1.8
     */
    public SendVerifyCodeVO getEmailDigestVeerifyCodeByType(GetEmailDigestCodeByTypeRequest request) {
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        // 1.参数校验
        ParamsValidator.validateParams(request, "emailDigest", "verifyCodeType");
        // 获取登录的信息
        CustInfoVO custInfo = custInfoService.getCustInfo();
        // 当前邮箱摘要和登录信息中的邮箱摘要相同的时候获取对应明文，并且发送邮箱验证码
        if (custInfo!= null && custInfo.getEmailDigest().equals(request.getEmailDigest())) {
            HkCustInfoPlaintextDTO custInfoPlaintext = custInfoService.getCustInfoPlaintext(custInfo.getHkCustNo());
            //修复历史问题,统一邮箱的验证码缓存key
            String verifyCodeKey = CacheKeyPrefix.VERIFY_CODE_KEY_PREFIX + custInfoPlaintext.getEmail() + VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(request.getVerifyCodeType()).getCode();
            if (CacheServiceImpl.getInstance().exists(verifyCodeKey)) {
                String code = CacheServiceImpl.getInstance().get(verifyCodeKey);
                smsVerifyCodeService.sendEmailVerifyCode(custInfoPlaintext.getEmail(), VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(request.getVerifyCodeType()),
                        code, JudgeRulesEnum.MOBILE_VERIFY_VERIFYCODE_RULE);
            } else {
                String verifyCode = RandomCodeUtil.createVerifyCode();
                verifyCode = initTestVerifyCode(verifyCode);
                smsVerifyCodeService.sendEmailVerifyCode(custInfoPlaintext.getEmail(), VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(request.getVerifyCodeType()),
                        verifyCode, JudgeRulesEnum.MOBILE_VERIFY_VERIFYCODE_RULE);
                CacheServiceImpl.getInstance().put(validTime, verifyCodeKey, verifyCode);
            }
            sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
            return sendVerifyCodeVO;
        }else {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.EMAIL_DIFF);
        }
        return sendVerifyCodeVO;
    }

    /**
     * @description: 默认初始化测试环境的验证码Code为 111111
     * @param verifyCode
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/5/29 18:15
     * @since JDK 1.8
     */
    private String initTestVerifyCode(String verifyCode) {
        if(env.equals(Constants.DEV) || env.equals(Constants.TEST)){
            verifyCode = Constants.TEST_VERIFY_CODE_111111;
        }
        return verifyCode;
    }

    /**
     * @description: 获取激活手机验证码
     * @param: [mobileDigest, messageTempleteEnum]
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO
     * @author: shaoyang.li
     * @date: 2023/6/7 19:26
     * @since JDK 1.8
     */
    public SendVerifyCodeVO getActivateMobileVerifyCode(String mobileDigest, MessageTempleteEnum messageTempleteEnum) {
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        // 校验是否登录、校验手机号码是否一致
        String hkCustNo = getLoginHkCustNo();
        HkCustInfoDTO hkCustInfoDTO = custInfoService.getHkCustInfo(hkCustNo);
        if (!Objects.equals(mobileDigest, hkCustInfoDTO.getMobileDigest())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.MOBILE_DIFF);
        }
        String mobile = custInfoService.getMobilePlaintext(hkCustInfoDTO.getHkCustNo());
        smsVerifyCodeService.sendSmsVerifyCode(mobile, messageTempleteEnum,
                RandomCodeUtil.createVerifyCode(), JudgeRulesEnum.ACTIVATE_VERIFYCODE_RULE);
        sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
        return sendVerifyCodeVO;
    }

    /**
     * @description: 获取激活邮箱验证码
     * @param: [emailDigest, messageTempleteEnum
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO
     * @author: shaoyang.li
     * @date: 2023/6/7 19:55
     * @since JDK 1.8
     */
    public SendVerifyCodeVO getActivateEmailVerifyCode(String emailDigest, MessageTempleteEnum messageTempleteEnum) {
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        String hkCustNo = getLoginHkCustNo();
        HkCustInfoDTO hkCustInfoDTO = custInfoService.getHkCustInfo(hkCustNo);
        if (!Objects.equals(emailDigest, hkCustInfoDTO.getEmailDigest())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.EMAIL_DIFF);
        }
        HkCustInfoPlaintextDTO hkCustInfoPlaintextDTO = custInfoService.getCustInfoPlaintext(hkCustInfoDTO.getHkCustNo());
        smsVerifyCodeService.sendEmailVerifyCode(hkCustInfoPlaintextDTO.getEmail(), messageTempleteEnum,
                RandomCodeUtil.createVerifyCode(), JudgeRulesEnum.EMAIL_VERIFYCODE_RULE);
        sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
        return sendVerifyCodeVO;
    }

    /**
     * @description: 校验短信验证码
     * @param: [mobile, verifyCode, messageTempleteEnum]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/8 10:41
     * @since JDK 1.8
     */
    public void checkSmsVerifyCode(String mobile, String verifyCode, MessageTempleteEnum messageTempleteEnum) {
        smsVerifyCodeOuterService.checkSmsVerifyCode(mobile, verifyCode, messageTempleteEnum);
        //手机验证验证码,保存手机验证验证码的状态，主要是开户步骤2个人信息页,不用重复验证手机号。
        if (messageTempleteEnum.getCode().equals(MessageTempleteEnum.MOBILE_WRITE_OPEN_FILE.getCode())) {
            String loginHkCustNo = getLoginHkCustNo();
            openAcctCatchService.saveCheckVerifyCodeStatus(loginHkCustNo,mobile);
        }
    }

    /**
     * @description: 校验邮箱验证码
     * @param: [email, verifyCode, messageTempleteEnum]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/8 10:41
     * @since JDK 1.8
     */
    public  void checkEmailVerifyCode(String email, String verifyCode, MessageTempleteEnum messageTempleteEnum) {
        smsVerifyCodeOuterService.checkEmailVerifyCode(email, verifyCode, messageTempleteEnum);
        //邮箱证验证码,保存邮箱验证验的状态，主要是开户步骤2个人信息页,不用重复验证邮箱。
        if (messageTempleteEnum.getCode().equals(MessageTempleteEnum.EMAIL_WRITE_OPEN_FILE.getCode())) {
            String loginHkCustNo = getLoginHkCustNo();
            openAcctCatchService.saveEmailCheckVerifyCodeStatus(loginHkCustNo,email);
        }
    }

    /**
     * @param mobile 手机号
     * @param verifyCode 验证码
     * @return void
     * @description:(修改手机号)
     * @author: xufanchao
     * @date: 2023/12/8 15:06
     * @since JDK 1.8
     */
    public void editMobile(String mobile, String verifyCode, String txPassword) {
        String loginHkCustNo = getLoginHkCustNo();
        // 校验验证码
        checkSmsVerifyCode(mobile, verifyCode, MessageTempleteEnum.EDIT_MOBILE_VERIFYCODE);
        //通过香港客户号获取对应的一账通号
        String bindHboneNo = hkCustInfoOuterService.getBindHboneNo(loginHkCustNo);
        if(StringUtils.isBlank(bindHboneNo)){
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
        // 通过香港客户号获取交易信息
        String txAcctByHbone = hkCustInfoOuterService.getTxAcctByHbone(bindHboneNo);
        if(StringUtils.isNotBlank(txAcctByHbone)){
            throw new BusinessException(ExceptionCodeEnum.HW_CUSTOMER_REAL_NAME_EDIT_MOBILE_ERROR);
        }
        // 调用 香港账户中心的修改手机号接口 具体字段值需要修改
        hkPasswordOuterService.changeCustMobile(mobile,loginHkCustNo, txPassword);
    }


    /**
     * @description:(修改邮件方法)
     * @param email 邮箱号
     * @param verifyCode 验证码
     * @param txPassword 交易密码
     * @param syncStatementEmail 是否同步更新结单邮箱 1-是
     * @return void
     * @author: xufanchao
     * @date: 2023/12/8 15:08
     * @since JDK 1.8
     */
    public void editEmail(String email, String verifyCode, String txPassword, String syncStatementEmail) {
        // 校验验证码
        checkEmailVerifyCode(email, verifyCode, MessageTempleteEnum.EDIT_EMAIL_VERIFYCODE);
        // 获取登录用户的香港客户号
        String loginHkCustNo = getLoginHkCustNo();
        hkPasswordOuterService.changeEmail(email, loginHkCustNo, txPassword, syncStatementEmail);
    }

    private static String getLoginHkCustNo() {
        return RequestUtil.getParameter(Constants.HKCUSTNO);
    }


    /**
     * @description:(获取邮箱验证码)
     * @param email
     * @param codeType
     * @return void
     * @author: xufanchao
     * @date: 2023/12/8 15:38
     * @since JDK 1.8
     */
    public SendVerifyCodeVO sendEmailVerifyCode(String email, String codeType) {
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        //获取当前登陆用户的信息
        CustInfoVO custInfo = custInfoService.getCustInfo();
        String emailDigest = custInfo.getEmailDigest();
        // 当验证码类型为 重置交易密码和设置交易密码的是欧判断是否一致
        if ((codeType.equals(VerifyCodeTypeEnum.RESET_TRADE_PASSWORD_EMAIL_VERIFY_CODE.getCode()) || codeType.equals(VerifyCodeTypeEnum.SET_TRADE_PASSWORD_EMAIL_VERIFY_CODE.getCode())) && !Objects.equals(emailDigest, DigestUtil.digest(email))) {
            sendVerifyCodeVO.setSendState(YesNoEnum.NO.getCode());
            throw new BusinessException(ExceptionCodeEnum.EMAIL_DIFF.getCode(), "传入邮箱与客户实际邮箱不一致");
        }
        // 发送验证码
        // 判断当前邮箱以及对应验证码是否存在
        //历史问题处理 统一email的verifyCodeKey缓存
        String verifyCodeKey = CacheKeyPrefix.VERIFY_CODE_KEY_PREFIX + email + codeType;
        if (CacheServiceImpl.getInstance().exists(verifyCodeKey)) {
            String code = CacheServiceImpl.getInstance().get(verifyCodeKey);
            smsVerifyCodeService.sendEmailVerifyCode(email, VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(codeType),
                    code, JudgeRulesEnum.EMAIL_VERIFYCODE_RULE);
        }else {
            String verifyCode = RandomCodeUtil.createVerifyCode();
            smsVerifyCodeService.sendEmailVerifyCode(email, VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(codeType),
                    verifyCode, JudgeRulesEnum.EMAIL_VERIFYCODE_RULE);
            CacheServiceImpl.getInstance().put(validTime, verifyCodeKey, verifyCode);
        }
        sendVerifyCodeVO.setSendState(YesNoEnum.YES.getCode());
        return sendVerifyCodeVO;
    }
}
