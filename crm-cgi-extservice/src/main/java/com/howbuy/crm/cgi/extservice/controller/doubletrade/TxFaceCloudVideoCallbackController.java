package com.howbuy.crm.cgi.extservice.controller.doubletrade;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.common.utils.AppEncUtils;
import com.howbuy.crm.cgi.extservice.service.doubletrade.DoubleTradeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 甜新双录视频生成回调接口
 * @Date 2024/6/17 18:36
 */
@Slf4j
@RestController
@RequestMapping("/doubletrade/videocallback")
@Validated
public class TxFaceCloudVideoCallbackController {

    @Autowired
    private DoubleTradeService doubleTradeService;

    /**
     * @api {GET} /ext/doubletrade/videocallback/notice notice()
     * @apiVersion 1.0.0
     * @apiGroup TxFaceCloudVideoCallbackController
     * @apiName notice()
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} bizId  双录流水id
     * @apiParamExample 请求参数示例
     * bizId=6zyI5xN
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Q6Z","description":"0MoXk6","timestampServer":"CJN3vFE"}
     */
    @GetMapping("/notice")
    public CgiResponse<Body> notice(
            @NotBlank(message = "bizId不可为空！")
            @RequestParam(value = "bizId") String bizId
    ) {
        return doubleTradeService.notice(bizId);
    }

    public static void main(String[] args) {
        String l3ZoNERFVmRsM0E9 = AppEncUtils.decrypt("L3ZoNERFVmRsM0E9");
        System.out.println(l3ZoNERFVmRsM0E9);
    }
}
