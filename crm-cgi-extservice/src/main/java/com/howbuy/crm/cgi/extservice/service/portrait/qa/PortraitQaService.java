/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.qa;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.extservice.convert.portrait.PortraitMaterialConvert;
import com.howbuy.crm.cgi.extservice.convert.portrait.QaReplyConvert;
import com.howbuy.crm.cgi.extservice.request.portrait.QaDirectoryRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.QaReplyRequest;
import com.howbuy.crm.cgi.extservice.vo.portrait.material.PortraitMaterialDirectoryVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.qa.QaReplyVO;
import com.howbuy.crm.cgi.manager.domain.portrait.qa.CmPortraitQaReplyDTO;
import com.howbuy.crm.cgi.manager.outerservice.portrait.material.QueryMaterialDirectoryOuterService;
import com.howbuy.crm.cgi.manager.outerservice.portrait.qa.PortraitQaOuterService;
import com.howbuy.crm.portrait.client.domain.response.material.QueryMaterialDirectoryResponse;
import com.howbuy.crm.portrait.client.enums.qa.DirectorySourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 客户画像问答服务
 *
 * <AUTHOR>
 * @date 2025-03-21 10:26:47
 */
@Service
@Slf4j
public class PortraitQaService {

    @Resource
    private PortraitQaOuterService portraitQaOuterService;

    @Resource
    private QueryMaterialDirectoryOuterService queryMaterialDirectoryOuterService;

    /**
     * 获取问答回复
     *
     * @param request 请求参数
     * @return 回复结果
     */
    public QaReplyVO getQaReply(QaReplyRequest request) {
        CmPortraitQaReplyDTO replyDTO = portraitQaOuterService.getQaReply(
                request.getSource(),
                request.getSearchPool(),
                request.getSearchCondition(),
                request.getHboneNo(),
                request.getConscode(),
                request.getPage(),
                request.getSize(),
                request.getFirstReply(),
                request.getSecondReply()
        );
        return QaReplyConvert.convertToVO(replyDTO);
    }

    /**
     * 获取素材库目录
     * @description 获取素材库目录信息
     * @return PortraitMaterialDirectoryVO 素材库目录响应对象
     * <AUTHOR>
     * @date 2025-03-13 13:18:57
     */
    public PortraitMaterialDirectoryVO getDirectory(QaDirectoryRequest request) {
        log.info("获取素材库目录-开始");

        // 调用外部服务获取素材库目录
        String source = StringUtils.isBlank(request.getSource()) ? DirectorySourceEnum.ZNSCK.getCode() : request.getSource();
        QueryMaterialDirectoryResponse response = queryMaterialDirectoryOuterService.queryMaterialDirectory(source);

        log.info("获取素材库目录-响应结果：{}", JSON.toJSONString(response));

        // 使用转换类构建响应对象
        return PortraitMaterialConvert.convertToDirectoryVO(response);
    }
} 