package com.howbuy.crm.cgi.extservice.common.enums.fund;

import org.apache.commons.lang3.StringUtils;

/**
 * @description: 海外基金交易类型枚举
 * @author: jinqing.rao
 * @date: 2024/5/6 11:20
 * @since JDK 1.8
 */
public enum HKFundTradeTypeEnum {

    BUY("10", "买入","买入","+","1"),

    SELL("11", "卖出","卖出","-","2"),

    BATCH_PAID("12", "批量实缴","买入","+","1"),

    // 实缴
    SUB_AND_FIRST_PAID("13", "认缴和首次实缴","买入","+","1"),

    FULL_BATCH_SUBSCRIBE("14", "全委专户批量认申购","买入","+","1"),

    FULL_BATCH_REDEEM("15", "卖出","卖出","-","2"),

    NO_TRADE_OVER_ACCOUNT_IN("16", "非交易过户转入","非交易转入","+","0"),

    NO_TRADE_OVER_ACCOUNT_OUT("17", "非交易过户转出","非交易转出","-","0"),

    DIV_MODE_CHANGE("18", "修改分红方式","修改分红方式","+","0"),

    FORCE_REDEEM("19", "强制赎回","强赎","-","0"),

    DIV("20", "红利下发","红利下发","+","0"),

    FORCE_ADD("21", "份额强增","强增","+","0"),

    FORCE_SUBTRACT("22", "份额强减","强减","-","0"),

    FUND_TERMINATION("23", "基金终止","基金终止","-","0"),

    FUND_LIQUIDATION("24", "基金清盘","基金清盘","-","0"),

    // 交易过户买入
    TRANSFER_BUY("25", "过户买入","买入","+","1"),

    // 交易过户卖出
    TRANSFER_SELL("26", "过户卖出","卖出","-","2"),

    //系列合并转入
    SERIES_MERGE_IN("27", "系列合并转入","系列合并转入","+","0"),

    //系列合并转出
    SERIES_MERGE_OUT("28", "系列合并转出","系列合并转出","-","0"),

    // 基金转换
    FUND_CONVERSION("29", "基金转换","基金转换","","3"),

    // 平衡因子兑换
    BALANCE_FACTOR_EXCHANGE("30", "平衡因子兑换","平衡因子兑换","+","0"),

    ;
    private final String code;
    private final String desc;
    private final String disName;
    private final String direction;

    private final String typeIcon;

    HKFundTradeTypeEnum(String code, String desc,String disName,String direction,String typeIcon) {
        this.code = code;
        this.desc = desc;
        this.disName = disName;
        this.direction = direction;
        this.typeIcon = typeIcon;
    }
    
    /**
     * @description: 通过Code获取对应的desc
     * @param code
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/5/6 11:23
     * @since JDK 1.8
     */
    public static String getDescByCode(String code) {
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (HKFundTradeTypeEnum value : HKFundTradeTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static String getDisNameByCode(String code) {
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (HKFundTradeTypeEnum value : HKFundTradeTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDisName();
            }
        }
        return null;
    }

    /**
     * @description: 通过Code获取对应的direction
     * @param code
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/5/6 11:23
     * @since JDK 1.8
     */
    public static String getDirectionByCode(String code) {
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (HKFundTradeTypeEnum value : HKFundTradeTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDirection();
            }
        }
        return null;
    }

    public static String getTypeIconByCode(String code) {
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (HKFundTradeTypeEnum value : HKFundTradeTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getTypeIcon();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getDirection() {
        return direction;
    }

    public String getTypeIcon() {
        return typeIcon;
    }

    public String getDisName() {
        return disName;
    }
}
