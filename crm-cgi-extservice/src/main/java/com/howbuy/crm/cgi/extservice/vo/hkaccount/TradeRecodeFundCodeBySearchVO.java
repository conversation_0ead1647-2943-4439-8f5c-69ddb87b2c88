package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 交易记录基金模糊查询响应VO
 * <AUTHOR>
 * @date 2025/6/30 10:00:00
 */
@Setter
@Getter
public class TradeRecodeFundCodeBySearchVO extends Body {

    private static final long serialVersionUID = 6582236664852448798L;
    /**
     * 基金列表
     */
    private List<FundInfoVO> fundList = new ArrayList<>();

    /**
     * 基金信息VO
     */
    @Setter
    @Getter
    public static class FundInfoVO {

        /**
         * 基金编码
         */
        private String fundCode;

        /**
         * 基金名称
         */
        private String fundName;

        /**
         * 基金英文名称
         */
        private String fundEnName;
    }
} 