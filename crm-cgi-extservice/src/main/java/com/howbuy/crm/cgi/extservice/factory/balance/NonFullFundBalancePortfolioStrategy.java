package com.howbuy.crm.cgi.extservice.factory.balance;

import com.google.common.collect.Lists;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.TxChannelEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.extservice.common.annotation.FundTypeSupport;
import com.howbuy.crm.cgi.extservice.common.enums.balance.HkCustBalanceDisPalyCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.enums.refund.RefundApplyDataSourceEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherAuditStatusEnum;
import com.howbuy.crm.cgi.extservice.common.utils.balance.BalanceUtils;
import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.AllInTransitTradeBalancePortfolioDetail;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.PayVoucherListPageRequestDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.capital.QueryInTransitTradeCapitalRequestDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.capital.QueryInTransitTradeCapitalResponseDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.piggy.PiggyPayVoucherRecordPageDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryBalanceDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryFinReceiptDTO;
import com.howbuy.crm.cgi.manager.domain.hkfin.HkFinRefundInfoRequestDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.PiggyPayVoucherOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.capital.QueryInTransitTradeCapitalOuterService;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import com.howbuy.dtms.order.client.enums.PayVoucherTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 非全委基金持仓在途买入类
 * @author: 陈杰文
 * @date: 2025-07-03 13:11:28
 * @since JDK 1.8
 */
@Component
@Slf4j
@FundTypeSupport(HkCustBalanceDisPalyCategoryEnum.NON_FULL_FUND_BALANCE_IN_TRANSIT)
public class NonFullFundBalancePortfolioStrategy extends AbstractBalancePortfolioStrategy implements BalancePortfolioStrategy<AllInTransitTradeBalancePortfolioDetail> {

    @Resource
    private QueryInTransitTradeCapitalOuterService queryInTransitTradeCapitalOuterService;

    @Override
    public Class<AllInTransitTradeBalancePortfolioDetail> getSupportedType() {
        return AllInTransitTradeBalancePortfolioDetail.class;
    }

    @Override
    public AllInTransitTradeBalancePortfolioDetail calculate(BalanceContent content) {
        AllInTransitTradeBalancePortfolioDetail.AllInTransitTradeBalancePortfolioDetailBuilder builder = AllInTransitTradeBalancePortfolioDetail.builder();
        if (CollectionUtils.isEmpty(content.getFundTxAcctNoDTOList())) {
            log.info("没有非全委的基金交易账号,非全委的储蓄罐在途策略不执行，返回空数据 , hkCustNo :{} ", content.getHkCustNo());
            return AllInTransitTradeBalancePortfolioDetail.builder().build();
        }
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = content.getFundTxAcctNoDTOList().stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.NON_FULL.getCode().equals(m.getFundTxAccType()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(hkFundTxAcctDTOS)) {
            log.error("用户有基金交易账号,没有非全委的基金交易账号,非全委的基金持仓在途策略不执行，返回空数据 , hkCustNo :{} ", content.getHkCustNo());
            return AllInTransitTradeBalancePortfolioDetail.builder().build();
        }

        List<String> nonFullFundTxAcctNoList = BalanceUtils.extractFieldList(hkFundTxAcctDTOS, HkFundTxAcctDTO::getFundTxAcctNo);

        // 获取海外储蓄罐基金列表信息
        List<FundBasicInfoDTO> fundBasicInfoDTOList = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList();
        List<String> fundCodes = CollectionUtils.isEmpty(fundBasicInfoDTOList) ? new ArrayList<>() :
                fundBasicInfoDTOList.stream()
                        .map(FundBasicInfoDTO::getFundCode)
                        .distinct()
                        .collect(Collectors.toList());

        // 查询在途信息接口
        QueryBalanceDTO queryBalanceDTO = new QueryBalanceDTO();
        queryBalanceDTO.setHkCustNo(content.getHkCustNo());
        // 指定非全委基金交易账号
        queryBalanceDTO.setFundTxAcctNoList(nonFullFundTxAcctNoList);
        // 排除海外储蓄罐产品
        queryBalanceDTO.setExcludeFundCodeList(fundCodes);
        queryBalanceDTO.setDisPlayCurrency(content.getDisPlayCurrencyCode());

        // 查询在途数据
        QueryFinReceiptDTO queryFinReceiptDTO = queryBalanceOuterService.queryInTransitTradeBalance(queryBalanceDTO);
        // 在途资金信息（在途资金数量 & 在途资金订单号）
        buildInTransitTradeCapitalCountAndDealNo(content, builder);

        // 在途交易相关数据
        List<String> allInTransitList = this.mergeUnpaidAndUnconfirmed(queryFinReceiptDTO);
        String inTransitDealNo = this.getSingleDealNoIfOnlyOne(allInTransitList);

        // 查询现金存入打款凭证列表信息
        buildCashAccountPayVoucherList(content, builder);

        // 在途提款&到账资金申请个数信息
        buildCashBalanceHkFinRefundCountAndDealNo(getHkFinRefundInfoRequestDTO(content), builder);

        return builder
                // 在途交易笔数
                .inTransitTradeCount(String.valueOf(allInTransitList.size()))
                // 在途交易订单号
                .inTransitTradeDealNo(inTransitDealNo)
                .build();
    }

    /**
     * @param content
     * @param builder
     * @return void
     * @description: 在途资金信息
     * @author: jinqing.rao
     * @date: 2025/7/9 13:59
     * @since JDK 1.8
     */
    private void buildInTransitTradeCapitalCountAndDealNo(BalanceContent content, AllInTransitTradeBalancePortfolioDetail.AllInTransitTradeBalancePortfolioDetailBuilder builder) {

        QueryInTransitTradeCapitalResponseDTO queryInTransitTradeCapitalResponseDTO = queryInTransitTradeCapitalOuterService.queryInTransitTradeCapital(QueryInTransitTradeCapitalRequestDTO.builder()
                .hkCustNo(content.getHkCustNo())
                .build());
        if (null == queryInTransitTradeCapitalResponseDTO) {
            return;
        }
        if (CollectionUtils.isEmpty(queryInTransitTradeCapitalResponseDTO.getInTransitTradeCapitalList())) {
            return;
        }
        // 在途交易资金笔数
        String inTransitTradeCapitalCount = String.valueOf(queryInTransitTradeCapitalResponseDTO.getInTransitTradeCapitalList().size());
        // 在途交易资金订单号
        String inTransitTradeCapitalDealNo = null;
        if (queryInTransitTradeCapitalResponseDTO.getInTransitTradeCapitalList().size() == Constants.NUMBER_ONE) {
            inTransitTradeCapitalDealNo = queryInTransitTradeCapitalResponseDTO.getInTransitTradeCapitalList().get(Constants.NUMBER_ZERO).getDealNo();
        }
        builder.inTransitTradeCapitalCount(StringUtils.isEmpty(inTransitTradeCapitalCount) ? Constants.CONSTANT_ZERO : inTransitTradeCapitalCount);
        builder.inTransitTradeCapitalDealNo(inTransitTradeCapitalDealNo);
    }

    /**
     * @param content            上下文
     * @return void
     * @description: 获取现金余额在途提款请求参数
     * <AUTHOR>
     * @date 2025-07-10 14:13:43
     */
    private static HkFinRefundInfoRequestDTO getHkFinRefundInfoRequestDTO(BalanceContent content) {
        return HkFinRefundInfoRequestDTO.builder()
                .hkCustNo(content.getHkCustNo())
                .fundTxAcctNoList(getHkFundTxAcctNoList(content))
                .dataSource(RefundApplyDataSourceEnum.ONLINE.getCode())
                .deleteFlag(YesNoEnum.NO.getCode())
                .build();
    }

    /**
     * @param dto 合并在途交易数量（待付款订单数量+待确认订单）
     * @return com.howbuy.crm.cgi.extservice.vo.balance.QueryFullFundAssetV1VO
     * @description: 查询全委基金资产接口
     * <AUTHOR>
     * @date 2025-07-03 13:15:21
     */
    private List<String> mergeUnpaidAndUnconfirmed(QueryFinReceiptDTO dto) {
        List<String> unpaid = Optional.ofNullable(dto.getUnpaidList())
                .orElse(Collections.emptyList());
        List<String> unconfirmed = Optional.ofNullable(dto.getUnconfirmedList())
                .orElse(Collections.emptyList());
        List<String> merged = new ArrayList<>(unpaid);
        merged.addAll(unconfirmed);
        return merged;
    }

    /**
     * @param allInTransitList 获取在途交易订单号
     * @return com.howbuy.crm.cgi.extservice.vo.balance.QueryFullFundAssetV1VO
     * @description: 只有一条数据时，获取订单号
     * <AUTHOR>
     * @date 2025-07-03 13:19:46
     */
    private String getSingleDealNoIfOnlyOne(List<String> allInTransitList) {
        return allInTransitList.size() == 1 ? allInTransitList.get(0) : null;
    }
}