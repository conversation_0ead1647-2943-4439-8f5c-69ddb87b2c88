/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkfund;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKRedeemFundBaseRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKRedeemFundOrderRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKRedeemFundSubmitRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.QueryFundCanSellRequest;
import com.howbuy.crm.cgi.extservice.service.hkfund.HKRedeemFundOrderService;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKFundVerificationVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKRedeemFundOrderInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKRedeemFundOrderSubmitVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKRedeemFundPageInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.QueryFundCanSellVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 海外基金赎回
 * <AUTHOR>
 * @date 2024/4/18 13:18
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkfund/redeem/")
public class RedeemFundController {

    @Resource
    private HKRedeemFundOrderService hkRedeemFundService;

    /**
     * @api {POST} /ext/hkfund/redeem/verification redeemFundVerification()
     * @apiVersion 1.0.0
     * @apiGroup RedeemFundController
     * @apiName redeemFundVerification()
     * @apiDescription 赎回页-赎回校验接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"DoPfl"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.buyVerfiyState 购买验证状态      0-校验通过      1-去开户      2-继续处理      3-查看开户进度      4-修改开户资料      5-去入金      6-查看入金进度      7-修改入金资料      8-客户状态异常      19-交易账号未激活      20-未绑定银行卡      21-风险等级不匹配      22-客户衍生品经验为否      23-客户非专业投资者      24-客户资产证明有效期已过期
     * @apiSuccess (响应结果) {String} data.customRiskLevelDesc 客户风险等级描述   风险等级      0-保守型（最低）      1-保守型      2-稳健型      3-平衡型      4-成长型      5-进取型
     * @apiSuccess (响应结果) {String} data.fundRiskLevelDesc 基金风险等级描述  R1-低风险,R2-中低风险 R3-中风险 R4-中高风险 R5-高风险
     * @apiSuccess (响应结果) {String} data.openAcctStep 开户订单信息,填写到具体的步骤,开户步骤标识：1-证件信息页；2-个人信息页；3-职业信息页；4-声明信息页；5-投资经验页；6-银行卡页；7-电子签名页      该字段通过循环查询缓存,判断最大不步骤页
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"g1ZHkk1524","data":{"buyVerfiyState":"E","fundRiskLevelDesc":"V0ntdeaM","customRiskLevelDesc":"T2okgENMt","openAcctStep":"SJv"},"description":"6EzGx3G","timestampServer":"UofZW"}
     */
    @PostMapping("verification")
    public CgiResponse<HKFundVerificationVO> redeemFundVerification(@RequestBody HKRedeemFundBaseRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkRedeemFundService.redeemFundVerification(request));
    }

    /**
     * @api {POST} /ext/hkfund/redeem/queryredeempageinfo queryRedeemPageInfo()
     * @apiVersion 1.0.0
     * @apiGroup RedeemFundController
     * @apiName queryRedeemPageInfo()
     * @apiDescription 赎回页面-查询赎回页信息接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParamExample 请求体示例
     * {"fundCode":"Dn69yubh","hkCustNo":"ihXIV5SE"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.mobileAreaCode 手机区号
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱掩码
     * @apiSuccess (响应结果) {Object} data.redeemFundInfoVO 赎回页信息
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.fundShortName 基金简称
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.fundEnName 基金英文名
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.supportPrebook 是否支持预约  1 : 是 0 : 否
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.openStartDate 开放开始日期
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.openEndDate 开放结束日期
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.openEndTime 开放结束时间
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.tradeDate 交易时间 yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.currentDate 当前日期
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.currencyCode 币种
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.currencyDesc 币种描述
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.inTransitOrder 有无在途订单 0：否 1：是
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.totalVol 总份额
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.volUnit 份额单位
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.availableVol 可用份额
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.totalAsset 总资产
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.availableAsset 可用资产
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.navDate 净值日期
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.fundRedeemMethod 1-按份额、2-按金额、3-全部
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.amtRedeemDiscountRatio 按金额赎回折价比率
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.minHoldVol 最低持有份额
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.minHoldAmt 最低持有金额
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.minAppVol 最低申请份额
     * @apiSuccess (响应结果) {String} data.redeemFundInfoVO.minAppAmt 最低申请金额
     * @apiSuccess (响应结果) {Object} data.redeemPrebookInfoVO 赎回预约信息
     * @apiSuccess (响应结果) {String} data.redeemPrebookInfoVO.prebookRedeemMethod 预约赎回方式 1-按份额、2-按金额
     * @apiSuccess (响应结果) {String} data.redeemPrebookInfoVO.prebookRedeemAmt 预约赎回金额
     * @apiSuccess (响应结果) {String} data.redeemPrebookInfoVO.prebookRedeemVol 预约赎回份额
     * @apiSuccess (响应结果) {String} data.redeemPrebookInfoVO.prebookRedeemDate 预约赎回日期
     * @apiSuccess (响应结果) {String} data.redeemPrebookInfoVO.prebookRedeemTime 预约赎回时间
     * @apiSuccess (响应结果) {String} data.redeemPrebookInfoVO.prebookDealNo 预约单号
     * @apiSuccess (响应结果) {Array} data.redeemFeeRateInfoVO 赎回费率信息
     * @apiSuccess (响应结果) {String} data.redeemFeeRateInfoVO.minFeeRateDays 最小费率天数
     * @apiSuccess (响应结果) {String} data.redeemFeeRateInfoVO.maxFeeRateDays 最大费率天数
     * @apiSuccess (响应结果) {String} data.redeemFeeRateInfoVO.feeRate 手续费率
     * @apiSuccess (响应结果) {Object} data.redeemDirectionInfoVO 赎回方向信息
     * @apiSuccess (响应结果) {String} data.redeemDirectionInfoVO.signCxg 是否签约储蓄罐
     * @apiSuccess (响应结果) {Array} data.redeemDirectionInfoVO.redeemDirectionBankInfoVO 赎回银行卡列表
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"xAfBtbmF0","data":{"mobileAreaCode":"kAhlbXg","redeemFundInfoVO":{"supportPrebook":"SZR","currencyDesc":"b3K1FAPJ","totalAsset":"jY","minHoldVol":"86lK6ZZ7iP","navDate":"EQh5qZ","openEndTime":"R8","currentDate":"eCkjJkY","fundShortName":"e7HXGgg","tradeDate":"Q","availableAsset":"OJEHV","minAppAmt":"Qn9mZbyV33","openEndDate":"b","minAppVol":"84l1ropIZ","fundEnName":"SejIHbr","volUnit":"DoCOj79n0","minHoldAmt":"bxEN","openStartDate":"E7HSnVz","inTransitOrder":"gkdok","totalVol":"4YE4zix2PO","amtRedeemDiscountRatio":"wQ96S09","fundRedeemMethod":"Q6v","availableVol":"8PRiMQKNo","currencyCode":"Yll"},"redeemPrebookInfoVO":{"prebookRedeemDate":"AJ","prebookRedeemMethod":"Lgz9","prebookRedeemVol":"0NagXxf","prebookRedeemTime":"3jtU","prebookRedeemAmt":"JDYxpv1Y0","prebookDealNo":"NH"},"redeemDirectionInfoVO":{"signCxg":"Ck9UCeazI","redeemDirectionBankInfoVO":[]},"emailMask":"QJSfkpOWa","redeemFeeRateInfoVO":[{"maxFeeRateDays":"ad","minFeeRateDays":"lwuTUCmh","feeRate":"IcEdCkonY"}],"mobileMask":"GjNbvty"},"description":"4wr1TVO2","timestampServer":"er4VsZi0er"}
     */
    @PostMapping("queryredeempageinfo")
    public CgiResponse<HKRedeemFundPageInfoVO> queryRedeemPageInfo(@RequestBody HKRedeemFundBaseRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkRedeemFundService.queryRedeemPageInfo(request));
    }


    /**
     * @api {POST} /ext/hkfund/redeem/redeemsubmit redeemOrderSubmit()
     * @apiVersion 1.0.0
     * @apiGroup RedeemFundController
     * @apiName redeemOrderSubmit()
     * @apiDescription 赎回页-赎回提交接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} txPassword 交易密码
     * @apiParam (请求体) {String} verifyCodeType 验证码类型
     * @apiParam (请求体) {String} verifyCode 验证码
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} redeemMethod 赎回方式 必须 1-按份额、2-按金额
     * @apiParam (请求体) {String} redeemAmt 赎回金额
     * @apiParam (请求体) {String} redeemVol 赎回份额
     * @apiParam (请求体) {String} redeemDirection 赎回方向 1-回银行卡|电汇、2-留账好买香港账户、3-回海外储蓄罐、4-支票
     * @apiParam (请求体) {String} cpAcctNo 资金账号  1-回银行卡|电汇时必须
     * @apiParam (请求体) {String} prebookDealNo 预约单号
     * @apiParam (请求体) {Array} contractList 合同及列表
     * @apiParam (请求体) {String} contractList.fileCode 文件代码
     * @apiParam (请求体) {String} contractList.fileName 文件名称
     * @apiParam (请求体) {String} contractList.filePathUrl 文件路径
     * @apiParam (请求体) {String} hbSceneId 防重复字段
     * @apiParamExample 请求体示例
     * {"txPassword":"ihZ","verifyCode":"bKcyz","hkCustNo":"9kku0NlCm","redeemVol":"Vp4KEQO","verifyCodeType":"ZGYGZ8","redeemDirection":"zqKMz7i","prebookDealNo":"FcgLZjaNmg","redeemMethod":"N7ppPy7SfS","fundCode":"dcylpH0","hbSceneId":"QaF3bi","redeemAmt":"y0S8h","contractList":[{"filePathUrl":"sU8ehIuRca","fileName":"M","fileCode":"fDbtES"}],"cpAcctNo":"QZrvXy"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.orderNo 订单号
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"v","data":{"orderNo":"CiJijXuh"},"description":"X","timestampServer":"ACln6J8Zl"}
     */
    @PostMapping("redeemsubmit")
    public CgiResponse<HKRedeemFundOrderSubmitVO> redeemOrderSubmit(@RequestBody HKRedeemFundSubmitRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkRedeemFundService.redeemOrderSubmit(request));
    }


    /**
     * @api {POST} /ext/hkfund/redeem/redeemapplyresult redeemApplyResult()
     * @apiVersion 1.0.0
     * @apiGroup RedeemFundController
     * @apiName redeemApplyResult()
     * @apiDescription 赎回页-赎回申请结果接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} orderNo 订单号
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParamExample 请求体示例
     * {"orderNo":"2h8MNY9","hkCustNo":"TNaFkfAb"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.redeemMethod 赎回方式  0-按份额、1-按金额
     * @apiSuccess (响应结果) {String} data.supportPrebook 是否支持预约 1:是 0：否
     * @apiSuccess (响应结果) {String} data.currencyCode 币种代码
     * @apiSuccess (响应结果) {String} data.currencyDesc 币种描述
     * @apiSuccess (响应结果) {String} data.redeemAmt 赎回金额
     * @apiSuccess (响应结果) {String} data.redeemVol 赎回份额
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"ELWAnXtw2P","data":{"supportPrebook":"NqtvzN1Fe7","currencyDesc":"t","redeemMethod":"20hl","redeemAmt":"sZwD1gw","currencyCode":"Nnv2w","redeemVol":"TrMq52XOr"},"description":"taNy3Je","timestampServer":"PISIKW2oGS"}
     */
    @PostMapping("redeemapplyresult")
    public CgiResponse<HKRedeemFundOrderInfoVO> redeemApplyResult(@RequestBody HKRedeemFundOrderRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkRedeemFundService.redeemApplyResult(request));
    }

    /**
     * @api {POST} /ext/hkfund/redeem/can/sell/verify queryCanSell()
     * @apiVersion 1.0.0
     * @apiGroup RedeemFundController
     * @apiName queryCanSell()
     * @apiDescription 是否可卖出校验接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParamExample 请求体示例
     * {"hkCustNo":"DoPfl","fundCode":"ABC123"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.isCanSell 是否可卖出 0-不可卖出 1-可卖出 2-不显示
     * @apiSuccess (响应结果) {String} data.notCanSellCode 不可卖出错误码
     * @apiSuccess (响应结果) {String} data.notCanSellMsg 不可卖出错误信息
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"200","data":{"isCanSell":"1","notCanSellCode":"","notCanSellMsg":""},"description":"成功","timestampServer":"20241201120000"}
     */
    @PostMapping("can/sell/verify")
    public CgiResponse<QueryFundCanSellVO> queryCanSell(@RequestBody QueryFundCanSellRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkRedeemFundService.queryCanSell(request));
    }
}
