package com.howbuy.crm.cgi.extservice.vo.balance;

/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 储蓄罐基金列表接口
 * @author: jinqing.rao
 * @date: 2025/6/24 17:12
 * @since JDK 1.8
 */
@Setter
@Getter
public class PiggyAssetFundListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品名称(简称)
     */
    private String fundName;

    /**
     * 产品代码
     */
    private String fundCode;

    /**
     * 参考市值
     */
    private String currencyMarketValue;


    /**
     * 费后当前收益（当前币种）
     */
    private String currentAssetCurrencyExFee;

    /**
     * 币种描述
     */
    private String currencyDesc;

    /**
     * 参考净值
     */
    private String nav;


    /**
     * 净值日期
     */
    private String navDate;

    /**
     * 收益计算状态
     */
    private String incomeCalStat;

    /**
     * 当前收益率(不含费)
     */
    private String yieldRateExFee;

    /**
     * 展示数据的正负
     */
    private String showDirection;
} 