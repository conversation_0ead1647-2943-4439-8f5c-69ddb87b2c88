package com.howbuy.crm.cgi.extservice.common.enums.product;

import java.util.stream.Stream;

/**
 * @description: 产品业务类型枚举
 * @author: 陈杰文
 * @date: 2025-07-04 18:57:14
 * @since JDK 1.8
 */
public enum ProductBizTypeEnum {

    /**
     * 海外公募
     */
    OVERSEAS_PUBLIC("1", "海外公募"),
    
    /**
     * 海外私募
     */
    OVERSEAS_PRIVATE("2", "海外私募");

    private final String code;
    private final String description;

    /**
     * 根据编码获取枚举对象
     *
     * @param code 编码
     * @return 枚举对象
     */
    public static ProductBizTypeEnum getByCode(String code) {
        return Stream.of(values())
                .filter(type -> type.code.equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(String code) {
        ProductBizTypeEnum typeEnum = getByCode(code);
        return typeEnum == null ? null : typeEnum.getDescription();
    }

    /**
     * 获取编码
     *
     * @return 编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    public String getDescription() {
        return description;
    }

    ProductBizTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
} 