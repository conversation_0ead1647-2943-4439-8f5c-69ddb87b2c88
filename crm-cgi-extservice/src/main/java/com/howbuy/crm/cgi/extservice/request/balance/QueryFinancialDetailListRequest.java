package com.howbuy.crm.cgi.extservice.request.balance;

/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @description: 资金明细查询请求类
 * @author: 陈杰文
 * @date: 2025-06-17 18:07:37
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFinancialDetailListRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 业务类型列表
     */
    @NotEmpty(message = "业务类型列表不能为空")
    private List<String> businessTypeList;

    /**
     * 基金交易账号（默认非全委基金交易账号）
     */
    private String fundTxAcctNo;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    private Integer page;

    /**
     * 页大小
     */
    @NotNull(message = "页大小不能为空")
    private Integer size;
} 