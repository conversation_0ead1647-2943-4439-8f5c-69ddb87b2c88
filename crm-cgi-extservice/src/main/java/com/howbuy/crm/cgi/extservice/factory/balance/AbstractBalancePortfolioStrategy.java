/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.factory.balance;

import com.google.common.collect.Lists;
import com.howbuy.crm.base.YesOrNoEnum;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.TxChannelEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.refund.RefundApplyAuditStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.refund.RefundApplyDataSourceEnum;
import com.howbuy.crm.cgi.extservice.common.enums.refund.RefundApplyOrderSettleStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherAuditStatusEnum;
import com.howbuy.crm.cgi.extservice.common.utils.balance.BalanceUtils;
import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.AllInTransitTradeBalancePortfolioDetail;
import com.howbuy.crm.cgi.extservice.factory.domain.BalancePortfolioDetail;
import com.howbuy.crm.cgi.extservice.factory.domain.PiggyBalancePortfolio;
import com.howbuy.crm.cgi.extservice.service.business.balance.FundTxAcctNoService;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.cash.HkFundTxAcctNoCashBalanceChangeDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.PayVoucherListPageRequestDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.capital.QueryInTransitTradeCapitalRequestDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.capital.QueryInTransitTradeCapitalResponseDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.piggy.PiggyPayVoucherRecordPageDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.AcctBalanceDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.BalanceBeanDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryBalanceDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryFinReceiptDTO;
import com.howbuy.crm.cgi.manager.domain.hkfin.HkFinRefundInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkfin.HkFinRefundInfoRequestDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.PiggyPayVoucherOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.QueryBalanceOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.QueryHkCashBalanceOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.capital.QueryInTransitTradeCapitalOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkfin.QueryHkFinOuterService;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import com.howbuy.dtms.order.client.enums.PayVoucherTypeEnum;
import com.howbuy.paramcenter.serverfacade.dis.queryhbdiscodelist.QueryHbDisCodeListFacade;
import com.howbuy.paramcenter.serverfacade.dis.queryhbdiscodelist.QueryHbDisCodeListRequest;
import com.howbuy.paramcenter.serverfacade.dto.DisDto;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/6/23 19:39
 * @since JDK 1.8
 */
@Slf4j
public abstract class AbstractBalancePortfolioStrategy {

    @Value("${app.cust.asset.voucher.limit}")
    private String voucherLimit;
    @Resource
    protected FundTxAcctNoService fundTxAcctNoService;

    @Resource
    protected QueryFundBasicInfoOuterService queryFundBasicInfoOuterService;

    @Resource
    protected QueryBalanceOuterService queryBalanceOuterService;

    @Resource
    private QueryHkCashBalanceOuterService queryHkCashBalanceOuterService;

    @Resource
    protected QueryInTransitTradeCapitalOuterService queryInTransitTradeCapitalOuterService;

    @DubboReference(registry = "param-server", check = false)
    private QueryHbDisCodeListFacade queryHbDisCodeListFacade;

    @Resource
    private QueryHkFinOuterService queryHkFinOuterService;

    @Resource
    protected PiggyPayVoucherOuterService piggyPayVoucherOuterService;


    protected List<String> getDisCodeList() {
        QueryHbDisCodeListRequest queryHbDisCodeListRequest = new QueryHbDisCodeListRequest();
        com.howbuy.paramcenter.vo.Result<List<DisDto>> listResult = queryHbDisCodeListFacade.execute(queryHbDisCodeListRequest);
        List<DisDto> dtoList = listResult.getData();
        List<String> disCodeList = new ArrayList<>();
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(dtoList)) {
            for (DisDto disDto : dtoList) {
                if (YesOrNoEnum.YES.getCode().equals(disDto.getHowbuyFlag())) {
                    disCodeList.add(disDto.getDisCode());
                }
            }
        } else {
            disCodeList.add(DisCodeEnum.HM.getCode());
        }
        return disCodeList;
    }
    /**
     * @param queryBalanceDTO
     * @param builder
     * @return void
     * @description: 查询用户的在途订单
     * @author: jinqing.rao
     * @date: 2025/7/9 13:43
     * @since JDK 1.8
     */
    protected void buildAllInTransitCountAndDealNo(QueryBalanceDTO queryBalanceDTO, AllInTransitTradeBalancePortfolioDetail.AllInTransitTradeBalancePortfolioDetailBuilder builder) {
        // 查询在途数据
        QueryFinReceiptDTO queryFinReceiptDTO = queryBalanceOuterService.queryInTransitTradeBalance(queryBalanceDTO);

        // 在途交易相关数据
        List<String> allInTransitList = mergeUnpaidAndUnconfirmed(queryFinReceiptDTO);
        // 获取在途买入个数
        int inTransitCount = allInTransitList.size();
        // 单笔数据，返回对应的单号
        String inTransitDealNo = getSingleDealNoIfOnlyOne(allInTransitList);
        builder.inTransitTradeCount(String.valueOf(inTransitCount));
        builder.inTransitTradeDealNo(inTransitDealNo);
    }

    /**
     * @param requestDTO
     * @param hkFundTxAcctNoList
     * @param builder
     * @return void
     * @description: 在途提款申请个数信息
     * @author: jinqing.rao
     * @date: 2025/7/9 13:58
     * @since JDK 1.8
     */
    protected void buildHkFinRefundCountAndDealNo(HkFinRefundInfoRequestDTO requestDTO, List<String> hkFundTxAcctNoList, AllInTransitTradeBalancePortfolioDetail.AllInTransitTradeBalancePortfolioDetailBuilder builder) {
        // 获取数据
        List<HkFinRefundInfoDTO> hkFinRefundInfoDTOS = queryHkFinOuterService.queryRefundApplyOrderList(requestDTO);
        if (CollectionUtils.isEmpty(hkFinRefundInfoDTOS)) {
            return;
        }

        // 在途提款申请个数和单号
        Result inTransitDrawMoneyCountAndDealNoResult = getInTransitDrawMoneyCountAndDealNo(hkFinRefundInfoDTOS);
        builder.inTransitDrawMoneyAppCount(StringUtils.isEmpty(inTransitDrawMoneyCountAndDealNoResult.inTransitDrawMoneyAppCount) ? Constants.CONSTANT_ZERO : inTransitDrawMoneyCountAndDealNoResult.inTransitDrawMoneyAppCount);
        builder.inTransitDrawMoneyAppNo(inTransitDrawMoneyCountAndDealNoResult.inTransitDrawMoneyAppNo);

        // 在途提款资金个数和单号
        Result inTransitDrawMoneyCapitalCountAndDealNoResult = getInTransitDrawMoneyCapitalCountAndDealNoResult(hkFinRefundInfoDTOS);
        builder.inTransitDrawMoneyCapitalCount(StringUtils.isEmpty(inTransitDrawMoneyCapitalCountAndDealNoResult.inTransitDrawMoneyCapitalCount) ? Constants.CONSTANT_ZERO : inTransitDrawMoneyCapitalCountAndDealNoResult.inTransitDrawMoneyCapitalCount);
        builder.inTransitDrawMoneyCapitalNo(inTransitDrawMoneyCapitalCountAndDealNoResult.inTransitDrawMoneyCapitalNo);
    }

    /**
     * @param requestDTO
     * @param builder
     * @return void
     * @description: 构建现金余额在途提款申请信息
     * <AUTHOR>
     * @date 2025-07-10 14:13:43
     */
    protected void buildCashBalanceHkFinRefundCountAndDealNo(HkFinRefundInfoRequestDTO requestDTO, AllInTransitTradeBalancePortfolioDetail.AllInTransitTradeBalancePortfolioDetailBuilder builder) {

        // 获取数据
        List<HkFinRefundInfoDTO> hkFinRefundInfoDTOS = queryHkFinOuterService.queryRefundApplyOrderList(requestDTO);
        if (CollectionUtils.isEmpty(hkFinRefundInfoDTOS)) {
            return;
        }

        // 在途提款申请个数和单号
        Result inTransitDrawMoneyCountAndDealNoResult = getInTransitDrawMoneyCountAndDealNo(hkFinRefundInfoDTOS);
        builder.inTransitDrawMoneyAppCount(StringUtils.isEmpty(inTransitDrawMoneyCountAndDealNoResult.inTransitDrawMoneyAppCount) ? Constants.CONSTANT_ZERO : inTransitDrawMoneyCountAndDealNoResult.inTransitDrawMoneyAppCount);
        builder.inTransitDrawMoneyAppNo(inTransitDrawMoneyCountAndDealNoResult.inTransitDrawMoneyAppNo);

        // 在途提款资金个数和单号
        Result inTransitDrawMoneyCapitalCountAndDealNoResult = getInTransitDrawMoneyCapitalCountAndDealNoResult(hkFinRefundInfoDTOS);
        builder.inTransitDrawMoneyCapitalCount(StringUtils.isEmpty(inTransitDrawMoneyCapitalCountAndDealNoResult.inTransitDrawMoneyCapitalCount) ? Constants.CONSTANT_ZERO : inTransitDrawMoneyCapitalCountAndDealNoResult.inTransitDrawMoneyCapitalCount);
        builder.inTransitDrawMoneyCapitalNo(inTransitDrawMoneyCapitalCountAndDealNoResult.inTransitDrawMoneyCapitalNo);
    }

    /**
     * @param content
     * @return void
     * @description: 获取现金存入在途打款凭证列表
     * <AUTHOR>
     * @date 2025-07-10 14:13:43
     */
    protected void buildCashAccountPayVoucherList(BalanceContent content, AllInTransitTradeBalancePortfolioDetail.AllInTransitTradeBalancePortfolioDetailBuilder builder) {
        PayVoucherListPageRequestDTO payVoucherListPageRequestDTO = new PayVoucherListPageRequestDTO();
        // APP渠道 H5渠道 小程渠道(已经下线)
        List<String> tradeChannelList = Arrays.asList(TxChannelEnum.WAP.getCode(), TxChannelEnum.HK_APP.getCode(), TxChannelEnum.H5.getCode());
        // 审核状态 2-等待复核
        List<String> auditStatusList = Lists.newArrayList(PayVoucherAuditStatusEnum.WAIT_REVIEW.getCode());
        // 凭证类型 2-存入现金账户凭证
        List<String> voucherTypeList = Lists.newArrayList(PayVoucherTypeEnum.CASH_ACCOUNT.getCode());

        payVoucherListPageRequestDTO.setHkCustNo(content.getHkCustNo());
        payVoucherListPageRequestDTO.setAuditStatusList(auditStatusList);
        payVoucherListPageRequestDTO.setTradeChannelList(tradeChannelList);
        payVoucherListPageRequestDTO.setVoucherTypeList(voucherTypeList);

        // 固定查2条
        PiggyPayVoucherRecordPageDTO piggyPayVoucherRecordPageDTO = piggyPayVoucherOuterService.queryPiggyDepositVoucherRecordList(payVoucherListPageRequestDTO, 1, 2);
        List<PiggyPayVoucherRecordPageDTO.PiggyPayVoucherRecordListDTO> piggyPayVoucherRecordList = piggyPayVoucherRecordPageDTO.getPiggyPayVoucherRecordListDTO();
        String inTransitPayVoucherNo = CollectionUtils.isNotEmpty(piggyPayVoucherRecordList) && piggyPayVoucherRecordList.size() == 1 ? piggyPayVoucherRecordList.get(0).getVoucherNo() : null;
        builder.inTransitPayVoucherNo(inTransitPayVoucherNo);
        builder.inTransitPayVoucherCount(piggyPayVoucherRecordPageDTO.getTotal());
    }

    /**
     * @param hkFinRefundInfoDTOS
     * @return void
     * @description: 在途提款资金个数和单号
     * @author: 陈杰文
     * @date: 2025-07-10 14:21:43
     * @since JDK 1.8
     */
    private static Result getInTransitDrawMoneyCapitalCountAndDealNoResult(List<HkFinRefundInfoDTO> hkFinRefundInfoDTOS) {
        Result result = new Result();
        // 审核不通过的订单
        List<String> orderPayStatusList = Arrays.asList(RefundApplyOrderSettleStatusEnum.WAIT_CLEAR.getCode(),
                RefundApplyOrderSettleStatusEnum.CLEARING.getCode(),
                RefundApplyOrderSettleStatusEnum.CLEAR_FAIL.getCode());

        // 在途提款资金个数
        List<HkFinRefundInfoDTO> auditPassOrderList = hkFinRefundInfoDTOS.stream()
                .filter(hkFinRefundInfoDTO -> RefundApplyAuditStatusEnum.NORMAL.getCode().equals(hkFinRefundInfoDTO.getRecStat()))
                .filter(hkFinRefundInfoDTO -> RefundApplyDataSourceEnum.ONLINE.getCode().equals(hkFinRefundInfoDTO.getDataSource()))
                .filter(hkFinRefundInfoDTO -> {
                    if(orderPayStatusList.contains(hkFinRefundInfoDTO.getSettleStatus())){
                        return true;
                    }
                    // 划款成功的且 划款时间 是当天的
                    boolean successStatus = RefundApplyOrderSettleStatusEnum.CLEAR_SUCCESS.getCode().equals(hkFinRefundInfoDTO.getSettleStatus());
                    int compareDate = DateUtils.compareDate(hkFinRefundInfoDTO.getSettleDt(), DateUtils.getCurrentDate(DateUtils.YYYYMMDD), DateUtils.YYYYMMDD);
                    return successStatus && compareDate == Constants.NUMBER_ZERO;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(auditPassOrderList)) {
            return result;
        }
        String inTransitDrawMoneyCapitalCount = String.valueOf(auditPassOrderList.size());
        String inTransitDrawMoneyCapitalNo = null;
        if (auditPassOrderList.size() == 1) {
            inTransitDrawMoneyCapitalNo = auditPassOrderList.get(Constants.NUMBER_ZERO).getApplyId();
        }

        result.setInTransitDrawMoneyCapitalCount(inTransitDrawMoneyCapitalCount);
        result.setInTransitDrawMoneyCapitalNo(inTransitDrawMoneyCapitalNo);
        return result;
    }

    @Getter
    @Setter
    public static class Result {
        public String inTransitDrawMoneyCapitalCount;
        public String inTransitDrawMoneyCapitalNo;
        public String inTransitDrawMoneyAppCount;
        public String inTransitDrawMoneyAppNo;
    }

    /**
     * @param hkFinRefundInfoDTOS
     * @return void
     * @description: 获取在途提款申请信息
     * @author: 陈杰文
     * @date: 2025-07-10 14:21:43
     * @since JDK 1.8
     */
    private static Result getInTransitDrawMoneyCountAndDealNo(List<HkFinRefundInfoDTO> hkFinRefundInfoDTOS) {
        Result result = new Result();
        List<HkFinRefundInfoDTO> refundApplyOrderList = hkFinRefundInfoDTOS.stream()
                .filter(hkFinRefundInfoDTO -> RefundApplyAuditStatusEnum.UNAUDITED.getCode().equals(hkFinRefundInfoDTO.getRecStat()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refundApplyOrderList)) {
            return result;
        }
        String inTransitDrawMoneyAppCount = String.valueOf(refundApplyOrderList.size());
        String inTransitDrawMoneyAppNo = null;
        if (refundApplyOrderList.size() == Constants.NUMBER_ONE) {
            inTransitDrawMoneyAppNo = refundApplyOrderList.get(Constants.NUMBER_ZERO).getApplyId();
        }

        result.setInTransitDrawMoneyAppCount(inTransitDrawMoneyAppCount);
        result.setInTransitDrawMoneyAppNo(inTransitDrawMoneyAppNo);
        return result;
    }

    /**
     * @param voucherRecord 请求参数
     * @param builder       构造器
     * @return void
     * @description: 构建打款凭证的请求参数
     * @author: jinqing.rao
     * @date: 2025/7/9 13:46
     * @since JDK 1.8
     */
    protected void buildPiggyPayVoucherCountAndDealNo(PiggyPayVoucherRecordPageDTO voucherRecord, AllInTransitTradeBalancePortfolioDetail.AllInTransitTradeBalancePortfolioDetailBuilder builder) {
        // 打款凭证数量
        long underReviewCount = countUnderReviewVouchers(voucherRecord);
        // 单个打款凭证时，需要返回打款凭证号,跳转详情
        String voucherNo = getSingleVoucherNoIfOnlyOne(voucherRecord, underReviewCount);

        builder.inTransitCashPayVoucherLimit(underReviewCount > Long.parseLong(voucherLimit) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        builder.inTransitPayVoucherCount(String.valueOf(underReviewCount));
        builder.inTransitPayVoucherNo(voucherNo);
    }

    /**
     * @param content 请求上下文
     * @return java.util.List<java.lang.String>
     * @description: 获取基金交易账号
     * @author: jinqing.rao
     * @date: 2025/7/9 13:42
     * @since JDK 1.8
     */
    protected static List<String> getHkFundTxAcctNoList(BalanceContent content) {
        List<HkFundTxAcctDTO> fundTxAcctNoDTOList = content.getFundTxAcctNoDTOList();
        return fundTxAcctNoDTOList.stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()))
                .map(HkFundTxAcctDTO::getFundTxAcctNo)
                .collect(Collectors.toList());
    }

    /**
     * @param allInTransitList
     * @return java.lang.String
     * @description: 只有一条数据时，获取订单号
     * @author: jinqing.rao
     * @date: 2025/6/24 12:57
     * @since JDK 1.8
     */
    private String getSingleDealNoIfOnlyOne(List<String> allInTransitList) {
        return allInTransitList.size() == Constants.NUMBER_ONE ? allInTransitList.get(Constants.NUMBER_ZERO) : null;
    }

    /**
     * @param dto
     * @return java.util.List<java.lang.String>
     * @description: 合并未确认的数据
     * @author: jinqing.rao
     * @date: 2025/6/24 12:56
     * @since JDK 1.8
     */
    private List<String> mergeUnpaidAndUnconfirmed(QueryFinReceiptDTO dto) {
        List<String> unpaid = Optional.ofNullable(dto.getUnpaidList()).orElse(Collections.emptyList());
        List<String> unconfirmed = Optional.ofNullable(dto.getUnconfirmedList()).orElse(Collections.emptyList());

        List<String> merged = new ArrayList<>(unpaid);
        merged.addAll(unconfirmed);
        return merged;
    }

    /**
     * @param requestDTO
     * @param builder
     * @return void
     * @description: 在途资金信息
     * @author: jinqing.rao
     * @date: 2025/7/9 13:59
     * @since JDK 1.8
     */
    protected void buildInTransitTradeCapitalCountAndDealNo(QueryInTransitTradeCapitalRequestDTO requestDTO, AllInTransitTradeBalancePortfolioDetail.AllInTransitTradeBalancePortfolioDetailBuilder builder) {

        QueryInTransitTradeCapitalResponseDTO queryInTransitTradeCapitalResponseDTO = queryInTransitTradeCapitalOuterService.queryInTransitTradeCapital(requestDTO);
        if (null == queryInTransitTradeCapitalResponseDTO) {
            return;
        }
        if (CollectionUtils.isEmpty(queryInTransitTradeCapitalResponseDTO.getInTransitTradeCapitalList())) {
            return;
        }
        // 在途交易资金笔数
        String inTransitTradeCapitalCount = String.valueOf(queryInTransitTradeCapitalResponseDTO.getInTransitTradeCapitalList().size());
        // 在途交易资金订单号
        String inTransitTradeCapitalDealNo = null;
        if (queryInTransitTradeCapitalResponseDTO.getInTransitTradeCapitalList().size() == Constants.NUMBER_ONE) {
            inTransitTradeCapitalDealNo = queryInTransitTradeCapitalResponseDTO.getInTransitTradeCapitalList().get(Constants.NUMBER_ZERO).getDealNo();
        }
        builder.inTransitTradeCapitalCount(StringUtils.isEmpty(inTransitTradeCapitalCount) ? Constants.CONSTANT_ZERO : inTransitTradeCapitalCount);
        builder.inTransitTradeCapitalDealNo(inTransitTradeCapitalDealNo);
    }

    /**
     * @param currency
     * @param hkCustNo
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.cash.HkCashBalanceChangeDTO
     * @description: 查询用户的现金余额
     * @author: jinqing.rao
     * @date: 2025/6/24 9:24
     * @since JDK 1.8
     */
    protected HkFundTxAcctNoCashBalanceChangeDTO getHkCustNoCashBalancePair(String disCurrency, String hkCustNo, List<String> allFundTxAcctNoList) {
        //查询香港账户余额
        return queryHkCashBalanceOuterService.queryHkCashBalanceByFundTxAcctNo(hkCustNo, disCurrency, allFundTxAcctNoList);
    }

    /**
     * @param content 上下文
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryBalanceDTO
     * @description: 构建非全委的储蓄罐查询条件
     * @author: jinqing.rao
     * @date: 2025/6/25 16:51
     * @since JDK 1.8
     */
    protected QueryBalanceDTO buildNonFullPiggyQueryBalanceDTO(BalanceContent content) {
        if (CollectionUtils.isEmpty(content.getFundTxAcctNoDTOList())) {
            throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "没有非全委的基金交易账号");
        }
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = content.getFundTxAcctNoDTOList().stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.NON_FULL.getCode().equals(m.getFundTxAccType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hkFundTxAcctDTOS)) {
            throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "没有非全委的基金交易账号");
        }

        List<String> nonFullFundTxAcctNoList = BalanceUtils.extractFieldList(hkFundTxAcctDTOS, HkFundTxAcctDTO::getFundTxAcctNo);

        // 获取储蓄罐基金
        List<FundBasicInfoDTO> fundBasicInfoDTOList = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList();
        List<String> piggyFundCodeList = BalanceUtils.extractFieldList(fundBasicInfoDTOList, FundBasicInfoDTO::getFundCode);

        // 查询持仓信息接口,基金交易账号类型、是否储蓄罐统计基金、储蓄罐、全委三个维度的资产、收益、收益计算状态
        QueryBalanceDTO queryBalanceDTO = new QueryBalanceDTO();
        queryBalanceDTO.setHkCustNo(content.getHkCustNo());
        queryBalanceDTO.setFundCodeList(piggyFundCodeList);
        queryBalanceDTO.setFundTxAcctNoList(nonFullFundTxAcctNoList);
        queryBalanceDTO.setDisPlayCurrency(content.getDisPlayCurrencyCode());
        return queryBalanceDTO;
    }


    /**
     * @param content
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryBalanceDTO
     * @description: 查询所有在途
     * @author: jinqing.rao
     * @date: 2025/7/8 13:03
     * @since JDK 1.8
     */
    protected QueryBalanceDTO buildAllInComeQueryBalanceDTO(BalanceContent content) {
        QueryBalanceDTO queryBalanceDTO = new QueryBalanceDTO();
        queryBalanceDTO.setHkCustNo(content.getHkCustNo());
        queryBalanceDTO.setDisPlayCurrency(content.getDisPlayCurrencyCode());
        return queryBalanceDTO;
    }

    /**
     * @param acctBalanceDTO
     * @return com.howbuy.crm.cgi.extservice.factory.domain.PiggyBalancePortfolio
     * @description: 构建储蓄罐的持仓数据信息
     * @author: jinqing.rao
     * @date: 2025/6/25 16:39
     * @since JDK 1.8
     */
    protected static PiggyBalancePortfolio getPiggyBalancePortfolio(AcctBalanceDTO acctBalanceDTO) {
        List<BalanceBeanDTO> allBalanceList = acctBalanceDTO.getBalanceList();
        if (CollectionUtils.isEmpty(allBalanceList)) {
            log.info("没有持仓订单明细记录,储蓄罐资产紫策略不执行，返回空数据");
            return PiggyBalancePortfolio.builder().totalAsset(BigDecimal.ZERO).totalIncome(BigDecimal.ZERO).itemList(new ArrayList<>()).build();
        }
        // 筛选是储蓄罐的订单明细
        List<BalanceBeanDTO> piggyProductList = allBalanceList.stream().filter(balance -> YesNoEnum.YES.getCode().equals(balance.getSfhwcxg())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(piggyProductList)) {
            log.info("没有储蓄罐的订单明细");
            return PiggyBalancePortfolio.builder().totalAsset(BigDecimal.ZERO).totalIncome(BigDecimal.ZERO).itemList(new ArrayList<>()).build();
        }
        // 计算总金额
        BigDecimal cxgTotalAsset = BalanceUtils.sumBigDecimal(piggyProductList, BalanceBeanDTO::getDisCurMarketValue);

        // 计算总收益
        BigDecimal cxgTotalIncome = BalanceUtils.sumBigDecimal(piggyProductList, BalanceBeanDTO::getDisCurCurrentAssetCurrency);

        // 收益状态
        String incomeCalStat = BalanceUtils.calculateIncomeCalStat(piggyProductList);

        return PiggyBalancePortfolio.builder()
                .totalAsset(cxgTotalAsset)
                .totalIncome(cxgTotalIncome)
                .incomeStatus(incomeCalStat)
                .itemList(piggyProductList)
                .build();
    }


    /**
     * @param content
     * @param acctBalanceDTO
     * @return com.howbuy.crm.cgi.extservice.factory.domain.BalancePortfolioDetail
     * @description: 非全委且非海外储蓄罐 持仓数据封装
     * @author: jinqing.rao
     * @date: 2025/6/25 17:36
     * @since JDK 1.8
     */
    public BalancePortfolioDetail getFundBalancePortfolio(BalanceContent content, AcctBalanceDTO acctBalanceDTO) {
        // 获取非全委的基金交易账号
        List<String> fundTxAcctNoList = fundTxAcctNoService.getNonFullFundTxAcctNoList(content.getHkCustNo());
        List<BalanceBeanDTO> allBalanceList = acctBalanceDTO.getBalanceList();
        if (CollectionUtils.isEmpty(allBalanceList)) {
            log.info("getFundBalancePortfolio >>>> 没有持仓订单明细记录,基金持仓策略不执行，返回空数据");
            return BalancePortfolioDetail.builder().totalAsset(BigDecimal.ZERO).totalIncome(BigDecimal.ZERO).itemList(new ArrayList<>()).build();
        }
        // 非全委基金交易账号  且 非海外储蓄罐产品
        List<BalanceBeanDTO> piggyProductList = allBalanceList.stream().filter(balance -> fundTxAcctNoList.contains(balance.getFundTxAcctNo()) && !YesNoEnum.YES.getCode().equals(balance.getSfhwcxg())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(piggyProductList)) {
            log.info("getFundBalancePortfolio >>>> 没有非全委基金交易账号  且 非海外储蓄罐产品持仓记录信息 ");
            return BalancePortfolioDetail.builder().totalAsset(BigDecimal.ZERO).totalIncome(BigDecimal.ZERO).itemList(new ArrayList<>()).build();
        }
        // 计算总金额
        BigDecimal cxgTotalAsset = BalanceUtils.sumBigDecimal(piggyProductList, BalanceBeanDTO::getDisCurMarketValue);

        // 计算总收益
        BigDecimal cxgTotalIncome = BalanceUtils.sumBigDecimal(piggyProductList, BalanceBeanDTO::getDisCurCurrentAssetCurrency);

        // 收益状态
        String incomeCalStat = BalanceUtils.calculateIncomeCalStat(piggyProductList);

        return BalancePortfolioDetail.builder()
                .totalAsset(cxgTotalAsset)
                .totalIncome(cxgTotalIncome)
                .incomeStatus(incomeCalStat)
                .itemList(piggyProductList)
                .build();
    }

    /**
     * @param content
     * @param acctBalanceDTO
     * @return com.howbuy.crm.cgi.extservice.factory.domain.BalancePortfolioDetail
     * @description: 封装全委的持仓信息
     * @author: jinqing.rao
     * @date: 2025/6/25 17:41
     * @since JDK 1.8
     */
    public BalancePortfolioDetail getFullPiggyBalancePortfolio(BalanceContent content, AcctBalanceDTO acctBalanceDTO) {

        // 获取非全委的基金交易账号
        List<String> fundTxAcctNoList = fundTxAcctNoService.getFullFundTxAcctNoList(content.getHkCustNo());

        List<BalanceBeanDTO> allBalanceList = acctBalanceDTO.getBalanceList();
        if (CollectionUtils.isEmpty(allBalanceList)) {
            log.info("getFundBalancePortfolio >>>> 没有持仓订单明细记录,基金持仓策略不执行，返回空数据");
            return BalancePortfolioDetail.builder().totalAsset(BigDecimal.ZERO).totalIncome(BigDecimal.ZERO).itemList(new ArrayList<>()).build();
        }
        // 非全委基金交易账号  且 非海外储蓄罐产品
        List<BalanceBeanDTO> piggyProductList = allBalanceList.stream().filter(balance -> fundTxAcctNoList.contains(balance.getFundTxAcctNo()) && !YesNoEnum.YES.getCode().equals(balance.getSfhwcxg())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(piggyProductList)) {
            log.info("getFundBalancePortfolio >>>> 没有非全委基金交易账号  且 非海外储蓄罐产品持仓记录信息 ");
            return BalancePortfolioDetail.builder().totalAsset(BigDecimal.ZERO).totalIncome(BigDecimal.ZERO).itemList(new ArrayList<>()).build();
        }
        // 计算总金额
        BigDecimal cxgTotalAsset = BalanceUtils.sumBigDecimal(piggyProductList, BalanceBeanDTO::getDisCurMarketValue);

        // 计算总收益
        BigDecimal cxgTotalIncome = BalanceUtils.sumBigDecimal(piggyProductList, BalanceBeanDTO::getDisCurCurrentAssetCurrency);

        // 收益状态
        String incomeCalStat = BalanceUtils.calculateIncomeCalStat(piggyProductList);

        return BalancePortfolioDetail.builder()
                .totalAsset(cxgTotalAsset)
                .totalIncome(cxgTotalIncome)
                .incomeStatus(incomeCalStat)
                .itemList(piggyProductList)
                .build();
    }

    /**
     * @param recordPageDTO
     * @return long
     * @description: 统计待审核打款凭证
     * @author: jinqing.rao
     * @date: 2025/6/24 12:58
     * @since JDK 1.8
     */
    private long countUnderReviewVouchers(PiggyPayVoucherRecordPageDTO recordPageDTO) {
        if (recordPageDTO == null || org.apache.commons.collections.CollectionUtils.isEmpty(recordPageDTO.getPiggyPayVoucherRecordListDTO())) {
            return Constants.NUMBER_ZERO;
        }
        return recordPageDTO.getPiggyPayVoucherRecordListDTO().stream()
                .filter(this::underReview)
                .count();
    }

    /**
     * @param recordPageDTO
     * @param underReviewCount
     * @return java.lang.String
     * @description: 单个单款凭证获取打款凭证号
     * @author: jinqing.rao
     * @date: 2025/6/24 12:58
     * @since JDK 1.8
     */
    private String getSingleVoucherNoIfOnlyOne(PiggyPayVoucherRecordPageDTO recordPageDTO, long underReviewCount) {
        if (underReviewCount != Constants.NUMBER_ONE || recordPageDTO == null) {
            return null;
        }
        return recordPageDTO.getPiggyPayVoucherRecordListDTO().stream()
                .filter(this::underReview)
                .findFirst()
                .map(PiggyPayVoucherRecordPageDTO.PiggyPayVoucherRecordListDTO::getVoucherNo)
                .orElse(null);
    }

    /**
     * @param f
     * @return boolean
     * @description:(请在此添加描述)
     * @author: jinqing.rao
     * @date: 2025/6/24 12:58
     * @since JDK 1.8
     */
    private boolean underReview(PiggyPayVoucherRecordPageDTO.PiggyPayVoucherRecordListDTO f) {
        return PayVoucherAuditStatusEnum.WAIT_REVIEW.getCode().equals(f.getAuditStatus());
    }


    /**
     * @param hkCustNo         香港客户号
     * @param tradeChannelList 渠道List
     * @param voucherType      打款凭证类别
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.response.piggy.PiggyPayVoucherRecordPageDTO
     * @description: 查询打款凭证状态
     * @author: jinqing.rao
     * @date: 2025/6/24 12:57
     * @since JDK 1.8
     */
    protected PiggyPayVoucherRecordPageDTO queryPiggyPayVoucherRecord(String hkCustNo,
                                                                    List<String> tradeChannelList,
                                                                    List<String> voucherType) {
        return piggyPayVoucherOuterService.queryPiggyDepositVoucherRecordList(
                hkCustNo, voucherType, null, tradeChannelList, 1, 500);
    }
}
