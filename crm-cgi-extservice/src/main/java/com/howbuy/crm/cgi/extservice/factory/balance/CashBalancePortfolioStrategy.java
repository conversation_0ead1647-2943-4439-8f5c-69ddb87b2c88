package com.howbuy.crm.cgi.extservice.factory.balance;

import com.howbuy.crm.cgi.extservice.common.annotation.FundTypeSupport;
import com.howbuy.crm.cgi.extservice.common.enums.balance.HkCustBalanceDisPalyCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.utils.balance.BalanceUtils;
import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.CashBalancePortfolioDetail;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.cash.HkFundTxAcctNoCashBalanceChangeDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.CurrencyBalanceDetailDTO;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 现金余额资产查询策略类（全委+非全委）
 * @author: 陈杰文
 * @date: 2025-07-03 13:11:28
 * @since JDK 1.8
 */
@Component
@Slf4j
@FundTypeSupport(HkCustBalanceDisPalyCategoryEnum.CASH_BALANCE)
public class CashBalancePortfolioStrategy extends AbstractBalancePortfolioStrategy implements BalancePortfolioStrategy<CashBalancePortfolioDetail> {

    @Override
    public Class<CashBalancePortfolioDetail> getSupportedType() {
        return CashBalancePortfolioDetail.class;
    }

    @Override
    public CashBalancePortfolioDetail calculate(BalanceContent content) {
        CashBalancePortfolioDetail.CashBalancePortfolioDetailBuilder builder = CashBalancePortfolioDetail.builder();

        // 查询基金交易账号（全委+非全委）
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = content.getFundTxAcctNoDTOList();
        List<String> allFundTxAcctNoList = BalanceUtils.extractFieldList(hkFundTxAcctDTOS, HkFundTxAcctDTO::getFundTxAcctNo);

        // 现金余额资产
        HkFundTxAcctNoCashBalanceChangeDTO hkCashBalanceChangeDTO = getHkCustNoCashBalancePair(content.getDisPlayCurrencyCode(), content.getHkCustNo(), allFundTxAcctNoList);
        // 全委 + 非全委 总资产&冻结余额&可用余额
        final BigDecimal[] totalFullAsset = {BigDecimal.ZERO};
        final BigDecimal[] totalFullFrozenAmt = {BigDecimal.ZERO};
        final BigDecimal[] totalFullAvailableBalance = {BigDecimal.ZERO};
        final BigDecimal[] nonFullTotalAsset = {BigDecimal.ZERO};
        final BigDecimal[] nonFullTotalFrozenAmt = {BigDecimal.ZERO};
        final BigDecimal[] nonFullTotalAvailableBalance = {BigDecimal.ZERO};
        final List<HkFundTxAcctNoCashBalanceChangeDTO.CurrencyBalanceDTO> fullCurrencyBalanceList = new ArrayList<>();
        final List<HkFundTxAcctNoCashBalanceChangeDTO.CurrencyBalanceDTO> nonFullCurrencyBalanceList = new ArrayList<>();
        // 根据基金交易账号类型分组
        Map<String, List<HkFundTxAcctDTO>> fundTxAccTypeMap = hkFundTxAcctDTOS.stream()
                .collect(Collectors.groupingBy(HkFundTxAcctDTO::getFundTxAccType));
        // 全委基金交易账号列表
        List<String> fullFundTxAcctDTOList = fundTxAccTypeMap.getOrDefault(FundTxAcctTypeEnum.FULL.getCode(), new ArrayList<>()).stream()
                .map(HkFundTxAcctDTO::getFundTxAcctNo)
                .collect(Collectors.toList());
        // 非全委基金交易账号列表
        List<String> nonFullFundTxAcctDTOList = fundTxAccTypeMap.getOrDefault(FundTxAcctTypeEnum.NON_FULL.getCode(), new ArrayList<>()).stream()
                .map(HkFundTxAcctDTO::getFundTxAcctNo)
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(hkCashBalanceChangeDTO.getFundTxAcctBalanceList())){
            return builder.build();
        }
        hkCashBalanceChangeDTO.getFundTxAcctBalanceList().forEach(item -> {
            // 基金交易账号下的币种余额明细列表
            List<HkFundTxAcctNoCashBalanceChangeDTO.CurrencyBalanceDTO> currencyBalanceList = item.getCurrencyBalanceList();
            if (fullFundTxAcctDTOList.contains(item.getFundTxAcctNo())) {
                totalFullAsset[0] = totalFullAsset[0].add(item.getExchangeTotalCashBalance());
                totalFullFrozenAmt[0] = totalFullFrozenAmt[0].add(item.getExchangeTotalFrozenAmt());
                totalFullAvailableBalance[0] = totalFullAvailableBalance[0].add(item.getExchangeTotalAvailableBalance());
                fullCurrencyBalanceList.addAll(currencyBalanceList);
            }
            if (nonFullFundTxAcctDTOList.contains(item.getFundTxAcctNo())) {
                nonFullTotalAsset[0] = nonFullTotalAsset[0].add(item.getExchangeTotalCashBalance());
                nonFullTotalFrozenAmt[0] = nonFullTotalFrozenAmt[0].add(item.getExchangeTotalFrozenAmt());
                nonFullTotalAvailableBalance[0] = nonFullTotalAvailableBalance[0].add(item.getExchangeTotalAvailableBalance());
                nonFullCurrencyBalanceList.addAll(currencyBalanceList);
            }
        });

        // 获取全委 & 非全委基金交易账户下的币种余额列表
        List<CurrencyBalanceDetailDTO> fullCurrencyBalanceDetailDTOS = fullCurrencyBalanceList.stream()
                .map(this::convertCurrencyBalanceDTO)
                .collect(Collectors.toList());
        List<CurrencyBalanceDetailDTO> nonFullCurrencyBalanceDetailDTOS = nonFullCurrencyBalanceList.stream()
                .map(this::convertCurrencyBalanceDTO)
                .collect(Collectors.toList());

        return builder
                .totalAsset(hkCashBalanceChangeDTO.getTotalExchangeCashBalance())
                .totalFullAsset(totalFullAsset[0])
                .fullTotalFrozenAmt(totalFullFrozenAmt[0])
                .fullTotalAvailableBalance(totalFullAvailableBalance[0])
                .totalNonFullAsset(nonFullTotalAsset[0])
                .nonFullTotalFrozenAmt(nonFullTotalFrozenAmt[0])
                .nonFullTotalAvailableBalance(nonFullTotalAvailableBalance[0])
                .fullCurrencyBalanceList(fullCurrencyBalanceDetailDTOS)
                .nonFullCurrencyBalanceList(nonFullCurrencyBalanceDetailDTOS)
                .build();
    }

    /**
     * 币种余额明细转换
     *
     * @param item
     * @return
     */
    private CurrencyBalanceDetailDTO convertCurrencyBalanceDTO(HkFundTxAcctNoCashBalanceChangeDTO.CurrencyBalanceDTO item) {
        CurrencyBalanceDetailDTO currencyBalanceDetailDTO = new CurrencyBalanceDetailDTO();
        currencyBalanceDetailDTO.setCurCode(item.getCurCode());
        currencyBalanceDetailDTO.setFundTxAcctNo(item.getFundTxAcctNo());
        currencyBalanceDetailDTO.setInTransitAmt(item.getInTransitAmt());
        currencyBalanceDetailDTO.setCashBalance(item.getCashBalance());
        currencyBalanceDetailDTO.setFrozenAmt(item.getFrozenAmt());
        currencyBalanceDetailDTO.setAvailableBalance(item.getAvailableBalance());
        currencyBalanceDetailDTO.setCurrencyRate(item.getCurrencyRate());
        currencyBalanceDetailDTO.setExchangeCurrency(item.getExchangeCurrency());
        currencyBalanceDetailDTO.setExchangeCashBalance(item.getExchangeCashBalance());
        currencyBalanceDetailDTO.setExchangeFrozenAmt(item.getExchangeFrozenAmt());
        currencyBalanceDetailDTO.setExchangeAvailableBalance(item.getExchangeAvailableBalance());
        return currencyBalanceDetailDTO;
    }

}