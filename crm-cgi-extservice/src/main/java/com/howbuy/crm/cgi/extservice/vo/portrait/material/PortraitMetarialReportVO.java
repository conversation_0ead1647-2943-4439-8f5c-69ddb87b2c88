package com.howbuy.crm.cgi.extservice.vo.portrait.material;

import com.howbuy.crm.cgi.common.base.Body;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 客户画像素材产品报告搜索结果VO
 * @Date 2024/9/2 18:34
 */
public class PortraitMetarialReportVO extends Body implements Serializable {

    /**
     * 总数量
     */
    private String total;

    /**
     * 产品报告列表
     */
    private List<MetarialReport> dataList;

    public static class MetarialReport {

        /**
         * 报告id
         */
        private String materialId;

        /**
         * 报告日期
         */
        private String reportDate;

        /**
         * 报告标题
         */
        private String title;

        /**
         * 发送次数
         */
        private String sendNum;

        /**
         * 报告url
         */
        private String redirectUrl;

        /**
         * 报告类型
         */
        private String reportType;

        /**
         * 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
         */
        private String materialSendType;

        /**
         * 报告类型名称
         */
        private String reportTypeName;

        /**
         * 报告来源(1:CMS报告；2:参数中心报告)
         */
        private String reportSource;

        public String getReportDate() {
            return reportDate;
        }

        public void setReportDate(String reportDate) {
            this.reportDate = reportDate;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getSendNum() {
            return sendNum;
        }

        public void setSendNum(String sendNum) {
            this.sendNum = sendNum;
        }

        public String getRedirectUrl() {
            return redirectUrl;
        }

        public void setRedirectUrl(String redirectUrl) {
            this.redirectUrl = redirectUrl;
        }

        public String getReportType() {
            return reportType;
        }

        public void setReportType(String reportType) {
            this.reportType = reportType;
        }

        public String getReportTypeName() {
            return reportTypeName;
        }

        public void setReportTypeName(String reportTypeName) {
            this.reportTypeName = reportTypeName;
        }

        public String getMaterialId() {
            return materialId;
        }

        public void setMaterialId(String materialId) {
            this.materialId = materialId;
        }

        public String getMaterialSendType() {
            return materialSendType;
        }

        public void setMaterialSendType(String materialSendType) {
            this.materialSendType = materialSendType;
        }

        public String getReportSource() {
            return reportSource;
        }

        public void setReportSource(String reportSource) {
            this.reportSource = reportSource;
        }
    }

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public List<MetarialReport> getDataList() {
        return dataList;
    }

    public void setDataList(List<MetarialReport> dataList) {
        this.dataList = dataList;
    }
}
