package com.howbuy.crm.cgi.extservice.request.hkaccount;

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * @description: 交易记录基金模糊查询请求参数
 * <AUTHOR>
 * @date 2025/6/30 10:00:00
 */
@Setter
@Getter
public class TradeRecodeFundCodeBySearchRequest extends AccountBaseRequest {

    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 基金搜索关键字
     */
    @NotBlank(message = "基金搜索关键字不能为空")
    private String searchKeyword;
} 