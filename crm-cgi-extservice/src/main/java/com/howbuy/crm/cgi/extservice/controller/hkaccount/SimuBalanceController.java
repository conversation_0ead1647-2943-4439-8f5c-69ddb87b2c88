/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkaccount;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.dtmsorder.QueryFundBalanceDetailRequest;
import com.howbuy.crm.cgi.extservice.request.dtmsproduct.QueryProductContractRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.SimuBalanceBaseRequest;
import com.howbuy.crm.cgi.extservice.service.hkaccount.HkSimuBalanceService;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.AssetInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.BalanceListInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TotalAssetDetailInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TotalAssetInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HkAppCenterCashTabVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HkAppCenterFundTabVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @description: (私募持仓接口)
 * @date 2024/2/23 08:54
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/balance")
public class SimuBalanceController {

    @Autowired
    private HkSimuBalanceService hkSimuBalanceService;

    /**
     * @api {POST} /ext/hkaccount/balance/query queryTotalAsset()
     * @apiVersion 1.0.0
     * @apiGroup SimuBalanceController
     * @apiName queryTotalAsset()
     * @apiDescription 查询私募总持仓接口
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} currency 当前展示的币种
     * @apiParam (请求体) {String} fundTxCode 基金交易账号
     * @apiParam (请求体) {String} version app版本号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"84qqZ2CGC","currency":"ht","fundTxCode":"EN","version":"D8"}
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.totalAsset 总资产
     * @apiSuccess (响应结果) {String} data.showAsset 是否显示资产 [资产小眼睛] (0:显示, 1: 隐藏)
     * @apiSuccess (响应结果) {Array} data.totalAssetsDetail 总资产明细弹窗内容列表
     * @apiSuccess (响应结果) {String} data.totalAssetsDetail.fundName 产品名称
     * @apiSuccess (响应结果) {String} data.totalAssetsDetail.unPaidInAmt 待投金额
     * @apiSuccess (响应结果) {String} data.inTransitTradeNums 在途交易笔数
     * @apiSuccess (响应结果) {String} data.inTransitTradeAmt 在途资产总额
     * @apiSuccess (响应结果) {String} data.inTransitDealNo 交易单号 (在途交易笔数 == 1 时的交易单号)
     * @apiSuccess (响应结果) {String} data.waitSignNums 待签约交易笔数
     * @apiSuccess (响应结果) {String} data.fundReceivedNums 资金到账笔数
     * @apiSuccess (响应结果) {String} data.underReviewCount 打款凭证审核中的数量
     * @apiSuccess (响应结果) {String} data.notPassCount 打款凭证审核不通过的数量
     * @apiSuccess (响应结果) {String} data.voucherNo 打款凭证只有一个的时候的打款凭证编号,APP需要直接跳转到详情页
     * @apiSuccess (响应结果) {String} data.fundAsset 基金持仓总资产
     * @apiSuccess (响应结果) {String} data.piggyBankAsset 存钱罐资产
     * @apiSuccess (响应结果) {String} data.cashBalanceAsset 现金余额
     * @apiSuccess (响应结果) {String} data.cashBalanceAssetDate 现金余额日期
     * @apiSuccess (响应结果) {String} data.cashBalanceAssetLimit 现金凭证是否已达上限 1-是,0-否
     * @apiSuccess (响应结果) {Object} data.supplementalAgreementInfo 补充协议信息
     * @apiSuccess (响应结果) {String} data.supplementalAgreementInfo.agreementId 协议ID
     * @apiSuccess (响应结果) {String} data.supplementalAgreementInfo.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.supplementalAgreementInfo.agreementCount 协议数量
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"CLGIhjrNC","data":{"totalAsset":"Yrum","inTransitTradeAmt":"kxqZQkER","cashBalanceAssetDate":"PPUpAXrJfU","fundAsset":"HF0fgiUNb9","notPassCount":"m","piggyBankAsset":"RnjrWigXXG","cashBalanceAssetLimit":"MQKMbrWc","inTransitTradeNums":"DakpwV9pN","fundReceivedNums":"N","voucherNo":"aULC","underReviewCount":"Z0QGSvWLH","cashBalanceAsset":"wFXVc","totalAssetsDetail":[{"fundName":"lB4Rcgqx2W","unPaidInAmt":"4CL4T"}],"showAsset":"l1hal","inTransitDealNo":"o","supplementalAgreementInfo":{"fundCode":"oALUNeuS","agreementId":"hD","agreementCount":"fv"},"waitSignNums":"9fhO6M"},"description":"005","timestampServer":"l"}
     */
    @PostMapping("/query")
    @ResponseBody
    public CgiResponse<TotalAssetInfoVO> queryTotalAsset(@RequestBody SimuBalanceBaseRequest request) throws ExecutionException, InterruptedException {
        return CgiResponse.appOk(hkSimuBalanceService.queryBalance(request));
    }

    /**
     * @api {POST} /ext/hkaccount/balance/querydetail queryTotalAssetDetail()
     * @apiVersion 1.0.0
     * @apiGroup SimuBalanceController
     * @apiName queryTotalAssetDetail()
     * @apiDescription 持仓明细列表接口
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} currency 当前展示的币种
     * @apiParam (请求体) {String} fundTxCode 基金交易账号
     * @apiParam (请求体) {String} version app版本号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"QBdJZ4","currency":"JAVQErwwC","fundTxCode":"7NulY","version":"BfPg"}
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.totalAsset 总资产
     * @apiSuccess (响应结果) {String} data.showAsset 是否显示资产 [资产小眼睛] (0:显示, 1: 隐藏)
     * @apiSuccess (响应结果) {Array} data.totalAssetsDetail 总资产明细弹窗内容列表
     * @apiSuccess (响应结果) {String} data.totalAssetsDetail.fundName 产品名称
     * @apiSuccess (响应结果) {String} data.totalAssetsDetail.unPaidInAmt 待投金额
     * @apiSuccess (响应结果) {String} data.inTransitTradeAmt 在途资产总额
     * @apiSuccess (响应结果) {String} data.inTransitTradeAmtFlag 在途资产总额是否显示标志
     * @apiSuccess (响应结果) {Array} data.assetFundTypeList 类型基金持仓列表
     * @apiSuccess (响应结果) {String} data.assetFundTypeList.totalAsset 总资产
     * @apiSuccess (响应结果) {String} data.assetFundTypeList.disCurTotalAsset 展示币种总资产
     * @apiSuccess (响应结果) {String} data.assetFundTypeList.fundSetType 产品集合类型 0 海外储蓄罐 1 阳光私募
     * @apiSuccess (响应结果) {Array} data.assetFundTypeList.assetFundDetailList 基金持仓详情列表
     * @apiSuccess (响应结果) {String} data.cashBalanceAsset 现金余额
     * @apiSuccess (响应结果) {String} data.cashBalanceAssetFlag 现金余额是否显示标志 1 是 0 否
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"oAkw","data":{"assetFundTypeList":[{"totalAsset":"ujaMQ","fundSetType":"sjGA90su","disCurTotalAsset":"EWeY3ZkzvM","assetFundDetailList":[]}],"totalAsset":"y","inTransitTradeAmt":"9rCqMXDY","cashBalanceAsset":"YbDpZ","totalAssetsDetail":[{"fundName":"Fa4en9Vg","unPaidInAmt":"0vZAq4"}],"showAsset":"SR","cashBalanceAssetFlag":"A7Dn","inTransitTradeAmtFlag":"CkTOLnW"},"description":"7","timestampServer":"9XnEfOt"}
     */
    @PostMapping("/querydetail")
    @ResponseBody
    public CgiResponse<TotalAssetDetailInfoVO> queryTotalAssetDetail(@RequestBody SimuBalanceBaseRequest request) {
        return CgiResponse.ok(hkSimuBalanceService.queryBalanceList(request));
    }

    /**
     * @api {POST} /ext/hkaccount/balance/querydetailbyfund queryDetailByFund()
     * @apiVersion 1.0.0
     * @apiGroup SimuBalanceController
     * @apiName queryDetailByFund()
     * @apiDescription 根据基金代码查询资产详情
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParamExample 请求体示例
     * {"fundCode":"KjEzTaFj"}
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.fundName 基金名称
     * @apiSuccess (响应结果) {String} data.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.currencyMarketValue 参考市值
     * @apiSuccess (响应结果) {String} data.currencyDesc 币种描述 仅参考市值用
     * @apiSuccess (响应结果) {String} data.currency 币种 其余的市值 用
     * @apiSuccess (响应结果) {String} data.nav 参考净值
     * @apiSuccess (响应结果) {String} data.navDate 净值日期 YYYY-MM-DD或者MM-DD
     * @apiSuccess (响应结果) {String} data.paidInAmt 总实缴金额
     * @apiSuccess (响应结果) {String} data.currencyUnPaidInAmt 待投金额（当前币种）
     * @apiSuccess (响应结果) {String} data.totalBalanceVol 持仓份额
     * @apiSuccess (响应结果) {String} data.dayIncomeRate 当前收益率
     * @apiSuccess (响应结果) {String} data.navDivFlag 分红提醒标识 0:无需分红提醒;1:提醒收益偏差;2:提醒即将分红
     * @apiSuccess (响应结果) {String} data.crisisFlag 清算标识    0-否; 1-是
     * @apiSuccess (响应结果) {String} data.qianXiFlag 千禧年产品标识 0-否; 1-是
     * @apiSuccess (响应结果) {String} data.stageEstablishFlag 是否分期成立 0-否; 1-是
     * @apiSuccess (响应结果) {String} data.naFlag 是否NA产品
     * @apiSuccess (响应结果) {String} data.receivManageFee 累计应收管理费
     * @apiSuccess (响应结果) {String} data.receivPreformFee 累计应收业绩报酬
     * @apiSuccess (响应结果) {String} data.currencyMarketValueExFee 产品费后参考市值
     * @apiSuccess (响应结果) {String} data.currentAssetCurrency 产品费后持仓收益
     * @apiSuccess (响应结果) {String} data.yieldRate 产品费后持仓收益率
     * @apiSuccess (响应结果) {String} data.yieldRateStr 产品费后持仓收益率 保留几位小数
     * @apiSuccess (响应结果) {String} data.balanceFactor 平衡因子
     * @apiSuccess (响应结果) {String} data.incomeCalStat
     * @apiSuccess (响应结果) {String} data.convertFinish 平衡因子转换完成 1-是 0-否
     * @apiSuccess (响应结果) {String} data.balanceFactorDate 平衡因子日期
     * @apiSuccess (响应结果) {String} data.buySubFundCode 买入子基金代码
     * @apiSuccess (响应结果) {String} data.isCanBuy 是否可买入 1-可交易 2-不可交易 3-按钮不展示
     * @apiSuccess (响应结果) {String} data.isCanSell 是否可卖出 1-可交易 2-不可交易 3-按钮不展示
     * @apiSuccess (响应结果) {String} data.notCanBuyCode 不支持 买入Code
     * @apiSuccess (响应结果) {String} data.notCanSellCode 不支持 卖出的Code
     * @apiSuccess (响应结果) {Array} data.assetDetailInfoVOList 详情列表数据
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.fundName 基金名称
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.subFundName 子基金名称
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.subFundCode 子基金代码
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.currencyMarketValue 参考市值
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.currencyDesc 币种描述 仅参考市值用
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.currency 币种 其余的市值 用
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.nav 参考净值
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.navDate 净值日期 YYYY-MM-DD或者MM-DD
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.paidInAmt 总实缴金额
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.currencyUnPaidInAmt 待投金额（当前币种）
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.totalBalanceVol 持仓份额
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.dayIncomeRate 当前收益率
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.navDivFlag 分红提醒标识 0:无需分红提醒;1:提醒收益偏差;2:提醒即将分红
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.crisisFlag 清算标识    0-否; 1-是
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.qianXiFlag 千禧年产品标识 0-否; 1-是
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.stageEstablishFlag 是否分期成立
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.naFlag 是否NA产品
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.receivManageFee 累计应收管理费
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.receivPreformFee 累计应收业绩报酬
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.currencyMarketValueExFee 产品费后参考市值
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.currentAssetCurrency 产品费后持仓收益
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.yieldRate 产品费后持仓收益率
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.yieldRateStr 产品费后持仓收益率 保留几位小数
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.balanceFactor 平衡因子
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.convertFinish 平衡因子转换完成 1-是 0-否
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.balanceFactorDate 平衡因子日期
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.incomeCalStat
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.isCanSell 是否可卖出 1-可交易 2-不可交易 3-按钮不展示
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.notCanSellCode 不支持 卖出的Code
     * @apiSuccess (响应结果) {String} data.subTotalAmt 认缴总金额
     * @apiSuccess (响应结果) {String} data.paidTotalAmt 实缴总金额
     * @apiSuccess (响应结果) {String} data.paidSubTotalRatio 实缴认缴百分比
     * @apiSuccess (响应结果) {String} data.gradationCall 分次Call产品 1-是 0-否
     * @apiSuccess (响应结果) {String} data.productBusiType 产品业务类型 1 海外公募   2 海外私募
     * @apiSuccess (响应结果) {String} data.productBusiSubType 产品业务子类型 1 阳光私募  2 私募股权
     * @apiSuccess (响应结果) {String} data.latestIncome 最新收益
     * @apiSuccess (响应结果) {String} data.latestIncomeDate 最新收益时间
     * @apiSuccess (响应结果) {String} data.latestIncomeRate 最新收益率
     * @apiSuccess (响应结果) {String} data.accumIncome 累计收益
     * @apiSuccess (响应结果) {String} data.unitCost 单位成本
     * @apiSuccess (响应结果) {String} data.latestNav 最新净值
     * @apiSuccess (响应结果) {String} data.accumTotalBack 累计总回款
     * @apiSuccess (响应结果) {String} data.accumBackRatio 回款比例
     * @apiSuccess (响应结果) {String} data.totalInvestCost 投资总成本
     * @apiSuccess (响应结果) {String} data.motherChildFund 是否母子基金、 1 是 0 否
     * @apiSuccess (响应结果) {String} data.initInvestCost 初始投资成本
     * @apiSuccess (响应结果) {String} data.productTermDes 产品期限说明
     * @apiSuccess (响应结果) {String} data.productTermCode 产品期限说明编码(非交易拼接) 例如 4+2+1年
     * @apiSuccess (响应结果) {String} data.supplementalAgreementCount 补充协议个数
     * @apiSuccess (响应结果) {String} data.supplementalAgreementFundCode 补充协议产品
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"V4uGL3","data":{"crisisFlag":"mv9iZ","buySubFundCode":"bOMYY","totalBalanceVol":"yCNH0WK4Q","productBusiType":"772O","yieldRateStr":"WZaU","isCanSell":"J3f10l0174","naFlag":"zv","fundCode":"GrEg","notCanSellCode":"SxKZ3","currencyDesc":"c","nav":"xcx","receivPreformFee":"7iD218q","accumIncome":"haLr6cl6i","currencyUnPaidInAmt":"9QqIn1","motherChildFund":"cogyjjeI","latestIncome":"E","fundName":"M98","paidTotalAmt":"I","productTermDes":"se4EbXTev","stageEstablishFlag":"3FlPKs21iB","totalInvestCost":"Zdm","accumTotalBack":"HJ7Zg","balanceFactorDate":"G4hNWuJ","gradationCall":"A3zxzE","navDivFlag":"dd73","isCanBuy":"NZ","currencyMarketValueExFee":"psjToUTdt","assetDetailInfoVOList":[{"balanceFactorDate":"hnzP","crisisFlag":"d","navDivFlag":"hfZc90","totalBalanceVol":"b7bAQCBc7b","currencyMarketValueExFee":"ok","yieldRateStr":"2pFbrY2p4","isCanSell":"a2zbHY1pef","naFlag":"H9w","subFundCode":"Jdqdw","currencyMarketValue":"1m03","fundCode":"4KQWLLP","currency":"cFyxzBY","balanceFactor":"fEo5","notCanSellCode":"W","currencyDesc":"pt","nav":"fI","navDate":"tvR7","receivPreformFee":"gb01s7J","dayIncomeRate":"NFEByUt2","currencyUnPaidInAmt":"mGm909Rka","receivManageFee":"z","qianXiFlag":"xPUBMzA","incomeCalStat":"qO5Zwfr","yieldRate":"M","subFundName":"nx9mtaCt5t","paidInAmt":"h5","fundName":"R","currentAssetCurrency":"Ra","stageEstablishFlag":"Zr5VkZu3p","convertFinish":"c1ssIgh8VC"}],"productTermCode":"2hnlSKcD","latestIncomeRate":"eppXX","currencyMarketValue":"DyMNh","currency":"sb8omVo2","balanceFactor":"J","latestIncomeDate":"3RTMNF97bl","navDate":"OPJFGCrv4","supplementalAgreementCount":"LIIio","dayIncomeRate":"E6Iuir3Z","initInvestCost":"eChMROq1bp","receivManageFee":"v50","qianXiFlag":"wg","paidSubTotalRatio":"B","incomeCalStat":"lVgUCsAC","productBusiSubType":"hoPBf","yieldRate":"X","notCanBuyCode":"6e","accumBackRatio":"7KDeWqg8","supplementalAgreementFundCode":"vNjk","subTotalAmt":"5","unitCost":"QTaOSu","paidInAmt":"ycU","latestNav":"B7ozfnttN","currentAssetCurrency":"yR","convertFinish":"KfIvsR"},"description":"5","timestampServer":"q"}
     */
    @PostMapping("/querydetailbyfund")
    public CgiResponse<AssetInfoVO> queryDetailByFund(@RequestBody QueryProductContractRequest request, HttpServletRequest httpServletRequest) {
        return CgiResponse.ok(hkSimuBalanceService.queryAssetByFundCode(request.getFundCode(), httpServletRequest));
    }

    /**
     * @api {POST} /ext/hkaccount/balance/querybalancedetailbyfund queryBalanceDetailByFund()
     * @apiVersion 1.0.0
     * @apiGroup SimuBalanceController
     * @apiName queryBalanceDetailByFund()
     * @apiDescription 查询持仓份额明细数据
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParamExample 请求体示例
     * {"fundCode":"AUSwulazT"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.totalBalanceVol 持仓份额
     * @apiSuccess (响应结果) {String} data.availableBalanceVol 可用份额
     * @apiSuccess (响应结果) {String} data.unavailableBalanceVol 不可用份额
     * @apiSuccess (响应结果) {Array} data.balanceDetailGroupInfoVOList 母子基金分组明细数据
     * @apiSuccess (响应结果) {String} data.balanceDetailGroupInfoVOList.fundName 基金名称
     * @apiSuccess (响应结果) {Array} data.balanceDetailGroupInfoVOList.balanceDetailList 基金列表
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"UHCWbo1","data":{"balanceDetailList":{},"unavailableBalanceVol":"25D8gSM","totalBalanceVol":"qNiipboXT","availableBalanceVol":"VkO"},"description":"o8","timestampServer":"iWCN9"}
     */
    @PostMapping("/querybalancedetailbyfund")
    public CgiResponse<BalanceListInfoVO> queryBalanceDetailByFund(@RequestBody QueryFundBalanceDetailRequest request) {
        return CgiResponse.ok(hkSimuBalanceService.queryBalanceDetailByFund(request));
    }

    /**
     * @api {POST} /ext/hkaccount/balance/fundtab/query queryFundTabInfo()
     * @apiVersion 1.0.0
     * @apiGroup SimuBalanceController
     * @apiName queryFundTabInfo()
     * @apiDescription app个人中心资产中心基金tab页面查询接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} currency 当前展示的币种
     * @apiParam (请求体) {String} version app版本号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"8ZogWWuh","currency":"tnDT3ha","version":"P"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.fundTotalAsset 基金产品总资产
     * @apiSuccess (响应结果) {Array} data.fundInfoList 千禧年的基金名称和待投金额
     * @apiSuccess (响应结果) {String} data.fundInfoList.fundAddr 基金简称
     * @apiSuccess (响应结果) {String} data.fundInfoList.residueAmt 待投金额
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"k0LCPBTnB","data":{"fundInfoList":[{"fundAddr":"Pv","residueAmt":"Kgk"}],"fundTotalAsset":"ntsj"},"description":"yHk9zr","timestampServer":"6M3TsNIPH"}
     */
    @PostMapping("/fundtab/query")
    public CgiResponse<HkAppCenterFundTabVO> queryFundTabInfo(@RequestBody SimuBalanceBaseRequest request) {
        return CgiResponse.ok(hkSimuBalanceService.queryFundTabInfo(request));
    }

    /**
     * @api {POST} /ext/hkaccount/balance/cashtab/query queryCashTabInfo()
     * @apiVersion 1.0.0
     * @apiGroup SimuBalanceController
     * @apiName queryCashTabInfo()
     * @apiDescription 个人中心资产模块中的现金弹框查询接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} hbTraceId = 1 链路ID,方便自动化使用
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} currency 当前展示的币种
     * @apiParam (请求体) {String} version app版本号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"LYEeZ2cnFo","currency":"tZH","version":"ZvH"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.cashBalanceAsset
     * @apiSuccess (响应结果) {Array} data.cashCurrencyList 现金余额
     * @apiSuccess (响应结果) {String} data.cashCurrencyList.currency 币种
     * @apiSuccess (响应结果) {String} data.cashCurrencyList.conversionAmt 换算后的币种金额
     * @apiSuccess (响应结果) {String} data.cashCurrencyList.originalAmt 原币种金额
     * @apiSuccess (响应结果) {String} data.cashCurrencyList.currencyDesc 币种描述
     * @apiSuccess (响应结果) {String} data.cashCurrencyList.excRate 汇率
     * @apiSuccess (响应结果) {String} data.cashCurrencyList.excRateDate 汇率时间 yyyyMMdd
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"kHA49XnP","data":{"cashBalanceAsset":"qPv8","cashCurrencyList":[{"currencyDesc":"ni7","originalAmt":"c","excRateDate":"H85hZxQOCR","excRate":"OpWfFBH6z6","currency":"u9CmOewh","conversionAmt":"sgvZ"}]},"description":"f","timestampServer":"QTRhFI4"}
     */
    @PostMapping("/cashtab/query")
    public CgiResponse<HkAppCenterCashTabVO> queryCashTabInfo(@RequestBody SimuBalanceBaseRequest request) {
        return CgiResponse.ok(hkSimuBalanceService.queryCashTabInfo(request));
    }
}