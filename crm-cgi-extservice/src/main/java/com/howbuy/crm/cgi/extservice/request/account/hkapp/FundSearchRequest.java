package com.howbuy.crm.cgi.extservice.request.account.hkapp;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: 基金搜索请求参数
 * @author: jinqing.rao
 * @date: 2024/3/11 10:20
 * @since JDK 1.8
 */
@Setter
@Getter
public class FundSearchRequest implements Serializable {

    private static final long serialVersionUID = 274091242243340207L;

    @NotBlank(message = "香港客户号必传")
    private String hkCustNo;

    /**
     * 搜索关键词
     */
    @NotBlank(message = "搜索关键词不能为空")
    private String searchValue;

    /**
     * 业务类型  1 交易记录列表的基金模糊搜索  2.合同列表的基金模糊查询
     */
    private String bizType;
} 