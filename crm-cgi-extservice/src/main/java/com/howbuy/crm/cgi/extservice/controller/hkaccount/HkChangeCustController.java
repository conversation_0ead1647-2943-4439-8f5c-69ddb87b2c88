/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkaccount;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.base.HeadRequest;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.CommonResultEnum;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.VerifyCodeTypeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.enums.BindStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.UnBindStatusEnum;
import com.howbuy.crm.cgi.extservice.common.utils.ParamsValidator;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.request.account.EditEmailRequest;
import com.howbuy.crm.cgi.extservice.request.account.HboneInfoRequest;
import com.howbuy.crm.cgi.extservice.request.account.UnBindHboneInfoRequest;
import com.howbuy.crm.cgi.extservice.service.hkaccount.HboneNoService;
import com.howbuy.crm.cgi.extservice.service.hkaccount.VerifyCodeService;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.BindStatusVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.CancelHboneNoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.HboneNoInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.UnBindStatusVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: (个人中心相关接口)
 * @date 2023/11/29 10:13
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/changecust")
public class HkChangeCustController {

    @Autowired
    private VerifyCodeService verifyCodeService;

    @Autowired
    private HboneNoService hbNoService;

    /**
     * @api {POST} /ext/hkaccount/changecust/editemail 修改邮箱
     * @apiVersion 1.0.0
     * @apiGroup HkChangeCustController
     * @apiName editEmail()
     * @apiDescription 修改邮箱
     * @apiHeader (header - param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} email 邮箱
     * @apiParam (请求体) {String} verifyCode 验证码
     * @apiParam (请求体) {String} txPassword 交易密码
     * @apiParam (请求体) {String} [syncStatementEmail] 是否同步更新结单邮箱 1-是
     * @apiParamExample 请求体示例
     * {"txPassword":"myIw","verifyCode":"IoAmRfXBD3","email":"qcA8s"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Xkkn","description":"1ND4s","timestampServer":"0Q"}
     */
    @PostMapping("/editemail")
    @ResponseBody
    public CgiResponse<Body> editEmail(@RequestBody EditEmailRequest req) {
        // 1. 校验必填参数
        ParamsValidator.validateParams(req, "email", "verifyCode", "txPassword");
        // 2. 调用对应接口
        try {
            verifyCodeService.editEmail(req.getEmail(), req.getVerifyCode(), req.getTxPassword(), req.getSyncStatementEmail());
            return CgiResponse.ok(null);
        } catch (BusinessException e) {
            return CgiResponse.error(e.getCode(), e.getMessage());
        }
    }


    /**
     * @api {POST} /ext/hkaccount/changecust/getbindhboneinfo getBindHBOneInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkChangeCustController
     * @apiName getBindHBOneInfo()
     * @apiDescription 一账通号绑定信息查询接口
     * @apiHeader (header - param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hboneBindStatus 一账通绑定状态 0-未绑定 1-已绑定
     * @apiSuccess (响应结果) {String} data.hbCustName 好买姓名
     * @apiSuccess (响应结果) {String} data.hbIdType 好买证件类型
     * @apiSuccess (响应结果) {String} data.hbIdTypeDesc 好买证件类型描述
     * @apiSuccess (响应结果) {String} data.hbIdNoDigest 好买证件号码摘要
     * @apiSuccess (响应结果) {String} data.hbIdNoMask 好买证件号码掩码
     * @apiSuccess (响应结果) {String} data.hbMobileDigest 好买手机号摘要
     * @apiSuccess (响应结果) {String} data.hbMobileMask 好买手机号掩码
     * @apiSuccess (响应结果) {String} data.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.IdNoDigest 证件号码摘要
     * @apiSuccess (响应结果) {String} data.IdNoMask 证件号码掩码
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.emailDigest 邮箱摘要
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱掩码
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"kvk","data":{"hbIdTypeDesc":"0VV","hbIdNoMask":"0U2D","emailDigest":"RLXazd2","custName":"sskq","hbCustName":"kI1","hbIdNoDigest":"G","hbMobileMask":"b0V9FHcpux","hboneBindStatus":"Vz9sS","IdNoMask":"Xw","mobileDigest":"TFlc1","emailMask":"bbKD","hbIdType":"jY95","IdNoDigest":"P","hbMobileDigest":"8tjsrU","mobileMask":"Bf"},"description":"euaL0zsix","timestampServer":"N"}
     */
    @PostMapping("/getbindhboneinfo")
    @ResponseBody
    public CgiResponse<HboneNoInfoVO> getBindHBOneInfo() {
        // 1.从当前会话里面获取香港客户号
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        // 2.调用账户中心的一账通号绑定信息接口
        HboneNoInfoVO noInfoVO = hbNoService.getNoInfoVO(loginHkCustNo);
        // 3.返回
        return CgiResponse.ok(noInfoVO);
    }


    /**
     * @api {POST} /ext/hkaccount/changecust/bindhboneno bindHBOneNo()
     * @apiVersion 1.0.0
     * @apiGroup HkChangeCustController
     * @apiName bindHBOneNo()
     * @apiDescription 一账通号绑定接口
     * @apiHeader (header - param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hbCustName 好买姓名 前端加密
     * @apiParam (请求体) {String} hbIdType 好买证件类型
     * @apiParam (请求体) {String} hbIdNo 好买证件号
     * @apiParam (请求体) {String} hbMobile 好买手机号
     * @apiParam (请求体) {String} verifyCode 验证码
     * @apiParam (请求体) {String} verifyCodeType 验证码类型  06-绑定一账通香港手机号短信验证码 11-绑定一账通好买手机号短信验证码 56-绑定一账通香港邮箱邮箱验证码；
     * @apiParamExample 请求体示例
     * {"hbMobile":"0KcM84","verifyCode":"g","hbIdType":"hQ","hbIdNo":"jZlw","hbCustName":"ifUU","verifyCodeType":"T"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.bindStatus 绑定状态 0- 绑定失败  1-绑定成功
     * @apiSuccess (响应结果) {Object} data.warnVo 弹窗提示信息
     * @apiSuccess (响应结果) {String} data.warnVo.title 标题
     * @apiSuccess (响应结果) {Array} data.warnVo.tips 提示
     * @apiSuccess (响应结果) {String} data.warnVo.notice 提示
     * @apiSuccess (响应结果) {String} data.warnVo.phone 联系方式
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"vIS263b3w","data":{"warnVo":{"phone":"J","title":"k95t","tips":["0UXBxVIzT"],"notice":"ZKFAD"},"bindStatus":"vAFycAE9yj"},"description":"bjRlewQ","timestampServer":"uPvS"}
     */
    @PostMapping("/bindhboneno")
    @ResponseBody
    @Deprecated
    public CgiResponse<BindStatusVO> bindHBOneNo(@RequestBody HboneInfoRequest req) {
        // 功能下载,直接拦截异常
        if (true) {
            throw new BusinessException(ExceptionCodeEnum.AUTHORIZATION_MANAGEMENT_ERROR);
        }
        // 1. 参数校验 当验证码类型为08的时候必须校验 除开验证码类型以外的全部属性
        if (Objects.equals(req.getVerifyCodeType(), VerifyCodeTypeEnum.BIND_HBONE_MOBILE_VERIFY_CODE.getCode())) {
            ParamsValidator.validateParams(req, "hbCustName", "hbIdNo", "hbMobile", "verifyCode", "verifyCodeType");
        } else {
            ParamsValidator.validateParams(req, "verifyCode", "verifyCodeType");
        }
        try {
            BindStatusVO bindStatusVO = hbNoService.checkBindByVerifyCodeType(req);
            if (!Objects.isNull(bindStatusVO.getWarnVo())) {
                return CgiResponse.error(bindStatusVO);
            } else if (!Objects.equals(bindStatusVO.getBindStatus(), BindStatusEnum.DONE.getCode())) {
                //1,历史接口错误的约定逻辑,接口返回成功，将描述信息放到错误描述的字段description,2.现在App1.0的时候需要将成功码调整为0000,所以做成功码的状态兼容,不改变原有的约定
                String successCode = getSuccessCode();
                return CgiResponse.error(successCode, bindStatusVO.getDescription(), bindStatusVO);
            } else {
                return CgiResponse.ok(bindStatusVO);
            }
        } catch (BusinessException e) {
            BindStatusVO vo = new BindStatusVO();
            vo.setBindStatus(BindStatusEnum.FAIL.getCode());
            return CgiResponse.error(e.getCode(), e.getDesc(), vo);
        }
    }


    /**
     * @api {POST} /ext/hkaccount/changecust/unbindhboneno unbindHBOneNo()
     * @apiVersion 1.0.0
     * @apiGroup HkChangeCustController
     * @apiName unbindHBOneNo()
     * @apiDescription 一账通号解绑接口
     * @apiHeader (header - param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} verifyCode 验证码
     * @apiParam (请求体) {String} verifyCodeType 验证码类型 07-解绑一账通香港手机号短信验证码|08-解绑一账通好买手机号短信验证码|57-解绑一账通香港邮箱邮箱验证码
     * @apiParamExample 请求体示例
     * {"verifyCode":"x0SquI","verifyCodeType":"U24oi0Xhk"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.bindStatus 绑定状态 01-完成 |03-重置登录密码短信验证码|      |04-交易账户激活短信验证码|      |05-登录账户激活短信验证码|      |06-绑定一账通香港手机号短信验证码|      |07-解绑一账通香港手机号短信验证码|      |08-解绑一账通好买手机号短信验证码|      |09-重置交易密码短信验证码|      |10-设置交易密码短信验证码|      |11-绑定一账通好买手机号短信验证码|      |12-修改手机号短信验证码|      |13-绑定手机号短信验证码|
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机摘要
     * @apiSuccess (响应结果) {String} data.emailDigest 邮箱摘要
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"V","data":{"bindStatus":"W"},"description":"DL5qSZYX","timestampServer":"i0"}
     */
    @PostMapping("/unbindhboneno")
    @ResponseBody
    @Deprecated
    public CgiResponse<UnBindStatusVO> unbindHBOneNo(@RequestBody UnBindHboneInfoRequest request) {
        // 功能下架,直接拦截异常
        if (true) {
            throw new BusinessException(ExceptionCodeEnum.AUTHORIZATION_MANAGEMENT_ERROR);
        }
        try {
            UnBindStatusVO bindStatusVO = hbNoService.checkUnBindByVerifyCodeType(request);
            if (bindStatusVO.getDescription() != null) {
                //1,历史接口错误的约定逻辑,接口返回成功，将描述信息放到错误描述的字段description,2.现在App1.0的时候需要将成功码调整为0000,所以做成功码的状态兼容,不改变原有的约定
                String successCode = getSuccessCode();
                return CgiResponse.error(successCode, bindStatusVO.getDescription(), bindStatusVO);
            }
            return CgiResponse.ok(bindStatusVO);
        } catch (BusinessException e) {
            UnBindStatusVO bindStatusVO = new UnBindStatusVO();
            bindStatusVO.setBindStatus(UnBindStatusEnum.FAIL.getCode());
            return CgiResponse.error(e.getCode(), e.getDesc(), bindStatusVO);
        }
    }

    /**
     * @param
     * @return java.lang.String
     * @description: CRM-CGI的状态码从c010000调整为0000,后续可以删除兼容判断
     * @author: jinqing.rao
     * @date: 2024/3/20 10:24
     * @since JDK 1.8
     */
    private static String getSuccessCode() {
        String successCode = CommonResultEnum.SUCCESS.getCode();
        //返回成功状态码调整,C010000 调整到0000,目前App和H5都是0000,小程序版本大于1.0.0的都是0000,以前都是C010000
        String requestHeader = HeadRequest.getRequestHeader(Constants.WX_VERSION);
        if (StringUtils.isNotBlank(requestHeader) && requestHeader.compareTo("1.0.7") >= 0) {
            successCode = CommonResultEnum.GATEWAY_SUCCESS.getCode();
        }
        if (HeadRequest.isHkAppLogin()) {
            successCode = CommonResultEnum.GATEWAY_SUCCESS.getCode();
        }
        return successCode;
    }

    /**
     * @api {POST} /ext/hkaccount/changecust/cancelhkcust cancelHbOneNo()
     * @apiVersion 1.0.0
     * @apiGroup HkChangeCustController
     * @apiName cancelHbOneNo()
     * @apiDescription 香港客户号注销接口
     * @apiHeader (header - param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.customerTel 客服电话
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Xy","data":{"customerTel":"0ASb"},"description":"A289z6Cb3H","timestampServer":"oi8i"}
     */
    @PostMapping("/cancelhkcust")
    @ResponseBody
    public CgiResponse<CancelHboneNoVO> cancelHkCust() {
        CancelHboneNoVO cancelHboneNoVO = hbNoService.cancelHkBindHbone();
        if (Objects.isNull(cancelHboneNoVO.getWarnVo())) {
            return CgiResponse.ok(cancelHboneNoVO);
        } else {
            return CgiResponse.error(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), ExceptionCodeEnum.SYSTEM_ERROR.getDescription(), cancelHboneNoVO);
        }

    }
}