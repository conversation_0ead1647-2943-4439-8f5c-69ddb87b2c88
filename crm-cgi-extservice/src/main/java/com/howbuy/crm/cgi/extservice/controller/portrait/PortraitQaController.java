/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.QaDirectoryRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.QaReplyRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.qa.PortraitQaService;
import com.howbuy.crm.cgi.extservice.vo.portrait.material.PortraitMaterialDirectoryVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.qa.QaReplyVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 客户画像问答控制器
 *
 * <AUTHOR>
 * @date 2024-03-19 10:40:00
 */
@Slf4j
@RestController
@RequestMapping("/portrait/qa")
public class PortraitQaController {

    @Resource
    private PortraitQaService portraitQaService;

    /**
     * @api {POST} /ext/portrait/qa/directory getDirectory()
     * @apiVersion 1.0.0
     * @apiGroup PortraitQaController
     * @apiName getDirectory()
     * @apiDescription 素材库索引目录查询接口
     *
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.categories 大类列表
     * @apiSuccess (响应结果) {String} data.categories.category 大类名称
     * @apiSuccess (响应结果) {Array} data.categories.directoryList 子目录列表
     * @apiSuccess (响应结果) {String} data.categories.directoryList.directory 索引目录名
     * @apiSuccess (响应结果) {String} data.categories.directoryList.sortCode 排序编码
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     *
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "categories": [{
     *       "category": "基金投资",
     *       "directoryList": [{
     *         "directory": "基金基础知识",
     *         "code": "JJ01"
     *       }]
     *     }]
     *   },
     *   "timestampServer": "1677196800000"
     * }
     */
    @PostMapping("/directory")
    public CgiResponse<PortraitMaterialDirectoryVO> getDirectory(@RequestBody QaDirectoryRequest request) {
        log.info("素材库索引目录查询请求");
        PortraitMaterialDirectoryVO directory = portraitQaService.getDirectory(request);
        return CgiResponse.appOk(directory);
    }

    /**
     * @api {POST} /ext/portrait/qa/reply reply()
     * @apiVersion 1.0.0
     * @apiGroup PortraitQaController
     * @apiName reply()
     * @apiDescription 问答回复查询接口
     *
     * @apiParam (请求参数) {String} source 来源类型 0-1V1入口 1-工作台
     * @apiParam (请求参数) {String} searchPool 检索池类型 0-全池 1-推荐池
     * @apiParam (请求参数) {String} searchCondition 检索条件
     * @apiParam (请求参数) {String} hboneNo 一账通号
     * @apiParam (请求参数) {String} conscode 投顾编号
     * @apiParam (请求体) {Integer} page 分页页码
     * @apiParam (请求体) {Integer} size 每页数量
     * @apiParam (请求体) {String} firstReply 一级回复内容
     * @apiParam (请求体) {String} secondReply 二级回复内容
     *
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.isHkCons 是否香港投顾 1-是 0-否
     * @apiSuccess (响应结果) {String} data.searchCondition 检索条件
     * @apiSuccess (响应结果) {String} data.firstReply 一级回复
     * @apiSuccess (响应结果) {String} data.secondReply 二级回复
     * @apiSuccess (响应结果) {String} data.replyLevel 回复层级 1-一级回复 2-二级回复 3-三级回复
     * @apiSuccess (响应结果) {String} data.replyType 回复类型 1-标签 2-素材 3-产品报告 4-基金搜索 5-经理搜索 6-机构搜索 7-授权引导 8-产品 9-章节 10-专栏
     * @apiSuccess (响应结果) {String} data.replyTitle 回复标题
     * @apiSuccess (响应结果) {int} data.total 回复列表总记录数
     * @apiSuccess (响应结果) {Array} data.answerList 回复列表
     * @apiSuccess (响应结果) {String} data.answerList.tagName 标签名称（回复类型为标签时返回）
     * @apiSuccess (响应结果) {String} data.answerList.tagCode 标签code（回复类型为标签时返回标签code）
     * @apiSuccess (响应结果) {String} data.answerList.materialId 素材id
     * @apiSuccess (响应结果) {String} data.answerList.materialTitle 素材标题（回复类型为素材、产品报告时返回）
     * @apiSuccess (响应结果) {String} data.answerList.materialUrl 素材链接（回复类型为素材、产品报告时返回）
     * @apiSuccess (响应结果) {String} data.answerList.sendCount 发送次数（回复类型为素材、产品报告时返回）
     * @apiSuccess (响应结果) {String} data.answerList.learningProgress 学习进度（回复类型为素材-研习社课程时返回）
     * @apiSuccess (响应结果) {String} data.answerList.materialContentType 素材内容类型(1:文章、2:视频、3:直播、4:研习社课程、5-报告)
     * @apiSuccess (响应结果) {String} data.answerList.reportSource 报告来源(1:CMS报告；2:参数中心报告)
     * @apiSuccess (响应结果) {String} data.answerList.fundCode 产品代码（回复类型为产品时返回）
     * @apiSuccess (响应结果) {String} data.answerList.fundName 产品简称（回复类型为产品时返回）
     * @apiSuccess (响应结果) {String} timestampServer
     *
     * @apiParamExample 请求参数示例:
     * {
     *   "source": "1",
     *   "searchPool": "1",
     *   "searchCondition": "基金投资",
     *   "hboneNo": "HB123456789",
     *   "conscode": "TA123456",
     *   "pageNo": 1,
     *   "pageSize": 10,
     *   "firstReply": "基金投资策略",
     *   "secondReply": "定投方案"
     * }
     *
     * @apiSuccessExample 响应结果示例:
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "replyLevel": "1",
     *     "replyType": "2",
     *     "replyTitle": "基金投资相关素材",
     *     "total": 100,
     *     "answerList": [{
     *       “tagName”: "产品合同",
     *       “tagCode”: "1",
     *       "materialId": "123",
     *       "materialTitle": "基金投资入门",
     *       "materialUrl": "http://xxx",
     *       "sendCount": 10,
     *       "learningProgress": "80%",
     *       "materialContentType": "1",
     *       "fundCode": "P00107",
     *       "fundName": "淡水泉成长"
     *     }]
     *   },
     *   "timestampServer": "1677196800000"
     * }
     */
    @PostMapping("/reply")
    public CgiResponse<QaReplyVO> reply(@RequestBody QaReplyRequest request) {
        log.info("问答回复查询请求参数：{}", request);
        return CgiResponse.appOk(portraitQaService.getQaReply(request));
    }

} 