package com.howbuy.crm.cgi.extservice.service.report;

/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.report.ReportAuthenticationRequest;
import com.howbuy.crm.cgi.extservice.vo.report.ReportAuthenticationVO;
import com.howbuy.crm.cgi.manager.domain.hkproductreport.ReportAuthDTO;
import com.howbuy.crm.cgi.manager.outerservice.memberserver.HkHoldProductReportOuterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 香港产品报告服务类
 * @author: 陈杰文
 * @date: 2025-06-19 15:07:57
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkReportService {

    @Autowired
    private HkHoldProductReportOuterService hkHoldProductReportOuterService;

    /**
     * @description: 产品报告鉴权
     * @param request 请求参数
     * @return ReportAuthenticationVO
     * @author: 陈杰文
     * @date: 2025-06-19 15:07:57
     */
    public ReportAuthenticationVO reportAuthentication(ReportAuthenticationRequest request) {
        log.info("开始处理产品报告鉴权请求，香港客户号：{}，报告ID：{}", request.getHkCustNo(), request.getReportId());
        
        // 调用外部服务进行鉴权
        ReportAuthDTO reportAuthDTO = hkHoldProductReportOuterService.reportAuth(request.getHkCustNo(), request.getReportId());
        
        // 组装返回结果
        ReportAuthenticationVO result = new ReportAuthenticationVO();
        result.setIsViewed(reportAuthDTO.getIsViewed());
        result.setRedirectUrl(reportAuthDTO.getRedirectUrl());
        result.setReportType(reportAuthDTO.getReportType());
        
        log.info("产品报告鉴权处理完成，结果：isViewed={}, reportType={}", result.getIsViewed(), result.getReportType());
        
        return result;
    }
} 