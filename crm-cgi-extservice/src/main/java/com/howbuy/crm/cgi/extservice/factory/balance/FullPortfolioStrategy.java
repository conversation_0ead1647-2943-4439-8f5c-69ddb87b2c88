package com.howbuy.crm.cgi.extservice.factory.balance;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.utils.balance.BalanceUtils;
import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.BalancePortfolioDetail;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.AcctBalanceDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.BalanceBeanDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryBalanceDTO;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

//@Component
//@Slf4j
//@FundTypeSupport(HkCustBalanceDisPalyCategoryEnum.FULL_BALANCE)
public class FullPortfolioStrategy extends AbstractBalancePortfolioStrategy implements BalancePortfolioStrategy<BalancePortfolioDetail> {

    @Override
    public BalancePortfolioDetail calculate(BalanceContent content) {
        // 获取非全委的基金交易账号
        List<HkFundTxAcctDTO> fundTxAcctNoDTOList = content.getFundTxAcctNoDTOList();
        if(CollectionUtils.isEmpty(fundTxAcctNoDTOList)){
            throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(),"没有全委的基金交易账号");
        }
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = content.getFundTxAcctNoDTOList().stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.FULL.getCode().equals(m.getFundTxAccType()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(hkFundTxAcctDTOS)){
            throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(),"没有全委的基金交易账号");
        }
        List<String> fundTxAcctNoList = BalanceUtils.extractFieldList(hkFundTxAcctDTOS, HkFundTxAcctDTO::getFundTxAcctNo);

        // 获取储蓄罐基金
        List<FundBasicInfoDTO> fundBasicInfoDTOList = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList();
        List<String> piggyFundCodeList = BalanceUtils.extractFieldList(fundBasicInfoDTOList, FundBasicInfoDTO::getFundCode);

        QueryBalanceDTO queryBalanceDTO = new QueryBalanceDTO();
        queryBalanceDTO.setHkCustNo(content.getHkCustNo());
        queryBalanceDTO.setExcludeFundCodeList(piggyFundCodeList);
        queryBalanceDTO.setFundTxAcctNoList(fundTxAcctNoList);
        queryBalanceDTO.setDisPlayCurrency(content.getDisPlayCurrencyCode());

        AcctBalanceDTO acctBalanceDTO = queryBalanceOuterService.queryHkCustBalance(queryBalanceDTO);
        List<BalanceBeanDTO> balanceList = acctBalanceDTO.getBalanceList();

        // 计算总收益
        BigDecimal cxgTotalIncome = BalanceUtils.sumBigDecimal(balanceList, BalanceBeanDTO::getDisCurCurrentAssetCurrency);

        // 收益状态
        String incomeCalStat = BalanceUtils.calculateIncomeCalStat(balanceList);

        return BalancePortfolioDetail.builder()
                .totalAsset(acctBalanceDTO.getDisPlayCurrencyTotalMarketValue())
                .totalIncome(cxgTotalIncome)
                .incomeStatus(incomeCalStat)
                .itemList(balanceList)
                .build();
    }

    @Override
    public Class<BalancePortfolioDetail> getSupportedType() {
        return BalancePortfolioDetail.class;
    }
}