/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.traderecord;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.ParamsException;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.common.enums.contract.ContractFileBizTypeEnum;
import com.howbuy.crm.cgi.extservice.request.account.QueryTradeContractNewRequest;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * @description: 交易合同请求参数校验
 * @author: system
 * @date: 2025/7/3
 * @since JDK 1.8
 */
public class TradeContractValidator extends BasicDataTypeValidator {

    /**
     * 校验交易合同查询请求参数
     *
     * @param request 请求参数
     */
    public static void validatorParams(QueryTradeContractNewRequest request) {
        // 基础校验（使用@NotBlank等注解校验）
        validator(request);
        
        // 合同类型集合校验
        contractTypesValidation(request);
        
        // 时间范围校验
        timeRangeValidation(request);
    }

    /**
     * 校验合同类型集合参数
     *
     * @param request 请求参数
     */
    private static void contractTypesValidation(QueryTradeContractNewRequest request) {
        String contractTypes = request.getContractTypes();
        
        // 如果合同类型集合不为空，需要校验每个类型是否在枚举中存在
        if (StringUtils.isNotBlank(contractTypes)) {
            ContractFileBizTypeEnum enumByCode = ContractFileBizTypeEnum.getEnumByCode(contractTypes);
            if (enumByCode == null) {
                throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "合同类型不合法");
            }
        }
    }

    /**
     * 校验时间范围参数
     *
     * @param request 请求参数
     */
    private static void timeRangeValidation(QueryTradeContractNewRequest request) {
        String timeRangeType = request.getTimeRangeType();
        String startTime = request.getStartTime();
        String endTime = request.getEndTime();

        // 校验开始时间和结束时间：要么都不传，要么同时传
        boolean hasStartTime = StringUtils.isNotBlank(startTime);
        boolean hasEndTime = StringUtils.isNotBlank(endTime);

        if (hasStartTime != hasEndTime) {
            throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(),
                "开始时间和结束时间必须同时传入，不能只传一个");
        }

        // 如果传入了时间，则进行格式和逻辑校验
        if (hasStartTime && hasEndTime) {
            // 校验时间格式（yyyyMMdd）
            validateDateFormat(startTime, "开始时间");
            validateDateFormat(endTime, "结束时间");

            // 校验开始时间不能大于结束时间
            if (startTime.compareTo(endTime) > 0) {
                throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(),
                    "开始时间不能大于结束时间");
            }
        }

        // 如果时间范围类型为5（自定义时间范围），则开始时间和结束时间必须传入
        if ("5".equals(timeRangeType)) {
            if (!hasStartTime || !hasEndTime) {
                throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(),
                    "当时间范围类型为自定义时间范围时，开始时间和结束时间必须传入");
            }
        }

        // 校验时间范围类型是否合法（1-5）
        if (StringUtils.isNotBlank(timeRangeType)) {
            if (!Arrays.asList("1", "2", "3", "4", "5").contains(timeRangeType)) {
                throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(),
                    "时间范围类型不合法，支持的类型：1-近一月，2-近三个月，3-近六个月，4-近一年，5-自定义时间范围");
            }
        }
    }

    /**
     * 校验日期格式（yyyyMMdd）
     *
     * @param dateStr 日期字符串
     * @param fieldName 字段名称
     */
    private static void validateDateFormat(String dateStr, String fieldName) {
        if (StringUtils.isBlank(dateStr)) {
            return;
        }

        // 校验长度
        if (dateStr.length() != 8) {
            throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(),
                fieldName + "格式不正确，应为yyyyMMdd格式");
        }

        // 校验是否为数字
        if (!dateStr.matches("\\d{8}")) {
            throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(),
                fieldName + "格式不正确，应为yyyyMMdd格式");
        }

        // 简单校验年月日的合理性
        try {
            int year = Integer.parseInt(dateStr.substring(0, 4));
            int month = Integer.parseInt(dateStr.substring(4, 6));
            int day = Integer.parseInt(dateStr.substring(6, 8));

            if (year < 1900 || year > 2100) {
                throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(),
                    fieldName + "年份不合法");
            }

            if (month < 1 || month > 12) {
                throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(),
                    fieldName + "月份不合法");
            }

            if (day < 1 || day > 31) {
                throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(),
                    fieldName + "日期不合法");
            }
        } catch (NumberFormatException e) {
            throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(),
                fieldName + "格式不正确，应为yyyyMMdd格式");
        }
    }
}
