package com.howbuy.crm.cgi.extservice.common.enums;


/**
 * @description 邮箱业务场景枚举
 * <AUTHOR>
 * @date 2025-07-09 14:25:55
 * @since JDK 1.8
 */
public enum EmailBizScenarioEnum {
    // 1：用户没有维护过结单邮箱 2：用户有维护【一个】结单邮箱 3：用户有维护【多个】结单邮箱，且有和用户开户绑定的旧邮箱一致的邮箱 4：用户有维护【多个】结单邮箱，但没有和用户开户绑定的旧邮箱一致的邮箱
    NO_EMAIL_MAINTAINED("1", "用户没有维护过结单邮箱"),
    SINGLE_EMAIL_MAINTAINED("2", "用户有维护【一个】结单邮箱"),
    MULTIPLE_EMAIL_WITH_MATCHED("3", "用户有维护【多个】结单邮箱，且有和用户开户绑定的旧邮箱一致的邮箱"),
    MULTIPLE_EMAIL_WITHOUT_MATCHED("4", "用户有维护【多个】结单邮箱，但没有和用户开户绑定的旧邮箱一致的邮箱");

    private String code;
    private String description;

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    EmailBizScenarioEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

} 