/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/2/29 09:04
 * @since JDK 1.8
 */
@Setter
@Getter
public class AssetInfoVO extends AccountBaseVO implements Serializable {
    private static final long serialVersionUID = 3985795582320380449L;
    /** 基金名称 */
    private String fundName;

    /** 基金代码 */
    private String fundCode;

    /**
     * 参考市值
     */
    private String currencyMarketValue;

    /**
     * 币种描述 仅参考市值用
     */
    private String currencyDesc;

    /**
     * 币种 其余的市值 用
     */
    private String currency;

    /**
     * 参考净值
     */
    private String nav;

    /**
     * 净值日期 YYYY-MM-DD或者MM-DD
     */
    private String navDate;

    /**
     *  总实缴金额
     */
    private String paidInAmt;

    /**
     * 待投金额（当前币种）
     */
    private String currencyUnPaidInAmt;

    /**
     * 持仓份额
     */
    private String totalBalanceVol;

    /**
     * 当前收益率
     */
    private String dayIncomeRate;

    /**
     * 分红提醒标识 0:无需分红提醒;1:提醒收益偏差;2:提醒即将分红
     */
    private String navDivFlag;

    /**
     * 清算标识    0-否; 1-是
     */
    private String crisisFlag;

    /**
     * 千禧年产品标识 0-否; 1-是
     */
    private String qianXiFlag;

    /**
     * 是否分期成立 0-否; 1-是
     */
    private String stageEstablishFlag;

    /**
     * 是否NA产品
     */
    private String naFlag;

    /**
     * 累计应收管理费
     */
    private String receivManageFee;


    /**
     * 累计应收业绩报酬
     */
    private String receivPreformFee;

    /**
     * 产品费后参考市值
     */
    private String currencyMarketValueExFee;

    /**
     * 产品费后持仓收益
     */
    private String currentAssetCurrency;


    /**
     * 产品费后持仓收益率
     */
    private String yieldRate;

    /**
     * 产品费后持仓收益率 保留几位小数
     */
    private String yieldRateStr;


    /**
     * 平衡因子
     */
    private String balanceFactor;

    private String incomeCalStat;
    /**
     * 平衡因子转换完成 1-是 0-否
     */
    private String convertFinish;
    /**
     * 平衡因子日期
     */
    private String balanceFactorDate;

    /**
     * 买入子基金代码
     */
    private String buySubFundCode;

    /**
     * 是否可买入 1-可交易 2-不可交易 3-按钮不展示
     */
    private String isCanBuy;

    /**
     * 是否可卖出 1-可交易 2-不可交易 3-按钮不展示
     */
    private String isCanSell;


    /**
     * 不支持 买入Code
     */
    private String notCanBuyCode;

    /**
     * 不支持 卖出的Code
     */
    private String notCanSellCode;

    /**
     * 详情列表数据
     */
    private List<AssetDetailInfoVO> assetDetailInfoVOList;


    /**
     * 认缴总金额
     */
    private String subTotalAmt;

    /**
     * 实缴总金额
     */
    private String paidTotalAmt;

    /**
     * 实缴认缴百分比
     */
    private String paidSubTotalRatio;

    /**
     * 分次Call产品 1-是 0-否
     */
    private String gradationCall;


    /*********************海外需求3.0页面改版 新增字段***********************************/

    /**
     * 产品业务类型 1 海外公募   2 海外私募
     */
    private String productBusiType;

    /**
     * 产品业务子类型 1 阳光私募  2 私募股权
     */
    private String productBusiSubType;

    /**
     * 最新收益
     */
    private String latestIncome;

    /**
     * 最新收益时间
     */
    private String latestIncomeDate;

    /**
     * 最新收益率
     */
    private String latestIncomeRate;

    /**
     * 累计收益
     */
    private String accumIncome;

    /**
     * 单位成本
     */
    private String unitCost;

    /**
     * 累计总回款
     */
    private String accumTotalBack;

    /**
     * 回款比例
     */
    private String accumBackRatio;


    /**
     * 投资总成本
     */
    private String totalInvestCost;


    /**
     * 是否母子基金、 1 是 0 否
     */
    private String motherChildFund;

    /**
     * 初始投资成本
     */
    private String initInvestCost;

    /**
     * 产品期限说明
     */
    private String productTermDes;

    /**
     * 产品期限说明编码(非交易拼接) 例如 4+2+1年
     */
    private String productTermCode;


    /**
     * 补充协议个数
     */
    private String supplementalAgreementCount;

    /**
     * 补充协议产品
     */
    private String  supplementalAgreementFundCode;

}