package com.howbuy.crm.cgi.extservice.controller.hk.papersign;

/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.papersign.QueryPaperSignDetailRequest;
import com.howbuy.crm.cgi.extservice.request.papersign.SubmitPaperSignRequest;
import com.howbuy.crm.cgi.extservice.service.papersign.PaperSignService;
import com.howbuy.crm.cgi.extservice.vo.papersign.QueryPaperSignDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 纸质签名控制器
 * @author: 陈杰文
 * @date: 2025-06-17 16:54:24
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hk/papersign")
@Slf4j
public class PaperSignController {

    @Autowired
    private PaperSignService paperSignService;

    /**
     * @api {POST} /ext/hk/papersign/querypapersigndetail queryPaperSignDetail()
     * @apiVersion 1.0.0
     * @apiGroup PaperSignController
     * @apiName queryPaperSignDetail()
     * @apiDescription 查询纸质签名详情接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号 必传
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.uploadTime 上传时间 yyyy-mm-dd hh:mm
     * @apiSuccess (响应结果) {Object} data.signFileVO 签名文件
     * @apiSuccess (响应结果) {String} data.signFileVO.id 文件ID
     * @apiSuccess (响应结果) {String} data.signFileVO.url 图片地址
     * @apiSuccess (响应结果) {String} data.signFileVO.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.signFileVO.fileName 文件名称
     * @apiSuccess (响应结果) {String} data.signFileVO.fileFormatType 文件类型
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"A","data":{"uploadTime":"2025-06-17 16:54","signFileVO":{"id":"123","url":"https://example.com/sign.jpg","thumbnailUrl":"https://example.com/sign_thumb.jpg","fileName":"signature.jpg","fileFormatType":"jpg"}},"description":"操作成功","timestampServer":"2025-06-17 16:54:24"}
     */
    @PostMapping("/querypapersigndetail")
    public CgiResponse<QueryPaperSignDetailVO> queryPaperSignDetail(@RequestBody QueryPaperSignDetailRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        
        // 调用服务层查询纸质签名详情
        QueryPaperSignDetailVO result = paperSignService.queryPaperSignDetail(request);
        
        return CgiResponse.ok(result);
    }

    /**
     * @api {POST} /ext/hk/papersign/submit submitPaperSign()
     * @apiVersion 1.0.0
     * @apiGroup PaperSignController
     * @apiName submitPaperSign()
     * @apiDescription 提交纸质签名接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号 必传
     * @apiParam (请求体) {String} fileName 文件名称 必传
     * @apiParam (请求体) {String} thumbnailUrl 缩略图地址 必传
     * @apiParam (请求体) {String} fileFormatType 文件类型 必传
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","fileName":"signature.jpg","thumbnailUrl":"https://example.com/sign_thumb.jpg","fileFormatType":"jpg"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"A","description":"操作成功","timestampServer":"2025-06-17 17:02:15"}
     */
    @PostMapping("/submit")
    public CgiResponse<Body> submitPaperSign(@RequestBody SubmitPaperSignRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        
        // 调用服务层提交纸质签名
        paperSignService.submitPaperSign(request);
        
        return CgiResponse.ok(new Body());
    }
} 