/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils.balance;

import com.howbuy.crm.cgi.extservice.common.enums.balance.AssetIncomeStatusEnum;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.BalanceBeanDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:  资产计算工具类
 * <AUTHOR>
 * @date 2025/6/23 19:04
 * @since JDK 1.8
 */
public class BalanceUtils {

    /**
     * @description: 求和计算
     * @param list	数据列表
     * @param fieldExtractor 属性获取器
     * @return java.math.BigDecimal
     * @author: jinqing.rao
     * @date: 2025/6/23 19:36
     * @since JDK 1.8
     */
    public static <T> BigDecimal sumBigDecimal(List<T> list, Function<T, BigDecimal> fieldExtractor) {
        if (CollectionUtils.isEmpty(list)) {
            return BigDecimal.ZERO;
        }
        return list.stream()
                .map(bean -> Optional.ofNullable(fieldExtractor.apply(bean)).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 通用字段提取方法
     */
    public static <T, R> List<R> extractFieldList(List<T> dataList, Function<T, R> fieldExtractor) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }
        return dataList.stream()
                .map(fieldExtractor)
                .collect(Collectors.toList());
    }



    /**
     * @description: 计算收益状态
     * @param list 数据列表
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/6/23 19:57
     * @since JDK 1.8
     */
    public static String calculateIncomeCalStat(List<BalanceBeanDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return AssetIncomeStatusEnum.CALCULATING.getCode();
        }
        // 收益状态
        boolean hasCalculatingOrBlank = list.stream().anyMatch(balance ->
                StringUtils.isBlank(balance.getIncomeCalStat()) ||
                        AssetIncomeStatusEnum.CALCULATING.getCode().equals(balance.getIncomeCalStat()));
        return hasCalculatingOrBlank ? AssetIncomeStatusEnum.CALCULATING.getCode() : AssetIncomeStatusEnum.CALCULATED.getCode();
    }
}
