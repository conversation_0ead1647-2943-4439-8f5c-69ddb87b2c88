package com.howbuy.crm.cgi.extservice.vo.balance;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 查询总资产接口出参
 * @author: jinqing.rao
 * @date: 2025/6/17 9:46
 * @since JDK 1.8
 */
@Builder
@Getter
public class QueryTotalAssetV1VO extends Body implements Serializable{

    private static final long serialVersionUID = 3146498866439960856L;
    /**
     * 总资产
     */
    private String totalAsset;

    /**
     * 是否展示资产 0:隐藏, 1: 显示
     */
    private String showAsset;

    /**
     * 买入待确认金额(之前的在途金额)
     */
    private String inTransitTradeAmt;

    /**
     * 在途交易笔数
     */
    private String inTransitTradeCount;

    /**
     * 在途交易订单号
     */
    private String inTransitTradeDealNo;

    /**
     * 在途交易资金笔数
     */
    private String inTransitTradeCapitalCount;

    /**
     * 在途交易资金订单号
     */
    private String inTransitTradeCapitalDealNo;

    /**
     * 在途打款凭证笔数
     */
    private String inTransitPayVoucherCount;

    /**
     * 在途打款凭证编号
     */
    private String inTransitPayVoucherNo;

    /**
     * 在途资金打款凭证上限
     */
    private String inTransitCashPayVoucherLimit;

    /**
     * 在途提款申请笔数
     */
    private String inTransitDrawMoneyAppCount;

    /**
     * 在途提款申请编号
     */
    private String inTransitDrawMoneyAppNo;

    /**
     * 在途提款资金笔数
     */
    private String inTransitDrawMoneyCapitalCount;

    /**
     * 在途提款资金编号
     */
    private String inTransitDrawMoneyCapitalNo;

    /**
     * 基金持仓资产
     */
    private String fundAsset;

    /**
     * 基金持仓收益
     */
    private String fundIncome;

    /**
     * 基金持仓收益计算状态 0-计算中;1-计算成功
     */
    private String fundIncomeCalStatus;

    /**
     * 储蓄罐资产
     */
    private String piggyAsset;

    /**
     * 储蓄罐收益
     */
    private String piggyIncome;

    /**
     * 储蓄罐收益计算状态 0-计算中;1-计算成功
     */
    private String piggyIncomeCalStatus;

    /**
     * 储蓄罐签约状态 0-未签约1-已签约2-已暂停
     */
    private String piggySignStatus;

    /**
     * 全委基金持仓资产
     */
    private String fullFundAsset;

    /**
     * 全委基金持仓收益
     */
    private String fullFundIncome;

    /**
     * 全委基金持仓收益计算状态 0-计算中;1-计算成功
     */
    private String fullFundIncomeCalStatus;

    /**
     * 存在NA产品 0-不存在、1-存在
     */
    private String existsNaFund;

    /**
     * 现金余额资产
     */
    private String cashBalanceAsset;

    /**
     * 系统日期 yyyyMMDD
     */
    private String systemDate;

    @Setter
    @Getter
    public static class HkBalanceSupplementalAgreementInfoVO implements Serializable {

        private static final long serialVersionUID = 6965880055277452764L;
        /**
         * 协议ID
         */
        private String agreementId;

        /**
         * 基金代码
         */
        private String fundCode;
    }
} 