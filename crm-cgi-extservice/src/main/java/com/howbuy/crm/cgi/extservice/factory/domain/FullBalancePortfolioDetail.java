package com.howbuy.crm.cgi.extservice.factory.domain;

import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.BalanceBeanDTO;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 全委基金持仓资产页详情
 * @author: 陈杰文
 * @date: 2025-07-03 13:11:28
 * @since JDK 1.8
 */
@Getter
@Builder
public class FullBalancePortfolioDetail implements PortfolioDetail {

    /**
     * 总资产
     */
    private BigDecimal totalAsset;
    /**
     * 总收益
     */
    private BigDecimal totalIncome;

    /**
     * 买入待确认金额(之前的在途金额)
     */
    private BigDecimal inTransitTradeAmt;

    /**
     * 股权总回款
     */
    private BigDecimal totalCashCollection;

    /**
     * 收益状态 0-计算中;1-计算成功
     */
    private String incomeStatus;

    /**
     * 明细列表
     */
    private List<BalanceBeanDTO> itemList;
}