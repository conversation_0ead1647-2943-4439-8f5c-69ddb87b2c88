package com.howbuy.crm.cgi.extservice.common.enums.balance;

import lombok.Getter;

@Getter
public enum HkCustBalanceDisPalyCategoryEnum {

    ALL_BALANCE("ALL_BALANCE","全部资产"),

    ALL_IN_TRANSIT("ALL_IN_TRANSIT","所有在途"),

    CASH_BALANCE("CASH_BALANCE","现金资产"),

    NON_FULL_FUND_BALANCE("NON_FULL_FUND_BALANCE","非全委总资产"),

    NON_FULL_FUND_BALANCE_IN_TRANSIT("NON_FULL_FUND_BALANCE_IN_TRANSIT","非全委在途"),

    FULL_BALANCE("FULL_BALANCE","全委总资产"),

    FULL_IN_TRANSIT("FULL_IN_TRANSIT","全委在途"),

    NON_FULL_PIGGY("NON_FULL_PIGGY","非全委储蓄罐"),

    NON_FULL_PIGGY_IN_TRANSIT("NON_FULL_PIGGY_IN_TRANSIT","非全委储蓄罐在途")
    ;


    private final String code;
    private final String description;

    HkCustBalanceDisPalyCategoryEnum(String code,String description) {
        this.code = code;
        this.description = description;
    }

}