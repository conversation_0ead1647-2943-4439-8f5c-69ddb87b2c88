package com.howbuy.crm.cgi.extservice.common.enums.contract;

public enum ContractFileBizTypeEnum {

    // // 1.交易下单 2.补签协议 3.储蓄罐签署/变更文件 4.开户文件
    TRADING_ORDER("1", "交易下单"),
    RE_SIGN_CONTRACT("2", "补签协议"),
    PIGGY_SIGN_FILE("3", "储蓄罐签署/变更文件"),
    OPEN_ACCOUNT_FILE("4", "开户文件");

    private final String code;

    private final String desc;

    ContractFileBizTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(String code) {
        for (ContractFileBizTypeEnum value : ContractFileBizTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

    /**
     * @description: 通过Code获取对应的枚举
     * @param code
     * @return com.howbuy.crm.cgi.extservice.common.enums.contract.ContractFileBizTypeEnum
     * @author: jinqing.rao
     * @date: 2025/7/3 15:35
     * @since JDK 1.8
     */
    public static ContractFileBizTypeEnum getEnumByCode(String code) {
        for (ContractFileBizTypeEnum value : ContractFileBizTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
