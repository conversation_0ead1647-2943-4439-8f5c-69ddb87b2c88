package com.howbuy.crm.cgi.extservice.request.balance;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: 查询基金资产详情请求类
 * @author: 陈杰文
 * @date: 2025-06-17 15:00:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundAssetDetailRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 基金代码
     */
    @NotBlank(message = "基金代码不能为空")
    private String fundCode;

    /**
     * 当前展示的币种代码
     */
    @NotBlank(message = "当前展示的币种代码不能为空")
    private String disPlayCurrencyCode;
} 