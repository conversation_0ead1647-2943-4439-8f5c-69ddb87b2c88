package com.howbuy.crm.cgi.extservice.vo.hkfund;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class HKPurchaseFundPaymentVoucherRecordVO extends Body implements Serializable {
    private static final long serialVersionUID = 7768037814000588146L;

    /**
     * 银行卡号掩码
     */
    private String bankAcctMask;

    /**
     * 银行图标的url
     */
    private String bankLogoUrl;

    /**
     * 银行中文名称(没有获取英文名称)
     */
    private String bankName;

    /**
     *  汇款资金账号
     */
    private String remitCpAcctNo;

    /**
     * 汇款币种代码
     */
    private String remitCurCode;

    /**
     * 汇款币种描述
     */
    private String remitCurCodeDesc;

    /**
     * 汇款金额
     */
    private String remitAmt;


    /**
     * 备注
     */
    private String remark;


    /**
     * 资料id
     */
    private String orderId;

    /**
     * 文件类型id
     */
    private String fileTypeId;


    /**
     * 打款凭证来源 1 : 线上  0：线下
     */
    private String payVoucherSource;

    /**
     * 打款凭证状态  1-未上传、2-已上传、3-审核通过、4-审核不通过
     */
    private String payVoucherState;

    /**
     * 审核意见
     */
    private String auditOpinion;

    /**
     * 退回原因
     */
    private String returnReason;

    /**
     * 打款凭证图片列表
     */
    private List<HKFileVO> payVoucherImages;

    /**
     * swiftCode
     */
    private String swiftCode;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 打款凭证号
     */
    private String voucherNo;

    /**
     * 审核原因
     */
    private List<HkOpenAcctCheckVO> checkResult;

    /**
     * 是否重复打款凭证 1 是  0 否
     */
    private String duplicatePayment;

    /**
     * 订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
     */
    private String orderStatus;


    @Setter
    @Getter
    public static class VoucherAuditResultVO implements Serializable{

        private static final long serialVersionUID = 631044382370981889L;

        /**
         * 资金账号错误描述字段
         */
        private String bankAcctMask;

        /**
         * 货币错误字段
         */
        private String remitCurrency;

        /**
         * 申请金额错误字段
         */
        private String remitAmt;


        /**
         * 备注错误字段描述
         */
        private String remark;

        /**
         * 图片错误字段描述
         */
        private String payVoucherImages;
    }
}
