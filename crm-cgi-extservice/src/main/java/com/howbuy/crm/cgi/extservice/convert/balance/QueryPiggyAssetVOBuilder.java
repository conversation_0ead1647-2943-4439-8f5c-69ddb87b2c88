/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.balance;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.BigDecimalUtils;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggySignStatusEnum;
import com.howbuy.crm.cgi.extservice.factory.domain.AllInTransitTradeBalancePortfolioDetail;
import com.howbuy.crm.cgi.extservice.factory.domain.PiggyBalancePortfolio;
import com.howbuy.crm.cgi.extservice.vo.balance.PiggyAssetFundListVO;
import com.howbuy.crm.cgi.extservice.vo.balance.QueryPiggyAssetVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.PiggyAgreementSignDetailDTO;

import java.math.RoundingMode;
import java.util.List;

/**
 * @description: 查询储蓄罐资产响应构建者
 * <AUTHOR>
 * @date 2025/6/24 16:32
 * @since JDK 1.8
 */
public class QueryPiggyAssetVOBuilder {
    private final QueryPiggyAssetVO vo;

    public QueryPiggyAssetVOBuilder() {
        this.vo = new QueryPiggyAssetVO();
    }

    /**
     * 设置是否显示资产（资产小眼睛）
     * @param showAsset 是否显示资产
     * @return QueryPiggyAssetVOBuilder
     */
    public QueryPiggyAssetVOBuilder setShowAsset(String showAsset) {
        vo.setShowAsset(showAsset);
        return this;
    }

    /**
     * 设置储蓄罐基金组合信息
     * @param portfolioDetail 储蓄罐余额组合详情
     * @return QueryPiggyAssetVOBuilder
     */
    public QueryPiggyAssetVOBuilder setPiggyPortfolio(PiggyBalancePortfolio portfolioDetail) {
        if (portfolioDetail != null) {
            String totalAssetStr = BigDecimalUtils.bigDecimalToString(portfolioDetail.getTotalAsset(), 2, RoundingMode.DOWN);
            // 储蓄罐资产
            vo.setPiggyAsset(totalAssetStr);
            // 储蓄罐收益
            vo.setPiggyIncome(BigDecimalUtils.bigDecimalToString(portfolioDetail.getTotalIncome(),2, RoundingMode.DOWN));
            // 储蓄罐收益计算状态
            vo.setPiggyIncomeCalStatus(portfolioDetail.getIncomeStatus());
            // 储蓄罐在途交易金额
            vo.setPiggyInTransitTradeAmt(BigDecimalUtils.bigDecimalToString(portfolioDetail.getPiggyInTransitTradeAmt(), 2, RoundingMode.DOWN));
            // 储蓄罐签约状态
            vo.setPiggySignStatus(portfolioDetail.getPiggySignStatus());
        }
        return this;
    }

    /**
     * 设置储蓄罐在途交易详情
     * @param inTransitDetail 在途交易余额组合详情
     * @return QueryPiggyAssetVOBuilder
     */
    public QueryPiggyAssetVOBuilder setInTransitDetail(AllInTransitTradeBalancePortfolioDetail inTransitDetail) {
        if (inTransitDetail != null) {
            // 储蓄罐在途交易笔数
            vo.setPiggyInTransitTradeCount(inTransitDetail.getInTransitTradeCount());
            // 储蓄罐在途交易订单号
            vo.setPiggyInTransitTradeDealNo(inTransitDetail.getInTransitTradeDealNo());
            // 储蓄罐在途交易资金笔数
            vo.setPiggyInTransitTradeCapitalCount(inTransitDetail.getInTransitTradeCapitalCount());
            // 储蓄罐在途交易资金订单号
            vo.setPiggyInTransitTradeCapitalDealNo(inTransitDetail.getInTransitTradeCapitalDealNo());
        }
        return this;
    }

    /**
     * 设置储蓄罐协议签署信息
     * @param piggyAgreement 储蓄罐协议签署详情
     * @return QueryPiggyAssetVOBuilder
     */
    public QueryPiggyAssetVOBuilder setPiggySignInfo(PiggyAgreementSignDetailDTO piggyAgreement) {
        if (piggyAgreement != null) {
            // 是否在有效期内
            boolean futureDate = DateUtils.isCurrentDateInRange(piggyAgreement.getAgreementSignDt(),piggyAgreement.getAgreementSignExpiredDt());
            if(!futureDate){
                throw new BusinessException(ExceptionCodeEnum.NO_EFFECTIVE_PIGGY_ERROR);
            }
            // 设置签署的储蓄罐基金
            vo.setPiggySignFundCode(piggyAgreement.getPiggyFundCode());

            vo.setPiggySignStatus(futureDate ? PiggySignStatusEnum.SIGNED.getCode() : PiggySignStatusEnum.TERMINATED.getCode());
        }
        return this;
    }

    /**
     * 设置储蓄罐基金持仓列表
     * @param piggyAssetFundList 储蓄罐基金持仓列表
     * @return QueryPiggyAssetVOBuilder
     */
    public QueryPiggyAssetVOBuilder setPiggyAssetFundList(List<PiggyAssetFundListVO> piggyAssetFundList) {
        vo.setPiggyAssetFundListVO(piggyAssetFundList);
        return this;
    }

    /**
     * 构建QueryPiggyAssetVO对象
     * @return QueryPiggyAssetVO
     */
    public QueryPiggyAssetVO build() {
        return vo;
    }
}
