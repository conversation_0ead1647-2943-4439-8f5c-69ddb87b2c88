package com.howbuy.crm.cgi.extservice.vo.hkaccount;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 客户状态查询响应类
 * @author: 陈杰文
 * @date: 2025-06-17 11:11:28
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryCustStatusVO extends AccountBase<PERSON> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户状态 0-正常 1-注销 2-冻结 3-注销 4-开户流程成功
     */
    private String custState;

    /**
     * 开户入金状态
     * 01-去开户；02-继续开户；03-查看开户进度；04-修改开户资料；
     * 05-去入金；06-查看入金进度；07- 修改入金资料；08-隐藏开户入金区域
     */
    private String openDepositsStatus;

    /**
     * 开户步骤标识
     * 01-证件信息页；02-个人信息页；03-职业信息页；04-声明信息页；
     * 05-投资经验页；06-银行卡页；07-电子签名页
     */
    private String openAcctStepFlag;

    /**
     * 登录是否激活 1:是 0:否
     */
    private String loginActivate;

    /**
     * 交易是否激活 1:是 0:否
     */
    private String transactionActivation;

    /**
     * 绑卡数量
     */
    private String bindCardCount;
} 