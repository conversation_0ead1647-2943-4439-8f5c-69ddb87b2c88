/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.hkaccount;

import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.FileTypeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.CommonRequestUtil;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.*;
import com.howbuy.crm.cgi.extservice.common.enums.contract.ContractFileBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HKFundOrderStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggyProtocolSignTypeEnum;
import com.howbuy.crm.cgi.extservice.common.utils.AppEncUtils;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.common.utils.file.FileUploadUtils;
import com.howbuy.crm.cgi.extservice.request.account.QueryTradeContractNewRequest;
import com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct.OpenAcctService;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ContractFileVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenCustFileVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenFileVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.ContractInfoNewVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.ContractInfoVO;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggySignContractRecordVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.agreement.SupplementalAgreementDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.HkFundOrderContractResponseDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.HkFundOrderContractSignDtlDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.HkPiggyAgreementDealResponseDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.HkFundOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.agreement.SupplementalAgreementOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkPiggyBankOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkTradeContractOuterService;
import com.howbuy.dtms.common.enums.TradeChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description: (交易合同服务类)
 * @date 2024/2/26 10:22
 * @since JDK 1.
 */
@Slf4j
@Service
public class HkTradeContractService {

    @Autowired
    private HkCustInfoOuterService hkCustInfoOuterService;

    @Autowired
    private HkTradeContractOuterService hkTradeContractOuterService;

    @Autowired
    private OpenAcctService openAcctService;

    @Resource
    private HkFundOuterService hkFundOuterService;

    @Resource
    private QueryFundBasicInfoOuterService fundBasicInfoOuterService;

    @Resource
    private SupplementalAgreementOuterService supplementalAgreementOuterService;

    @Resource
    private HkPiggyBankOuterService hkPiggyBankOuterService;


    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.ContractInfoVO
     * @description:(查询合同文件数据)
     * @author: xufanchao
     * @date: 2024/2/26 13:07
     * @since JDK 1.8
     */
    public ContractInfoVO getTradeContract(String fundCode) {
        ContractInfoVO contractInfoVO = new ContractInfoVO();
        // 小程序隐藏签约列表
        boolean wxLogin = CommonRequestUtil.isWXLogin();
        if (wxLogin) {
            return contractInfoVO;
        }
        contractInfoVO.setIsSatisfy(YesNoEnum.NO.getCode());
        // 只满足 完成开户 且 开户方式为线上开户 显示 开户文件查询
        // 只满足 开户 且 开户方式为线上开户 并且绑定了一账通号 显示 产品合同文件查询
        String hkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
        boolean isOpenAcc = false;
        // 已完成开户
        if (hkCustInfo.getCustState().equals(CustStateEnum.NORMAL.getKey())) {
            // 完成开户线上开户 的数据
            String openType = hkCustInfo.getOpenType();
            if (StringUtils.isNotBlank(openType) && YesNoEnum.YES.getCode().equals(openType)) {
                isOpenAcc = true;
            }
            // 不是线上开户 并且 一账通号不为空 ==> 只展示 产品合同文件
            if (!isOpenAcc && !Objects.isNull(hkCustInfo.getHboneNo())) {
                contractInfoVO.setIsSatisfy(YesNoEnum.YES.getCode());
                contractInfoVO.setFileType(ContractFileTypeEnum.PRODUCT_CONTRACT_FILE.getCode());
                // 从海外中台获取产品合同文件
                getFundContractFileVO(fundCode, hkCustNo, contractInfoVO);
            }
            // 是线上开户，且 一账通号为空 ==> 只展示 开户文件
            else if (isOpenAcc && Objects.isNull(hkCustInfo.getHboneNo())) {
                contractInfoVO.setIsSatisfy(YesNoEnum.YES.getCode());
                contractInfoVO.setFileType(ContractFileTypeEnum.OPEN_ACC_FILE.getCode());
                OpenCustFileVO openCustFileVO = openAcctService.getOpenCustFileVO();
                contractInfoVO.setOpenFileList(openCustFileVO.getOpenFileVOList());
                contractInfoVO.setEbrokerID(openCustFileVO.getEbrokerID());
                contractInfoVO.setEmailMask(openCustFileVO.getEmailMask());
            }
            // 是线上开户，且 一账通号不为空 ==> 展示所有
            else if (isOpenAcc) {
                OpenCustFileVO openCustFileVO = openAcctService.getOpenCustFileVO();
                // 从海外中台获取产品合同文件
                getFundContractFileVO(fundCode, hkCustNo, contractInfoVO);
                contractInfoVO.setOpenFileList(openCustFileVO.getOpenFileVOList());
                contractInfoVO.setEbrokerID(openCustFileVO.getEbrokerID());
                contractInfoVO.setEmailMask(openCustFileVO.getEmailMask());
                contractInfoVO.setIsSatisfy(YesNoEnum.YES.getCode());
            }
        }
        return contractInfoVO;
    }

    /**
     * @param request 查询合同文件请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.ContractInfoNewVO
     * @description: 查询合同文件数据（新版本）
     * @author: system
     * @date: 2025/7/3
     * @since JDK 1.8
     */
    public ContractInfoNewVO getTradeContractNew(QueryTradeContractNewRequest request) {
        // 将时间范围枚举转换成时间
        if (StringUtils.isNotBlank(request.getTimeRangeType())) {
            request.setStartTime(DateUtils.getStrNextMonth(Integer.parseInt(request.getTimeRangeType())));
            request.setEndTime(DateUtils.getCurrentDate(DateUtils.YYYYMMDD));
        }
        ContractInfoNewVO contractInfoNewVO = new ContractInfoNewVO();
        String contractTypes = request.getContractTypes();
        // 不指定合同类型查询所有合同, 开户合同,补签协议合同，储蓄罐合同，交易合同
        if (StringUtils.isNotBlank(contractTypes)) {
            // 根据合同类型查询合同文件
            getContractInfoNewVOByType(request, contractTypes, contractInfoNewVO);
            // 根据时间范围检索
            sortByTimeRange(request, contractInfoNewVO);
            return contractInfoNewVO;
        }
        // 交易合同文件
        List<ContractFileVO> contractFileVOList = getTradeContractFileVOS(request.getFundCode(), request.getHkCustNo());

        // 添加补充协议的文件
        List<ContractFileVO> supplementalAgreementContractFileList = getSupplementalAgreementContractFileList(request.getHkCustNo(), request.getFundCode());

        // 交易合同文件+补充协议文件 聚合排序
        tradeContractFileComparatorSort(contractInfoNewVO, contractFileVOList, supplementalAgreementContractFileList);

        // 查询开户文件
        getOpenCustFile(contractInfoNewVO);

        // 获取储蓄罐相关文件
        getPiggyFileList(request, contractInfoNewVO);

        // 如果设计时间范围检索, 则查询指定时间段的交易合同文件
        sortByTimeRange(request, contractInfoNewVO);
        return contractInfoNewVO;
    }

    private void getContractInfoNewVOByType(QueryTradeContractNewRequest request, String contractTypes, ContractInfoNewVO contractInfoNewVO) {
        switch (Objects.requireNonNull(ContractFileBizTypeEnum.getEnumByCode(contractTypes))) {
            case TRADING_ORDER:
                // 获取交易合同文件
                getCustTradeContractFileVO(request, contractInfoNewVO);
                break;
            case RE_SIGN_CONTRACT:
                // 获取交易合同文件
                List<ContractFileVO> supplementalAgreementContractFileList = getSupplementalAgreementContractFileList(request.getHkCustNo(), request.getFundCode());
                tradeContractFileComparatorSort(contractInfoNewVO, new ArrayList<>(), supplementalAgreementContractFileList);
                break;
            case PIGGY_SIGN_FILE:
                // 查询用户所有的交易合同文件
                getPiggyFundTradeContractFile(request, contractInfoNewVO);
                // 获取储蓄罐相关文件
                getPiggyFileList(request, contractInfoNewVO);
                break;
            case OPEN_ACCOUNT_FILE:
                // 获取开户文件
                getOpenCustFile(contractInfoNewVO);
                break;
            default:
                throw new BusinessException(ExceptionCodeEnum.CONTRACT_FILE_BUSINESS_TYPE_NOT_EXIST);
        }
    }

    /**
     * @description: 获取用户的交易文件合同
     * @param request	
     * @param contractInfoNewVO
     * @return void
     * @author: jinqing.rao
     * @date: 2025/7/16 10:23
     * @since JDK 1.8
     */
    private void getCustTradeContractFileVO(QueryTradeContractNewRequest request, ContractInfoNewVO contractInfoNewVO) {
        List<ContractFileVO> contractFileVOList = getTradeContractFileVOS(request.getFundCode(), request.getHkCustNo());
        tradeContractFileComparatorSort(contractInfoNewVO, contractFileVOList, new ArrayList<>());
    }

    /**
     * @description: 根据时间范围检索
     * @param request	
     * @param contractInfoNewVO
     * @return void
     * @author: jinqing.rao
     * @date: 2025/7/3 17:26
     * @since JDK 1.8
     */
    private static void sortByTimeRange(QueryTradeContractNewRequest request, ContractInfoNewVO contractInfoNewVO) {
        if (StringUtils.isNotBlank(request.getStartTime()) && StringUtils.isNotBlank(request.getEndTime())) {
            ContractInfoNewVO.OpenContractFileVO openContractFileVO = contractInfoNewVO.getOpenContractFileVO();
            List<ContractFileVO> contractFileList = contractInfoNewVO.getContractFileList();
            List<ContractInfoNewVO.PiggySignContractInfo> openCustPiggyFileList = contractInfoNewVO.getOpenCustPiggyFileList();
            List<ContractInfoNewVO.PiggySignContractInfo> changeCustPiggyFileList = contractInfoNewVO.getChangeCustPiggyFileList();
            // 开户文件
            if (null != openContractFileVO && CollectionUtils.isNotEmpty(openContractFileVO.getOpenFileList())) {
                List<ContractInfoNewVO.OpenFileInfo> openFileInfos = openContractFileVO.getOpenFileList().stream().filter(it -> {
                    return DateUtils.isDateInRange(it.getUpDt(), request.getStartTime(), request.getEndTime(), DateUtils.YYYYMMDD);
                }).collect(Collectors.toList());
                openContractFileVO.setOpenFileList(openFileInfos);
                contractInfoNewVO.setOpenContractFileVO(openContractFileVO);
            }
            // 合同文件
            if (CollectionUtils.isNotEmpty(contractFileList)) {
                List<ContractFileVO> contractFileFilterList = contractFileList.stream().filter(it -> {
                    String signDate = DateUtils.formatDateStr(it.getSignDate(),  DateUtils.YYYYMMDD,DateUtils.YYYY_MM_DD_HH_MM);
                    return DateUtils.isDateInRange(signDate, request.getStartTime(), request.getEndTime(), DateUtils.YYYYMMDD);
                }).collect(Collectors.toList());
                contractInfoNewVO.setContractFileList(contractFileFilterList);
            }
            // 开通储蓄罐
            if (CollectionUtils.isNotEmpty(openCustPiggyFileList)) {
                List<ContractInfoNewVO.PiggySignContractInfo> openCustPiggyFileListFilter = openCustPiggyFileList.stream().filter(it -> {
                    String signDate = DateUtils.formatDateStr(it.getSignDate(),  DateUtils.YYYYMMDD,DateUtils.YYYY_MM_DD_HH_MM);
                    return DateUtils.isDateInRange(signDate, request.getStartTime(), request.getEndTime(), DateUtils.YYYYMMDD);
                }).collect(Collectors.toList());
                contractInfoNewVO.setOpenCustPiggyFileList(openCustPiggyFileListFilter);
            }
            // 储蓄罐变更
            if (CollectionUtils.isNotEmpty(changeCustPiggyFileList)) {
                List<ContractInfoNewVO.PiggySignContractInfo> changeCustPiggyFileListFilter = changeCustPiggyFileList.stream().filter(it -> {
                    String signDate = DateUtils.formatDateStr(it.getSignDate(),  DateUtils.YYYYMMDD,DateUtils.YYYY_MM_DD_HH_MM);
                    return DateUtils.isDateInRange(signDate, request.getStartTime(), request.getEndTime(), DateUtils.YYYYMMDD);
                }).collect(Collectors.toList());
                contractInfoNewVO.setChangeCustPiggyFileList(changeCustPiggyFileListFilter);
            }
        }
    }

    /**
     * @param contractInfoNewVO
     * @return void
     * @description: 获取开户文件
     * @author: jinqing.rao
     * @date: 2025/7/3 16:36
     * @since JDK 1.8
     */
    private void getOpenCustFile(ContractInfoNewVO contractInfoNewVO) {
        ContractInfoNewVO.OpenContractFileVO openContractFileVO = openAcctService.getOpenContractFileVO();
        contractInfoNewVO.setOpenContractFileVO(openContractFileVO);
    }

    /**
     * @param request           请求参数
     * @param contractInfoNewVO 响应对象
     * @description: 获取储蓄罐相关文件列表
     * @author: system
     * @date: 2025/7/3
     * @since JDK 1.8
     */
    private void getPiggyFileList(QueryTradeContractNewRequest request, ContractInfoNewVO contractInfoNewVO) {
        // 获取储蓄罐的签署和变更合同文件
        getPiggySignOrChangeContractFile(request, contractInfoNewVO);
    }

    /**
     * @description: 获取储蓄罐的签署和变更合同文件
     * @param request	
     * @param contractInfoNewVO
     * @return void
     * @author: jinqing.rao
     * @date: 2025/7/16 10:28
     * @since JDK 1.8
     */
    private void getPiggySignOrChangeContractFile(QueryTradeContractNewRequest request, ContractInfoNewVO contractInfoNewVO) {
        List<String> channelCodes = Arrays.asList(TradeChannelEnum.APP.getCode(), TradeChannelEnum.H5.getCode(), TradeChannelEnum.WAP.getCode());
        HkPiggyAgreementDealResponseDTO dealResponseDTO = hkPiggyBankOuterService.queryPiggySignContractRecord(request.getHkCustNo(), channelCodes, 1, 200);
        List<ContractInfoNewVO.PiggySignContractInfo> openCustPiggyFileList = new ArrayList<>();
        List<ContractInfoNewVO.PiggySignContractInfo> changeCustPiggyFileList = new ArrayList<>();
        if (null != dealResponseDTO && CollectionUtils.isNotEmpty(dealResponseDTO.getHkPiggyDealDTOList())) {
            for (HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO piggyAgreementDealDTO : dealResponseDTO.getHkPiggyDealDTOList()) {
                if (PiggyProtocolSignTypeEnum.AGREEMENT_SIGN_TYPE_ONLINE.getCode().equals(piggyAgreementDealDTO.getAgreementSignType())) {
                    ContractInfoNewVO.PiggySignContractInfo piggySignContractInfo = getPiggySignContractInfo(piggyAgreementDealDTO);
                    openCustPiggyFileList.add(piggySignContractInfo);
                }
                if (PiggyProtocolSignTypeEnum.AGREEMENT_SIGN_TYPE_FUND_CHANGE.getCode().equals(piggyAgreementDealDTO.getAgreementSignType())) {
                    ContractInfoNewVO.PiggySignContractInfo piggySignContractInfo = getPiggySignContractInfo(piggyAgreementDealDTO);
                    changeCustPiggyFileList.add(piggySignContractInfo);
                }
            }
        }
        contractInfoNewVO.setOpenCustPiggyFileList(openCustPiggyFileList);
        contractInfoNewVO.setChangeCustPiggyFileList(changeCustPiggyFileList);
    }

    /**
     * @description: 获取储蓄罐基金的交易合同
     * @param request	
     * @param contractInfoNewVO
     * @return void
     * @author: jinqing.rao
     * @date: 2025/7/16 10:27
     * @since JDK 1.8
     */
    private void getPiggyFundTradeContractFile(QueryTradeContractNewRequest request, ContractInfoNewVO contractInfoNewVO) {
        getCustTradeContractFileVO(request, contractInfoNewVO);
        // 查询所有的储蓄罐产品
        List<FundBasicInfoDTO> fundBasicInfoDTOS = fundBasicInfoOuterService.queryPiggyFundBasicInfoList();
        List<String> piggyFundCodeList = fundBasicInfoDTOS.stream().map(FundBasicInfoDTO::getFundCode).collect(Collectors.toList());
        List<ContractFileVO> contractFileList = contractInfoNewVO.getContractFileList();
        List<ContractFileVO> contractFileVOS = contractFileList.stream().filter(it -> piggyFundCodeList.contains(it.getProductCode())).collect(Collectors.toList());
        contractInfoNewVO.setContractFileList(contractFileVOS);
    }

    /**
     * @param m
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.ContractInfoNewVO.PiggySignContractInfo
     * @description: 储蓄罐文件转换
     * @author: jinqing.rao
     * @date: 2025/7/3 15:22
     * @since JDK 1.8
     */
    private static ContractInfoNewVO.PiggySignContractInfo getPiggySignContractInfo(HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO m) {
        ContractInfoNewVO.PiggySignContractInfo piggySignContractInfo = new ContractInfoNewVO.PiggySignContractInfo();
        piggySignContractInfo.setSignDate(DateUtils.formatDateStr(m.getAgreementSignDt(),DateUtils.YYYY_MM_DD_HH_MM,DateUtils.YYYYMMDD));
        PiggyProtocolSignTypeEnum piggyProtocolSignTypeEnum = PiggyProtocolSignTypeEnum.getByCode(m.getAgreementSignType());
        if (null != piggyProtocolSignTypeEnum) {
            piggySignContractInfo.setTypeName(piggyProtocolSignTypeEnum.getDisPlay());
        }
        if (CollectionUtils.isNotEmpty(m.getFilePathList())) {
            List<ContractInfoNewVO.PiggySignContractRecord> contractRecords = m.getFilePathList().stream().map(f -> {
                ContractInfoNewVO.PiggySignContractRecord piggySignContractRecord = new ContractInfoNewVO.PiggySignContractRecord();
                piggySignContractRecord.setUrl(f.getFilePathUrl());
                piggySignContractRecord.setFileName(f.getFileName());
                piggySignContractRecord.setFileType(f.getFileCode());
                piggySignContractRecord.setFileUrlType(FileDownLoadTypeEnum.STATIC_FILE_URL.getCode());
                return piggySignContractRecord;
            }).collect(Collectors.toList());
            piggySignContractInfo.setContractList(contractRecords);
        }
        return piggySignContractInfo;
    }

    /**
     * @return java.util.function.Predicate<com.howbuy.crm.cgi.manager.domain.hkacc.piggy.HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO>
     * @description: 筛选出来 线上签署的   储蓄罐变更的
     * @author: jinqing.rao
     * @date: 2025/7/3 14:57
     * @since JDK 1.8
     */
    private static Predicate<HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO> getPiggyAgreementDealDTOPredicate() {
        return piggyDealDTO -> PiggyProtocolSignTypeEnum.AGREEMENT_SIGN_TYPE_ONLINE.getCode().equals(piggyDealDTO.getAgreementSignType())
                || PiggyProtocolSignTypeEnum.AGREEMENT_SIGN_TYPE_FUND_CHANGE.getCode().equals(piggyDealDTO.getAgreementSignType());
    }

    /**
     * @param fundCode 基金代码
     * @param hkCustNo 香港客户号
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.hkaccount.ContractFileVO>
     * @description: 查询交易合同文件
     * @author: jinqing.rao
     * @date: 2025/7/3 14:24
     * @since JDK 1.8
     */
    private List<ContractFileVO> getTradeContractFileVOS(String fundCode, String hkCustNo) {
        // 复用原有逻辑，但需要根据新的过滤条件进行调整
        List<String> childFundCodes = new ArrayList<>();
        // 如果是母基金,获取对应的所有子基金Code
        if (StringUtils.isNotBlank(fundCode)) {
            childFundCodes = fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(fundCode);
            // 没有对应子基金编码，返回空
            if (CollectionUtils.isEmpty(childFundCodes)) {
                log.info("getFundContractFileVONew >>> 没有获取到对应的子基金信息, hkCustNo : {},mainFundCode :{}", hkCustNo, fundCode);
                return null;
            }
        }

        List<String> orderStatus = Arrays.asList(HKFundOrderStatusEnum.APPLY_SUCCESS.getCode(), HKFundOrderStatusEnum.PART_CONFIRM.getCode(),
                HKFundOrderStatusEnum.CONFIRM_SUCCESS.getCode());
        HkFundOrderContractResponseDTO contractResponseDTO = hkFundOuterService.queryHkFundOrderContractInfo(hkCustNo, childFundCodes, orderStatus);

        // 正常的交易文件
        return transToContractFileVO(contractResponseDTO, hkCustNo);
    }

    /**
     * 交易合同文件排序
     *
     * @param contractInfoNewVO
     * @param contractFileVOList                    交易合同
     * @param supplementalAgreementContractFileList 储蓄罐合同
     */
    private static void tradeContractFileComparatorSort(ContractInfoNewVO contractInfoNewVO, List<ContractFileVO> contractFileVOList, List<ContractFileVO> supplementalAgreementContractFileList) {
        List<ContractFileVO> contractTotalFileVOList = new ArrayList<>();

        // 添加开户文件合同
        if (CollectionUtils.isNotEmpty(contractFileVOList)) {
            contractTotalFileVOList.addAll(contractFileVOList);
        }

        // 添加补充协议合同
        if (CollectionUtils.isNotEmpty(supplementalAgreementContractFileList)) {
            contractTotalFileVOList.addAll(supplementalAgreementContractFileList);
        }

        if (CollectionUtils.isNotEmpty(contractTotalFileVOList)) {
            contractTotalFileVOList.sort((c1, c2) -> c2.getSignDate().compareTo(c1.getSignDate()));
            //文件排序，根据文件的类型 T > D > C 然后再子类升序 T1<T2<T3
            for (ContractFileVO contractFileVO : contractTotalFileVOList) {
                List<OpenFileVO> fileList = contractFileVO.getFileList();
                List<OpenFileVO> collect = fileList.stream().sorted(Comparator.comparing(OpenFileVO::getFileType, getStringComparator()))
                        .collect(Collectors.toList());
                contractFileVO.setFileList(collect);
            }
            contractInfoNewVO.setContractFileList(contractTotalFileVOList);
        }
    }


    /**
     * @param fundCode       基金编码
     * @param hkCustNo       香港客户号
     * @param contractInfoVO 合同文件
     * @return void
     * @description: 交易文件处理
     * @author: jinqing.rao
     * @date: 2025/7/3 15:23
     * @since JDK 1.8
     */
    private void getFundContractFileVO(String fundCode, String hkCustNo, ContractInfoVO contractInfoVO) {
        List<String> childFundCodes = new ArrayList<>();
        // 如果是母基金,获取对应的所有子基金Code
        if (StringUtils.isNotBlank(fundCode)) {
            childFundCodes = fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(fundCode);
            // 没有对应子基金编码，返回空
            if (CollectionUtils.isEmpty(childFundCodes)) {
                log.info("getFundContractFileVO >>> 没有获取到对应的子基金信息, hkCustNo : {},mainFundCode :{}", hkCustNo, fundCode);
                return;
            }
        }
        List<String> orderStatus = Arrays.asList(HKFundOrderStatusEnum.APPLY_SUCCESS.getCode(), HKFundOrderStatusEnum.PART_CONFIRM.getCode(),
                HKFundOrderStatusEnum.CONFIRM_SUCCESS.getCode());
        HkFundOrderContractResponseDTO contractResponseDTO = hkFundOuterService.queryHkFundOrderContractInfo(hkCustNo, childFundCodes, orderStatus);
        List<ContractFileVO> contractTotalFileVOList = new ArrayList<>();
        // 正常的开户文件
        List<ContractFileVO> contractFileVOList = transToContractFileVO(contractResponseDTO, hkCustNo);
        // 添加补充协议的文件
        List<ContractFileVO> supplementalAgreementContractFileList = getSupplementalAgreementContractFileList(hkCustNo, fundCode);

        // 添加开户文件合同
        if (CollectionUtils.isNotEmpty(contractFileVOList)) {
            contractTotalFileVOList.addAll(contractFileVOList);
        }

        // 添加补充协议合同
        if (CollectionUtils.isNotEmpty(supplementalAgreementContractFileList)) {
            contractTotalFileVOList.addAll(supplementalAgreementContractFileList);
        }
        if (CollectionUtils.isNotEmpty(contractTotalFileVOList)) {

            contractTotalFileVOList.sort((c1, c2) -> c2.getSignDate().compareTo(c1.getSignDate()));
            //文件排序，根据文件的类型 T > D > C 然后再子类升序 T1<T2<T3
            for (ContractFileVO contractFileVO : contractTotalFileVOList) {
                List<OpenFileVO> fileList = contractFileVO.getFileList();
                List<OpenFileVO> collect = fileList.stream().sorted(Comparator.comparing(OpenFileVO::getFileType, getStringComparator()))
                        .collect(Collectors.toList());
                contractFileVO.setFileList(collect);
            }
            contractInfoVO.setContractFileList(contractTotalFileVOList);
        }
    }

    /**
     * @param hkCustNo 香港客户号
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.hkaccount.ContractFileVO>
     * @description: 获取用户的补充协议信息
     * @author: jinqing.rao
     * @date: 2025/3/7 14:52
     * @since JDK 1.8
     */
    private List<ContractFileVO> getSupplementalAgreementContractFileList(String hkCustNo, String fundCode) {
        List<SupplementalAgreementDTO> supplementalAgreementDTOS = supplementalAgreementOuterService.querySupplementalAgreementList(hkCustNo,
                YesNoEnum.YES.getCode(), fundCode);
        if (CollectionUtils.isEmpty(supplementalAgreementDTOS)) {
            return new ArrayList<>();
        }
        return supplementalAgreementDTOS.stream().map(f -> {
            ContractFileVO contractFileVO = new ContractFileVO();
            contractFileVO.setProductCode(f.getFundCode());
            contractFileVO.setProductName(f.getFundName());
            String signDate = f.getSignDate();
            // 这里有两个时间格式
            String formatDateStr = DateUtils.formatDateStr(signDate.trim(), DateUtils.YYYY_MM_DD_HH_MM, DateUtils.YYYYMMDD_HHMMSS);
            if (StringUtils.isBlank(formatDateStr)) {
                formatDateStr = DateUtils.formatDateStr(signDate.trim(), DateUtils.YYYY_MM_DD_HH_MM, DateUtils.YYYYMMDDHHMMSS);
            }
            contractFileVO.setSignDate(formatDateStr);
            contractFileVO.setBusinessType(ContractFileBusinessTypeEnum.SIGN.getCode());
            contractFileVO.setBusinessTypeDesc(ContractFileBusinessTypeEnum.SIGN.getDisplayDesc());
            OpenFileVO openFileVO = new OpenFileVO(f.getAgreementName(), f.getAgreementUrl(), f.getAgreementName(), FileTypeEnum.ONLINE_SUPPLEMENTAL_AGREEMENT_TYPE.getKey(), FileDownLoadTypeEnum.STATIC_FILE_URL.getCode());
            contractFileVO.setFileList(Collections.singletonList(openFileVO));
            return contractFileVO;
        }).collect(Collectors.toList());
    }


    private static Comparator<String> getStringComparator() {
        return (s1, s2) -> {
            // 首先检查是否有一个或两个都是空值
            if (s1 == null && s2 == null) {
                return 0;
            } // 如果两者都为空，则认为相等
            if (s1 == null) {
                return 1;
            } // s1为空，s2非空，则s1"大于"s2，即s1排在s2之后
            if (s2 == null) {
                return -1;
            } // s2为空，s1非空，则s2"大于"s1，即s2排在s1之后
            // 首先按照fileType字段的首字母排序
            char fileType1 = s1.charAt(0);
            char fileType2 = s2.charAt(0);
            if (fileType1 != fileType2) {
                // 根据规则 T > D > C 排序
                return fileType2 - fileType1;
            } else {
                // 如果首字母相同，则按照子类型排序
                return s1.compareTo(s2);
            }
        };
    }

    private List<ContractFileVO> transToContractFileVO(HkFundOrderContractResponseDTO contractResponseDTO, String hkCustNo) {
        List<ContractFileVO> contractFileVOList = new ArrayList<>();
        if (null == contractResponseDTO || CollectionUtils.isEmpty(contractResponseDTO.getOrderContractSignVOList())) {
            return contractFileVOList;
        }
        contractResponseDTO.getOrderContractSignVOList().forEach(it -> {
            if (StringUtils.isBlank(it.getSignDate()) || CollectionUtils.isEmpty(it.getOrderContractSignDtlVOList())) {
                log.info("transToContractFileVO >>> 过滤没有合同信息的订单,hkCustNo:{},orderNo:{}", hkCustNo, it.getDealNo());
                return;
            }
            ContractFileVO contractFileVO = new ContractFileVO();
            contractFileVO.setProductName(it.getFundAbbr());
            contractFileVO.setProductCode(it.getFundCode());
            contractFileVO.setSignDate(DateUtils.formatDateStr(it.getSignDate(), DateUtils.YYYY_MM_DD_HH_MM, DateUtils.YYYY_MM_DD_HH_MM_SS));
            contractFileVO.setBusinessType(it.getMiddleBusiCode());
            contractFileVO.setBusinessTypeDesc(ContractFileBusinessTypeEnum.getDisplayDescByCode(it.getMiddleBusiCode()));
            List<OpenFileVO> openFileVOS = new ArrayList<>();
            List<HkFundOrderContractSignDtlDTO> contractSignDtlVOList = it.getOrderContractSignDtlVOList();
            contractSignDtlVOList.forEach(file -> {
                String fileUrlType = "1";
                String fileUrl = file.getFilePath();
                // 判断问价URL的类型,
                if (StringUtils.isNotBlank(file.getFileType())) {
                    List<String> typeLists = Arrays.asList(FileTypeEnum.PURCHASE_CONTRACT_TYPE_T1.getKey(), FileTypeEnum.PURCHASE_CONTRACT_TYPE_T2.getKey(), FileTypeEnum.SUB_PAID_CONTRACT_TYPE_T3.getKey());
                    if (typeLists.contains(file.getFileType())) {
                        fileUrlType = "2";
                        // 加密请求链接
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put(FileUploadUtils.FILE_NAME, file.getFileName());
                        jsonObject.put(FileUploadUtils.FILE_BIZ_TYPE, FileBizTypeEnum.FUND_CONTRACT_FILE.getCode());
                        jsonObject.put(FileUploadUtils.URL, file.getFilePath());
                        jsonObject.put(Constants.HKCUSTNO, hkCustNo);
                        fileUrl = AppEncUtils.encrypt(jsonObject.toJSONString()).replaceAll("\\s*", "");
                    }
                }
                openFileVOS.add(new OpenFileVO(file.getFileName(), fileUrl, file.getFileTypeDesc(), file.getFileType(), fileUrlType));
            });
            contractFileVO.setFileList(openFileVOS);
            contractFileVOList.add(contractFileVO);
        });
        return contractFileVOList;
    }
}