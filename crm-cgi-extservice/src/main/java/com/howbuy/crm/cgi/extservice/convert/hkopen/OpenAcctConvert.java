package com.howbuy.crm.cgi.extservice.convert.hkopen;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.common.utils.HkOpenAcctIdTypeUtils;
import com.howbuy.crm.cgi.extservice.common.enums.HkIdTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.*;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.common.utils.hkopen.HkOpenImageUrlUtils;
import com.howbuy.crm.cgi.extservice.request.account.OpenInvestRequest;
import com.howbuy.crm.cgi.extservice.request.account.RiskQuestionRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.HkOpenAcctProCityRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.bankinfo.HkOpenAcctBankRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.declare.HkOpenAcctDeclareRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.idInfo.HkAcctOpenIdInfoRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.occupation.HkOpenAcctOccupationRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.personalInfo.HkOpenPersonalInfoRequest;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.*;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkAcctAppPersonalCenterVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkCustFundHoldRiskLevelVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctAddressInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctProCitySaveVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.RiskQuestionnaireVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.bankinfo.HkOpenAcctBankInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.declare.HkOpenAcctDeclareVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.idinfo.HkAcctOpenIdDetailVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.investinfo.HkOpenAcctInvestVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.signnature.HkOpenAcctESignatureVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkAcctCountryDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkBankCardInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.*;
import com.howbuy.crm.cgi.manager.domain.ocr.IdentityCardOcrDTO;
import com.howbuy.crm.cgi.manager.enums.HkOpenAccountStepEnum;
import crm.howbuy.base.utils.DesUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 海外开户实名认证服务转换类
 * @date 2023/12/1 18:00
 * @since JDK 1.8
 */
public class OpenAcctConvert {

    /**
     * @param identityCardOcr 实名认证信息DTO
     * @param frontPictureUrl 照片的正面照片地址
     * @param backPictureUrl  照片的反面照片地址
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.IdentityCardVO
     * @description: 将IdentityCardOcrDTO转换成IdentityCardVO
     * @author: jinqing.rao
     * @date: 2023/12/1 18:02
     * @since JDK 1.8
     */
    public static IdentityCardVO toIdentityCardVO(IdentityCardOcrDTO identityCardOcr, ImageVO frontPictureUrl, ImageVO backPictureUrl) {
        IdentityCardVO.IdentityCardVOBuilder builder = IdentityCardVO.builder();
        if (null != identityCardOcr) {
            String expireTime = identityCardOcr.getExpireTime();
            expireTime = DateUtils.formatDateStr(expireTime, DateUtils.YYYYMMDD, DateUtils.YYYY_MM_DD);
            builder.idAlwaysValidFlag(identityCardOcr.getIdAlwaysValidFlag())
                    .custName(identityCardOcr.getUserName())
                    .idNo(identityCardOcr.getIdNo())
                    .expireTime(expireTime)
                    .frontImage(frontPictureUrl)
                    .backImage(backPictureUrl)
                    .idAddress(identityCardOcr.getAddress());
        }
        builder.frontImage(frontPictureUrl)
                .backImage(backPictureUrl);
        return builder.build();
    }


    /**
     * @param customerInfo             客户信息
     * @param hkOpenCustomerStatusEnum 客户用户状态枚举
     * @param hkBankCardInfoDTOList    客户银行卡的信息
     * @param hkOpenAccOrderInfoDTO    客户开户订单信息
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.PersonalCenterInfoVO
     * @description: 海外小程序, 客户个人中心信息VO
     * @author: jinqing.rao
     * @date: 2023/12/4 16:26
     * @since JDK 1.8
     */
    public static PersonalCenterInfoVO toPersonalCenterInfoVO(HkCustInfoDTO customerInfo, HkOpenCustomerStatusEnum hkOpenCustomerStatusEnum, List<HkBankCardInfoDTO> hkBankCardInfoDTOList, HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO, String hboneBindStatus) {
        //封装客户信息
        PersonalCenterInfoVO personalCenterInfoVO = fillCustInfoVo(customerInfo);
        //封装用户的实名信息
        if (StringUtils.isNotBlank(customerInfo.getCustName()) && StringUtils.isNotBlank(customerInfo.getIdNoDigest())) {
            personalCenterInfoVO.setRealNameCompletedFlag(YesNoEnum.YES.getCode());
        } else {
            personalCenterInfoVO.setRealNameCompletedFlag(YesNoEnum.NO.getCode());
        }
        //设置银行卡数量
        personalCenterInfoVO.setBindCardCount(String.valueOf(hkBankCardInfoDTOList.size()));
        if(null != hkOpenCustomerStatusEnum && null != hkOpenAccOrderInfoDTO){
            //App1.0需求过渡期
            //设置客户开户状态信息
            if(RequestUtil.isHkAppLogin()){
                personalCenterInfoVO.setOpenDepositsStatus(Integer.valueOf(hkOpenCustomerStatusEnum.getCode()).toString());
            }else{
                personalCenterInfoVO.setOpenDepositsStatus(hkOpenCustomerStatusEnum.getCode());
            }
            //开户入金状态为02-继续开户,开户入金状态为04-修改开户资料,则获取最大开户页
            if (HkOpenCustomerStatusEnum.CONTINUE_ACCOUNT.getCode().equals(hkOpenCustomerStatusEnum.getCode()) || HkOpenCustomerStatusEnum.MODIFY_ACCOUNT_INFO.getCode().equals(hkOpenCustomerStatusEnum.getCode())) {
                personalCenterInfoVO.setOpenAcctStepFlag(hkOpenAccOrderInfoDTO.getOpenAcctStep());
            }
        }
        //客户风测过期标识 1：是 0：否
        boolean result = Boolean.TRUE;
        if (StringUtils.isNotBlank(customerInfo.getRiskToleranceTerm())) {
            result = DateUtils.isBeforeOrEqualToCurrentDate(customerInfo.getRiskToleranceTerm());
        }
        personalCenterInfoVO.setRiskToleranceExpire(result ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        personalCenterInfoVO.setHboneBindStatus(StringUtils.isBlank(hboneBindStatus) ? YesNoEnum.NO.getCode() : hboneBindStatus);
        return personalCenterInfoVO;
    }

    /**
     *
     * @param customerInfo  香港客户信息DTO
     * @return  PersonalSimpleInfoVO   客户简要信息VO
     */
    public static PersonalSimpleInfoVO toPersonalSimpleInfoVO(HkCustInfoDTO customerInfo) {
        PersonalSimpleInfoVO personalSimpleInfoVO = new PersonalSimpleInfoVO();
        personalSimpleInfoVO.setHkCustNo(customerInfo.getHkCustNo());
        personalSimpleInfoVO.setInvstType(customerInfo.getInvstType());
        personalSimpleInfoVO.setCustName(customerInfo.getCustName());
        personalSimpleInfoVO.setMobileMask(customerInfo.getMobileMask());
        personalSimpleInfoVO.setMobileDigest(customerInfo.getMobileDigest());
        personalSimpleInfoVO.setMobileAreaCode(customerInfo.getMobileAreaCode());
        personalSimpleInfoVO.setEmailMask(customerInfo.getEmailMask());
        personalSimpleInfoVO.setEmailDigest(customerInfo.getEmailDigest());
        return personalSimpleInfoVO;
    }


    /**
     * @param hkCustInfoDTO            客户信息
     * @param hkOpenCustomerStatusEnum 用户状态枚举
     * @param hkBankCardInfoDTOList    银行卡列表
     * @param hkOpenAccOrderInfoDTO    开户订单信息
     * @param hboneBindStatus          是否绑定一账通
     * @param derivativeKnowledge
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkAcctAppPersonalCenterVO
     * @description: 组合海外App个人中心接口展示类
     * @author: jinqing.rao
     * @date: 2024/2/23 13:48
     * @since JDK 1.8
     */
    public static HkAcctAppPersonalCenterVO toAppPersonalCenterInfoVO(HkCustInfoDTO hkCustInfoDTO, HkOpenCustomerStatusEnum hkOpenCustomerStatusEnum, List<HkBankCardInfoDTO> hkBankCardInfoDTOList, HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO,
                                                                      String hboneBindStatus, String idTypeDesc, String investorAuditStatus, String piggySignStatus, String derivativeKnowledge) {
        //封装App客户信息
        HkAcctAppPersonalCenterVO hkAcctAppPersonalCenterVO = fillAppCustInfoVo(hkCustInfoDTO);
        hkAcctAppPersonalCenterVO.setIdTypeDesc(idTypeDesc);
        //设置银行卡数量
        hkAcctAppPersonalCenterVO.setBindCardCount(String.valueOf(hkBankCardInfoDTOList.size()));
        //客户风测过期标识 1：是 0：否,空就展示空
        if (StringUtils.isNotBlank(hkCustInfoDTO.getRiskToleranceTerm())) {
            boolean result = DateUtils.isBeforeOrEqualToCurrentDate(hkCustInfoDTO.getRiskToleranceTerm());
            hkAcctAppPersonalCenterVO.setRiskToleranceExpire(result ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        }
        if(null != hkOpenCustomerStatusEnum){
            //设置客户开户状态信息
            hkAcctAppPersonalCenterVO.setOpenDepositsStatus(Integer.valueOf(hkOpenCustomerStatusEnum.getCode()).toString());
            //开户入金状态为02-继续开户,开户入金状态为04-修改开户资料,则获取最大开户页
            if (HkOpenCustomerStatusEnum.CONTINUE_ACCOUNT.getCode().equals(hkOpenCustomerStatusEnum.getCode()) || HkOpenCustomerStatusEnum.MODIFY_ACCOUNT_INFO.getCode().equals(hkOpenCustomerStatusEnum.getCode())) {
                hkAcctAppPersonalCenterVO.setOpenAcctStep(hkOpenAccOrderInfoDTO.getOpenAcctStep());
            }
        }
        hkAcctAppPersonalCenterVO.setHboneBindStatus(StringUtils.isBlank(hboneBindStatus) ? YesNoEnum.NO.getCode() : hboneBindStatus);
        HkOpenAccountStepEnum hkOpenAccountStepEnum = HkOpenAccountStepEnum.getHkOpenAccountStepEnumByName(hkOpenAccOrderInfoDTO.getHkOpenAccountStepEnumName());
        hkAcctAppPersonalCenterVO.setOpenAcctStepProgress(HkOpenAccountProgressBarEnum.getProgressBarNameByName(hkOpenAccountStepEnum));
        hkAcctAppPersonalCenterVO.setInvestorAuditStatus(investorAuditStatus);
        hkAcctAppPersonalCenterVO.setPiggyBankSignStatus(piggySignStatus);
        hkAcctAppPersonalCenterVO.setDerivativeKnowledge(derivativeKnowledge);
        return hkAcctAppPersonalCenterVO;
    }


    /**
     * @description: 填充fillCustInfoVo
     * @param: [hkCustInfoDTO]
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.CustLessInfoVO
     * @author: shaoyang.li
     * @date: 2023/6/7 17:34
     * @since JDK 1.8
     */
    private static PersonalCenterInfoVO fillCustInfoVo(HkCustInfoDTO hkCustInfoDTO) {
        PersonalCenterInfoVO personalCenterInfoVO = new PersonalCenterInfoVO();
        personalCenterInfoVO.setHkCustNo(hkCustInfoDTO.getHkCustNo());
        // H5接口返回香港客户号密文，视频详情页通过香港客户号密文访问非交易cgi
        personalCenterInfoVO.setHkCustNoCipher(DesUtil.encrypt(hkCustInfoDTO.getHkCustNo(), Constants.HK_APP_CUST_NO_KEY));
        personalCenterInfoVO.setMobileMask(hkCustInfoDTO.getMobileMask());
        personalCenterInfoVO.setMobileDigest(hkCustInfoDTO.getMobileDigest());
        personalCenterInfoVO.setEmailMask(hkCustInfoDTO.getEmailMask());
        personalCenterInfoVO.setEmailDigest(hkCustInfoDTO.getEmailDigest());
        personalCenterInfoVO.setRiskToleranceLevel(hkCustInfoDTO.getRiskToleranceLevel());
        personalCenterInfoVO.setCustLoginPasswdType(hkCustInfoDTO.getCustLoginPasswdType());
        personalCenterInfoVO.setCustTxPasswdType(StringUtils.isBlank(hkCustInfoDTO.getCustTxPasswdType()) ? "2" : hkCustInfoDTO.getCustTxPasswdType());
        personalCenterInfoVO.setCustState(hkCustInfoDTO.getCustState());
        //登录是否激活,登录密码设置标识点等于重置状态
        personalCenterInfoVO.setLoginActivate("1".equals(hkCustInfoDTO.getCustLoginPasswdType()) ? YesNoEnum.NO.getCode() : YesNoEnum.YES.getCode());
        //交易是否激活,交易密码设置标识点等于重置状态/未设置状态
        boolean result = "2".equals(hkCustInfoDTO.getCustTxPasswdType()) || "1".equals(hkCustInfoDTO.getCustTxPasswdType())|| StringUtils.isBlank(hkCustInfoDTO.getCustTxPasswdType());
        personalCenterInfoVO.setTransactionActivation(result ? YesNoEnum.NO.getCode() : YesNoEnum.YES.getCode());
        personalCenterInfoVO.setInvestorType(hkCustInfoDTO.getInvestorQualification());
        return personalCenterInfoVO;
    }

    /**
     * @description: 组合海外App客户信息
     * @param hkCustInfoDTO
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkAcctAppPersonalCenterVO
     * @author: jinqing.rao
     * @date: 2024/2/23 13:49
     * @since JDK 1.8
     */
    private static HkAcctAppPersonalCenterVO fillAppCustInfoVo(HkCustInfoDTO hkCustInfoDTO) {
        HkAcctAppPersonalCenterVO appPersonalCenterVO = new HkAcctAppPersonalCenterVO();
        String hkCustNo = hkCustInfoDTO.getHkCustNo();
        appPersonalCenterVO.setHkCustNo(DesUtil.encrypt(hkCustNo, Constants.HK_APP_CUST_NO_KEY));
        appPersonalCenterVO.setMobileMask(hkCustInfoDTO.getMobileMask());
        appPersonalCenterVO.setMobileDigest(hkCustInfoDTO.getMobileDigest());
        appPersonalCenterVO.setEmailMask(hkCustInfoDTO.getEmailMask());
        appPersonalCenterVO.setEmailDigest(hkCustInfoDTO.getEmailDigest());
        appPersonalCenterVO.setRiskToleranceLevel(hkCustInfoDTO.getRiskToleranceLevel());
        appPersonalCenterVO.setCustState(hkCustInfoDTO.getCustState());
        appPersonalCenterVO.setOpenType(hkCustInfoDTO.getOpenType());
        appPersonalCenterVO.setCustTxPasswdType(StringUtils.isBlank(hkCustInfoDTO.getCustTxPasswdType()) ? "2" : hkCustInfoDTO.getCustTxPasswdType());
        appPersonalCenterVO.setCustLoginPasswdType(hkCustInfoDTO.getCustLoginPasswdType());
        appPersonalCenterVO.setIdType(hkCustInfoDTO.getIdType());
        appPersonalCenterVO.setHboneNo(hkCustInfoDTO.getHboneNo());
        appPersonalCenterVO.setBirthday(hkCustInfoDTO.getBirthday());
        appPersonalCenterVO.setCustName(hkCustInfoDTO.getCustName());
        //登录是否激活,登录密码设置标识点等于重置状态
        appPersonalCenterVO.setLoginActivate("1".equals(hkCustInfoDTO.getCustLoginPasswdType()) ? YesNoEnum.NO.getCode() : YesNoEnum.YES.getCode());
        //交易是否激活,交易密码设置标识点等于重置状态/未设置状态
        boolean result = "2".equals(hkCustInfoDTO.getCustTxPasswdType()) || "1".equals(hkCustInfoDTO.getCustTxPasswdType())|| StringUtils.isBlank(hkCustInfoDTO.getCustTxPasswdType());
        appPersonalCenterVO.setTransactionActivation(result ? YesNoEnum.NO.getCode() : YesNoEnum.YES.getCode());
        appPersonalCenterVO.setInvestorType(hkCustInfoDTO.getInvestorQualification());
        //设置资产时间是否有效
        boolean beforeCurrentDate = DateUtils.isBeforeCurrentDate(hkCustInfoDTO.getAssetCertExpiredDate(), DateUtils.YYYYMMDD);
        appPersonalCenterVO.setInvestorAssetsEffectiveStatus(beforeCurrentDate ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        appPersonalCenterVO.setMobileAreaCode(hkCustInfoDTO.getMobileAreaCode());
        
        // 新增字段
        appPersonalCenterVO.setGender(hkCustInfoDTO.getGender());
        appPersonalCenterVO.setNickname(hkCustInfoDTO.getNickname());
        appPersonalCenterVO.setHkCustNoPlaintext(hkCustNo);
        // TODO 代码注释
//        appPersonalCenterVO.setPaperSignUploaded(hkCustInfoDTO.getPaperSignUploaded());
//        appPersonalCenterVO.setPaperSignOrderNo(hkCustInfoDTO.getPaperSignOrderNo());
        
        return appPersonalCenterVO;
    }

    /**
     * @param request 证件信息保存参数
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctIdInfoDTO
     * @description: 将OpenIdRequest转换成HkOpenAcctIdInfoDTO
     * @author: jinqing.rao
     * @date: 2023/12/7 10:42
     * @since JDK 1.8
     */
    public static HkOpenAcctIdInfoDTO toHkOpenAcctIdInfoDTO(HkAcctOpenIdInfoRequest request) {
        String idExpireTime = request.getIdExpireTime();
        if(YesNoEnum.YES.getCode().equals(request.getIdAlwaysValidFlag())){
            idExpireTime = null;
        }
        HkOpenAcctIdInfoDTO openAcctIdInfoDTO = new HkOpenAcctIdInfoDTO();
        openAcctIdInfoDTO.setIdAreaCode(request.getIdAreaCode());
        openAcctIdInfoDTO.setIdAreaCodeDesc(request.getIdAreaCodeDesc());
        openAcctIdInfoDTO.setIdType(request.getIdType());
        openAcctIdInfoDTO.setIdTypeDesc(request.getIdTypeDesc());
        openAcctIdInfoDTO.setFrontPictureUrl(request.getFrontPictureUrl());
        openAcctIdInfoDTO.setFrontThumbnailUrl(request.getFrontThumbnailUrl());
        openAcctIdInfoDTO.setBackPictureUrl(request.getBackPictureUrl());
        openAcctIdInfoDTO.setBackThumbnailUrl(request.getBackThumbnailUrl());
        openAcctIdInfoDTO.setIdNo(request.getIdNo());
        openAcctIdInfoDTO.setIdAlwaysValidFlag(request.getIdAlwaysValidFlag());
        openAcctIdInfoDTO.setIdExpireTime(idExpireTime);
        openAcctIdInfoDTO.setOpenSaveType(request.getOpenSaveType());
        openAcctIdInfoDTO.setExtendFileJson(request.getExtendFileJson());
        return openAcctIdInfoDTO;
    }

    /**
     * @param hkOpenAcctIdInfoDTO 账户中心请求响应类
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.idinfo.OpenIdVO
     * @description: 将HkOpenAcctIdInfoDTO转换成OpenIdVO
     * @author: jinqing.rao
     * @date: 2023/12/7 11:13
     * @since JDK 1.8
     */
    public static HkAcctOpenIdDetailVO toOpenIdVO(HkOpenAcctIdInfoDTO hkOpenAcctIdInfoDTO, Map<String, String> countryInfoMap, Map<String, String> idTypeListMap, List<HkOpenAcctCheckVO> checkList) {
        HkAcctOpenIdDetailVO.HkAcctOpenIdDetailVOBuilder builder = HkAcctOpenIdDetailVO.builder();
        HkAcctOpenIdDetailVO.OpenIdValueVO openIdValueVO = HkAcctOpenIdDetailVO.OpenIdValueVO.builder()
                .idAreaCode(hkOpenAcctIdInfoDTO.getIdAreaCode())
                .idAreaCodeDesc(StringUtils.isBlank(hkOpenAcctIdInfoDTO.getIdAreaCodeDesc()) ? countryInfoMap.get(hkOpenAcctIdInfoDTO.getIdAreaCode()) : hkOpenAcctIdInfoDTO.getIdAreaCodeDesc())
                .idType(hkOpenAcctIdInfoDTO.getIdType())
                .idTypeDesc(StringUtils.isBlank(hkOpenAcctIdInfoDTO.getIdTypeDesc()) ? idTypeListMap.get(hkOpenAcctIdInfoDTO.getIdType()) : hkOpenAcctIdInfoDTO.getIdTypeDesc())
                .frontPictureUrl(hkOpenAcctIdInfoDTO.getFrontPictureUrl())
                .frontThumbnailUrl(HkOpenImageUrlUtils.getThumbnailUrlByBaseFileUrl(hkOpenAcctIdInfoDTO.getFrontPictureUrl()))
                .backPictureUrl(hkOpenAcctIdInfoDTO.getBackPictureUrl())
                .backThumbnailUrl(HkOpenImageUrlUtils.getThumbnailUrlByBaseFileUrl(hkOpenAcctIdInfoDTO.getBackPictureUrl()))
                .idNo(hkOpenAcctIdInfoDTO.getIdNo())
                .idAlwaysValidFlag(hkOpenAcctIdInfoDTO.getIdAlwaysValidFlag())
                .idExpireTime(hkOpenAcctIdInfoDTO.getIdExpireTime())
                .extendFileJson(hkOpenAcctIdInfoDTO.getExtendFileJson())
                .openSaveType(hkOpenAcctIdInfoDTO.getOpenSaveType())
                .build();
        builder.checkResult(checkList);
        return builder.openIdValueVO(openIdValueVO).build();
    }

    /**
     * @param request 请求参数
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctCustInfoDTO
     * @description: 海外开户个人信息填写页
     * @author: jinqing.rao
     * @date: 2023/12/7 15:37
     * @since JDK 1.8
     */
    public static HkOpenAcctCustInfoDTO toHkOpenAcctCustInfoDTO(HkOpenPersonalInfoRequest request, HkOpenAcctAddressInfoDTO residenceAddress,
                                                                HkOpenAcctAddressInfoDTO mailAddress,
                                                                HkOpenAcctAddressInfoDTO birthAddress, String mobile, String email) {
        HkOpenAcctCustInfoDTO.HkOpenAcctCustInfoDTOBuilder builder = HkOpenAcctCustInfoDTO.builder();
        //个人基本信息
        builder.custChineseName(request.getCustChineseName())
                .cnSurname(request.getCnSurname())
                .cnGivenName(request.getCnGivenName())
                .custEnName(request.getCustEnName())
                .enSurname(request.getEnSurname())
                .enGivenName(request.getEnGivenName())
                .gender(request.getGender())
                .cnFormerSurName(request.getCnFormerSurName())
                .cnFormerName(request.getCnFormerName())
                .enFormerSurName(request.getEnFormerSurName())
                .enFormerName(request.getEnFormerName())
                .birthday(request.getBirthday())
                .nationality(request.getNationality())
                .nationalityDesc(request.getNationalityDesc())
                .marriageStat(request.getMarriageStat())
                .marriageStatDesc(request.getMarriageStatDesc())
                .marriageStatRemark(request.getMarriageStatRemark())
                .mobileAreaCode(request.getMobileAreaCode())
                .mobileResource(request.getMobileResource())
                .mobileMask(request.getMobileMask())
                .mobileDigest(request.getMobileDigest())
                .mobile(mobile)
                .email(email)
                .emailResource(request.getEmailResource())
                .emailMask(request.getEmailMask())
                .emailDigest(request.getEmailDigest())
                .mailingFlag(request.getMailingFlag())
                .birthFlag(request.getBirthFlag())
                .openSaveType(request.getOpenSaveType());
        //添加现局国家
        addResidenceAddress(request, residenceAddress, builder);
        //通讯国家
        addMailAddress(request, mailAddress, builder);
        //出生地
        addBirthAddress(request, birthAddress, builder);
        return builder.build();
    }

    private static void addBirthAddress(HkOpenPersonalInfoRequest request, HkOpenAcctAddressInfoDTO birthAddress, HkOpenAcctCustInfoDTO.HkOpenAcctCustInfoDTOBuilder builder) {
        if (null != birthAddress) {
            builder.birthCountryCode(birthAddress.getCountryCode())
                    .birthProvCode(birthAddress.getProvCode())
                    .birthCityCode(birthAddress.getCityCode())
                    .birthCountyCode(birthAddress.getCountyCode())
                    .birthAddrCn(birthAddress.getDetailAddrCn())
                    .birthAddrEn(birthAddress.getDetailAddrEn())
                    .birthFullAddrCn(null == request.getBirthDetail() ? null : request.getBirthDetail().getDetailAddrCn())
                    .birthFullAddrEn(null == request.getBirthDetail() ? null : request.getBirthDetail().getDetailAddrEn())
                    .birthTown(birthAddress.getTownEn())
                    .birthState(birthAddress.getStateEn());
        }
    }

    private static void addMailAddress(HkOpenPersonalInfoRequest request, HkOpenAcctAddressInfoDTO mailAddress, HkOpenAcctCustInfoDTO.HkOpenAcctCustInfoDTOBuilder builder) {
        if (null != mailAddress) {
            builder.mailingCountryCode(mailAddress.getCountryCode())
                    .mailingCountryEn(mailAddress.getCountryEnglishDesc())
                    .mailingProvCode(mailAddress.getProvCode())
                    .mailingProvEn(mailAddress.getProvEnglishDesc())
                    .mailingCityCode(mailAddress.getCityCode())
                    .mailingCityEn(mailAddress.getCityEnglishDesc())
                    .mailingCountyCode(mailAddress.getCountyCode())
                    .mailingCountyEn(mailAddress.getCountyEnglishDesc())
                    .mailingAddrCn(mailAddress.getDetailAddrCn())
                    .mailingAddrEn(mailAddress.getDetailAddrEn())
                    .mailingFullAddrCn(null == request.getMailingDetail() ? null : request.getMailingDetail().getDetailAddrCn())
                    .mailingFullAddrEn(null == request.getMailingDetail() ? null : request.getMailingDetail().getDetailAddrEn())
                    .mailingTownEn(mailAddress.getTownEn())
                    .mailingStateEn(mailAddress.getStateEn());

        }
        if (CollectionUtils.isNotEmpty(request.getMailingInfoCertUrls())) {
            builder.mailingCertUrls(request.getMailingInfoCertUrls().stream().map(ImageVO::getUrl).collect(Collectors.toList()));
        }
    }

    private static void addResidenceAddress(HkOpenPersonalInfoRequest request, HkOpenAcctAddressInfoDTO residenceAddress, HkOpenAcctCustInfoDTO.HkOpenAcctCustInfoDTOBuilder builder) {
        if (null != residenceAddress) {
            builder.residenceCountryCode(residenceAddress.getCountryCode())
                    .residenceCountryEn(residenceAddress.getCountryEnglishDesc())
                    .residenceProvCode(residenceAddress.getProvCode())
                    .residenceProvEn(residenceAddress.getProvEnglishDesc())
                    .residenceCityCode(residenceAddress.getCityCode())
                    .residenceCityEn(residenceAddress.getCityEnglishDesc())
                    .residenceCountyCode(residenceAddress.getCountyCode())
                    .residenceCountyEn(residenceAddress.getCountyEnglishDesc())
                    .residenceAddrCn(residenceAddress.getDetailAddrCn())
                    .residenceAddrEn(residenceAddress.getDetailAddrEn())
                    .residenceFullAddrCn(null == request.getResidenceDetail() ? null : request.getResidenceDetail().getDetailAddrCn())
                    .residenceFullAddrEn(null == request.getResidenceDetail() ? null : request.getResidenceDetail().getDetailAddrEn())
                    .residenceTownEn(residenceAddress.getTownEn())
                    .residenceStateEn(residenceAddress.getStateEn());
        }
        //添加现居地址证明URL列表
        if (CollectionUtils.isNotEmpty(request.getResidenceInfoCertUrls())) {
            builder.residenceCertUrls(request.getResidenceInfoCertUrls().stream().map(ImageVO::getUrl).collect(Collectors.toList()));
        }
    }

    /**
     * @param acctCustInfoDTO 响应参数
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenPersonalInfoVO
     * @description: 海外开户个人信息转VO
     * @author: jinqing.rao
     * @date: 2023/12/7 16:06
     * @since JDK 1.8
     */
    public static OpenPersonalInfoVO toOpenPersonalInfoVO(HkOpenAcctCustInfoDTO acctCustInfoDTO, List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS,
                                                          Map<String, HkAcctCountryDTO> countryMap, HkCustInfoDTO hkCustInfoDTO) {
        OpenPersonalInfoVO.OpenPersonalInfoVOBuilder builder = OpenPersonalInfoVO.builder();
        //设置默认值
        builder.formerNameFlag(YesNoEnum.NO.getCode());
        builder.mailingFlag(YesNoEnum.YES.getCode());
        builder.birthFlag(YesNoEnum.YES.getCode());
        //设置手机号和邮箱掩码信息
        getMobileAndEmailMaskInfo(acctCustInfoDTO, hkCustInfoDTO, builder);
        //没有开户申请单数据,直接返回
        if (null == acctCustInfoDTO) {
            return builder.build();
        }
        //添加现居地,通讯地，出生地信息
        initHkOpenAcctDetailInfo(acctCustInfoDTO, builder);
        HkAcctCountryDTO hkAcctCountryDTO = countryMap.get(acctCustInfoDTO.getNationality());
        String nationalityDesc = acctCustInfoDTO.getNationalityDesc();
        if(StringUtils.isBlank(nationalityDesc) && null != hkAcctCountryDTO){
            nationalityDesc  = hkAcctCountryDTO.getChineseName();
        }
        //添加订单信息
        return builder.custChineseName(acctCustInfoDTO.getCustChineseName())
                .cnSurname(acctCustInfoDTO.getCnSurname())
                .cnGivenName(acctCustInfoDTO.getCnGivenName())
                .custEnName(acctCustInfoDTO.getCustEnName())
                .cnFormerSurName(acctCustInfoDTO.getCnFormerSurName())
                .cnFormerName(acctCustInfoDTO.getCnFormerName())
                .enFormerSurName(acctCustInfoDTO.getEnFormerSurName())
                .enFormerName(acctCustInfoDTO.getEnFormerName())
                .marriageStatRemark(acctCustInfoDTO.getMarriageStatRemark())
                .enSurname(acctCustInfoDTO.getEnSurname())
                .enGivenName(acctCustInfoDTO.getEnGivenName())
                .gender(acctCustInfoDTO.getGender())
                .formerNameFlag(StringUtils.isAnyBlank(acctCustInfoDTO.getCnFormerName(), acctCustInfoDTO.getEnFormerName()) ? YesNoEnum.NO.getCode() : YesNoEnum.YES.getCode())
                .birthday(acctCustInfoDTO.getBirthday())
                .nationality(acctCustInfoDTO.getNationality())
                .nationalityDesc(nationalityDesc)
                .marriageStat(acctCustInfoDTO.getMarriageStat())
                .marriageStatDesc(HkOpenAcctMarriageEnum.getDescByCode(acctCustInfoDTO.getMarriageStat()))
                .marriageStatRemark(acctCustInfoDTO.getMarriageStatRemark())
                .mobileAreaCode(acctCustInfoDTO.getMobileAreaCode())
                .mailingFlag(acctCustInfoDTO.getMailingFlag())
                .birthFlag(acctCustInfoDTO.getBirthFlag())
                .checkResult(hkOpenAcctCheckVOS)
                .build();
    }

    private static void initHkOpenAcctDetailInfo(HkOpenAcctCustInfoDTO acctCustInfoDTO, OpenPersonalInfoVO.OpenPersonalInfoVOBuilder builder) {
        //现居地
        OpenPersonalInfoVO.AcctAddressInfo acctAddressInfo = new OpenPersonalInfoVO.AcctAddressInfo();
        acctAddressInfo.setDetailAddrEn(acctCustInfoDTO.getResidenceFullAddrEn());
        acctAddressInfo.setDetailAddrCn(acctCustInfoDTO.getResidenceFullAddrCn());
        acctAddressInfo.setCountryCode(acctCustInfoDTO.getResidenceCountryCode());
        acctAddressInfo.setCompareDetail(acctCustInfoDTO.getResidenceCityEn() + acctCustInfoDTO.getResidenceCountyEn() + acctCustInfoDTO.getResidenceAddrCn());
        //现居地址证明URL
        List<ImageVO> residenceInfoCertUrls = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(acctCustInfoDTO.getResidenceCertUrls())) {
            residenceInfoCertUrls = acctCustInfoDTO.getResidenceCertUrls().stream().map(url -> new ImageVO(url, HkOpenImageUrlUtils.getThumbnailUrlByBaseFileUrl(url))).collect(Collectors.toList());
        }
        //通讯地地址证明URL
        List<ImageVO> mailingInfoCertUrls = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(acctCustInfoDTO.getMailingCertUrls())) {
            mailingInfoCertUrls = acctCustInfoDTO.getMailingCertUrls().stream().map(url -> new ImageVO(url, HkOpenImageUrlUtils.getThumbnailUrlByBaseFileUrl(url))).collect(Collectors.toList());
        }
        //通讯地
        OpenPersonalInfoVO.AcctAddressInfo mailAddressInfo = new OpenPersonalInfoVO.AcctAddressInfo();
        mailAddressInfo.setDetailAddrEn(acctCustInfoDTO.getMailingFullAddrEn());
        mailAddressInfo.setDetailAddrCn(acctCustInfoDTO.getMailingFullAddrCn());
        mailAddressInfo.setCountryCode(acctCustInfoDTO.getMailingCountryCode());
        mailAddressInfo.setCompareDetail(acctCustInfoDTO.getMailingCityEn() + acctCustInfoDTO.getMailingCountyEn() + acctCustInfoDTO.getMailingAddrCn());
        //出生地
        OpenPersonalInfoVO.AcctAddressInfo birthAddressInfo = new OpenPersonalInfoVO.AcctAddressInfo();
        birthAddressInfo.setDetailAddrEn(acctCustInfoDTO.getBirthFullAddrEn());
        birthAddressInfo.setDetailAddrCn(acctCustInfoDTO.getBirthFullAddrCn());
        birthAddressInfo.setCountryCode(acctCustInfoDTO.getBirthCountryCode());
        birthAddressInfo.setCompareDetail(acctCustInfoDTO.getResidenceCityEn() + acctCustInfoDTO.getResidenceCountyEn() + acctCustInfoDTO.getResidenceAddrCn());
        //添加参数
        builder.residenceDetail(acctAddressInfo)
                .mailingDetail(mailAddressInfo)
                .birthDetail(birthAddressInfo)
                .residenceInfoCertUrls(residenceInfoCertUrls)
                .mailingInfoCertUrls(mailingInfoCertUrls);
    }

    private static void getMobileAndEmailMaskInfo(HkOpenAcctCustInfoDTO acctCustInfoDTO, HkCustInfoDTO hkCustInfoDTO, OpenPersonalInfoVO.OpenPersonalInfoVOBuilder builder) {
        if (null != hkCustInfoDTO) {
            if (StringUtils.isNotBlank(hkCustInfoDTO.getMobileMask())) {
                builder.mobileMask(hkCustInfoDTO.getMobileMask())
                        .mobileDigest(hkCustInfoDTO.getMobileDigest())
                        .mobileResource(YesNoEnum.YES.getCode());
            } else {
                if (null != acctCustInfoDTO) {
                    builder.mobile(acctCustInfoDTO.getMobile());
                }
                builder.mobileResource(YesNoEnum.NO.getCode());
            }
            if (StringUtils.isNotBlank(hkCustInfoDTO.getEmailMask())) {
                builder.emailMask(hkCustInfoDTO.getEmailMask())
                        .emailDigest(hkCustInfoDTO.getEmailDigest())
                        .emailResource(YesNoEnum.YES.getCode());
            } else {
                if (null != acctCustInfoDTO) {
                    builder.email(acctCustInfoDTO.getEmail());
                }
                builder.emailResource(YesNoEnum.NO.getCode());
            }
        }
    }

    /**
     * @param acctCustInfoDTO
     * @param countryInfoMap
     * @param cityInfoMap
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctAddressInfoVO
     * @description: 分装海外开户个人信息的出生地址信息
     * @author: jinqing.rao
     * @date: 2023/12/13 15:56
     * @since JDK 1.8
     */
    public static HkOpenAcctAddressInfoVO getHkOpenAcctBirthplaceAddressInfoVO(HkOpenAcctCustInfoDTO acctCustInfoDTO, Map<String, String> countryInfoMap, Map<String, String> cityInfoMap) {
        HkOpenAcctAddressInfoVO hkOpenAcctAddressInfoVO = new HkOpenAcctAddressInfoVO();
        hkOpenAcctAddressInfoVO.setCountryCode(acctCustInfoDTO.getBirthCountryCode());
        hkOpenAcctAddressInfoVO.setCountryDesc(countryInfoMap.get(acctCustInfoDTO.getBirthCountryCode()));
        hkOpenAcctAddressInfoVO.setProvCode(acctCustInfoDTO.getBirthProvCode());
        hkOpenAcctAddressInfoVO.setProvDesc(cityInfoMap.get(acctCustInfoDTO.getBirthProvCode()));
        hkOpenAcctAddressInfoVO.setCityCode(acctCustInfoDTO.getBirthCityCode());
        hkOpenAcctAddressInfoVO.setCityDesc(cityInfoMap.get(acctCustInfoDTO.getBirthCityCode()));
        hkOpenAcctAddressInfoVO.setCountyCode(acctCustInfoDTO.getBirthCountyCode());
        hkOpenAcctAddressInfoVO.setCountyDesc(cityInfoMap.get(acctCustInfoDTO.getBirthCountyCode()));
        hkOpenAcctAddressInfoVO.setDetailAddrCn(acctCustInfoDTO.getBirthAddrCn());
        hkOpenAcctAddressInfoVO.setDetailAddrEn(acctCustInfoDTO.getBirthAddrEn());
        hkOpenAcctAddressInfoVO.setTownEn(acctCustInfoDTO.getMailingTownEn());
        hkOpenAcctAddressInfoVO.setStateEn(acctCustInfoDTO.getMailingStateEn());
        return hkOpenAcctAddressInfoVO;

    }

    /**
     * @param acctCustInfoDTO 个人信息
     * @param countryInfoMap
     * @param cityInfoMap
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctAddressInfoVO
     * @description: 分装海外开户个人信息的通讯地址信息
     * @author: jinqing.rao
     * @date: 2023/12/13 15:54
     * @since JDK 1.8
     */
    public static HkOpenAcctAddressInfoVO getHkOpenAcctMailingAddressInfoVO(HkOpenAcctCustInfoDTO acctCustInfoDTO, Map<String, String> countryInfoMap, Map<String, String> cityInfoMap) {
        HkOpenAcctAddressInfoVO hkOpenAcctAddressInfoVO = new HkOpenAcctAddressInfoVO();
        hkOpenAcctAddressInfoVO.setCountryCode(acctCustInfoDTO.getMailingCountryCode());
        hkOpenAcctAddressInfoVO.setCountryDesc(countryInfoMap.get(acctCustInfoDTO.getMailingCountryCode()));
        hkOpenAcctAddressInfoVO.setCountryEnglishDesc(acctCustInfoDTO.getMailingCountryEn());
        hkOpenAcctAddressInfoVO.setProvEnglishDesc(acctCustInfoDTO.getMailingProvEn());
        hkOpenAcctAddressInfoVO.setProvCode(acctCustInfoDTO.getMailingProvCode());
        hkOpenAcctAddressInfoVO.setProvDesc(cityInfoMap.get(acctCustInfoDTO.getMailingProvCode()));
        hkOpenAcctAddressInfoVO.setCityCode(acctCustInfoDTO.getMailingCityCode());
        hkOpenAcctAddressInfoVO.setCityDesc(cityInfoMap.get(acctCustInfoDTO.getMailingCityCode()));
        hkOpenAcctAddressInfoVO.setCityEnglishDesc(acctCustInfoDTO.getMailingCityEn());
        hkOpenAcctAddressInfoVO.setCountyCode(acctCustInfoDTO.getMailingCountyCode());
        hkOpenAcctAddressInfoVO.setCountyDesc(cityInfoMap.get(acctCustInfoDTO.getMailingCountyCode()));
        hkOpenAcctAddressInfoVO.setCountyEnglishDesc(acctCustInfoDTO.getMailingCountyEn());
        hkOpenAcctAddressInfoVO.setDetailAddrCn(acctCustInfoDTO.getMailingAddrCn());
        hkOpenAcctAddressInfoVO.setDetailAddrEn(acctCustInfoDTO.getMailingAddrEn());
        hkOpenAcctAddressInfoVO.setTownEn(acctCustInfoDTO.getMailingTownEn());
        hkOpenAcctAddressInfoVO.setStateEn(acctCustInfoDTO.getMailingStateEn());
        hkOpenAcctAddressInfoVO.setResidenceCertUrls(acctCustInfoDTO.getMailingCertUrls().stream().map(m -> new ImageVO(m, m)).collect(Collectors.toList()));
        return hkOpenAcctAddressInfoVO;
    }

    /**
     * @param acctCustInfoDTO 现居地址信息
     * @param countryInfoMap
     * @param cityInfoMap
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctAddressInfoVO
     * @description: 分装海外开户个人信息的现居地址信息
     * @author: jinqing.rao
     * @date: 2023/12/13 15:47
     * @since JDK 1.8
     */
    public static HkOpenAcctAddressInfoVO getHkOpenAcctResidenceAddressInfoVO(HkOpenAcctCustInfoDTO acctCustInfoDTO, Map<String, String> countryInfoMap, Map<String, String> cityInfoMap) {
        HkOpenAcctAddressInfoVO hkOpenAcctAddressInfoVO = new HkOpenAcctAddressInfoVO();
        hkOpenAcctAddressInfoVO.setCountryCode(acctCustInfoDTO.getResidenceCountryCode());
        hkOpenAcctAddressInfoVO.setCountryDesc(countryInfoMap.get(acctCustInfoDTO.getResidenceCountryCode()));
        hkOpenAcctAddressInfoVO.setCountryEnglishDesc(acctCustInfoDTO.getResidenceCountryEn());
        hkOpenAcctAddressInfoVO.setProvCode(acctCustInfoDTO.getResidenceProvCode());
        hkOpenAcctAddressInfoVO.setProvDesc(cityInfoMap.get(acctCustInfoDTO.getResidenceProvCode()));
        hkOpenAcctAddressInfoVO.setProvEnglishDesc(acctCustInfoDTO.getResidenceProvEn());
        hkOpenAcctAddressInfoVO.setCityCode(acctCustInfoDTO.getResidenceCityCode());
        hkOpenAcctAddressInfoVO.setCityDesc(cityInfoMap.get(acctCustInfoDTO.getResidenceCityCode()));
        hkOpenAcctAddressInfoVO.setCityEnglishDesc(acctCustInfoDTO.getResidenceCityEn());
        hkOpenAcctAddressInfoVO.setCountyCode(acctCustInfoDTO.getResidenceCountyCode());
        hkOpenAcctAddressInfoVO.setCountyDesc(cityInfoMap.get(acctCustInfoDTO.getResidenceCountyCode()));
        hkOpenAcctAddressInfoVO.setCountyEnglishDesc(acctCustInfoDTO.getResidenceCountyEn());
        hkOpenAcctAddressInfoVO.setDetailAddrCn(acctCustInfoDTO.getResidenceAddrCn());
        hkOpenAcctAddressInfoVO.setDetailAddrEn(acctCustInfoDTO.getResidenceAddrEn());
        hkOpenAcctAddressInfoVO.setTownEn(acctCustInfoDTO.getResidenceTownEn());
        hkOpenAcctAddressInfoVO.setStateEn(acctCustInfoDTO.getResidenceStateEn());
        hkOpenAcctAddressInfoVO.setResidenceCertUrls(acctCustInfoDTO.getResidenceCertUrls().stream().map(m -> new ImageVO(m, m)).collect(Collectors.toList()));
        return hkOpenAcctAddressInfoVO;
    }

    /**
     * @param request 请求参数信息
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctOccupationDTO
     * @description: 海外开户职业信息转VO
     * @author: jinqing.rao
     * @date: 2023/12/7 19:53
     * @since JDK 1.8
     */
    public static HkOpenAcctOccupationDTO toHkOpenAcctOccupationDTO(HkOpenAcctOccupationRequest request, HkOpenAcctAddressInfoDTO emplAddr) {
        HkOpenAcctOccupationDTO hkOpenAcctOccupationDTO = new HkOpenAcctOccupationDTO();
        hkOpenAcctOccupationDTO.setEmplStatus(request.getEmplStatus());
        hkOpenAcctOccupationDTO.setEmplNatureOfBusiness(request.getEmplNatureOfBusiness());
        hkOpenAcctOccupationDTO.setEmplCompanyName(request.getEmplCompanyName());
        hkOpenAcctOccupationDTO.setEmplStatusType(request.getEmplStatusType());
        hkOpenAcctOccupationDTO.setEmplNatureOfBusinessDesc(request.getEmplNatureOfBusinessDesc());
        hkOpenAcctOccupationDTO.setOtherEmplNatureOfBusiness(request.getOtherEmplNatureOfBusiness());
        hkOpenAcctOccupationDTO.setDesignation(request.getDesignation());
        hkOpenAcctOccupationDTO.setTaxJurisdictionCode(request.getTaxJurisdictionCode());
        hkOpenAcctOccupationDTO.setTaxJurisdictionCodeDesc(request.getTaxJurisdictionDesc());
        hkOpenAcctOccupationDTO.setHasTin(request.getHasTin());
        hkOpenAcctOccupationDTO.setNoTinReason(request.getNoTinReason());
        hkOpenAcctOccupationDTO.setNoObtainTinReason(request.getNoObtainTinReason());
        hkOpenAcctOccupationDTO.setTinType(request.getTinType());
        // 非身份证类型的获取 其他证件类型描述,前端联动没删除，服务端特殊兼容下
        if (!"01".equals(request.getTinType())) {
            hkOpenAcctOccupationDTO.setOtherTinTypeDesc(request.getOtherTinTypeDesc());
        }
        hkOpenAcctOccupationDTO.setEmplAddrCn(request.getEmplAddrCn());
        hkOpenAcctOccupationDTO.setEmplAddrEn(request.getEmplAddrEn());
        hkOpenAcctOccupationDTO.setTin(request.getTin());
        hkOpenAcctOccupationDTO.setOpenSaveType(request.getOpenSaveType());
        if (null != emplAddr) {
            hkOpenAcctOccupationDTO.setEmplCountryCode(emplAddr.getCountryCode());
            hkOpenAcctOccupationDTO.setEmplCountryDesc(emplAddr.getCountryDesc());
            hkOpenAcctOccupationDTO.setEmplCountryEnDesc(emplAddr.getCityEnglishDesc());
            hkOpenAcctOccupationDTO.setEmplProvCode(emplAddr.getProvCode());
            hkOpenAcctOccupationDTO.setEmplProvDesc(emplAddr.getProvDesc());
            hkOpenAcctOccupationDTO.setEmplProvEnDesc(emplAddr.getProvEnglishDesc());
            hkOpenAcctOccupationDTO.setEmplCityCode(emplAddr.getCityCode());
            hkOpenAcctOccupationDTO.setEmplCityCodeDesc(emplAddr.getCityDesc());
            hkOpenAcctOccupationDTO.setEmplCityEnDesc(emplAddr.getCityEnglishDesc());
            hkOpenAcctOccupationDTO.setEmplCountyCode(emplAddr.getCountyCode());
            hkOpenAcctOccupationDTO.setEmplCountyCodeDesc(emplAddr.getCountyDesc());
            hkOpenAcctOccupationDTO.setEmplCountyEnDesc(emplAddr.getCountyEnglishDesc());
            hkOpenAcctOccupationDTO.setEmplTown(emplAddr.getTownEn());
            hkOpenAcctOccupationDTO.setEmplState(emplAddr.getStateEn());
            hkOpenAcctOccupationDTO.setEmplAddr(StringUtils.isBlank(emplAddr.getDetailAddrCn()) ? emplAddr.getDetailAddrEn() : emplAddr.getDetailAddrCn());
        }
        return hkOpenAcctOccupationDTO;
    }

    /**
     * @param accountOccupationDTO 请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenOccupationVO
     * @description: 海外开户职业信息转VO
     * @author: jinqing.rao
     * @date: 2023/12/7 20:03
     * @since JDK 1.8
     */
    public static OpenOccupationVO toOpenOccupationVO(HkOpenAcctOccupationDTO accountOccupationDTO, Map<String, HkAcctCountryDTO> countryInfoMap, List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS,Map<String, String> map) {
        OpenOccupationVO.OpenOccupationVOBuilder builder = OpenOccupationVO.builder();
        String taxJurisdictionCodeDesc = accountOccupationDTO.getTaxJurisdictionCodeDesc();
        HkAcctCountryDTO hkAcctCountryDTO = countryInfoMap.get(accountOccupationDTO.getTaxJurisdictionCode());
        if(StringUtils.isBlank(taxJurisdictionCodeDesc) && null != hkAcctCountryDTO){
            taxJurisdictionCodeDesc = hkAcctCountryDTO.getChineseName();
        }
        OpenOccupationVO.OpenOccupationValueVO openOccupationValueVO = OpenOccupationVO.OpenOccupationValueVO.builder()
                .emplStatus(accountOccupationDTO.getEmplStatus())
                .emplCompanyName(accountOccupationDTO.getEmplCompanyName())
                .emplCountryCode(accountOccupationDTO.getEmplCountryCode())
                .emplStatusType(accountOccupationDTO.getEmplStatusType())
                .emplNatureOfBusiness(accountOccupationDTO.getEmplNatureOfBusiness())
                .emplNatureOfBusinessDesc(map.get(accountOccupationDTO.getEmplNatureOfBusiness()))
                .otherEmplNatureOfBusiness(accountOccupationDTO.getOtherEmplNatureOfBusiness())
                .designation(accountOccupationDTO.getDesignation())
                .taxJurisdictionCode(accountOccupationDTO.getTaxJurisdictionCode())
                .taxJurisdictionDesc(taxJurisdictionCodeDesc)
                .hasTin(accountOccupationDTO.getHasTin())
                .noTinReason(accountOccupationDTO.getNoTinReason())
                .noObtainTinReason(accountOccupationDTO.getNoObtainTinReason())
                .tinType(accountOccupationDTO.getTinType())
                .tin(accountOccupationDTO.getTin())
                //.tinTypeDesc(accountOccupationDTO.getTinTypeDesc())
                .otherTinTypeDesc(accountOccupationDTO.getOtherTinTypeDesc())
                .emplAddrCn(accountOccupationDTO.getEmplAddrCn())
                .emplAddrEn(accountOccupationDTO.getEmplAddrEn())
                .build();
        builder.checkResult(hkOpenAcctCheckVOS);
        return builder.openOccupationValueVO(openOccupationValueVO).build();
    }

    /**
     * @param request 请求参数信息
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctDeclarationInfoDTO
     * @description: 海外开户声明信息转VO
     * @author: jinqing.rao
     * @date: 2023/12/8 9:09
     * @since JDK 1.8
     */
    public static HkOpenAcctDeclarationInfoDTO toHkOpenAcctDeclarationInfoDTO(HkOpenAcctDeclareRequest request) {
        HkOpenAcctDeclarationInfoDTO declarationInfoDTO = new HkOpenAcctDeclarationInfoDTO();
        List<String> employerAgreeThumbnailUrls = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getEmployerAgreeThumbnailUrls())) {
            employerAgreeThumbnailUrls = request.getEmployerAgreeThumbnailUrls().stream().map(m -> m.getUrl()).collect(Collectors.toList());
        }
        declarationInfoDTO.setDeclareOne(request.getDeclareOne());
        declarationInfoDTO.setCompanyName(request.getCompanyName());
        declarationInfoDTO.setExchangeName(request.getExchangeName());
        declarationInfoDTO.setTradeNo(request.getTradeNo());
        declarationInfoDTO.setDeclareTwo(request.getDeclareTwo());
        declarationInfoDTO.setLicensedNo(request.getLicensedNo());
        declarationInfoDTO.setEmployerAgreeThumbnailUrls(employerAgreeThumbnailUrls);
        declarationInfoDTO.setDeclareThree(request.getDeclareThree());
        declarationInfoDTO.setRelateName(request.getRelateName());
        declarationInfoDTO.setDeclareFour(request.getDeclareFour());
        declarationInfoDTO.setDeclareFourDesc(request.getDeclareFourDesc());
        declarationInfoDTO.setOpenSaveType(request.getOpenSaveType());
        return declarationInfoDTO;
    }

    /**
     * @param acctDeclarationInfoDTO
     * @param hkOpenAcctCheckVOS
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.declare.OpenDeclareVO
     * @description: 海外开户声明信息转VO
     * @author: jinqing.rao
     * @date: 2023/12/8 10:26
     * @since JDK 1.8
     */
    public static HkOpenAcctDeclareVO toOpenDeclareVO(HkOpenAcctDeclarationInfoDTO acctDeclarationInfoDTO, List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS) {
        HkOpenAcctDeclareVO.HkOpenAcctDeclareVOBuilder builder = HkOpenAcctDeclareVO.builder();
        List<String> agreeThumbnailUrls = acctDeclarationInfoDTO.getEmployerAgreeThumbnailUrls();
        List<ImageVO> collect = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(agreeThumbnailUrls)) {
            collect = agreeThumbnailUrls.stream().map(m -> new ImageVO(m, m)).collect(Collectors.toList());
        }
        HkOpenAcctDeclareVO.OpenDeclareValueVO openDeclareValueVO = HkOpenAcctDeclareVO.OpenDeclareValueVO.builder()
                .declareOne(acctDeclarationInfoDTO.getDeclareOne())
                .companyName(acctDeclarationInfoDTO.getCompanyName())
                .exchangeName(acctDeclarationInfoDTO.getExchangeName())
                .tradeNo(acctDeclarationInfoDTO.getTradeNo())
                .declareTwo(acctDeclarationInfoDTO.getDeclareTwo())
                .licensedNo(acctDeclarationInfoDTO.getLicensedNo())
                .employerAgreeThumbnailUrls(collect)
                .declareThree(acctDeclarationInfoDTO.getDeclareThree())
                .relateName(acctDeclarationInfoDTO.getRelateName())
                .declareFour(acctDeclarationInfoDTO.getDeclareFour())
                .declareFourDesc(acctDeclarationInfoDTO.getDeclareFourDesc())
                .openSaveType(acctDeclarationInfoDTO.getOpenSaveType())
                .build();
        builder.checkResult(hkOpenAcctCheckVOS);
        return builder.openDeclareValueVO(openDeclareValueVO)
                .build();
    }

    /**
     * @param request 请求参数信息
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctInvestExpDTO
     * @description: 海外开户投资经验信息转VO
     * @author: jinqing.rao
     * @date: 2023/12/8 10:56
     * @since JDK 1.8
     */
    public static HkOpenAcctInvestExpDTO toHkOpenAccInvestExpDTO(OpenInvestRequest request, List<AnswerDTO> answerDTOList, HkOpenAcctRiskCalculateResultDTO hkOpenAcctRiskAssessmentRiskResult) {
        HkOpenAcctInvestExpDTO hkOpenAcctInvestExpDTO = new HkOpenAcctInvestExpDTO();
        List<String> urls = request.getAssetCertUrlList().stream().map(ImageVO::getUrl).collect(Collectors.toList());
        hkOpenAcctInvestExpDTO.setInvestorQualification(request.getInvestorQualification());
        hkOpenAcctInvestExpDTO.setAcceptDeclare(request.getAcceptDeclare());
        hkOpenAcctInvestExpDTO.setAssetCertUrlList(urls);
        hkOpenAcctInvestExpDTO.setExamId(request.getExamId());
        hkOpenAcctInvestExpDTO.setAnswerDTOList(answerDTOList);
        hkOpenAcctInvestExpDTO.setWealthSource(request.getWealthSource());
        hkOpenAcctInvestExpDTO.setWealthSourceDesc(request.getWealthSourceDesc());
        hkOpenAcctInvestExpDTO.setOpenSaveType(request.getOpenSaveType());
        // 计算风险评估结果
        if (null != hkOpenAcctRiskAssessmentRiskResult) {
            if(StringUtils.isBlank(request.getExamId())){
                hkOpenAcctInvestExpDTO.setExamId(hkOpenAcctRiskAssessmentRiskResult.getExamId());
            }
            hkOpenAcctInvestExpDTO.setScore(hkOpenAcctRiskAssessmentRiskResult.getScore());
            hkOpenAcctInvestExpDTO.setRiskToleranceDate(hkOpenAcctRiskAssessmentRiskResult.getRiskToleranceDate());
            hkOpenAcctInvestExpDTO.setRiskToleranceLevel(hkOpenAcctRiskAssessmentRiskResult.getLevelValue());
            hkOpenAcctInvestExpDTO.setRiskToleranceTerm(hkOpenAcctRiskAssessmentRiskResult.getRiskToleranceTerm());
            hkOpenAcctInvestExpDTO.setDerivativeKnowledge(hkOpenAcctRiskAssessmentRiskResult.getDerivativeKnowledge());
        }
        return hkOpenAcctInvestExpDTO;
    }

    /**
     * @param accountInvestExpDTO 请求参数
     * @param hkOpenAcctCheckVOS
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.investinfo.OpenInvestVO
     * @description: 海外开户投资经验信息转VO
     * @author: jinqing.rao
     * @date: 2023/12/8 11:20
     * @since JDK 1.8
     */
    public static HkOpenAcctInvestVO toOpenInvestVO(HkOpenAcctInvestExpDTO accountInvestExpDTO, RiskToleranceLevelVO riskToleranceLevelVO, List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS) {
        HkOpenAcctInvestVO.HkOpenAcctInvestVOBuilder builder = HkOpenAcctInvestVO.builder();
        List<ImageVO> urls = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(accountInvestExpDTO.getAssetCertUrlList())) {
            urls = accountInvestExpDTO.getAssetCertUrlList().stream().map(m -> new ImageVO(m, m)).collect(Collectors.toList());
        }
        List<HkOpenAcctInvestVO.Answer> answerDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(accountInvestExpDTO.getAnswerDTOList())) {
            answerDTOList = accountInvestExpDTO.getAnswerDTOList().stream().map(m -> new HkOpenAcctInvestVO.Answer(m.getQuestionId(), m.getOptionChars())).collect(Collectors.toList());
        }
        HkOpenAcctInvestVO.OpenInvestValueVO openInvestValueVO = HkOpenAcctInvestVO.OpenInvestValueVO.builder()
                .investorQualification(accountInvestExpDTO.getInvestorQualification())
                .assetCertUrlList(urls)
                .examId(accountInvestExpDTO.getExamId())
                .answerDTOList(answerDTOList)
                .wealthSource(accountInvestExpDTO.getWealthSource())
                .wealthSourceDesc(accountInvestExpDTO.getWealthSourceDesc())
                .acceptDeclare(accountInvestExpDTO.getAcceptDeclare())
                .build();
        builder.checkResult(hkOpenAcctCheckVOS);
        return builder.openInvestValueVO(openInvestValueVO)
                .riskToleranceLevelVO(riskToleranceLevelVO)
                .build();
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctBankInfoDTO
     * @description: 海外开户银行卡信息转VO
     * @author: jinqing.rao
     * @date: 2023/12/8 13:31
     * @since JDK 1.8
     */
    public static HkOpenAcctBankInfoDTO toHkOpenAcctBankDTO(HkOpenAcctBankRequest request) {
        HkOpenAcctBankInfoDTO hkOpenAcctBankInfoDTO = new HkOpenAcctBankInfoDTO();
        List<ImageVO> jointAccountFileList = request.getJointAccountFileList();
        List<String> jointAccountFiles = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(jointAccountFileList)) {
            jointAccountFiles = jointAccountFileList.stream().map(ImageVO::getUrl).collect(Collectors.toList());
        }
        List<String> bankAcctImage = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getBankAcctImageList())) {
            bankAcctImage = request.getBankAcctImageList().stream().map(ImageVO::getUrl).collect(Collectors.toList());
        }
        List<String> currencyVO = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getCurrencyVOList())) {
            currencyVO = request.getCurrencyVOList().stream().map(HkOpenAcctBankInfoVO.CurrencyVO::getCurrencyCode).collect(Collectors.toList());
        }

        hkOpenAcctBankInfoDTO.setBankCode(request.getBankCode());
        hkOpenAcctBankInfoDTO.setBankAcctName(request.getBankAcctName());
        hkOpenAcctBankInfoDTO.setBankAcct(request.getBankAcct());
        hkOpenAcctBankInfoDTO.setJointAccount(request.getJointAccount());
        hkOpenAcctBankInfoDTO.setJointAccountFileList(jointAccountFiles);
        hkOpenAcctBankInfoDTO.setSwiftCode(request.getSwiftCode());
        if(YesNoEnum.YES.getCode().equals(request.getHasBrokerBank())){
            hkOpenAcctBankInfoDTO.setBrokerBankCode(request.getBrokerBankCode());
            hkOpenAcctBankInfoDTO.setBrokerSwiftCode(request.getBrokerSwiftCode());
            hkOpenAcctBankInfoDTO.setBrokerBankAcct(request.getBrokerBankAcct());
            hkOpenAcctBankInfoDTO.setBrokerBankName(request.getBrokerBankName());
            hkOpenAcctBankInfoDTO.setBrokerBankEnName(request.getBrokerBankEnName());
        }
        hkOpenAcctBankInfoDTO.setBankAcctPicturePaths(bankAcctImage);
        hkOpenAcctBankInfoDTO.setCurrencyVOList(currencyVO);
        hkOpenAcctBankInfoDTO.setBankAcctHolder(request.getBankAcctHolder());
        hkOpenAcctBankInfoDTO.setBankAcctEnName(request.getBankAcctEnName());
        hkOpenAcctBankInfoDTO.setOpenSaveType(request.getOpenSaveType());
        return hkOpenAcctBankInfoDTO;
    }

    /**
     * @param accountBankInfoDTO
     * @param hkOpenAcctCheckVOS
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.bankinfo.OpenBankVO
     * @description: 海外开户银行卡信息转VO
     * @author: jinqing.rao
     * @date: 2023/12/8 13:39
     * @since JDK 1.8
     */
    public static HkOpenAcctBankInfoVO toOpenBankVO(HkOpenAcctBankInfoDTO accountBankInfoDTO, List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS,Map<String, String> cmsConfigInfo) {
        HkOpenAcctBankInfoVO.HkOpenAcctBankInfoVOBuilder builder = HkOpenAcctBankInfoVO.builder();
        if (null == accountBankInfoDTO) {
            return HkOpenAcctBankInfoVO.builder().build();
        }
        List<ImageVO> imageVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(accountBankInfoDTO.getBankAcctPicturePaths())) {
            imageVOList = accountBankInfoDTO.getBankAcctPicturePaths().stream().map(m -> new ImageVO(m, m)).collect(Collectors.toList());
        }
        List<HkOpenAcctBankInfoVO.CurrencyVO> currencyVOS = accountBankInfoDTO.getCurrencyVOList().stream().map(m -> new HkOpenAcctBankInfoVO.CurrencyVO(m, HkOpenAcctBankCurrencyEnum.getEnumByCode(m) == null ? null : HkOpenAcctBankCurrencyEnum.getEnumByCode(m).getDesc())).collect(Collectors.toList());
        HkOpenAcctBankInfoVO.OpenBankValueVO openBankValueVO = HkOpenAcctBankInfoVO.OpenBankValueVO.builder()
                .bankCode(accountBankInfoDTO.getBankCode())
                .bankAcctName(accountBankInfoDTO.getBankAcctName())
                .bankAcctCnName(accountBankInfoDTO.getBankAcctCnName())
                .bankAcctEnName(accountBankInfoDTO.getBankAcctEnName())
                .bankAcct(accountBankInfoDTO.getBankAcct())
                .jointAccount(accountBankInfoDTO.getJointAccount())
                .jointAccountFileList(accountBankInfoDTO.getJointAccountFileList().stream().map(m -> new ImageVO(m, m)).collect(Collectors.toList()))
                .swiftCode(accountBankInfoDTO.getSwiftCode())
                .brokerBankCode(accountBankInfoDTO.getBrokerBankCode())
                .brokerBankAcct(accountBankInfoDTO.getBrokerBankAcct())
                .currencyVOList(currencyVOS)
                .bankAcctImageList(imageVOList)
                .openSaveType(accountBankInfoDTO.getOpenSaveType())
                .bankAcctHolder(accountBankInfoDTO.getBankAcctHolder())
                .brokerSwiftCode(accountBankInfoDTO.getBrokerSwiftCode())
                .brokerBankName(accountBankInfoDTO.getBrokerBankName())
                .brokerBankCnName(accountBankInfoDTO.getBrokerBankCnName())
                .brokerBankEnName(accountBankInfoDTO.getBrokerBankEnName())
                .bankLogoUrl(cmsConfigInfo.get(accountBankInfoDTO.getSwiftCode()))
                .brokerBankLogo(cmsConfigInfo.get(accountBankInfoDTO.getBrokerSwiftCode()))
                .build();
        builder.checkResult(hkOpenAcctCheckVOS);
        return builder.openBankValueVO(openBankValueVO)
                .build();
    }

    public static HkOpenAcctRiskQuestionDTO toHkOpenAcctRiskQuestionDTO(RiskQuestionRequest request, String hkCustNo, String custBirthDay,List<FundBasicInfoDTO> fundBasicInfoDTOS) {
        List<AnswerDTO> answerDTOList = request.getAnswerDTOList().stream().map(m -> new AnswerDTO(m.getQuestionId(), m.getOptionChars())).collect(Collectors.toList());
        HkOpenAcctRiskQuestionDTO hkOpenAcctRiskQuestionDTO = new HkOpenAcctRiskQuestionDTO();
        hkOpenAcctRiskQuestionDTO.setExamId(request.getExamId());
        hkOpenAcctRiskQuestionDTO.setHkCustNo(hkCustNo);
        hkOpenAcctRiskQuestionDTO.setAnswerDTOList(answerDTOList);
        hkOpenAcctRiskQuestionDTO.setBirthday(custBirthDay);
        if(CollectionUtils.isNotEmpty(fundBasicInfoDTOS)){
            List<HkOpenAcctRiskQuestionDTO.FoldFundRiskLevelDTO> riskLevelDTOS = fundBasicInfoDTOS.stream().map(m -> {
                HkOpenAcctRiskQuestionDTO.FoldFundRiskLevelDTO foldFundRiskLevelDTO = new HkOpenAcctRiskQuestionDTO.FoldFundRiskLevelDTO();
                foldFundRiskLevelDTO.setFundCode(m.getFundCode());
                foldFundRiskLevelDTO.setFundRiskLevel(m.getFundRiskLevel());
                foldFundRiskLevelDTO.setFundName(m.getFundName());
                return foldFundRiskLevelDTO;
            }).collect(Collectors.toList());
            hkOpenAcctRiskQuestionDTO.setFoldFundRiskLevelDTOList(riskLevelDTOS);
        }
        return hkOpenAcctRiskQuestionDTO;
    }

    /**
     * @param resultDTO
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.RiskToleranceLevelVO
     * @description: 风险测评接口转换成前端展示的VO
     * @author: jinqing.rao
     * @date: 2023/12/12 9:36
     * @since JDK 1.8
     */
    public static RiskToleranceLevelVO toRiskToleranceLevelVO(HkOpenAcctRiskCalculateResultDTO resultDTO) {
        if (null == resultDTO) {
            return RiskToleranceLevelVO.builder().build();
        }
        String riskToleranceInvalid = "1";
        if (StringUtils.isNotBlank(resultDTO.getRiskToleranceTerm())) {
            riskToleranceInvalid = DateUtils.isBeforeCurrentDate(resultDTO.getRiskToleranceTerm(), DateUtils.YYYYMMDD) ? "1" : "0";
        }
        return RiskToleranceLevelVO.builder()
                .examId(resultDTO.getExamId())
                .riskToleranceLevel(resultDTO.getLevelValue())
                .riskToleranceLevelDesc(RiskLevelEnum.getDescByCode(resultDTO.getLevelValue()))
                .riskToleranceInvalid(riskToleranceInvalid)
                .score(resultDTO.getScore())
                .assessmentTime(resultDTO.getRiskToleranceDate())
                .expirationDate(resultDTO.getRiskToleranceTerm())
                .build();
    }

    /**
     * @param hkOpenAcctExamInfo 风险测评的题目信息
     * @param age                年龄
     * @param request            历史答案的请求信息
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.RiskQuestionnaireVO
     * @description: 封装用户分险测评的题目信息, 包含用户的年龄信息, 题目分类, 以及题目的选项及历史答案数据
     * @author: jinqing.rao
     * @date: 2023/12/12 15:42
     * @since JDK 1.8
     */
    public static RiskQuestionnaireVO toRiskQuestionnaireVO(HkOpenAcctExamInfoDTO hkOpenAcctExamInfo, int age, HkOpenAcctRiskQuestionDTO request) {
        //基本信息问题列表
        List<RiskQuestionnaireVO.QuestionInfoVO> baseQuestions = new ArrayList<>();
        //资产情况问题列表
        List<RiskQuestionnaireVO.QuestionInfoVO> assetsSituationQuestions = new ArrayList<>();
        //投资经验问题列表
        List<RiskQuestionnaireVO.QuestionInfoVO> investmentExperienceQuestions = new ArrayList<>();
        //资产计划问题列表
        List<RiskQuestionnaireVO.QuestionInfoVO> investmentPlanQuestions = new ArrayList<>();
        //历史答题信息
        Map<String, List<String>> historyAnswerMap = new HashMap<>();
        if (null != request && CollectionUtils.isNotEmpty(request.getAnswerDTOList())) {
            historyAnswerMap = request.getAnswerDTOList().stream().collect(Collectors.toMap(AnswerDTO::getQuestionId, AnswerDTO::getOptionChars));
        }
        //循环分类
        Map<String, List<String>> finalHistoryAnswerMap = historyAnswerMap;
        hkOpenAcctExamInfo.getQuestionList().forEach(f -> {
            RiskQuestionnaireVO.QuestionInfoVO questionInfoVO = getQuestionInfoVO(f, finalHistoryAnswerMap);
            if (HkOpenAcctInvestorProfileEnum.BASIC_INFO.getCode().equals(f.getQuestionClassify())) {
                baseQuestions.add(questionInfoVO);
            }
            if (HkOpenAcctInvestorProfileEnum.ASSET_SITUATION.getCode().equals(f.getQuestionClassify())) {
                assetsSituationQuestions.add(questionInfoVO);
            }
            if (HkOpenAcctInvestorProfileEnum.INVESTMENT_EXPERIENCE.getCode().equals(f.getQuestionClassify())) {
                investmentExperienceQuestions.add(questionInfoVO);
            }
            if (HkOpenAcctInvestorProfileEnum.INVESTMENT_ATTITUDE.getCode().equals(f.getQuestionClassify())) {
                investmentPlanQuestions.add(questionInfoVO);
            }
        });
        return RiskQuestionnaireVO.builder()
                .examId(hkOpenAcctExamInfo.getExamId())
                .examType(hkOpenAcctExamInfo.getExamType())
                .age(String.valueOf(age))
                .baseQuestions(baseQuestions)
                .assetsSituationQuestions(assetsSituationQuestions)
                .investmentPlanQuestions(investmentPlanQuestions)
                .investmentExperienceQuestions(investmentExperienceQuestions)
                .build();
    }

    /**
     * @param f                题目信息
     * @param historyAnswerMap 题目的历史答案
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.RiskQuestionnaireVO.QuestionInfoVO
     * @description: 封装用户的风险测评题目信息
     * @author: jinqing.rao
     * @date: 2023/12/12 15:44
     * @since JDK 1.8
     */
    private static RiskQuestionnaireVO.QuestionInfoVO getQuestionInfoVO(HkOpenAcctExamInfoDTO.RiskQuestionDTO f, Map<String, List<String>> historyAnswerMap) {
        RiskQuestionnaireVO.QuestionInfoVO questionInfoVO = new RiskQuestionnaireVO.QuestionInfoVO();
        questionInfoVO.setQuestionId(f.getQuestionId());
        questionInfoVO.setQuestion(f.getQuestion());
        questionInfoVO.setSortNum(f.getSortNum());
        questionInfoVO.setQuestionClassify(f.getQuestionClassify());
        questionInfoVO.setMultipleOption(f.getSupportMulti());
        questionInfoVO.setClassifyName(f.getClassifyName());
        questionInfoVO.setHistoryAnswer(historyAnswerMap.get(f.getQuestionId()));
        List<HkOpenAcctExamInfoDTO.RiskOptionDTO> optionList = f.getOptionList();
        List<RiskQuestionnaireVO.OptionInfoVO> optionInfoVOS = optionList.stream().map(m -> {
            RiskQuestionnaireVO.OptionInfoVO optionInfoVO = new RiskQuestionnaireVO.OptionInfoVO();
            optionInfoVO.setOptionId(m.getOptionId());
            optionInfoVO.setOptionChar(m.getOptionChar());
            optionInfoVO.setOptionDesc(m.getOptionDesc());
            optionInfoVO.setDefaultLevel(m.getDefaultLevel());
            optionInfoVO.setLimitType(m.getLimitType());
            optionInfoVO.setLimitValue(m.getLimitValue());
            optionInfoVO.setScore(m.getScore());
            optionInfoVO.setDerivativeFlag(m.getDerivativeFlag());
            return optionInfoVO;
        }).collect(Collectors.toList());
        questionInfoVO.setOptions(optionInfoVOS);
        return questionInfoVO;
    }

    /**
     * @param acctAddressInfoDTO 地址信息
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctAddressInfoVO
     * @description: 地址信息
     * @author: jinqing.rao
     * @date: 2023/12/19 15:48
     * @since JDK 1.8
     */
    public static HkOpenAcctAddressInfoVO toHkOpenAcctAddressInfoVO(HkOpenAcctAddressInfoDTO acctAddressInfoDTO) {
        HkOpenAcctAddressInfoVO hkOpenAcctAddressInfoVO = new HkOpenAcctAddressInfoVO();
        if (null == acctAddressInfoDTO) {
            return hkOpenAcctAddressInfoVO;
        }
        hkOpenAcctAddressInfoVO.setCountryCode(acctAddressInfoDTO.getCountryCode());
        hkOpenAcctAddressInfoVO.setCountryDesc(acctAddressInfoDTO.getCountryDesc());
        hkOpenAcctAddressInfoVO.setCountryEnglishDesc(acctAddressInfoDTO.getCountryEnglishDesc());
        hkOpenAcctAddressInfoVO.setProvCode(acctAddressInfoDTO.getProvCode());
        hkOpenAcctAddressInfoVO.setProvDesc(acctAddressInfoDTO.getProvDesc());
        hkOpenAcctAddressInfoVO.setProvEnglishDesc(acctAddressInfoDTO.getProvEnglishDesc());
        hkOpenAcctAddressInfoVO.setCityCode(acctAddressInfoDTO.getCityCode());
        hkOpenAcctAddressInfoVO.setCityDesc(acctAddressInfoDTO.getCityDesc());
        hkOpenAcctAddressInfoVO.setCityEnglishDesc(acctAddressInfoDTO.getCityEnglishDesc());
        hkOpenAcctAddressInfoVO.setCountyCode(acctAddressInfoDTO.getCountyCode());
        hkOpenAcctAddressInfoVO.setCountyDesc(acctAddressInfoDTO.getCountyDesc());
        hkOpenAcctAddressInfoVO.setCountyEnglishDesc(acctAddressInfoDTO.getCountyEnglishDesc());
        hkOpenAcctAddressInfoVO.setDetailAddrCn(acctAddressInfoDTO.getDetailAddrCn());
        hkOpenAcctAddressInfoVO.setDetailAddrEn(acctAddressInfoDTO.getDetailAddrEn());
        hkOpenAcctAddressInfoVO.setStateEn(acctAddressInfoDTO.getStateEn());
        hkOpenAcctAddressInfoVO.setTownEn(acctAddressInfoDTO.getTownEn());
        return hkOpenAcctAddressInfoVO;
    }

    /**
     * @param request 请求参数
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctAddressInfoDTO
     * @description: 地址详细信息转成DTO
     * @author: jinqing.rao
     * @date: 2023/12/19 16:09
     * @since JDK 1.8
     */
    public static HkOpenAcctAddressInfoDTO toHkOpenAcctAddressInfoDTO(HkOpenAcctProCityRequest request) {
        return HkOpenAcctAddressInfoDTO.builder()
                .countryCode(request.getCountryCode())
                .countryDesc(request.getCountryDesc())
                .countryEnglishDesc(request.getCountryEnglishDesc())
                .provCode(request.getProvCode())
                .provDesc(request.getProvDesc())
                .provEnglishDesc(request.getProvEnglishDesc())
                .cityCode(request.getCityCode())
                .cityDesc(request.getCityDesc())
                .cityEnglishDesc(request.getCityEnglishDesc())
                .countyCode(request.getCountyCode())
                .countyDesc(request.getCountyDesc())
                .countyEnglishDesc(request.getCountyEnglishDesc())
                .detailAddrCn(request.getDetailAddrCn())
                .detailAddrEn(request.getDetailAddrEn())
                .stateEn(request.getStateEn())
                .townEn(request.getTownEn())
                .build();
    }

    /**
     * @param detailAddrCn  中文地址
     * @param englishAddress 翻译后的英文地址
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctProCitySaveVO
     * @description: 商品地址信息保存结果
     * @author: jinqing.rao
     * @date: 2023/12/19 17:15
     * @since JDK 1.8
     */
    public static HkOpenAcctProCitySaveVO toHkOpenAcctProCitySaveVO(String countryCode, String detailAddrCn, String englishAddress) {
        HkOpenAcctProCitySaveVO.HkOpenAcctProCitySaveVOBuilder builder = HkOpenAcctProCitySaveVO.builder();
        //非中国的不展示中文地址
        if (HkOpenAcctIdTypeUtils.SPECIAL_ADMINISTRATIVE_REGION.contains(countryCode) || HkOpenAcctIdTypeUtils.CN.equals(countryCode)) {
            builder.detailAddrCn(detailAddrCn).detailAddrEn(englishAddress);
        } else {
            builder.detailAddrEn(detailAddrCn);
        }
        return builder.build();
    }

    public static BaiduTranslateForeignRequestDTO toBaiduTranslateForeignRequestDTO(String detail, String from, String to) {
        //去除开头和结尾的空格符
        detail = StringUtils.isBlank(detail) ? detail : detail.trim();
        return BaiduTranslateForeignRequestDTO.builder()
                .from(from)
                .to(to)
                .openTranslateInfoList(Arrays.asList(BaiduTranslateForeignInfoDTO.builder().uuidKey(UUID.randomUUID().toString()).chineseLanguage(detail).build()))
                .build();
    }


    public static HkOpenAcctAddressInfoVO getHkOpenAcctAccountOccupationAddressInfoVO(HkOpenAcctOccupationDTO accountOccupationDTO, Map<String, String> countryInfoMap, Map<String, String> cityInfoMap) {
        HkOpenAcctAddressInfoVO hkOpenAcctAddressInfoVO = new HkOpenAcctAddressInfoVO();
        hkOpenAcctAddressInfoVO.setCountryCode(accountOccupationDTO.getEmplCountryCode());
        hkOpenAcctAddressInfoVO.setCountryDesc(countryInfoMap.get(accountOccupationDTO.getEmplCountryCode()));
        hkOpenAcctAddressInfoVO.setCountryEnglishDesc(accountOccupationDTO.getEmplCountryEnDesc());
        hkOpenAcctAddressInfoVO.setProvCode(accountOccupationDTO.getEmplProvCode());
        hkOpenAcctAddressInfoVO.setProvDesc(cityInfoMap.get(accountOccupationDTO.getEmplProvCode()));
        hkOpenAcctAddressInfoVO.setProvEnglishDesc(accountOccupationDTO.getEmplProvEnDesc());
        hkOpenAcctAddressInfoVO.setCityCode(accountOccupationDTO.getEmplCityCode());
        hkOpenAcctAddressInfoVO.setCityDesc(cityInfoMap.get(accountOccupationDTO.getEmplCityCode()));
        hkOpenAcctAddressInfoVO.setCityEnglishDesc(accountOccupationDTO.getEmplCityEnDesc());
        hkOpenAcctAddressInfoVO.setCountyCode(accountOccupationDTO.getEmplCountyCode());
        hkOpenAcctAddressInfoVO.setCountyDesc(cityInfoMap.get(accountOccupationDTO.getEmplCountyCode()));
        hkOpenAcctAddressInfoVO.setCountyEnglishDesc(accountOccupationDTO.getEmplCountyEnDesc());
        hkOpenAcctAddressInfoVO.setDetailAddrCn(accountOccupationDTO.getEmplAddr());
        hkOpenAcctAddressInfoVO.setDetailAddrEn(accountOccupationDTO.getEmplAddrEn());
        hkOpenAcctAddressInfoVO.setStateEn(accountOccupationDTO.getEmplState());
        hkOpenAcctAddressInfoVO.setTownEn(accountOccupationDTO.getEmplTown());
        return hkOpenAcctAddressInfoVO;
    }

    public static HkOpenAcctESignatureVO toHkOpenAcctESignatureVO(String userName, List<ImageVO> imageVOList,List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS ) {
        HkOpenAcctESignatureVO hkOpenAcctESignatureVO = new HkOpenAcctESignatureVO();
        hkOpenAcctESignatureVO.setUserName(userName);
        hkOpenAcctESignatureVO.setSignatureImages(imageVOList);
        hkOpenAcctESignatureVO.setCheckResult(hkOpenAcctCheckVOS);
        return hkOpenAcctESignatureVO;
    }


    public static List<HkCustFundHoldRiskLevelVO.FundHoldRiskLevelVO> toFundHoldRiskLevelVOList(List<FundBasicInfoDTO> fundBasicInfoDTOS,String riskLevel) {
        if(CollectionUtils.isEmpty(fundBasicInfoDTOS)){
            return new ArrayList<>();
        }
        return fundBasicInfoDTOS.stream()
                .filter(OpenAcctConvert::notChildFund)
                .filter(f -> checkLevel(riskLevel, f))// 剔除子基金
                .map(m -> {
            HkCustFundHoldRiskLevelVO.FundHoldRiskLevelVO fundHoldRiskLevelVO = new HkCustFundHoldRiskLevelVO.FundHoldRiskLevelVO();
            fundHoldRiskLevelVO.setFundRiskLevel(m.getFundRiskLevel());
            fundHoldRiskLevelVO.setFundCode(m.getFundCode());
            fundHoldRiskLevelVO.setFundName(m.getFundAbbr());
            return fundHoldRiskLevelVO;
        }).collect(Collectors.toList());
    }

    private static boolean checkLevel(String riskLevel, FundBasicInfoDTO f) {
        if(StringUtils.isBlank(riskLevel)){
            return true;
        }
        if(StringUtils.isBlank(f.getFundRiskLevel())){
            return false;
        }
        return Integer.valueOf(f.getFundRiskLevel()).compareTo(Integer.valueOf(riskLevel)) > 0;
    }

    /**
     * @description: 判断是否是母子基金
     * @param f
     * @return boolean
     * @author: jinqing.rao
     * @date: 2024/10/16 13:55
     * @since JDK 1.8
     */
    private static boolean notChildFund(FundBasicInfoDTO f) {
        // 默认普通基金
        if(StringUtils.isBlank(f.getIsMotherChildFund())){
            return true;
        }
        // 不是母子基金结构
        if(YesNoEnum.NO.getCode().equals(f.getIsMotherChildFund())){
            return true;
        }
        // 子基金等母基金，表示母基金,不相等表示子基金
        return StringUtils.equals(f.getFundCode(), f.getMainFundCode());
    }
}
