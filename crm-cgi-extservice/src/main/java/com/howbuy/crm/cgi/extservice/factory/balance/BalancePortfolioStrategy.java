/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.factory.balance;

import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.PortfolioDetail;

/**
 * @description: 资产投资组合策略工厂
 * <AUTHOR>
 * @date 2025/6/23 16:19
 * @since JDK 1.8
 */
public interface BalancePortfolioStrategy<T extends PortfolioDetail> {

    /**
     * 获取当前策略支持的类型
     */
    Class<T> getSupportedType();

    /**
     * 根据上下文内容执行策略
     */
    T calculate(BalanceContent content);

}
