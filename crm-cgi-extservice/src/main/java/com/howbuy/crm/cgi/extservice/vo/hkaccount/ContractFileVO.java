/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (合同文件对象vo)
 * @date 2024/2/22 18:28
 * @since JDK 1.8
 */

@Getter
@Setter
public class ContractFileVO implements Serializable {

    private static final long serialVersionUID = 6042344295375063258L;
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 签约日期 yy-mm-dd hh:mm:ss
     */
    private String signDate;

    /**
     * 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减,
     */
    private String businessType;

    /**
     * 业务类型描述
     */
    private String businessTypeDesc;

    /**
     * 业务类型
     */
    private String contractType;

    /**
     * 资料订单id
     */
    private String orderId;

    /**
     * 合同文件列表数据
     */
    private List<OpenFileVO> fileList;


}