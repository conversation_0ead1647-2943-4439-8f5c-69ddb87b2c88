package com.howbuy.crm.cgi.extservice.request.statement;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: 发送邮箱请求类
 * @author: 陈杰文
 * @date: 2025-06-17 16:42:18
 * @since JDK 1.8
 */
@Setter
@Getter
public class SendStatementRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务类型 1.结单发送邮箱
     */
    @NotBlank(message = "业务类型不能为空")
    private String bizType;

    /**
     * 邮箱密文
     */
    private String fundTxAcctNo;

    /**
     * 邮箱摘要
     */
    @NotBlank(message = "邮箱摘要不能为空")
    private String hkCustNo;
} 