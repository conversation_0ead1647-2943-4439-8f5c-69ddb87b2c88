/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.fundfold;

import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.extservice.vo.fundfold.CustFundFoldResponseVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.FundHoldingsOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 基金持仓查询
 * @date 2024/9/6 10:34
 * @since JDK 1.8
 */
@Service
public class CustFundFoldService {

    @Resource
    private FundHoldingsOuterService fundHoldingsOuterService;

    @Resource
    private QueryFundBasicInfoOuterService queryFundBasicInfoOuterService;

    /**
     * @param hkCustNo 香港客户号
     * @return com.howbuy.crm.cgi.extservice.vo.fundfold.CustFundFoldResponseVO
     * @description: 查询用户的持仓基金信息, 基金编码和基金名称
     * @author: jinqing.rao
     * @date: 2024/9/6 15:59
     * @since JDK 1.8
     */
    public CustFundFoldResponseVO getFundHoldInfo(String hkCustNo) {
        //查询用户的持仓信息
        List<String> holdFundCodeList = fundHoldingsOuterService.queryCustFundHoldFundByHkCustNo(hkCustNo);
        if (CollectionUtils.isEmpty(holdFundCodeList)) {
            return new CustFundFoldResponseVO();
        }
        // 去重
        List<String> fundCodes = holdFundCodeList.stream()
                .distinct()
                .collect(Collectors.toList());
        List<FundBasicInfoDTO> fundBasicInfoDTOS = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList(fundCodes);

        CustFundFoldResponseVO custFundFoldResponseVO = new CustFundFoldResponseVO();
        List<CustFundFoldResponseVO.FundHoldVO> fundHoldVOS = new ArrayList<>(fundBasicInfoDTOS.stream()
                .map(item -> {
                    CustFundFoldResponseVO.FundHoldVO fundHoldVO = new CustFundFoldResponseVO.FundHoldVO();
                    if (hasMotherFund(item) && StringUtils.isNotBlank(item.getMainFundCode())) {
                        fundHoldVO.setFundCode(item.getMainFundCode());
                        fundHoldVO.setFundName(item.getFundName());
                    } else {
                        fundHoldVO.setFundCode(item.getFundCode());
                        fundHoldVO.setFundName(item.getFundName());
                    }
                    return fundHoldVO;
                })
                .collect(Collectors.toMap(CustFundFoldResponseVO.FundHoldVO::getFundCode, fund -> fund, (oldValue, newValue) -> oldValue))
                .values());
        custFundFoldResponseVO.setFundHoldList(fundHoldVOS);
        return custFundFoldResponseVO;
    }

    /**
     * @description: 是否有母子基金
     * @param fundBasicInfoDTO
     * @return boolean
     * @author: jinqing.rao
     * @date: 2024/11/21 15:21
     * @since JDK 1.8
     */
    private static boolean hasMotherFund(FundBasicInfoDTO fundBasicInfoDTO) {
        if (StringUtils.isBlank(fundBasicInfoDTO.getIsMotherChildFund())) {
            return false;
        }
        if (YesNoEnum.NO.getCode().equals(fundBasicInfoDTO.getIsMotherChildFund())) {
            return false;
        }
        return !fundBasicInfoDTO.getFundCode().equals(fundBasicInfoDTO.getMainFundCode());
    }
}
