/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 储蓄罐文件VO
 * @author: system
 * @date: 2025/7/3
 * @since JDK 1.8
 */
@Data
public class PiggyFileVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件类型
     * 1-开通文件 2-变更文件
     */
    private String fileType;

    /**
     * 文件类型描述
     */
    private String fileTypeDesc;

    /**
     * 操作时间
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    private String operateTime;

    /**
     * 文件列表
     */
    private List<OpenFileVO> fileList;
}
