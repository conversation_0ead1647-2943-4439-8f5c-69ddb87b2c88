package com.howbuy.crm.cgi.extservice.common.enums.balance;

import org.apache.commons.lang3.StringUtils;

public enum HkBalanceDisPlayCurrencyCodeEnum {

    /**
     * 156-人民币
     */
    RMB("156", "元"),
    /**
     * 840-美元
     */
    USD("840", "美元"),
    /**
     * 344-港元
     */
    HKD("344", "港元"),
    ;

    private final String code;

    private final String description;

    HkBalanceDisPlayCurrencyCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * @description: 通过Code获取枚举
     * @param code
     * @return com.howbuy.crm.cgi.extservice.common.enums.balance.HkBalanceDisPlayCurrencyCodeEnum
     * @author: jinqing.rao
     * @date: 2025/6/23 14:24
     * @since JDK 1.8
     */
    public static HkBalanceDisPlayCurrencyCodeEnum getEnumByCode(String code) {
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (HkBalanceDisPlayCurrencyCodeEnum value : HkBalanceDisPlayCurrencyCodeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * @description: 通过Code获取描述
     * @param code
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/6/23 14:24
     * @since JDK 1.8
     */
    public static String getDescriptionByCode(String code) {
        for (HkBalanceDisPlayCurrencyCodeEnum value : HkBalanceDisPlayCurrencyCodeEnum.values()) {
            if (value.code.equals(code)) {
                return value.description;
            }
        }
        return null;
    }


    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
