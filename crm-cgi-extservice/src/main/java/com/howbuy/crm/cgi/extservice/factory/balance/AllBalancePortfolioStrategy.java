package com.howbuy.crm.cgi.extservice.factory.balance;

import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.utils.BigDecimalUtils;
import com.howbuy.crm.cgi.extservice.common.annotation.FundTypeSupport;
import com.howbuy.crm.cgi.extservice.common.enums.balance.HkCustBalanceDisPalyCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.utils.balance.BalanceUtils;
import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.TotalBalancePortfolioDetail;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.AcctBalanceDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.BalanceBeanDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryBalanceDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.UnconfirmeProductDTO;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@FundTypeSupport(HkCustBalanceDisPalyCategoryEnum.ALL_BALANCE)
public class AllBalancePortfolioStrategy extends AbstractBalancePortfolioStrategy implements BalancePortfolioStrategy<TotalBalancePortfolioDetail> {

    @Override
    public Class<TotalBalancePortfolioDetail> getSupportedType() {
        return TotalBalancePortfolioDetail.class;
    }

    @Override
    public TotalBalancePortfolioDetail calculate(BalanceContent content) {
        List<HkFundTxAcctDTO> fundTxAcctNoDTOList = content.getFundTxAcctNoDTOList();
        // 获取非全委基金
        List<String> nonFullFundTxAcctNoList = fundTxAcctNoDTOList.stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.NON_FULL.getCode().equals(m.getFundTxAccType()))
                .map(HkFundTxAcctDTO::getFundTxAcctNo)
                .collect(Collectors.toList());
        // 全委基金
        List<String> fullFundTxAcctNoList = fundTxAcctNoDTOList.stream().
                filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.FULL.getCode().equals(m.getFundTxAccType()))
                .map(HkFundTxAcctDTO::getFundTxAcctNo)
                .collect(Collectors.toList());

        // 查询总持仓信息接口
        QueryBalanceDTO queryBalanceDTO = new QueryBalanceDTO();
        queryBalanceDTO.setHkCustNo(content.getHkCustNo());
        queryBalanceDTO.setDisPlayCurrency(content.getDisPlayCurrencyCode());
        queryBalanceDTO.setDisCodeList(getDisCodeList());
        AcctBalanceDTO acctBalanceDTO = queryBalanceOuterService.queryHkCustBalance(queryBalanceDTO);

        if (null == acctBalanceDTO) {
            log.info("AllBalancePortfolioStrategy >>> 没有持仓信息,返回空数据");
            return TotalBalancePortfolioDetail.builder().totalAsset(BigDecimal.ZERO).build();
        }

        List<BalanceBeanDTO> allBalanceList = acctBalanceDTO.getBalanceList();
        if (CollectionUtils.isEmpty(allBalanceList)) {
            log.info("AllBalancePortfolioStrategy >>> 没有持仓订单明细记录,，返回空数据");
            return TotalBalancePortfolioDetail.builder().totalAsset(BigDecimal.ZERO).build();
        }
        // 总资产 = 持仓金额+ 在途金额
        BigDecimal totalAsset = BigDecimalUtils.add(acctBalanceDTO.getDisPlayCurrencyTotalMarketValue(), acctBalanceDTO.getTotalDisCurUnconfirmedAmt());
        // 记录储蓄罐基金Code
        List<String> piggyFundList = new ArrayList<>();
        // 储蓄罐持仓
        List<BalanceBeanDTO> piggyFundBalanceList = new ArrayList<>();
        // 非全委基金
        List<BalanceBeanDTO> nonFullFundBalanceList = new ArrayList<>();
        // 全委基金
        List<BalanceBeanDTO> fullFundBalanceList = new ArrayList<>();
        buildBalanceBeanDTO(allBalanceList, nonFullFundTxAcctNoList, piggyFundList, piggyFundBalanceList, nonFullFundBalanceList, fullFundTxAcctNoList, fullFundBalanceList);
        List<UnconfirmeProductDTO> unconfirmeProducts = acctBalanceDTO.getUnconfirmeProducts();
        // 储蓄罐在途持仓
        List<UnconfirmeProductDTO> piggyFundInTransitBalanceList = new ArrayList<>();
        // 非全委基金在途持仓
        List<UnconfirmeProductDTO> nonFullFundInTransitBalanceList = new ArrayList<>();
        // 全委基金在途持仓
        List<UnconfirmeProductDTO> fullFundInTransitBalanceList = new ArrayList<>();
        buildUnconfirmeProductDTO(unconfirmeProducts, piggyFundList, nonFullFundTxAcctNoList, piggyFundInTransitBalanceList, nonFullFundInTransitBalanceList, fullFundTxAcctNoList, fullFundInTransitBalanceList);
        // 储蓄罐总资产
        BigDecimal piggyFundTotalDisCurMarketValue = calculateTotalDisCurMarketValue(piggyFundBalanceList, piggyFundInTransitBalanceList);
        // 储蓄罐总收益
        BigDecimal piggyTotalIncome = BalanceUtils.sumBigDecimal(piggyFundBalanceList, BalanceBeanDTO::getDisCurCurrentAssetCurrency);

        // 储蓄罐总状态
        String piggyIncomeCalStat = BalanceUtils.calculateIncomeCalStat(piggyFundBalanceList);
        // 非全委基金总资产
        BigDecimal nonFullFundTotalDisCurMarketValue = calculateTotalDisCurMarketValue(nonFullFundBalanceList, nonFullFundInTransitBalanceList);
        // 非全委基金总收益
        BigDecimal nonFullTotalIncome = BalanceUtils.sumBigDecimal(nonFullFundBalanceList, BalanceBeanDTO::getDisCurCurrentAssetCurrency);
        // 非全委基金总状态
        String nonFullIncomeCalStat = BalanceUtils.calculateIncomeCalStat(nonFullFundBalanceList);
        // 全委基金总资产
        BigDecimal fullFundTotalDisCurMarketValue = calculateTotalDisCurMarketValue(fullFundBalanceList, fullFundInTransitBalanceList);
        // 全委基金总收益
        BigDecimal fullTotalIncome = BalanceUtils.sumBigDecimal(fullFundBalanceList, BalanceBeanDTO::getDisCurCurrentAssetCurrency);
        // 全委基金总状态
        String fullIncomeCalStat = BalanceUtils.calculateIncomeCalStat(fullFundBalanceList);

        return TotalBalancePortfolioDetail.builder()
                .totalAsset(totalAsset)
                .inTransitTradeAmt(acctBalanceDTO.getTotalDisCurUnconfirmedAmt())
                .piggyAsset(piggyFundTotalDisCurMarketValue)
                .piggyIncome(piggyTotalIncome)
                .piggyIncomeCalStatus(piggyIncomeCalStat)
                .fundAsset(nonFullFundTotalDisCurMarketValue)
                .fundIncome(nonFullTotalIncome)
                .fundIncomeCalStatus(nonFullIncomeCalStat)
                .fullFundAsset(fullFundTotalDisCurMarketValue)
                .fullFundIncome(fullTotalIncome)
                .fullFundIncomeCalStatus(fullIncomeCalStat)
                .itemList(allBalanceList)
                .build();
    }

    /**
     * @description: 数据分类
     * @param allBalanceList	
     * @param nonFullFundTxAcctNoList	
     * @param piggyFundList	
     * @param piggyFundBalanceList	
     * @param nonFullFundBalanceList	
     * @param fullFundTxAcctNoList	
     * @param fullFundBalanceList
     * @return void
     * @author: jinqing.rao
     * @date: 2025/7/14 13:17
     * @since JDK 1.8
     */
    private static void buildBalanceBeanDTO(List<BalanceBeanDTO> allBalanceList, List<String> nonFullFundTxAcctNoList, List<String> piggyFundList, List<BalanceBeanDTO> piggyFundBalanceList, List<BalanceBeanDTO> nonFullFundBalanceList, List<String> fullFundTxAcctNoList, List<BalanceBeanDTO> fullFundBalanceList) {
        for (BalanceBeanDTO balanceBeanDTO : allBalanceList) {
            // 储蓄罐基金 (非全委)
            if (YesNoEnum.YES.getCode().equals(balanceBeanDTO.getSfhwcxg()) && nonFullFundTxAcctNoList.contains(balanceBeanDTO.getFundTxAcctNo())) {
                piggyFundList.add(balanceBeanDTO.getProductCode());
                piggyFundBalanceList.add(balanceBeanDTO);
            }
            // 非全委基金
            if (!YesNoEnum.YES.getCode().equals(balanceBeanDTO.getSfhwcxg()) && nonFullFundTxAcctNoList.contains(balanceBeanDTO.getFundTxAcctNo())) {
                nonFullFundBalanceList.add(balanceBeanDTO);
            }
            // 全委基金
            if (fullFundTxAcctNoList.contains(balanceBeanDTO.getFundTxAcctNo())) {
                fullFundBalanceList.add(balanceBeanDTO);
            }
        }
    }

    /**
     * @description: 数据分类
     * @param unconfirmeProducts	
     * @param piggyFundList	
     * @param nonFullFundTxAcctNoList	
     * @param piggyFundInTransitBalanceList	
     * @param nonFullFundInTransitBalanceList	
     * @param fullFundTxAcctNoList	
     * @param fullFundInTransitBalanceList
     * @return void
     * @author: jinqing.rao
     * @date: 2025/7/14 13:17
     * @since JDK 1.8
     */
    private static void buildUnconfirmeProductDTO(List<UnconfirmeProductDTO> unconfirmeProducts, List<String> piggyFundList, List<String> nonFullFundTxAcctNoList, List<UnconfirmeProductDTO> piggyFundInTransitBalanceList, List<UnconfirmeProductDTO> nonFullFundInTransitBalanceList, List<String> fullFundTxAcctNoList, List<UnconfirmeProductDTO> fullFundInTransitBalanceList) {
        for (UnconfirmeProductDTO unconfirmeProductDTO : unconfirmeProducts) {
            // 储蓄罐基金 (非全委)
            if (piggyFundList.contains(unconfirmeProductDTO.getFundCode()) && nonFullFundTxAcctNoList.contains(unconfirmeProductDTO.getFundTxAcctNo())) {
                piggyFundInTransitBalanceList.add(unconfirmeProductDTO);
            }
            // 非全委基金
            if (!piggyFundList.contains(unconfirmeProductDTO.getFundCode()) && nonFullFundTxAcctNoList.contains(unconfirmeProductDTO.getFundTxAcctNo())) {
                nonFullFundInTransitBalanceList.add(unconfirmeProductDTO);
            }
            // 全委基金
            if (fullFundTxAcctNoList.contains(unconfirmeProductDTO.getFundTxAcctNo())) {
                fullFundInTransitBalanceList.add(unconfirmeProductDTO);
            }
        }
    }

    /**
     * @param fundBalanceList          持仓资产
     * @param fundInTransitBalanceList 买入在途资产
     * @return java.math.BigDecimal
     * @description: 计算资产和买入在途的资产
     * @author: jinqing.rao
     * @date: 2025/7/8 11:04
     * @since JDK 1.8
     */
    public static BigDecimal calculateTotalDisCurMarketValue(List<BalanceBeanDTO> fundBalanceList, List<UnconfirmeProductDTO> fundInTransitBalanceList) {
        BigDecimal totalBalance = BigDecimal.ZERO;
        BigDecimal totalInTransitBalance = BigDecimal.ZERO;


        if (CollectionUtils.isNotEmpty(fundBalanceList)) {
            totalBalance = fundBalanceList.stream()
                    .map(balance -> balance.getDisCurMarketValue() == null ? BigDecimal.ZERO : balance.getDisCurMarketValue())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if (CollectionUtils.isNotEmpty(fundInTransitBalanceList)) {
            totalInTransitBalance = fundInTransitBalanceList.stream()
                    .map(balance -> balance.getDisCurUnconfirmedAmt() == null ? BigDecimal.ZERO : balance.getDisCurUnconfirmedAmt())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return BigDecimalUtils.add(totalBalance, totalInTransitBalance);
    }

}