/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT Co., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.fund;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 购买日历VO
 * @author: 陈杰文
 * @date: 2025-06-25 11:28:45
 * @since JDK 1.8
 */
@Getter
@Setter
public class BuyCalendarVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否支持购买预约交易，1-是，0-否
     */
    private String supportPrebook;

    /**
     * 预约购买截止日期，格式：yyyy-MM-dd HH:mm
     */
    private String prebookBuyEndDt;

    /**
     * 预计打款截止时间，格式：yyyy-MM-dd HH:mm
     */
    private String prebookPayEndDt;

    /**
     * 当前购买开放开始日期，格式：yyyy-MM-dd
     */
    private String openStartDt;

    /**
     * 当前购买开放结束日期，格式：yyyy-MM-dd
     */
    private String openEndDt;

    /**
     * 预计交易日期，格式：yyyy-MM-dd
     */
    private String expectTradeDt;

    /**
     * 下单结束时间
     * 格式 HH:mm
     */
    private String orderEndTm;

    /**
     * 打款结束时间
     */
    private String paymentEndTm;

    /**
     * 预计上报日期
     */
    private String preSubmitTaDt;

    /**
     * 预计上报时间
     */
    private String preSubmitTaTm;
} 