package com.howbuy.crm.cgi.extservice.factory.balance;

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.extservice.common.annotation.FundTypeSupport;
import com.howbuy.crm.cgi.extservice.common.enums.balance.HkCustBalanceDisPalyCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.utils.balance.BalanceUtils;
import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.AllInTransitTradeBalancePortfolioDetail;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.capital.QueryInTransitTradeCapitalRequestDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.capital.QueryInTransitTradeCapitalResponseDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryBalanceDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryFinReceiptDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.capital.QueryInTransitTradeCapitalOuterService;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description: 全委基金在途策略
 * @author: 陈杰文
 * @date: 2025-07-010 16:22:11
 * @since JDK 1.8
 */
@Component
@Slf4j
@FundTypeSupport(HkCustBalanceDisPalyCategoryEnum.FULL_IN_TRANSIT)
public class FullInTransitPortfolioStrategy extends AbstractBalancePortfolioStrategy implements BalancePortfolioStrategy<AllInTransitTradeBalancePortfolioDetail> {

    @Resource
    private QueryInTransitTradeCapitalOuterService queryInTransitTradeCapitalOuterService;

    @Override
    public Class<AllInTransitTradeBalancePortfolioDetail> getSupportedType() {
        return AllInTransitTradeBalancePortfolioDetail.class;
    }

    @Override
    public AllInTransitTradeBalancePortfolioDetail calculate(BalanceContent content) {
        AllInTransitTradeBalancePortfolioDetail.AllInTransitTradeBalancePortfolioDetailBuilder builder = AllInTransitTradeBalancePortfolioDetail.builder();
        if (CollectionUtils.isEmpty(content.getFundTxAcctNoDTOList())) {
            log.info("基金交易账号为空,全委在途策略不执行，返回空数据 , hkCustNo :{} ", content.getHkCustNo());
            return AllInTransitTradeBalancePortfolioDetail.builder().build();
        }
        // 过滤出全委基金交易账号
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = content.getFundTxAcctNoDTOList().stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.FULL.getCode().equals(m.getFundTxAccType()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(hkFundTxAcctDTOS)) {
            log.error("用户有基金交易账号,没有全委的基金交易账号,全委持仓在途策略不执行，返回空数据 , hkCustNo :{} ", content.getHkCustNo());
            return AllInTransitTradeBalancePortfolioDetail.builder().build();
        }
        // 获取全委基金交易账号
        List<String> fullFundTxAcctNoList = BalanceUtils.extractFieldList(hkFundTxAcctDTOS, HkFundTxAcctDTO::getFundTxAcctNo);

        // 获取海外储蓄罐基金列表信息
        List<FundBasicInfoDTO> fundBasicInfoDTOList = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList();
        List<String> fundCodes = CollectionUtils.isEmpty(fundBasicInfoDTOList) ? new ArrayList<>() :
                fundBasicInfoDTOList.stream()
                        .map(FundBasicInfoDTO::getFundCode)
                        .distinct()
                        .collect(Collectors.toList());

        // 查询在途信息接口
        QueryBalanceDTO queryBalanceDTO = new QueryBalanceDTO();
        queryBalanceDTO.setHkCustNo(content.getHkCustNo());
        // 指定全委基金交易账号
        queryBalanceDTO.setFundTxAcctNoList(fullFundTxAcctNoList);
        // 排除海外储蓄罐产品
        queryBalanceDTO.setExcludeFundCodeList(fundCodes);
        queryBalanceDTO.setDisPlayCurrency(content.getDisPlayCurrencyCode());

        // 查询在途数据
        QueryFinReceiptDTO queryFinReceiptDTO = queryBalanceOuterService.queryInTransitTradeBalance(queryBalanceDTO);
        // 全委在途资金信息（在途资金数量 & 在途资金订单号）
        buildInTransitTradeCapitalCountAndDealNo(content, builder);

        // 在途交易相关数据
        List<String> allInTransitList = this.mergeUnpaidAndUnconfirmed(queryFinReceiptDTO);
        String inTransitDealNo = this.getSingleDealNoIfOnlyOne(allInTransitList);

        return builder
                // 在途交易笔数
                .inTransitTradeCount(String.valueOf(allInTransitList.size()))
                // 在途交易订单号
                .inTransitTradeDealNo(inTransitDealNo)
                .build();
    }

    /**
     * @param content
     * @param builder
     * @return void
     * @description: 在途资金信息
     * @author: jinqing.rao
     * @date: 2025/7/9 13:59
     * @since JDK 1.8
     */
    private void buildInTransitTradeCapitalCountAndDealNo(BalanceContent content, AllInTransitTradeBalancePortfolioDetail.AllInTransitTradeBalancePortfolioDetailBuilder builder) {

        QueryInTransitTradeCapitalResponseDTO queryInTransitTradeCapitalResponseDTO = queryInTransitTradeCapitalOuterService.queryInTransitTradeCapital(QueryInTransitTradeCapitalRequestDTO.builder()
                .hkCustNo(content.getHkCustNo())
                .build());
        if (null == queryInTransitTradeCapitalResponseDTO) {
            return;
        }
        if (CollectionUtils.isEmpty(queryInTransitTradeCapitalResponseDTO.getInTransitTradeCapitalList())) {
            return;
        }
        // 全委基金交易账号列表
        List<HkFundTxAcctDTO> fundTxAcctNoDTOList = content.getFundTxAcctNoDTOList();
        if (CollectionUtils.isEmpty(fundTxAcctNoDTOList)) {
            return;
        }

        List<String> fundTxAcctNoList = fundTxAcctNoDTOList.stream()
                .map(HkFundTxAcctDTO::getFundTxAcctNo)
                .distinct()
                .collect(Collectors.toList());

        List<QueryInTransitTradeCapitalResponseDTO.InTransitTradeCapitalDTO> inTransitTradeCapitalList = queryInTransitTradeCapitalResponseDTO.getInTransitTradeCapitalList();
        // 过滤掉入参全委基金交易账号的在途资金数据
        inTransitTradeCapitalList = inTransitTradeCapitalList.stream().filter(m -> fundTxAcctNoList.contains(m.getFundTxAcctNo()))
                .collect(Collectors.toList());

        // 在途交易资金笔数
        String inTransitTradeCapitalCount = String.valueOf(inTransitTradeCapitalList.size());
        // 在途交易资金订单号
        String inTransitTradeCapitalDealNo = null;
        if (inTransitTradeCapitalList.size() == Constants.NUMBER_ONE) {
            inTransitTradeCapitalDealNo = inTransitTradeCapitalList.get(Constants.NUMBER_ZERO).getDealNo();
        }
        builder.inTransitTradeCapitalCount(StringUtils.isEmpty(inTransitTradeCapitalCount) ? Constants.CONSTANT_ZERO : inTransitTradeCapitalCount);
        builder.inTransitTradeCapitalDealNo(inTransitTradeCapitalDealNo);
    }

    /**
     * @param dto 合并在途交易数量（待付款订单数量+待确认订单）
     * @return com.howbuy.crm.cgi.extservice.vo.balance.QueryFullFundAssetV1VO
     * @description: 查询全委基金资产接口
     * <AUTHOR>
     * @date 2025-07-03 13:15:21
     */
    private List<String> mergeUnpaidAndUnconfirmed(QueryFinReceiptDTO dto) {
        List<String> unpaid = Optional.ofNullable(dto.getUnpaidList())
                .orElse(Collections.emptyList());
        List<String> unconfirmed = Optional.ofNullable(dto.getUnconfirmedList())
                .orElse(Collections.emptyList());
        List<String> merged = new ArrayList<>(unpaid);
        merged.addAll(unconfirmed);
        return merged;
    }

    /**
     * @param allInTransitList 获取在途交易订单号
     * @return com.howbuy.crm.cgi.extservice.vo.balance.QueryFullFundAssetV1VO
     * @description: 只有一条数据时，获取订单号
     * <AUTHOR>
     * @date 2025-07-03 13:19:46
     */
    private String getSingleDealNoIfOnlyOne(List<String> allInTransitList) {
        return allInTransitList.size() == 1 ? allInTransitList.get(0) : null;
    }
}