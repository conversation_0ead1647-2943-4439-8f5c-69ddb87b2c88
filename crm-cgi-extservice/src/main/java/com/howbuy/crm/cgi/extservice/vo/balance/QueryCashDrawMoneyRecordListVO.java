package com.howbuy.crm.cgi.extservice.vo.balance;

/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 现金提取记录列表响应类
 * @author: 陈杰文
 * @date: 2025-06-17 17:46:35
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryCashDrawMoneyRecordListVO extends Body implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 现金提取记录
     */
    private List<CashDrawMoneyRecordVO> cashdRawMoneyRecordListVO;
} 