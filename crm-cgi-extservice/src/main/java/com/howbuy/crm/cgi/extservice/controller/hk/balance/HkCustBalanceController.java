/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hk.balance;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.balance.CashDrawMoneySubmitRequest;
import com.howbuy.crm.cgi.extservice.request.balance.QueryCashBalanceAssetDetailV1Request;
import com.howbuy.crm.cgi.extservice.request.balance.QueryCashDrawMoneyPageRequest;
import com.howbuy.crm.cgi.extservice.request.balance.QueryCashDrawMoneyRecordDetailRequest;
import com.howbuy.crm.cgi.extservice.request.balance.QueryCashDrawMoneyRecordListRequest;
import com.howbuy.crm.cgi.extservice.request.balance.QueryFinancialDetailListRequest;
import com.howbuy.crm.cgi.extservice.request.balance.QueryFundAssetDetailRequest;
import com.howbuy.crm.cgi.extservice.request.balance.QueryFundAssetRequest;
import com.howbuy.crm.cgi.extservice.request.balance.QueryFullFundAssetRequest;
import com.howbuy.crm.cgi.extservice.request.balance.QueryNotFullCashBalanceAssetRequest;
import com.howbuy.crm.cgi.extservice.request.balance.QueryPiggyAssetRequest;
import com.howbuy.crm.cgi.extservice.request.balance.QueryTotalAssetV1Request;
import com.howbuy.crm.cgi.extservice.service.balance.HkCustBalanceService;
import com.howbuy.crm.cgi.extservice.validator.balance.HkBalanceValidator;
import com.howbuy.crm.cgi.extservice.vo.balance.QueryCashBalanceAssetDetailVO;
import com.howbuy.crm.cgi.extservice.vo.balance.QueryCashDrawMoneyPageVO;
import com.howbuy.crm.cgi.extservice.vo.balance.QueryCashDrawMoneyRecordDetailVO;
import com.howbuy.crm.cgi.extservice.vo.balance.QueryCashDrawMoneyRecordListVO;
import com.howbuy.crm.cgi.extservice.vo.balance.QueryFinancialDetailListVO;
import com.howbuy.crm.cgi.extservice.vo.balance.QueryFundAssetDetailVO;
import com.howbuy.crm.cgi.extservice.vo.balance.QueryFundAssetVO;
import com.howbuy.crm.cgi.extservice.vo.balance.QueryFullFundAssetVO;
import com.howbuy.crm.cgi.extservice.vo.balance.QueryNotFullCashBalanceAssetVO;
import com.howbuy.crm.cgi.extservice.vo.balance.QueryPiggyAssetVO;
import com.howbuy.crm.cgi.extservice.vo.balance.QueryTotalAssetV1VO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 香港账户资产控制器
 * @author: jinqing.rao
 * @date: 2025/6/17 9:45
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hk/balance/")
public class HkCustBalanceController {

    @Resource
    private HkCustBalanceService hkCustBalanceService;

    /**
     * @api {POST} /ext/hk/balance/querytotalassetv1 queryTotalAssetV1()
     * @apiVersion 1.0.0
     * @apiGroup HkBalanceController
     * @apiName queryTotalAssetV1()
     * @apiDescription 查询总资产接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} disPlayCurrencyCode 当前展示的币种代码
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","disPlayCurrencyCode":"USD"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.totalAsset 总资产
     * @apiSuccess (响应结果) {String} data.showAsset 是否展示资产 0:隐藏, 1: 显示
     * @apiSuccess (响应结果) {String} data.inTransitTradeAmt 在途交易金额
     * @apiSuccess (响应结果) {String} data.inTransitTradeCount 在途交易笔数
     * @apiSuccess (响应结果) {String} data.inTransitTradeDealNo 在途交易订单号
     * @apiSuccess (响应结果) {String} data.inTransitTradeCapitalCount 在途交易资金笔数
     * @apiSuccess (响应结果) {String} data.inTransitTradeCapitalDealNo 在途交易资金订单号
     * @apiSuccess (响应结果) {String} data.inTransitPayVoucherCount 在途打款凭证笔数
     * @apiSuccess (响应结果) {String} data.inTransitPayVoucherNo 在途打款凭证编号
     * @apiSuccess (响应结果) {String} data.inTransitDrawMoneyAppCount 在途提款申请笔数
     * @apiSuccess (响应结果) {String} data.inTransitDrawMoneyAppNo 在途提款申请编号
     * @apiSuccess (响应结果) {String} data.inTransitDrawMoneyCapitalCount 在途提款资金笔数
     * @apiSuccess (响应结果) {String} data.inTransitDrawMoneyCapitalNo 在途提款资金编号
     * @apiSuccess (响应结果) {String} data.fundAsset 基金持仓资产
     * @apiSuccess (响应结果) {String} data.fundIncome 基金持仓收益
     * @apiSuccess (响应结果) {String} data.fundIncomeCalStatus 基金持仓收益计算状态 0-计算中;1-计算成功
     * @apiSuccess (响应结果) {String} data.piggyAsset 储蓄罐资产
     * @apiSuccess (响应结果) {String} data.piggyIncome 储蓄罐收益
     * @apiSuccess (响应结果) {String} data.piggyIncomeCalStatus 储蓄罐收益计算状态 0-计算中;1-计算成功
     * @apiSuccess (响应结果) {String} data.piggySignStatus 储蓄罐签约状态 0-未签约1-已签约2-已暂停
     * @apiSuccess (响应结果) {String} data.fullFundAsset 全委基金持仓资产
     * @apiSuccess (响应结果) {String} data.fullFundIncome 全委基金持仓收益
     * @apiSuccess (响应结果) {String} data.fullFundIncomeCalStatus 全委基金持仓收益计算状态 0-计算中;1-计算成功
     * @apiSuccess (响应结果) {String} data.existsNaFund 存在NA产品 0-不存在、1-存在
     * @apiSuccess (响应结果) {String} data.cashBalanceAsset 现金余额资产
     * @apiSuccess (响应结果) {String} data.agencyAgreementCount 补充协议个数
     * @apiSuccess (响应结果) {Array} data.supplementalAgreementInfo 补充协议信息
     * @apiSuccess (响应结果) {String} data.supplementalAgreementInfo.agreementId 协议ID
     * @apiSuccess (响应结果) {String} data.supplementalAgreementInfo.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.systemDate 系统日期 yyyyMMDD
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","data":{"totalAsset":"100000.00","showAsset":"1","inTransitTradeAmt":"0.00","inTransitTradeCount":"0","fundAsset":"50000.00","fundIncome":"1000.00","fundIncomeCalStatus":"1","piggyAsset":"30000.00","piggyIncome":"500.00","piggyIncomeCalStatus":"1","piggySignStatus":"1","fullFundAsset":"20000.00","fullFundIncome":"300.00","fullFundIncomeCalStatus":"1","existsNaFund":"0","cashBalanceAsset":"0.00","agencyAgreementCount":"1","supplementalAgreementInfo":[{"agreementId":"AGR001","fundCode":"FUND001"}],"systemDate":"20240224"},"description":"成功","timestampServer":"*************"}
     */
    @RequestMapping("querytotalassetv1")
    public CgiResponse<QueryTotalAssetV1VO> queryTotalAssetV1(@RequestBody QueryTotalAssetV1Request request) {
        // 参数校验
        HkBalanceValidator.validatorParams(request);
        return CgiResponse.ok(hkCustBalanceService.queryTotalAssetV1(request));
    }

    /**
     * @api {POST} /ext/hk/balance/querynotfullcashbalanceasset queryNotFullCashBalanceAsset()
     * @apiVersion 1.0.0
     * @apiGroup HkBalanceController
     * @apiName queryNotFullCashBalanceAsset()
     * @apiDescription 非全委托现金资产查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} disPlayCurrencyCode 当前展示的币种代码
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","disPlayCurrencyCode":"USD"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.showAsset 是否显示资产
     * @apiSuccess (响应结果) {String} data.cashBalanceAsset 现金资产总资产
     * @apiSuccess (响应结果) {String} data.availableBalance 可用余额
     * @apiSuccess (响应结果) {String} data.freezeBalance 冻结余额
     * @apiSuccess (响应结果) {String} data.inTransitDrawMoneyAppCount 在途提取申请笔数
     * @apiSuccess (响应结果) {String} data.inTransitDrawMoneyAppNo 在途提取申请单号
     * @apiSuccess (响应结果) {String} data.inTransitDrawMoneyCapitalCount 在途提取资金笔数
     * @apiSuccess (响应结果) {String} data.inTransitDrawMoneyCapitalNo 在途提取资金单号
     * @apiSuccess (响应结果) {String} data.inTransitPayVoucherCount 在途打款凭证笔数
     * @apiSuccess (响应结果) {String} data.inTransitPayVoucherNo 在途打款凭证单号
     * @apiSuccess (响应结果) {Array} data.cashBalanceDetailList 现金资产明细列表
     * @apiSuccess (响应结果) {String} data.cashBalanceDetailList.currencyCode 币种
     * @apiSuccess (响应结果) {String} data.cashBalanceDetailList.cashBalanceAsset 现金资产总资产
     * @apiSuccess (响应结果) {String} data.cashBalanceDetailList.availableBalance 可用余额
     * @apiSuccess (响应结果) {String} data.cashBalanceDetailList.freezeBalance 冻结余额
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","data":{"cashBalanceAsset":"50000.00","availableBalance":"45000.00","freezeBalance":"5000.00","inTransitDrawMoneyAppCount":"0","inTransitDrawMoneyAppNo":"","inTransitDrawMoneyCapitalCount":"0","inTransitDrawMoneyCapitalNo":"","inTransitPayVoucherCount":"0","inTransitPayVoucherNo":"","cashBalanceDetailList":[{"currencyCode":"USD","cashBalanceAsset":"30000.00","availableBalance":"28000.00","freezeBalance":"2000.00"},{"currencyCode":"HKD","cashBalanceAsset":"20000.00","availableBalance":"17000.00","freezeBalance":"3000.00"}]},"description":"成功","timestampServer":"*************"}
     */
    @RequestMapping("querynotfullcashbalanceasset")
    public CgiResponse<QueryNotFullCashBalanceAssetVO> queryNotFullCashBalanceAsset(@RequestBody QueryNotFullCashBalanceAssetRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkCustBalanceService.queryNotFullCashBalanceAsset(request));
    }

    /**
     * @api {POST} /ext/hk/balance/querycashbalanceassetdetailv1 queryCashBalanceAssetDetailV1()
     * @apiVersion 1.0.0
     * @apiGroup HkBalanceController
     * @apiName queryCashBalanceAssetDetailV1()
     * @apiDescription 查询现金余额明细接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundTxAcctNo 基金交易交易账号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","fundTxAcctNo":"TXA123456"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.cashBalanceDetailList 现金余额明细列表
     * @apiSuccess (响应结果) {String} data.cashBalanceDetailList.currencyCode 币种代码
     * @apiSuccess (响应结果) {String} data.cashBalanceDetailList.cashBalanceAsset 现金余额资产
     * @apiSuccess (响应结果) {String} data.cashBalanceDetailList.availableBalance 可用余额
     * @apiSuccess (响应结果) {String} data.cashBalanceDetailList.freezeBalance 冻结余额
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","data":{"cashBalanceDetailList":[{"currencyCode":"USD","cashBalanceAsset":"30000.00","availableBalance":"28000.00","freezeBalance":"2000.00"},{"currencyCode":"HKD","cashBalanceAsset":"20000.00","availableBalance":"17000.00","freezeBalance":"3000.00"}]},"description":"成功","timestampServer":"*************"}
     */
    @RequestMapping("querycashbalanceassetdetailv1")
    public CgiResponse<QueryCashBalanceAssetDetailVO> queryCashBalanceAssetDetailV1(@RequestBody QueryCashBalanceAssetDetailV1Request request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkCustBalanceService.queryCashBalanceAssetDetailV1(request));
    }

    /**
     * @api {POST} /ext/hk/balance/queryfundassetv1 queryFundAssetV1()
     * @apiVersion 1.0.0
     * @apiGroup HkBalanceController
     * @apiName queryFundAssetV1()
     * @apiDescription 查询基金资产接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} disPlayCurrencyCode 当前展示的币种代码
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","disPlayCurrencyCode":"USD"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.showAsset 是否显示资产
     * @apiSuccess (响应结果) {String} data.fundAsset 基金持仓资产
     * @apiSuccess (响应结果) {String} data.fundIncome 基金持仓收益
     * @apiSuccess (响应结果) {String} data.existsFundEquityFund 存在基金阳光私募产品 1-是 0-否
     * @apiSuccess (响应结果) {String} data.existsGqEquityFund 存在基金私募股权产品 1-是 0-否
     * @apiSuccess (响应结果) {String} data.fundIncomeCalStatus 基金持仓收益计算状态 0-计算中;1-计算成功
     * @apiSuccess (响应结果) {String} data.fundTotalEquityRecovery 基金股权总回款
     * @apiSuccess (响应结果) {String} data.fundInTransitTradeAmt 基金在途交易金额（买入待确认资产）
     * @apiSuccess (响应结果) {String} data.existsFullTxAcct 是否存在全委基金交易账号
     * @apiSuccess (响应结果) {String} data.fundInTransitTradeCount 基金在途交易笔数
     * @apiSuccess (响应结果) {String} data.fundInTransitTradeDealNo 基金在途交易订单号
     * @apiSuccess (响应结果) {String} data.fundInTransitTradeCapitalCount 基金在途交易资金笔数
     * @apiSuccess (响应结果) {String} data.fundInTransitTradeCapitalDealNo 基金在途交易资金订单号
     * @apiSuccess (响应结果) {String} data.fundSupplementalAgreementFundCount 基金补签协议基金个数
     * @apiSuccess (响应结果) {String} data.fundSupplementalAgreementFundCode 基金补签协议基金代码
     * @apiSuccess (响应结果) {Array} data.assetFundDetailList 基金持仓详情列表
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundName 产品名称
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundCode 产品代码
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.showType 展示类型
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.crisisFlag 清算标识
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.strategyType 策略类型 0.多策略 1.股票型 2.股权型 3.CTA 4.另类 5.固收与中性
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currencyMarketValue 参考市值
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currentAssetCurrency 当前收益（当前币种）
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currentAssetCurrencyExFee 费后当前收益（当前币种）
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currencyDesc 币种描述
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.nav 参考净值
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.navDivFlag 净值分红标识
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.navDate 净值日期
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.naFlag na flag 标识
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.incomeCalStat 收益计算状态
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.stageEstablishFlag 分期成立
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.paidInAmt 总实缴金额
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currencyUnPaidInAmt 待投金额（当前币种）
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.totalBalanceVol 持仓份额
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.yieldRate 当前收益率
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.yieldRateExFee 当前收益率(不含费)
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.showDirection 展示数据的正负
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.hasDetail 是否存在明细
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.balanceFactor 平衡因子
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.convertFinish 平衡因子转换完成
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.balanceFactorDate 平衡因子日期
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.receivManageFee 累计应收管理费
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.receivPreformFee 累计应收业绩报酬
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currencyNetBuyAmount 净购买金额(投资成本)(当前币种)
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currencyTotalCollectionExFee 费后累计总回款(当前币种)
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currencyMarketValueExFee 费后持仓市值
     * @apiSuccess (响应结果) {Array} data.assetFundDetailList.fundItemVOList 产品详细信息列表
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.currencyMarketValue 参考市值
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.nav 参考净值
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.navDate 净值日期
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.totalBalanceVol 持仓份额
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.currentAssetCurrency 当前收益（当前币种）
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.currentAssetCurrencyExFee 费后当前收益（当前币种）
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.currencyDesc 币种描述
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.yieldRate 当前收益率
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.yieldRateExFee 当前收益率(不含费)
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.navDivFlag 分红标志
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.currencyMarketValueExFee 费后持仓市值
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.receivManageFee 累计应收管理费
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.receivPreformFee 累计应收业绩报酬
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.currencyNetBuyAmount 净购买金额(投资成本)(当前币种)
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.currencyTotalCollectionExFee 费后累计总回款(当前币种)
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.balanceFactor 平衡因子
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.incomeCalStat 收益计算状态
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.crisisFlag 清算标识
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.convertFinish 平衡因子转换完成
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundItemVOList.balanceFactorDate 平衡因子日期
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","data":{"showAsset":"1","fundAsset":"100000.00","fundIncome":"5000.00","existsFundEquityFund":"0","fundIncomeCalStatus":"1","fundTotalEquityRecovery":"0.00","fundInTransitTradeAmt":"0.00","existsFullTxAcct":"0","fundInTransitTradeCount":"0","fundInTransitTradeDealNo":"","fundInTransitTradeCapitalCount":"0","fundInTransitTradeCapitalDealNo":"","fundSupplementalAgreementFundCount":"0","fundSupplementalAgreementFundCode":"","assetFundDetailList":[{"fundName":"测试基金","fundCode":"FUND001","showType":"1","crisisFlag":"0","strategyType":"1","currencyMarketValue":"50000.00","currentAssetCurrency":"2500.00","currentAssetCurrencyExFee":"2400.00","currencyDesc":"USD","nav":"1.25","navDivFlag":"0","navDate":"2025-06-17","naFlag":"0","incomeCalStat":"1","stageEstablishFlag":"0","paidInAmt":"48000.00","currencyUnPaidInAmt":"0.00","totalBalanceVol":"40000.00","yieldRate":"5.21%","yieldRateExFee":"5.00%","showDirection":"1","hasDetail":"1","balanceFactor":"1.0","convertFinish":"1","balanceFactorDate":"2025-06-17","receivManageFee":"100.00","receivPreformFee":"0.00","currencyNetBuyAmount":"48000.00","currencyTotalCollectionExFee":"0.00","currencyMarketValueExFee":"49900.00","fundItemVOList":[{"currencyMarketValue":"50000.00","nav":"1.25","navDate":"2025-06-17","totalBalanceVol":"40000.00","currentAssetCurrency":"2500.00","currentAssetCurrencyExFee":"2400.00","currencyDesc":"USD","yieldRate":"5.21%","yieldRateExFee":"5.00%","navDivFlag":"0","currencyMarketValueExFee":"49900.00","receivManageFee":"100.00","receivPreformFee":"0.00","currencyNetBuyAmount":"48000.00","currencyTotalCollectionExFee":"0.00","balanceFactor":"1.0","incomeCalStat":"1","crisisFlag":"0","convertFinish":"1","balanceFactorDate":"2025-06-17"}]}]},"description":"成功","timestampServer":"*************"}
     */
    @RequestMapping("queryfundassetv1")
    public CgiResponse<QueryFundAssetVO> queryFundAssetV1(@RequestBody QueryFundAssetRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkCustBalanceService.queryFundAssetV1(request));
    }

    /**
     * @api {POST} /ext/hk/balance/queryfundassetdetailv1 queryFundAssetDetailV1()
     * @apiVersion 1.0.0
     * @apiGroup HkBalanceController
     * @apiName queryFundAssetDetailV1()
     * @apiDescription 查询基金资产详情接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} disPlayCurrencyCode 当前展示的币种代码
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","fundCode":"FUND001","disPlayCurrencyCode":"USD"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.fundName 基金名称
     * @apiSuccess (响应结果) {String} data.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.existsSupplementalAgreement 是否存在补签协议 0-否，1-是
     * @apiSuccess (响应结果) {String} data.currencyMarketValue 参考市值
     * @apiSuccess (响应结果) {String} data.currencyDesc 币种描述 仅参考市值用
     * @apiSuccess (响应结果) {String} data.currency 币种 其余的市值 用
     * @apiSuccess (响应结果) {String} data.nav 参考净值
     * @apiSuccess (响应结果) {String} data.navDate 净值日期 YYYY-MM-DD或者MM-DD
     * @apiSuccess (响应结果) {String} data.gradationCall 分次Call产品 1-是 0-否
     * @apiSuccess (响应结果) {String} data.subTotalAmt 认缴总金额
     * @apiSuccess (响应结果) {String} data.paidTotalAmt 实缴总金额
     * @apiSuccess (响应结果) {String} data.paidSubTotalRatio 实缴认缴百分比
     * @apiSuccess (响应结果) {String} data.currencyUnPaidInAmt 待投金额（当前币种）
     * @apiSuccess (响应结果) {String} data.totalBalanceVol 持仓份额
     * @apiSuccess (响应结果) {String} data.dayIncomeRate 当前收益率
     * @apiSuccess (响应结果) {String} data.accumIncome 累计收益
     * @apiSuccess (响应结果) {String} data.latestIncome 最新收益
     * @apiSuccess (响应结果) {String} data.latestIncomeRate 最新收益率
     * @apiSuccess (响应结果) {String} data.navDivFlag 分红提醒标识 0:无需分红提醒;1:提醒收益偏差;2:提醒即将分红
     * @apiSuccess (响应结果) {String} data.crisisFlag 清算标识 0-否; 1-是
     * @apiSuccess (响应结果) {String} data.qianXiFlag 千禧年产品标识 0-否; 1-是
     * @apiSuccess (响应结果) {String} data.stageEstablishFlag 是否分期成立 0-否; 1-是
     * @apiSuccess (响应结果) {String} data.naFlag 是否NA产品
     * @apiSuccess (响应结果) {String} data.receivManageFee 累计应收管理费
     * @apiSuccess (响应结果) {String} data.receivPreformFee 累计应收业绩报酬
     * @apiSuccess (响应结果) {String} data.currencyMarketValueExFee 产品费后参考市值
     * @apiSuccess (响应结果) {String} data.currentAssetCurrency 产品费后持仓收益
     * @apiSuccess (响应结果) {String} data.yieldRate 产品费后持仓收益率
     * @apiSuccess (响应结果) {String} data.yieldRateStr 产品费后持仓收益率 保留几位小数
     * @apiSuccess (响应结果) {String} data.balanceFactor 平衡因子
     * @apiSuccess (响应结果) {String} data.incomeCalStat 收益计算状态 0-计算中;1-计算成功
     * @apiSuccess (响应结果) {String} data.convertFinish 平衡因子转换完成 1-是 0-否
     * @apiSuccess (响应结果) {String} data.balanceFactorDate 平衡因子日期
     * @apiSuccess (响应结果) {String} data.buySubFundCode 买入子基金代码
     * @apiSuccess (响应结果) {String} data.isCanBuy 是否可买入 1-可交易 2-不可交易 3-按钮不展示
     * @apiSuccess (响应结果) {String} data.isCanSell 是否可卖出 1-可交易 2-不可交易 3-按钮不展示
     * @apiSuccess (响应结果) {Array} data.assetDetailInfoVOList 详情列表数据
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.fundName 基金名称
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.subFundName 子基金名称
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.subFundCode 子基金代码
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.currencyMarketValue 参考市值
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.currencyDesc 币种描述 仅参考市值用
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.currency 币种 其余的市值 用
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.nav 参考净值
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.navDate 净值日期 YYYY-MM-DD或者MM-DD
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.paidInAmt 总实缴金额
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.currencyUnPaidInAmt 待投金额（当前币种）
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.totalBalanceVol 持仓份额
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.dayIncomeRate 当前收益率
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.accumIncome 累计收益
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.latestIncome 最新收益
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.latestIncomeRate 最新收益率
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.unitCost 单位成本
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.navDivFlag 分红提醒标识 0:无需分红提醒;1:提醒收益偏差;2:提醒即将分红
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.crisisFlag 清算标识 0-否; 1-是
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.qianXiFlag 千禧年产品标识 0-否; 1-是
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.stageEstablishFlag 是否分期成立 0-否; 1-是
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.naFlag 是否NA产品
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.receivManageFee 累计应收管理费
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.receivPreformFee 累计应收业绩报酬
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.currencyMarketValueExFee 产品费后参考市值
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.currentAssetCurrency 产品费后持仓收益
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.yieldRate 产品费后持仓收益率
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.yieldRateStr 产品费后持仓收益率 保留几位小数
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.balanceFactor 平衡因子
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.convertFinish 平衡因子转换完成 1-是 0-否
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.balanceFactorDate 平衡因子日期
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.incomeCalStat 收益计算状态 0-计算中;1-计算成功
     * @apiSuccess (响应结果) {String} data.assetDetailInfoVOList.isCanSell 是否可卖出 1-可交易 2-不可交易 3-按钮不展示
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","data":{"fundName":"测试基金","fundCode":"FUND001","existsSupplementalAgreement":"0","currencyMarketValue":"50000.00","currencyDesc":"美元","currency":"USD","nav":"1.25","navDate":"2025-06-17","gradationCall":"0","subTotalAmt":"60000.00","paidTotalAmt":"50000.00","paidSubTotalRatio":"83.33%","currencyUnPaidInAmt":"10000.00","totalBalanceVol":"40000.00","dayIncomeRate":"5.21%","accumIncome":"2500.00","latestIncome":"100.00","latestIncomeRate":"0.20%","navDivFlag":"0","crisisFlag":"0","qianXiFlag":"0","stageEstablishFlag":"0","naFlag":"0","receivManageFee":"100.00","receivPreformFee":"0.00","currencyMarketValueExFee":"49900.00","currentAssetCurrency":"2400.00","yieldRate":"5.00%","yieldRateStr":"5.00","balanceFactor":"1.0","incomeCalStat":"1","convertFinish":"1","balanceFactorDate":"2025-06-17","buySubFundCode":"SUB001","isCanBuy":"1","isCanSell":"1","assetDetailInfoVOList":[{"fundName":"测试基金","fundCode":"FUND001","subFundName":"子基金A","subFundCode":"SUB001","currencyMarketValue":"25000.00","currencyDesc":"美元","currency":"USD","nav":"1.25","navDate":"2025-06-17","paidInAmt":"20000.00","currencyUnPaidInAmt":"5000.00","totalBalanceVol":"20000.00","dayIncomeRate":"5.21%","accumIncome":"1250.00","latestIncome":"50.00","latestIncomeRate":"0.20%","unitCost":"1.00","navDivFlag":"0","crisisFlag":"0","qianXiFlag":"0","stageEstablishFlag":"0","naFlag":"0","receivManageFee":"50.00","receivPreformFee":"0.00","currencyMarketValueExFee":"24950.00","currentAssetCurrency":"1200.00","yieldRate":"5.00%","yieldRateStr":"5.00","balanceFactor":"1.0","convertFinish":"1","balanceFactorDate":"2025-06-17","incomeCalStat":"1","isCanSell":"1"}]},"description":"成功","timestampServer":"*************"}
     */
    @RequestMapping("queryfundassetdetailv1")
    public CgiResponse<QueryFundAssetDetailVO> queryFundAssetDetailV1(@RequestBody QueryFundAssetDetailRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkCustBalanceService.queryFundAssetDetailV1(request));
    }

    /**
     * @api {POST} /ext/hk/balance/queryfullfundassetv1 queryFullFundAssetV1()
     * @apiVersion 1.0.0
     * @apiGroup HkBalanceController
     * @apiName queryFullFundAssetV1()
     * @apiDescription 查询全委基金资产接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} disPlayCurrencyCode 当前展示的币种代码
     * @apiParam (请求体) {String} [fundTxAcctNo] 基金交易交易账号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","disPlayCurrencyCode":"USD","fundTxAcctNo":"TX001"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.showAsset 是否显示资产 [资产小眼睛] 0:显示, 1: 隐藏
     * @apiSuccess (响应结果) {String} data.fullFundAsset 全委基金持仓资产
     * @apiSuccess (响应结果) {String} data.fullFundIncome 全委基金持仓收益
     * @apiSuccess (响应结果) {String} data.existsFundEquityFund 存在全委基金阳光私募产品 0-不存在、1-存在
     * @apiSuccess (响应结果) {String} data.fullFundIncomeCalStatus 全委基金持仓收益计算状态 0-计算中;1-计算成功
     * @apiSuccess (响应结果) {String} data.existsFullFundEquityFund 存在全委基金股权基金 0-不存在、1-存在
     * @apiSuccess (响应结果) {String} data.fullFundTotalEquityRecovery 全委基金股权总回款
     * @apiSuccess (响应结果) {String} data.fullFundInTransitTradeAmt 全委基金在途交易金额
     * @apiSuccess (响应结果) {String} data.fullFundInTransitTradeCount 全委基金在途交易笔数
     * @apiSuccess (响应结果) {String} data.fullFundInTransitTradeDealNo 全委基金在途交易订单号 在途交易笔数 == 1 时的交易单号
     * @apiSuccess (响应结果) {String} data.fullFundInTransitTradeCapitalCount 全委基金在途交易资金笔数
     * @apiSuccess (响应结果) {String} data.fullFundInTransitTradeCapitalDealNo 全委基金在途交易资金订单号 在途交易资金笔数 == 1 时的交易单号
     * @apiSuccess (响应结果) {String} data.fullCashBalanceAsset 全委现金余额资产
     * @apiSuccess (响应结果) {String} data.fullAvailableBalance 全委可用余额
     * @apiSuccess (响应结果) {String} data.fullFreezeBalance 全委冻结余额
     * @apiSuccess (响应结果) {String} data.fullHoldFundAsset 全委持仓总资产
     * @apiSuccess (响应结果) {Array} data.assetFundDetailList 基金持仓详情列表
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundName 产品名称
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.fundCode 产品代码
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.showType 展示类型 1.非分期成立 2.分起成立 3.千禧年产品
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.crisisFlag 清算标识 0-否; 1-是
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.strategyType 策略类型 0.多策略 1.股票型 2.股权型 3.CTA 4.另类 5.固收与中性
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currencyMarketValue 参考市值
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currentAssetCurrency 当前收益（当前币种）
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currentAssetCurrencyExFee 费后当前收益（当前币种）
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currencyDesc 币种描述
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.nav 参考净值
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.navDivFlag 净值分红标识 0-否，1-是
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.navDate 净值日期 YYYY-MM-DD或者MM-DD
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.naFlag na flag 标识
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.incomeCalStat 收益计算状态 0-计算中;1-计算成功
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.stageEstablishFlag 分期成立
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.paidInAmt 总实缴金额
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currencyUnPaidInAmt 待投金额（当前币种）
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.totalBalanceVol 持仓份额
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.yieldRate 当前收益率 带百分号
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.yieldRateExFee 当前收益率(不含费)
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.showDirection 展示数据的正负 1 正数, 0 相同 ,-1 负数
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.hasDetail 是否存在明细 1:存在 0:不存在
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.balanceFactor 平衡因子
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.convertFinish 平衡因子转换完成 1-是 0-否
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.balanceFactorDate 平衡因子日期
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.receivManageFee 累计应收管理费
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.receivPreformFee 累计应收业绩报酬
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currencyNetBuyAmount 净购买金额(投资成本)(当前币种)
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currencyTotalCollectionExFee 费后累计总回款(当前币种)
     * @apiSuccess (响应结果) {String} data.assetFundDetailList.currencyMarketValueExFee 费后持仓市值
     * @apiSuccess (响应结果) {Array} data.assetFundDetailList.fundItemVOList 产品详细信息 如果是分期成立,则会有多条
     * @apiSuccess (响应结果) {Array} data.fundTxAcctList 全委基金交易账号列表
     * @apiSuccess (响应结果) {String} data.fundTxAcctList.fundTxAcctNo 全委基金交易账号
     * @apiSuccess (响应结果) {String} data.fundTxAcctList.fundTxAcctName 全委基金交易账号名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","data":{"showAsset":"0","fullFundAsset":"100000.00","fullFundIncome":"5000.00","existsFundEquityFund":"1","fullFundIncomeCalStatus":"1","existsFullFundEquityFund":"0","fullFundTotalEquityRecovery":"0.00","fullFundInTransitTradeAmt":"0.00","fullFundInTransitTradeCount":"0","fullFundInTransitTradeDealNo":"","fullFundInTransitTradeCapitalCount":"0","fullFundInTransitTradeCapitalDealNo":"","fullFundSupplementalAgreementFundCount":"0","fullFundSupplementalAgreementFundCode":"","fullCashBalanceAsset":"50000.00","fullAvailableBalance":"48000.00","fullFreezeBalance":"2000.00","assetFundDetailList":[{"fundName":"测试全委基金","fundCode":"FULL001","showType":"1","crisisFlag":"0","strategyType":"0","currencyMarketValue":"100000.00","currentAssetCurrency":"5000.00","currentAssetCurrencyExFee":"4900.00","currencyDesc":"美元","nav":"1.05","navDivFlag":"0","navDate":"2025-06-17","naFlag":"0","incomeCalStat":"1","stageEstablishFlag":"0","paidInAmt":"95000.00","currencyUnPaidInAmt":"0.00","totalBalanceVol":"95238.10","yieldRate":"5.26%","yieldRateExFee":"5.16%","showDirection":"1","hasDetail":"1","balanceFactor":"1.0","convertFinish":"1","balanceFactorDate":"2025-06-17","receivManageFee":"100.00","receivPreformFee":"0.00","currencyNetBuyAmount":"95000.00","currencyTotalCollectionExFee":"0.00","currencyMarketValueExFee":"99900.00","fundItemVOList":[]}],"fundTxAcctList":[{"fundTxAcctNo":"TX001","fundTxAcctName":"全委交易账号1"}]},"description":"成功","timestampServer":"*************"}
     */
    @RequestMapping("queryfullfundassetv1")
    public CgiResponse<QueryFullFundAssetVO> queryFullFundAssetV1(@RequestBody QueryFullFundAssetRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkCustBalanceService.queryFullFundAssetV1(request));
    }

    /**
     * @api {POST} /ext/hk/balance/cashdrawmoney/querycashdrawmoneypage queryCashDrawMoneyPage()
     * @apiVersion 1.0.0
     * @apiGroup HkBalanceController
     * @apiName queryCashDrawMoneyPage()
     * @apiDescription 查询现金提取页面接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.cashBalanceDetailList 现金资产明细列表
     * @apiSuccess (响应结果) {String} data.cashBalanceDetailList.currencyCode 币种代码
     * @apiSuccess (响应结果) {String} data.cashBalanceDetailList.cashBalanceAsset 现金资产总资产
     * @apiSuccess (响应结果) {String} data.cashBalanceDetailList.availableBalance 可用余额
     * @apiSuccess (响应结果) {String} data.cashBalanceDetailList.freezeBalance 冻结余额
     * @apiSuccess (响应结果) {Array} data.bankCardInfoVOList 银行卡信息
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.hkCpAcctNo 香港资金账号
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankCode 银行代码
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankName 银行名称（银行名称优先级为：取到银行简称，否则取银行全称）
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankRegionCode 银行号
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankAcctName 银行账户名称
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankAcctDigest 摘要-银行账号
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankAcctMask 掩码-银行账号
     * @apiSuccess (响应结果) {Array} data.currencyCodes 币种代码列表
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","data":{"cashBalanceDetailList":[{"currencyCode":"USD","cashBalanceAsset":"30000.00","availableBalance":"28000.00","freezeBalance":"2000.00"},{"currencyCode":"HKD","cashBalanceAsset":"20000.00","availableBalance":"17000.00","freezeBalance":"3000.00"}],"bankCardInfoVOList":[{"hkCpAcctNo":"HK001","bankCode":"HSBC","bankName":"汇丰银行","bankRegionCode":"004","bankAcctName":"张三","bankAcctDigest":"尾号1234","bankAcctMask":"****1234"}],"currencyCodes":["USD","HKD"]},"description":"成功","timestampServer":"*************"}
     */
    @RequestMapping("cashdrawmoney/querycashdrawmoneypage")
    public CgiResponse<QueryCashDrawMoneyPageVO> queryCashDrawMoneyPage(@RequestBody QueryCashDrawMoneyPageRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkCustBalanceService.queryCashDrawMoneyPage(request));
    }

    /**
     * @api {POST} /ext/hk/balance/cashdrawmoney/submit submitCashDrawMoney()
     * @apiVersion 1.0.0
     * @apiGroup HkBalanceController
     * @apiName submitCashDrawMoney()
     * @apiDescription 现金提取提交接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} tradePassword 交易密码
     * @apiParam (请求体) {String} currencyCode 币种代码
     * @apiParam (请求体) {String} drawmoneyAmount 提取金额
     * @apiParam (请求体) {String} hkCpAcctNo 香港资金账号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","tradePassword":"123456","currencyCode":"USD","drawmoneyAmount":"1000.00","hkCpAcctNo":"HK001"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"成功","timestampServer":"*************"}
     */
    @RequestMapping("cashdrawmoney/submit")
    public CgiResponse<Body> submitCashDrawMoney(@RequestBody CashDrawMoneySubmitRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        hkCustBalanceService.submitCashDrawMoney(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /ext/hk/balance/cashdrawmoney/querycashdrawmoneyrecordlist queryCashDrawMoneyRecordList()
     * @apiVersion 1.0.0
     * @apiGroup HkBalanceController
     * @apiName queryCashDrawMoneyRecordList()
     * @apiDescription 现金提取记录列表接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} timePeriod 时间范围枚举 1-近一个月、2-近半年、3-近一年
     * @apiParam (请求体) {String} startDt 开始时间 格式：YYYYMMdd
     * @apiParam (请求体) {String} endDt 结束时间 格式：YYYYMMdd
     * @apiParam (请求体) {String} drawMoneyStatus 提取状态 1-提取中 2-提取成功 3-提取失败
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","timePeriod":"1","startDt":"20250601","endDt":"********","drawMoneyStatus":"2"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.cashdRawMoneyRecordListVO 现金提取记录
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.cashdRawMoneyId 现金提取Id
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.appDt 申请日期 YYYYMMDD
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.appTm 申请时间 hhmmss
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.currencyCode 币种代码
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.drawmoneyAmount 提取金额
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.hkCpAcctNo 香港资金账号
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.bankCode 银行代码
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.bankName 银行名称
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.bankRegionCode 银行号
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.bankAcctName 银行账户名称
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.bankAcctDigest 摘要-银行账号
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.bankAcctMask 掩码-银行账号
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.drawMoneyStatus 提取状态 1-提取中 2-提取成功 3-提取失败
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.failMsg 失败原因
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.source 来源 待定枚举-TODO
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.updateTime 修改时间 YYYYMMDDhhmmss
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordListVO.transferTime 转账时间 YYYYMMDDhhmmss
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","data":{"cashdRawMoneyRecordListVO":[{"cashdRawMoneyId":"CDM001","appDt":"********","appTm":"143000","currencyCode":"USD","drawmoneyAmount":"1000.00","hkCpAcctNo":"HK001","bankCode":"HSBC","bankName":"汇丰银行","bankRegionCode":"004","bankAcctName":"张三","bankAcctDigest":"尾号1234","bankAcctMask":"****1234","drawMoneyStatus":"2","failMsg":"","source":"APP","updateTime":"********143500","transferTime":"********150000"}]},"description":"成功","timestampServer":"*************"}
     */
    @RequestMapping("cashdrawmoney/querycashdrawmoneyrecordlist")
    public CgiResponse<QueryCashDrawMoneyRecordListVO> queryCashDrawMoneyRecordList(@RequestBody QueryCashDrawMoneyRecordListRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkCustBalanceService.queryCashDrawMoneyRecordList(request));
    }

    /**
     * @api {POST} /ext/hk/balance/cashdrawmoney/querycashdrawmoneyrecorddetail queryCashDrawMoneyRecordDetail()
     * @apiVersion 1.0.0
     * @apiGroup HkBalanceController
     * @apiName queryCashDrawMoneyRecordDetail()
     * @apiDescription 现金提取记录详情接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} cashdRawMoneyId 现金提取Id
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","cashdRawMoneyId":"CDM001"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.cashdRawMoneyRecordVO 现金提取记录VO
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.cashdRawMoneyId 现金提取Id
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.appDt 申请日期 YYYYMMDD
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.appTm 申请时间 hhmmss
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.currencyCode 币种代码
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.drawmoneyAmount 提取金额
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.hkCpAcctNo 香港资金账号
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.bankCode 银行代码
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.bankName 银行名称
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.bankRegionCode 银行号
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.bankAcctName 银行账户名称
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.bankAcctDigest 摘要-银行账号
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.bankAcctMask 掩码-银行账号
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.drawMoneyStatus 提取状态 1-提取中 2-提取成功 3-提取失败
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.failMsg 失败原因
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.source 来源 待定枚举-TODO
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.updateTime 修改时间 YYYYMMDDhhmmss
     * @apiSuccess (响应结果) {String} data.cashdRawMoneyRecordVO.transferTime 转账时间 YYYYMMDDhhmmss
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","data":{"cashdRawMoneyRecordVO":{"cashdRawMoneyId":"CDM001","appDt":"********","appTm":"143000","currencyCode":"USD","drawmoneyAmount":"1000.00","hkCpAcctNo":"HK001","bankCode":"HSBC","bankName":"汇丰银行","bankRegionCode":"004","bankAcctName":"张三","bankAcctDigest":"尾号1234","bankAcctMask":"****1234","drawMoneyStatus":"2","failMsg":"","source":"APP","updateTime":"********143500","transferTime":"********150000"}},"description":"成功","timestampServer":"*************"}
     */
    @RequestMapping("cashdrawmoney/querycashdrawmoneyrecorddetail")
    public CgiResponse<QueryCashDrawMoneyRecordDetailVO> queryCashDrawMoneyRecordDetail(@RequestBody QueryCashDrawMoneyRecordDetailRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
                 return CgiResponse.ok(hkCustBalanceService.queryCashDrawMoneyRecordDetail(request));
     }

    /**
     * @api {POST} /ext/hk/balance/queryfinancialdetaillist queryFinancialDetailList()
     * @apiVersion 1.0.0
     * @apiGroup HkBalanceController
     * @apiName queryFinancialDetailList()
     * @apiDescription 资金明细查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {Array} businessTypeList 业务类型列表
     * @apiParam (请求体) {String} [fundTxAcctNo] 基金交易账号（默认非全委基金交易账号）
     * @apiParam (请求体) {String} [currencyCode] 币种代码
     * @apiParam (请求体) {int} page 页码
     * @apiParam (请求体) {int} size 页大小
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","businessTypeList":["01","02"],"fundTxAcctNo":"TX001","currencyCode":"USD","page":1,"size":10}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {int} data.page 页码
     * @apiSuccess (响应结果) {int} data.size 页大小
     * @apiSuccess (响应结果) {long} data.total 总记录数
     * @apiSuccess (响应结果) {String} data.closingBalance 期末余额
     * @apiSuccess (响应结果) {String} data.updateTime 更新时间 YYYYMMDDhhmmss
     * @apiSuccess (响应结果) {String} data.currencyCode 币种代码
     * @apiSuccess (响应结果) {Array} data.currencyFinancialListVO 币种资金列表
     * @apiSuccess (响应结果) {String} data.currencyFinancialListVO.currencyCode 币种代码
     * @apiSuccess (响应结果) {String} data.currencyFinancialListVO.isCurrentSelected 是否当前选中 1-是 0-否
     * @apiSuccess (响应结果) {String} data.currencyFinancialListVO.currencyAccountName 币种账户名称
     * @apiSuccess (响应结果) {Array} data.financiaDetailListVO 资金明细列表
     * @apiSuccess (响应结果) {String} data.financiaDetailListVO.businessType 业务类型
     * @apiSuccess (响应结果) {String} data.financiaDetailListVO.remark 备注
     * @apiSuccess (响应结果) {String} data.financiaDetailListVO.tradeDt 交易日期
     * @apiSuccess (响应结果) {String} data.financiaDetailListVO.changeAmount 变动金额
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","data":{"page":1,"size":10,"total":25,"closingBalance":"50000.00","updateTime":"********180000","currencyCode":"USD","currencyFinancialListVO":[{"currencyCode":"USD","isCurrentSelected":"1","currencyAccountName":"美元账户"},{"currencyCode":"HKD","isCurrentSelected":"0","currencyAccountName":"港币账户"}],"financiaDetailListVO":[{"businessType":"01","remark":"基金申购","tradeDt":"********","changeAmount":"-10000.00"},{"businessType":"02","remark":"基金赎回","tradeDt":"********","changeAmount":"5000.00"},{"businessType":"01","remark":"基金申购","tradeDt":"********","changeAmount":"-8000.00"}]},"description":"成功","timestampServer":"*************"}
     */
    @RequestMapping("queryfinancialdetaillist")
    public CgiResponse<QueryFinancialDetailListVO> queryFinancialDetailList(@RequestBody QueryFinancialDetailListRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkCustBalanceService.queryFinancialDetailList(request));
    }

    /**
     * @api {POST} /ext/hk/balance/querypiggyassetv1 queryPiggyAssetV1()
     * @apiVersion 1.0.0
     * @apiGroup HkBalanceController
     * @apiName queryPiggyAssetV1()
     * @apiDescription 查询储蓄罐资产接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} disPlayCurrencyCode 当前展示的币种代码
     * @apiParamExample 请求体示例
     * {"hkCustNo":"SQ","disPlayCurrencyCode":"M1H"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.showAsset 是否显示资产 [资产小眼睛]
     * @apiSuccess (响应结果) {String} data.piggyAsset 储蓄罐资产
     * @apiSuccess (响应结果) {String} data.piggyIncome 储蓄罐收益
     * @apiSuccess (响应结果) {String} data.piggyIncomeCalStatus 储蓄罐收益计算状态
     * @apiSuccess (响应结果) {String} data.piggySignStatus 储蓄罐签约状态 0-未签约1-已签约2-已暂停
     * @apiSuccess (响应结果) {String} data.piggyInTransitTradeAmt 储蓄罐在途交易金额
     * @apiSuccess (响应结果) {String} data.piggyInTransitTradeCount 储蓄罐在途交易笔数
     * @apiSuccess (响应结果) {String} data.piggyInTransitTradeDealNo 储蓄罐在途交易订单号
     * @apiSuccess (响应结果) {String} data.piggyInTransitTradeCapitalCount 储蓄罐在途交易资金笔数
     * @apiSuccess (响应结果) {String} data.piggyInTransitTradeCapitalDealNo 储蓄罐在途交易资金订单号
     * @apiSuccess (响应结果) {Array} data.piggyAssetFundListVOList 储蓄罐持仓列表
     * @apiSuccess (响应结果) {String} data.piggyAssetFundListVOList.fundName 产品名称(简称)
     * @apiSuccess (响应结果) {String} data.piggyAssetFundListVOList.fundCode 产品代码
     * @apiSuccess (响应结果) {String} data.piggyAssetFundListVOList.currencyMarketValue 参考市值
     * @apiSuccess (响应结果) {String} data.piggyAssetFundListVOList.currentAssetCurrencyExFee 费后当前收益（当前币种）
     * @apiSuccess (响应结果) {String} data.piggyAssetFundListVOList.currencyDesc 币种描述
     * @apiSuccess (响应结果) {String} data.piggyAssetFundListVOList.nav 参考净值
     * @apiSuccess (响应结果) {String} data.piggyAssetFundListVOList.navDate 净值日期
     * @apiSuccess (响应结果) {String} data.piggyAssetFundListVOList.incomeCalStat 收益计算状态
     * @apiSuccess (响应结果) {String} data.piggyAssetFundListVOList.yieldRateExFee 当前收益率(不含费)
     * @apiSuccess (响应结果) {String} data.piggyAssetFundListVOList.showDirection 展示数据的正负
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"PyPgs","data":{"piggyAsset":"4IN","piggyIncomeCalStatus":"jY1r9nu66","piggyAssetFundListVOList":[{"currencyDesc":"PswfQzEX","nav":"1SHGWSZ5i5","navDate":"CysSPMchJM","currencyMarketValue":"FzbxkeW3","showDirection":"WU7l","fundCode":"GOCzG","currentAssetCurrencyExFee":"Lj4GHBKa6","yieldRateExFee":"liMS","fundName":"c","incomeCalStat":"24"}],"showAsset":"8pE9","piggySignStatus":"pe2dB","piggyInTransitTradeCapitalCount":"IZNwmCtigC","piggyIncome":"eLNisHSG","piggyInTransitTradeAmt":"nPM0iYL0z","piggyInTransitTradeCount":"5fcMuu","piggyInTransitTradeCapitalDealNo":"Jrpcpv98w","piggyInTransitTradeDealNo":"QqBX"},"description":"G2yz","timestampServer":"FE"}
     */
    @RequestMapping("querypiggyassetv1")
    public CgiResponse<QueryPiggyAssetVO> queryPiggyAssetV1(@RequestBody QueryPiggyAssetRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkCustBalanceService.queryPiggyAssetV1(request));
    }
} 