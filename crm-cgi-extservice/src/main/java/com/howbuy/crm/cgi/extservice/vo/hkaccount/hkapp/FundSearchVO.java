package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 基金搜索响应对象
 * @author: jinqing.rao
 * @date: 2024/3/11 10:20
 * @since JDK 1.8
 */
@Builder
@Getter
public class FundSearchVO extends Body implements Serializable {

    private static final long serialVersionUID = -5230700226581829219L;
    /**
     * 基金列表
     */
    private List<FundInfo> fundList;

    @Builder
    @Getter
    public static class FundInfo {
        /**
         * 基金编码
         */
        private String fundCode;

        /**
         * 基金名称
         */
        private String fundName;

        /**
         * 英文名称
         */
        private String fundNameEn;
    }
} 