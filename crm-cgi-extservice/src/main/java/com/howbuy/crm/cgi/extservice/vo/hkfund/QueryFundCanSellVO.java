/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkfund;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 查询基金是否可卖出响应类
 * <AUTHOR>
 * @date 2024/4/18 13:25
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundCanSellVO extends Body {

    /**
     * 是否可卖出 0-不可卖出 1-可卖出 2-不显示
     */
    private String isCanSell;

    /**
     * 不可卖出错误码
     */
    private String notCanSellCode;

    /**
     * 不可卖出错误信息
     */
    private String notCanSellMsg;

} 