/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils.file;

import com.howbuy.crm.cgi.extservice.common.enums.FileBizTypeEnum;

/**
 * @description: 文件上传
 * <AUTHOR>
 * @date 2024/5/10 19:41
 * @since JDK 1.8
 */
public abstract class AbstractFile {

    /**
     * 文件名称字段
     */
    public static final String FILE_NAME = "fileName";


    public static final String FILE_ID = "fileId";

    /**
     * 文件业务类型,用户获取对应的储存配置信息,读写都是需要
     */
    public static final String FILE_BIZ_TYPE = "fileBizType";

    /**
     * 上传的URL地址
     */
    public static final String URL = "url";

    /**
     * 文件名称
     */
    protected String fileName;


    /**
     * 文件名称
     */
    protected String fileId;
    /**
     * 文件后缀类型
     */
    protected String fileType;

    /**
     * 文件业务类型
     */
    protected String fileBizType;

    /**
     * 文件上传路径
     */
    protected String filePath;

    /**
     * 文件业务类型配置属性枚举
     */
    protected FileBizTypeEnum fileBizTypeEnum;


    /**
     * 获取文件名称
     */
    protected abstract String initFileName();

    /**
     * 获取文件上传路径
     */
    protected abstract String initFilePath();
}
