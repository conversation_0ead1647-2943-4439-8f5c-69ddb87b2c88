/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.piggy;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.enums.CurrencyEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.utils.BigDecimalUtils;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.FileBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherAuditStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherDisPlayStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherTypeEnum;
import com.howbuy.crm.cgi.extservice.common.utils.AppEncUtils;
import com.howbuy.crm.cgi.extservice.common.utils.NumberUtils;
import com.howbuy.crm.cgi.extservice.common.utils.ReflectUtils;
import com.howbuy.crm.cgi.extservice.common.utils.file.FileUploadUtils;
import com.howbuy.crm.cgi.extservice.request.piggy.PiggyDepositVoucherSubmitRequest;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKFileVO;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherDetailVO;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherRecordListVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.piggy.PiggyPayVoucherSubmitRequestDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.piggy.PiggyPayVoucherRecordDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.piggy.PiggyPayVoucherRecordPageDTO;
import com.howbuy.dtms.order.client.enums.PayVoucherOrderStatusEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/7/24 15:38
 * @since JDK 1.8
 */
@Slf4j
public class PiggyDepositConvert {

    public static PiggyPayVoucherSubmitRequestDTO toPiggyPayVoucherSubmitRequestDTO(PiggyDepositVoucherSubmitRequest request) {
        PiggyPayVoucherSubmitRequestDTO payVoucherSubmitRequestDTO = new PiggyPayVoucherSubmitRequestDTO();
        payVoucherSubmitRequestDTO.setVoucherNo(request.getVoucherNo());
        payVoucherSubmitRequestDTO.setCpAcctNo(request.getCpAcctNo());
        payVoucherSubmitRequestDTO.setHkCustNo(request.getHkCustNo());
        payVoucherSubmitRequestDTO.setBankAcctMask(request.getBankAcctMask());
        payVoucherSubmitRequestDTO.setBankName(request.getBankName());
        payVoucherSubmitRequestDTO.setSwiftCode(request.getSwiftCode());
        payVoucherSubmitRequestDTO.setRemitAmt(new BigDecimal(request.getRemitAmt()));
        payVoucherSubmitRequestDTO.setRemitCurrency(request.getRemitCurrency());
        payVoucherSubmitRequestDTO.setRemark(request.getRemark());
        payVoucherSubmitRequestDTO.setHbSceneId(request.getHbSceneId());
        payVoucherSubmitRequestDTO.setBankName(request.getBankName());
        payVoucherSubmitRequestDTO.setVoucherType(PayVoucherTypeEnum.DEPOSIT_CASH_ACCOUNT.getCode());
        payVoucherSubmitRequestDTO.setAgreeCurrencyExchange(request.getAgreeCurrencyExchange());
        // 材料附件
        if (CollectionUtils.isNotEmpty(request.getPayVoucherFiles())) {
            List<PiggyPayVoucherSubmitRequestDTO.PiggyPayVoucherSubmitAttachments> submitAttachments = request.getPayVoucherFiles().stream().map(m -> {
                PiggyPayVoucherSubmitRequestDTO.PiggyPayVoucherSubmitAttachments attachments = new PiggyPayVoucherSubmitRequestDTO.PiggyPayVoucherSubmitAttachments();
                attachments.setFileName(AppEncUtils.decrypt(m.getFileName()));
                attachments.setFileFormatType(m.getFileFormatType());
                attachments.setFilePathUrl(FileUploadUtils.getDecryptUrl(m.getThumbnailUrl(), request.getHkCustNo()));
                return attachments;
            }).collect(Collectors.toList());
            payVoucherSubmitRequestDTO.setPayVoucherFiles(submitAttachments);
        }
        return payVoucherSubmitRequestDTO;
    }

    public static PiggyDepositVoucherDetailVO toPiggyDepositVoucherDetailVO(PiggyPayVoucherRecordDTO piggyPayVoucherRecordDTO) {
        PiggyDepositVoucherDetailVO voucherRecordVO = new PiggyDepositVoucherDetailVO();
        voucherRecordVO.setRemitCurrency(piggyPayVoucherRecordDTO.getRemitCurrency());
        voucherRecordVO.setRemitAmt(null == piggyPayVoucherRecordDTO.getRemitAmt() ? null : piggyPayVoucherRecordDTO.getRemitAmt().toString());
        voucherRecordVO.setRemitCurrencyDesc(CurrencyEnum.getDescription(piggyPayVoucherRecordDTO.getRemitCurrency()));
        voucherRecordVO.setBankName(piggyPayVoucherRecordDTO.getBankName());
        voucherRecordVO.setRemark(piggyPayVoucherRecordDTO.getRemark());
        voucherRecordVO.setBankAcctMask(piggyPayVoucherRecordDTO.getBankAcct());
        voucherRecordVO.setSwiftCode(piggyPayVoucherRecordDTO.getSwiftCode());
        voucherRecordVO.setCpAcctNo(piggyPayVoucherRecordDTO.getRemitCpAcctNo());
        String bankName = StringUtils.isBlank(piggyPayVoucherRecordDTO.getBankChineseName()) ? piggyPayVoucherRecordDTO.getBankName() : piggyPayVoucherRecordDTO.getBankChineseName();
        voucherRecordVO.setBankName(bankName);
        voucherRecordVO.setPayVoucherState(piggyPayVoucherRecordDTO.getAuditStatus());
        voucherRecordVO.setDuplicatePayment(piggyPayVoucherRecordDTO.getRepeatVoucher());
        if (CollectionUtils.isNotEmpty(piggyPayVoucherRecordDTO.getFileList())) {
            List<HKFileVO> detailFiles = piggyPayVoucherRecordDTO.getFileList().stream().map(m -> {
                //加密
                String encryptUrl = FileUploadUtils.getEncryptUrl(piggyPayVoucherRecordDTO.getHkCustNo(), m.getFilePath(), m.getFileName(), FileBizTypeEnum.PAYMENT_VOUCHER_SAVE);
                return new HKFileVO(m.getFileId(), encryptUrl, encryptUrl, AppEncUtils.encrypt(m.getFileName()), FileUploadUtils.getSuffix(m.getFileName()));
            }).collect(Collectors.toList());
            voucherRecordVO.setPayVoucherImages(detailFiles);
        }
        //添加审核不通过的原因
        if (null != piggyPayVoucherRecordDTO.getAuditReason()) {
            PiggyDepositVoucherDetailVO.VoucherAuditResultVO voucherAuditResultVO = new PiggyDepositVoucherDetailVO.VoucherAuditResultVO();
            voucherAuditResultVO.setBankAcctMask(piggyPayVoucherRecordDTO.getAuditReason().getRemitCpAcctNo());
            voucherAuditResultVO.setRemitCurrency(piggyPayVoucherRecordDTO.getAuditReason().getRemitCurrency());
            voucherAuditResultVO.setRemark(piggyPayVoucherRecordDTO.getAuditReason().getRemark());
            voucherAuditResultVO.setRemitAmt(piggyPayVoucherRecordDTO.getAuditReason().getRemitAmt());
            voucherAuditResultVO.setPayVoucherImages(piggyPayVoucherRecordDTO.getAuditReason().getFileReason());
            List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS = ReflectUtils.extractFieldInfo(voucherAuditResultVO);
            voucherRecordVO.setCheckResult(hkOpenAcctCheckVOS);
        }
        return voucherRecordVO;
    }

    public static PiggyDepositVoucherRecordListVO toPiggyDepositVoucherRecordListVO(PiggyPayVoucherRecordPageDTO piggyPayVoucherRecordPageDTO,Integer page,Integer size) {
        PiggyDepositVoucherRecordListVO recordListVO = new PiggyDepositVoucherRecordListVO();
        if (null == piggyPayVoucherRecordPageDTO) {
            return recordListVO;
        }
        List<PiggyPayVoucherRecordPageDTO.PiggyPayVoucherRecordListDTO> piggyPayVoucherRecordListDTO = piggyPayVoucherRecordPageDTO.getPiggyPayVoucherRecordListDTO();
        if (CollectionUtils.isEmpty(piggyPayVoucherRecordListDTO)) {
            return recordListVO;
        }
        //分装参数
        List<PiggyDepositVoucherRecordListVO.PiggyDepositVoucherRecord> recordLists = piggyPayVoucherRecordListDTO.stream()
                .filter(voucherOrder ->StringUtils.isNotBlank(voucherOrder.getAuditStatus()) && !PayVoucherOrderStatusEnum.VOIDED.getCode().equals(voucherOrder.getAuditStatus()))
                .map(PiggyDepositConvert::getPiggyDepositVoucherRecordDetail) // 组合参数
                .sorted(getComparator())// 排序
                .collect(Collectors.toList());
        // 计算起始索引和结束索引
        int startIndex = (page - 1) * size;
        int endIndex = Math.min(startIndex + size, recordLists.size());
        // 截取子列表
        recordLists =  recordLists.subList(startIndex, endIndex);
        recordListVO.setPiggyDepositVoucherRecordList(recordLists);
        recordListVO.setTotal(piggyPayVoucherRecordPageDTO.getTotal());
        return recordListVO;
    }

    /**
     * @description: 获取比较构造器，先按照审核状态排序，然后按照申请的时间排序
     * @return java.util.Comparator<com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherRecordListVO.PiggyDepositVoucherRecordList>
     * @author: jinqing.rao
     * @date: 2024/9/5 11:06
     * @since JDK 1.8
     */
    private static Comparator<PiggyDepositVoucherRecordListVO.PiggyDepositVoucherRecord> getComparator() {
        return Comparator.comparing(getRecordListLocalDateTimeFunction(),Comparator.reverseOrder());
    }

    /**
     * @description: 获取记录的LocalDateTime的构造器
     * @return java.util.function.Function<com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherRecordListVO.PiggyDepositVoucherRecordList,java.time.LocalDateTime>
     * @author: jinqing.rao
     * @date: 2024/9/5 11:17
     * @since JDK 1.8
     */
    private static Function<PiggyDepositVoucherRecordListVO.PiggyDepositVoucherRecord, LocalDateTime> getRecordListLocalDateTimeFunction() {
        return record -> {
            try {
                return DateUtils.strToDate(record.getAppDate(), record.getAppTime(), DateUtils.YYYYMMDDHHMMSS);
            } catch (Exception e) {
               // 如果异常了,指定个最大值,放在最后
                return LocalDateTime.MAX;
            }
        };
    }

    /**
     * @param m
     * @return com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherRecordListVO.PiggyDepositVoucherRecordList
     * @description: 生成PiggyDepositVoucherRecordListVO.PiggyDepositVoucherRecordList 对象
     * @author: jinqing.rao
     * @date: 2024/9/5 10:20
     * @since JDK 1.8
     */
    private static PiggyDepositVoucherRecordListVO.PiggyDepositVoucherRecord getPiggyDepositVoucherRecordDetail(PiggyPayVoucherRecordPageDTO.PiggyPayVoucherRecordListDTO m) {
        PiggyDepositVoucherRecordListVO.PiggyDepositVoucherRecord piggyDepositVoucherRecord = new PiggyDepositVoucherRecordListVO.PiggyDepositVoucherRecord();
        piggyDepositVoucherRecord.setAppTime(m.getAppTime());
        piggyDepositVoucherRecord.setAppDate(m.getAppDate());
        piggyDepositVoucherRecord.setRemittanceAmount(NumberUtils.bigDecimalToString(m.getRemitAmt()));
        piggyDepositVoucherRecord.setRemittanceCurrency(m.getRemitCurrency());
        piggyDepositVoucherRecord.setVoucherNo(m.getVoucherNo());
        piggyDepositVoucherRecord.setActualReceiptAmount(BigDecimalUtils.procesFinalString(m.getActualPayAmt(),2, RoundingMode.DOWN));
        piggyDepositVoucherRecord.setActualReceiptCurrency(m.getActualPayCurrency());
        piggyDepositVoucherRecord.setVoucherType(m.getVoucherType());
        piggyDepositVoucherRecord.setTradeOrderNo(m.getTradeOrderNo());

        // 是否是重复凭证
        boolean duplicateVoucherFlag = StringUtils.equals(YesNoEnum.YES.getCode(), m.getDuplicateVoucher());
        List<String> statusNotPassStatus = Arrays.asList(PayVoucherAuditStatusEnum.REJECT_TO_CUSTOMER.getCode());

        // 是否展示删除按钮 审核不通过或者 重复凭证
        boolean canDeleted = duplicateVoucherFlag || statusNotPassStatus.contains(m.getAuditStatus());
        piggyDepositVoucherRecord.setCanDeleted(canDeleted ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        // 设置重复凭证标识
        if(duplicateVoucherFlag){
            piggyDepositVoucherRecord.setPayVoucherState(PayVoucherDisPlayStatusEnum.DUPLICATE_VOUCHER.getCode());
            piggyDepositVoucherRecord.setPayVoucherStateDesc(PayVoucherDisPlayStatusEnum.DUPLICATE_VOUCHER.getDescription());
            return piggyDepositVoucherRecord;
        }

        if (statusNotPassStatus.contains(m.getAuditStatus())) {
            piggyDepositVoucherRecord.setPayVoucherState(PayVoucherDisPlayStatusEnum.CREDIT_FAILED.getCode());
            piggyDepositVoucherRecord.setPayVoucherStateDesc(PayVoucherDisPlayStatusEnum.CREDIT_FAILED.getDescription());
            //添加审核不通过原因
            List<VoucherAuditReasonDTO> voucherAuditReasonDTOS = JSON.parseArray(m.getAuditReason(), VoucherAuditReasonDTO.class);
            if (CollectionUtils.isNotEmpty(voucherAuditReasonDTOS)) {
                List<String> reasonList = voucherAuditReasonDTOS.stream().filter(f -> !f.passFlag)
                        .map(reason -> reason.getFieldDesc() + ": " + reason.getRemark())
                        .collect(Collectors.toList());
                piggyDepositVoucherRecord.setRejectReason(reasonList);
            }
        } else {
            // 等待复核
            if(PayVoucherAuditStatusEnum.WAIT_REVIEW.getCode().equals(m.getAuditStatus())){
                piggyDepositVoucherRecord.setPayVoucherState(PayVoucherDisPlayStatusEnum.SUBMITTED.getCode());
                piggyDepositVoucherRecord.setPayVoucherStateDesc(PayVoucherDisPlayStatusEnum.SUBMITTED.getDescription());
            }
            // 审核通过
            if(PayVoucherAuditStatusEnum.APPROVE.getCode().equals(m.getAuditStatus())){
                piggyDepositVoucherRecord.setPayVoucherState(PayVoucherDisPlayStatusEnum.ALREADY_CREDITED.getCode());
                piggyDepositVoucherRecord.setPayVoucherStateDesc(PayVoucherDisPlayStatusEnum.ALREADY_CREDITED.getDescription());
            }
        }
        return piggyDepositVoucherRecord;
    }

    @Setter
    @Getter
    public static class VoucherAuditReasonDTO {

        /**
         * 字段描述
         */
        private String fieldDesc;

        /**
         * 字段名称
         */
        private String fieldName;

        /**
         * 字段类型
         */
        private String fieldType;

        /**
         * 是否通过
         */
        private Boolean passFlag;

        /**
         * 不通过原因
         */
        private String remark;
    }
}
