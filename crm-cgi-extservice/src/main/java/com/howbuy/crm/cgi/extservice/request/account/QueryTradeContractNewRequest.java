/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: 交易合同查询新版本请求类
 * @author: system
 * @date: 2025/7/3
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryTradeContractNewRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 基金编码
     */
    private String fundCode;

    /**
     * 合同类型集合
     * 1.交易下单 2.补签协议 3.储蓄罐签署/变更文件 4.开户文件
     */
    private String contractTypes;

    /**
     * 时间范围类型
     * 1-近一月，2-近三个月，3-近六个月，4.近一年
     */
    private String timeRangeType;

    /**
     * 开始时间
     * 格式：yyyyMMdd，timeRangeType=5时必填
     */
    private String startTime;

    /**
     * 结束时间
     * 格式：yyyyMMdd，timeRangeType=5时必填
     */
    private String endTime;

    /**
     * 包含储蓄罐 1 是 0 否
     */
    private String includePiggy;
}
