/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.hkaccount;

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.CurrencyEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.RedeemMethodEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.*;
import com.howbuy.crm.cgi.extservice.common.enums.traderecord.TradeRecordFundCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.utils.NumberUtils;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.ChangeRedeemDirectionRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.QueryTradeRecordListRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.QueryWaitRefundListRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.RelationOrderListRequest;
import com.howbuy.crm.cgi.extservice.vo.dtmsorder.BankCardVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TradeRecordDetailInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TradeRecordInfoListVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TradeRecordInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TradeRecordMultiDetialInfoVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.BankCardDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.capital.QueryInTransitTradeCapitalRequestDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.QueryTradeRecordListForTradeListRequestDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkBankCardInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hktraderecord.OrderNumAndUnitDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hktraderecord.TradeRecordDetailInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hktraderecord.TradeRecordInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hktraderecord.TradeRecordMultiDetialInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.HkFundOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.capital.QueryInTransitTradeCapitalOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkBankCardInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkTradeOuterService;
import com.howbuy.dtms.common.enums.AckStatusEnum;
import com.howbuy.dtms.common.enums.BusinessCodeEnum;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/2/29 15:21
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkTradeRecordService {

    @Autowired
    private HkTradeOuterService hkTradeOuterService;

    @Resource
    private HkFundOuterService hkFundOuterService;

    @Resource
    private QueryFundBasicInfoOuterService fundBasicInfoOuterService;


    @Resource
    private HkBankCardInfoOuterService hkBankCardInfoOuterService;

    @Resource
    private QueryInTransitTradeCapitalOuterService queryInTransitTradeCapitalOuterService;

    @Resource
    private HkCustInfoOuterService hkCustInfoOuterService;


    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TradeRecordInfoListVO
     * @description: 交易记录列表查询接口
     * @author: jinqing.rao
     * @date: 2025/4/1 14:49
     * @since JDK 1.8
     */
    public TradeRecordInfoListVO queryTradeRecordListForTradeList(QueryTradeRecordListRequest request) {
        String hkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        if (StringUtils.isNotBlank(request.getTimePeriod())) {
            request.setOrderStartDt(HKFundTimePeriodEnum.getStartDt(request.getTimePeriod()));
            request.setOrderEndDt(HKFundTimePeriodEnum.getEndDt());
        }
        // 构建请求参数
        QueryTradeRecordListForTradeListRequestDTO requestDTO = buildRequestParams(request, hkCustNo);

        // 获取交易记录信息接口
        List<TradeRecordInfoDTO> tradeRecordInfoDTOS = hkFundOuterService.queryTradeRecordListForTradeList(requestDTO);
        if (CollectionUtils.isEmpty(tradeRecordInfoDTOS)) {
            return new TradeRecordInfoListVO();
        }
        // 获取基金CodeList
        List<String> fundCodes = tradeRecordInfoDTOS.stream().map(TradeRecordInfoDTO::getFundCode).collect(Collectors.toList());
        // 获取基金编码和volPrecision的Map, 用于获取基金产品金额/份额的 小数位的精度处理
        Map<String, Integer> volPreviewMap = fundBasicInfoOuterService.queryFundVolPreviewMap(fundCodes);
        return transToTradeRecordInfoListVO(tradeRecordInfoDTOS, volPreviewMap);
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.QueryTradeRecordListForTradeListRequestDTO
     * @description: 构建请求参数
     * @author: jinqing.rao
     * @date: 2025/4/1 15:19
     * @since JDK 1.8
     */
    private static QueryTradeRecordListForTradeListRequestDTO buildRequestParams(QueryTradeRecordListRequest request, String hkCustNo) {
        QueryTradeRecordListForTradeListRequestDTO requestDTO = new QueryTradeRecordListForTradeListRequestDTO();
        requestDTO.setTradeStatus(request.getTradeStatus());
        requestDTO.setFundCode(request.getFundCode());
        // 基金分类 不是真正产品维度的,也前端业务维度的 具体参考 TradeRecordFundCategoryEnum
        List<String> fundCategoryList = request.getFundCategoryList();

        // 检查 fundCategoryList 是否包含 "3"
        if (CollectionUtils.isNotEmpty(fundCategoryList) && fundCategoryList.contains(TradeRecordFundCategoryEnum.DEPOSIT_FUND.getCode())) {
            // 设置 PiggyFund 为 "1"
            requestDTO.setPiggyFund(YesNoEnum.YES.getCode());
            // 移除 "3" 并重新赋值 FundCategoryList
            fundCategoryList = fundCategoryList.stream()
                    .filter(item -> !TradeRecordFundCategoryEnum.DEPOSIT_FUND.getCode().equals(item))
                    .collect(Collectors.toList());
        }
        // 将处理后的 fundCategoryList 赋值给 requestDTO
        requestDTO.setFundCategoryList(fundCategoryList);
        requestDTO.setBusiTypeList(request.getBusiTypeList());
        requestDTO.setTradeStatusList(request.getTradeStatusList());
        requestDTO.setHoldStatus(request.getHoldStatus());
        requestDTO.setOrderStartDt(request.getOrderStartDt());
        requestDTO.setOrderEndDt(request.getOrderEndDt());
        requestDTO.setFundTxCode(request.getFundTxCode());
        requestDTO.setHkCustNo(hkCustNo);
        requestDTO.setPage(request.getPage());
        requestDTO.setSize(request.getSize());
        return requestDTO;
    }

    /**
     * @param request
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TradeRecordInfoListVO>
     * @description:(查询当前用户的交易记录列表数据)
     * @author: xufanchao
     * @date: 2024/2/29 15:23
     * @since JDK 1.8
     */
    public TradeRecordInfoListVO queryTradeRecordList(QueryTradeRecordListRequest request) {
        String hkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        List<String> childfundCodes = new ArrayList<>();
        // 如果是母基金,获取对应的所有子基金Code
        if (StringUtils.isNotBlank(request.getFundCode())) {
            childfundCodes = fundBasicInfoOuterService.queryFundBasicInfoListByMainFundCode(request.getFundCode());
            // 没有对应子基金编码，返回空
            if (CollectionUtils.isEmpty(childfundCodes)) {
                log.info("queryTradeRecordList >>> 没有获取到对应的子基金信息, hkCustNo : {},mainFundCode :{}", hkCustNo, request.getFundCode());
                return new TradeRecordInfoListVO();
            }
        }
        // 获取基金列表信息
        List<TradeRecordInfoDTO> tradeRecordInfoDTOS = hkTradeOuterService.queryHwFundRecordList(hkCustNo, request.getTradeStatus(), childfundCodes, request.getPage(), request.getSize());
        if (CollectionUtils.isEmpty(tradeRecordInfoDTOS)) {
            return new TradeRecordInfoListVO();
        }
        // 获取基金CodeList
        List<String> fundCodes = tradeRecordInfoDTOS.stream().map(TradeRecordInfoDTO::getFundCode).collect(Collectors.toList());
        // 获取基金编码和volPrecision的Map, 用于获取基金产品金额/份额的 小数位的精度处理
        Map<String, Integer> volPreviewMap = fundBasicInfoOuterService.queryFundVolPreviewMap(fundCodes);
        return transToTradeRecordInfoListVO(tradeRecordInfoDTOS, volPreviewMap);
    }

    /**
     * @param tradeRecordInfoDTOS
     * @param volPreviewMap
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TradeRecordInfoListVO
     * @description: 转换交易记录列表数据
     * @author: jinqing.rao
     * @date: 2025/5/28 17:06
     * @since JDK 1.8
     */
    private TradeRecordInfoListVO transToTradeRecordInfoListVO(List<TradeRecordInfoDTO> tradeRecordInfoDTOS, Map<String, Integer> volPreviewMap) {
        TradeRecordInfoListVO tradeRecordInfoListVO = new TradeRecordInfoListVO();
        List<TradeRecordInfoVO> tradeRecordInfoList = new ArrayList<>();
        tradeRecordInfoDTOS.forEach(it -> {
            TradeRecordInfoVO tradeRecordInfoVO = new TradeRecordInfoVO();
            tradeRecordInfoVO.setDealNo(it.getDealNo());
            tradeRecordInfoVO.setTradeType(it.getTradeType());
            tradeRecordInfoVO.setSubTradeType(it.getMiddleBusiCode());
            // 认缴和实缴需要单独展示
            if (HKFundTradeTypeEnum.SUB_AND_FIRST_PAID.getCode().equals(it.getTradeType())) {
                if (BusinessCodeEnum._112A.getMCode().equals(it.getMiddleBusiCode())) {
                    tradeRecordInfoVO.setTradeTypeDesc("认缴");
                }
                if (BusinessCodeEnum._112B.getMCode().equals(it.getMiddleBusiCode())) {
                    tradeRecordInfoVO.setTradeTypeDesc("实缴");
                }
            } else if (HKFundTradeTypeEnum.BATCH_PAID.getCode().equals(it.getTradeType()) && BusinessCodeEnum._112B.getMCode().equals(it.getMiddleBusiCode())) {
                tradeRecordInfoVO.setTradeTypeDesc("实缴");
            } else if (HKFundTradeTypeEnum.FULL_BATCH_SUBSCRIBE.getCode().equals(it.getTradeType())) {
                if (BusinessCodeEnum.SUBS.getMCode().equals(it.getMiddleBusiCode()) || BusinessCodeEnum.PURCHASE.getMCode().equals(it.getMiddleBusiCode())) {
                    tradeRecordInfoVO.setTradeTypeDesc("买入");
                }
                if (BusinessCodeEnum._112A.getMCode().equals(it.getMiddleBusiCode())) {
                    tradeRecordInfoVO.setTradeTypeDesc("认缴");
                }
            } else {
                tradeRecordInfoVO.setTradeTypeDesc(HKFundTradeTypeEnum.getDescByCode(it.getTradeType()));
            }

            // 红利下发特殊展示
            if (HKFundTradeTypeEnum.DIV.getCode().equals(it.getTradeType())) {
                if ("1".equals(it.getFundDivMode())) {
                    tradeRecordInfoVO.setTradeTypeDesc("现金分红");
                }
                if ("0".equals(it.getFundDivMode())) {
                    tradeRecordInfoVO.setTradeTypeDesc("红利再投");
                }
            }
            tradeRecordInfoVO.setTradeDirection(HKFundTradeTypeEnum.getDirectionByCode(it.getTradeType()));
            // 如果是平衡因子兑换的话，需要根据申请份额进行特殊处理展示方向
            if (HKFundTradeTypeEnum.BALANCE_FACTOR_EXCHANGE.getCode().equals(it.getTradeType())) {
                setTradeDirection(it, tradeRecordInfoVO);
            }
            //设置交易状态
            String tradeStatusDesc = getOrderTradeStatus(it.getOrderStatus(), it.getPayStatus());
            tradeRecordInfoVO.setTradeStatus(tradeStatusDesc);
            tradeRecordInfoVO.setTradeStatusColor(it.getTradeStatusColor());
            tradeRecordInfoVO.setAppDate(it.getAppDate());
            tradeRecordInfoVO.setProductName(it.getProductName());
            //设置金额/份额以及单位
            Integer volPreview = volPreviewMap.get(it.getFundCode());

            OrderNumAndUnitDTO orderNumAndUnitDTO = buildOrderNumAndUnitDTO(it, volPreview);
            Pair<String, String> numAndUnit = getOrderNumAndUnit(orderNumAndUnitDTO);


            // 把appdate 20240326 取后四位，转换成 0326 apptime 000000 转换成 00:00:00 合并转换一下
            tradeRecordInfoVO.setAppTime(DateUtils.formatDate(it.getAppDate() + it.getAppTime()));
            tradeRecordInfoVO.setNum(numAndUnit.getKey());
            tradeRecordInfoVO.setUnit(numAndUnit.getValue());
            tradeRecordInfoVO.setTypeIcon(HKFundTradeTypeEnum.getTypeIconByCode(it.getTradeType()));
            tradeRecordInfoVO.setSpecialTag(it.getSpecialTag());
            tradeRecordInfoVO.setCurrency(it.getCurrency());
            tradeRecordInfoVO.setTransferProductName(it.getTransferProductName());
            // 交易过户申购/赎回 基金转换 也加上有详情页
            if (HKFundTradeTypeEnum.BUY.getCode().equals(it.getTradeType())
                    || HKFundTradeTypeEnum.SELL.getCode().equals(it.getTradeType())
                    || HKFundTradeTypeEnum.TRANSFER_BUY.getCode().equals(it.getTradeType())
                    || HKFundTradeTypeEnum.TRANSFER_SELL.getCode().equals(it.getTradeType())
                    || HKFundTradeTypeEnum.FUND_CONVERSION.getCode().equals(it.getTradeType())
                    || HKFundTradeTypeEnum.SUB_AND_FIRST_PAID.getCode().equals(it.getTradeType())
                    || HKFundTradeTypeEnum.BATCH_PAID.getCode().equals(it.getTradeType())
                    || HKFundTradeTypeEnum.FULL_BATCH_SUBSCRIBE.getCode().equals(it.getTradeType())
                    || HKFundTradeTypeEnum.FULL_BATCH_REDEEM.getCode().equals(it.getTradeType())

            ) {
                tradeRecordInfoVO.setCanGoToDetail(YesNoEnum.YES.getCode());
            } else {
                tradeRecordInfoVO.setCanGoToDetail(YesNoEnum.NO.getCode());
            }
            tradeRecordInfoList.add(tradeRecordInfoVO);
        });
        tradeRecordInfoListVO.setTradeRecordInfoList(tradeRecordInfoList);
        return tradeRecordInfoListVO;
    }


    /**
     * @param it
     * @param tradeRecordInfoVO
     * @return void
     * @description:(设置展示方向)
     * @author: xufanchao
     * @date: 2025/1/10 10:37
     * @since JDK 1.8
     */
    private static void setTradeDirection(TradeRecordInfoDTO it, TradeRecordInfoVO tradeRecordInfoVO) {
        if (it.getAppVol() != null && it.getAppVol().compareTo(BigDecimal.ZERO) > 0) {
            tradeRecordInfoVO.setTradeDirection("+");
        } else if (it.getAppVol() != null && it.getAppVol().compareTo(BigDecimal.ZERO) < 0) {
            tradeRecordInfoVO.setTradeDirection("-");
        }
    }

    /**
     * 展示金额 （历史的代码逻辑，复制高端的）
     *
     * @param orderNumAndUnitDTO
     * @return
     */
    private Pair<String, String> getOrderNumAndUnit(OrderNumAndUnitDTO orderNumAndUnitDTO) {
        String appAmtStr = NumberUtils.formatToThousandths(orderNumAndUnitDTO.getAppAmt(), 2, RoundingMode.DOWN);
        String ackAmtStr = NumberUtils.formatToThousandths(orderNumAndUnitDTO.getAckAmt(), 2, RoundingMode.DOWN);
        List<String> buyType = Arrays.asList(HKFundTradeTypeEnum.BUY.getCode(),
                HKFundTradeTypeEnum.FULL_BATCH_SUBSCRIBE.getCode(), HKFundTradeTypeEnum.SUB_AND_FIRST_PAID.getCode(), HKFundTradeTypeEnum.BATCH_PAID.getCode());
        if (buyType.contains(orderNumAndUnitDTO.getTradeType())) {
            // 买入，取订单的【申请金额】
            return new Pair<>(appAmtStr, CurrencyEnum.getDescription(orderNumAndUnitDTO.getCurrency()));
        }

        // 平衡因子兑换 --> 确认份额绝对值
        if (HKFundTradeTypeEnum.BALANCE_FACTOR_EXCHANGE.getCode().equals(orderNumAndUnitDTO.getTradeType())) {
            return new Pair<>(NumberUtils.formatToThousandths(orderNumAndUnitDTO.getAckVol().abs(), orderNumAndUnitDTO.getVolPrecision(), RoundingMode.DOWN), "份");
        }
        // 系列合并转入/转出 确认份额
        if (HKFundTradeTypeEnum.SERIES_MERGE_IN.getCode().equals(orderNumAndUnitDTO.getTradeType()) || HKFundTradeTypeEnum.SERIES_MERGE_OUT.getCode().equals(orderNumAndUnitDTO.getTradeType())) {
            return new Pair<>(NumberUtils.formatToThousandths(orderNumAndUnitDTO.getAckVol(), orderNumAndUnitDTO.getVolPrecision(), RoundingMode.DOWN), "份");
        }
        // 交易过户转入 取申请金额
        if (HKFundTradeTypeEnum.TRANSFER_BUY.getCode().equals(orderNumAndUnitDTO.getTradeType())) {
            return new Pair<>(appAmtStr, CurrencyEnum.getDescription(orderNumAndUnitDTO.getCurrency()));
        }
        //  交易过户赎回/ 基金转换 取申请份额
        if (HKFundTradeTypeEnum.TRANSFER_SELL.getCode().equals(orderNumAndUnitDTO.getTradeType()) || HKFundTradeTypeEnum.FUND_CONVERSION.getCode().equals(orderNumAndUnitDTO.getTradeType())) {
            return new Pair<>(NumberUtils.formatToThousandths(orderNumAndUnitDTO.getAppVol(), orderNumAndUnitDTO.getVolPrecision(), RoundingMode.DOWN), "份");
        }
        List<String> sellType = Arrays.asList(HKFundTradeTypeEnum.SELL.getCode(), HKFundTradeTypeEnum.FULL_BATCH_REDEEM.getCode());
        if (sellType.contains(orderNumAndUnitDTO.getTradeType())) {
            if (RedeemMethodEnum.AMT.getKey().equals(orderNumAndUnitDTO.getRedeemType())) {
                return new Pair<>(appAmtStr, CurrencyEnum.getDescription(orderNumAndUnitDTO.getCurrency()));
            }
            if (RedeemMethodEnum.VOL.getKey().equals(orderNumAndUnitDTO.getRedeemType())) {
                return new Pair<>(NumberUtils.formatToThousandths(orderNumAndUnitDTO.getAppVol(), orderNumAndUnitDTO.getVolPrecision(), RoundingMode.DOWN), "份");
            }
            return new Pair<>(orderNumAndUnitDTO.getUnit(), orderNumAndUnitDTO.getNum());
        }
        if (HKFundTradeTypeEnum.DIV.getCode().equals(orderNumAndUnitDTO.getTradeType())) {
            if ("1".equals(orderNumAndUnitDTO.getFundDivMode())) {
                return new Pair<>(ackAmtStr, CurrencyEnum.getDescription(orderNumAndUnitDTO.getCurrency()));
            }
            if ("0".equals(orderNumAndUnitDTO.getFundDivMode())) {
                return new Pair<>(NumberUtils.formatToThousandths(orderNumAndUnitDTO.getAckVol(), orderNumAndUnitDTO.getVolPrecision(), RoundingMode.DOWN), "份");
            }
            //获取分红的方式
            return new Pair<>(appAmtStr, CurrencyEnum.getDescription(orderNumAndUnitDTO.getCurrency()));
        }
        // 交易类型 = 非交易转入/非交易转出/强增/强减/强赎/基金终止/基金清盘：取订单的【确认份额】
        List<String> tradeTypeList = new ArrayList<>();
        tradeTypeList.add(HKFundTradeTypeEnum.NO_TRADE_OVER_ACCOUNT_IN.getCode());
        tradeTypeList.add(HKFundTradeTypeEnum.NO_TRADE_OVER_ACCOUNT_OUT.getCode());
        tradeTypeList.add(HKFundTradeTypeEnum.FORCE_ADD.getCode());
        tradeTypeList.add(HKFundTradeTypeEnum.FORCE_SUBTRACT.getCode());
        tradeTypeList.add(HKFundTradeTypeEnum.FORCE_REDEEM.getCode());
        tradeTypeList.add(HKFundTradeTypeEnum.FUND_TERMINATION.getCode());
        tradeTypeList.add(HKFundTradeTypeEnum.FUND_LIQUIDATION.getCode());
        if (tradeTypeList.contains(orderNumAndUnitDTO.getTradeType())) {
            return new Pair<>(NumberUtils.bigDecimalToString(orderNumAndUnitDTO.getAckVol(), orderNumAndUnitDTO.getVolPrecision(), RoundingMode.DOWN), "份");
        }
        return new Pair<>("", "");
    }

    /**
     * 获取订单的交易状态
     *
     * @param orderStatus
     * @param payStatus
     * @return
     */
    private String getOrderTradeStatus(String orderStatus, String payStatus) {
        HKFundTradeStatusEnum hkFundTradeStatusEnum = getHKFundOrderStatusEnumByOrder(orderStatus, payStatus);
        return null == hkFundTradeStatusEnum ? null : hkFundTradeStatusEnum.getDesc();
    }

    private HKFundTradeStatusEnum getHKFundOrderStatusEnumByOrder(String orderStatus, String payStatus) {
        //        付款中：【订单状态】=申请成功 &【支付标识】=未付款/付款中
        //        确认中：【订单状态】=申请成功 &【支付标识】=无需付款/付款成功
        //        自行撤单：【订单状态】=自行撤销
        //        强制撤单：【订单状态】=强制撤销
        //        交易成功：【订单状态】=确认成功
        //        部分成功：【订单状态】=部分确认
        //        交易失败：【订单状态】=确认失败
        if (HKFundOrderStatusEnum.APPLY_SUCCESS.getCode().equals(orderStatus) && (HKFundOrderPayStatusEnum.UN_PAY.getCode().equals(payStatus) ||
                HKFundOrderPayStatusEnum.PAYING.getCode().equals(payStatus))) {
            return HKFundTradeStatusEnum.PAYING;
        } else if (HKFundOrderStatusEnum.APPLY_SUCCESS.getCode().equals(orderStatus) && (HKFundOrderPayStatusEnum.NO_NEED_PAY.getCode().equals(payStatus) ||
                HKFundOrderPayStatusEnum.PAY_SUCCESS.getCode().equals(payStatus))) {
            return HKFundTradeStatusEnum.WAIT_CONFIRM;
        } else if (HKFundOrderStatusEnum.SELF_CANCEL.getCode().equals(orderStatus)) {
            return HKFundTradeStatusEnum.SELF_CANCEL;
        } else if (HKFundOrderStatusEnum.FORCE_CANCEL.getCode().equals(orderStatus)) {
            return HKFundTradeStatusEnum.FORCE_CANCEL;
        } else if (HKFundOrderStatusEnum.CONFIRM_SUCCESS.getCode().equals(orderStatus)) {
            return HKFundTradeStatusEnum.CONFIRM_SUCCESS;
        } else if (HKFundOrderStatusEnum.PART_CONFIRM.getCode().equals(orderStatus)) {
            return HKFundTradeStatusEnum.PART_CONFIRM;
        } else if (HKFundOrderStatusEnum.CONFIRM_FAIL.getCode().equals(orderStatus)) {
            return HKFundTradeStatusEnum.CONFIRM_FAIL;
        }
        return null;
    }

    /**
     * @param contractNo
     * @return void
     * @description:(查询交易记录的详情数据)
     * @author: xufanchao
     * @date: 2024/3/1 13:16
     * @since JDK 1.8
     */
    public TradeRecordDetailInfoVO queryTradeRecordDetail(String contractNo) {
        String hkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        //获取记忆记录的订单信息
        TradeRecordDetailInfoDTO tradeRecordDetailInfoDTO = hkTradeOuterService.queryHwTradeRecordDetailInfo(contractNo, hkCustNo);
        FundBasicInfoDTO fundBasicInfoDTO = fundBasicInfoOuterService.queryFundBasicInfo(tradeRecordDetailInfoDTO.getFundCode());
        // 是否展示打款凭证上传
        boolean supportUpload = isSupportUpload(tradeRecordDetailInfoDTO);
        tradeRecordDetailInfoDTO.setTradeStatus(getOrderTradeStatus(tradeRecordDetailInfoDTO.getOrderStatus(), tradeRecordDetailInfoDTO.getPayStatus()));
        // 获取用户银行卡信息
        List<HkBankCardInfoDTO> hkBankAcctLogoList = hkBankCardInfoOuterService.getHkBankAcctLogoList(hkCustNo);
        return hwTransToDetailInfoVO(tradeRecordDetailInfoDTO, supportUpload, fundBasicInfoDTO,hkBankAcctLogoList,hkCustNo);
    }
    /**
     * @param tradeRecordDetailInfoDTO
     * @return boolean
     * @description: 是否展示打款凭证入口
     * @author: jinqing.rao
     * @date: 2024/6/13 14:23
     * @since JDK 1.8
     */
    private static boolean isSupportUpload(TradeRecordDetailInfoDTO tradeRecordDetailInfoDTO) {
        // 【支付类型】=银行转账划款 &【交易类型】=认购/申购/实缴 且 打款凭证状态不等于无需上传
        boolean bankPay = HkFundPayMethodEnum.isBnakPayMethod(tradeRecordDetailInfoDTO.getPaymentType());
        boolean buyMethod = HkFundMBusiCodeEnum.SUBS.getMCode().equals(tradeRecordDetailInfoDTO.getMiddleBusiCode())
                || HkFundMBusiCodeEnum.PURCHASE.getMCode().equals(tradeRecordDetailInfoDTO.getMiddleBusiCode())
                || HkFundMBusiCodeEnum._112B.getMCode().equals(tradeRecordDetailInfoDTO.getMiddleBusiCode());
        // 打款凭证状态不等于无需上传
        boolean noneUpload = HKFundPayVoucherStateEnum.NONE.getCode().equals(tradeRecordDetailInfoDTO.getPayVoucherStatus());
        // 申请成功且 支付标识不等于 付款成功/付款失败
        boolean applyResult = HKFundOrderStatusEnum.APPLY_SUCCESS.getCode().equals(tradeRecordDetailInfoDTO.getOrderStatus());
        boolean b = !HKFundOrderPayStatusEnum.PAY_SUCCESS.getCode().equals(tradeRecordDetailInfoDTO.getPayStatus());
        boolean b1 = !HKFundOrderPayStatusEnum.PAY_FAILURE.getCode().equals(tradeRecordDetailInfoDTO.getPayStatus());
        boolean orderAndPayStatus = applyResult && b && b1;

        return bankPay && buyMethod && !noneUpload && orderAndPayStatus;
    }

    /**
     * @param t
     * @param supportUpload
     * @param fundBasicInfoDTO
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TradeRecordDetailInfoVO
     * @description:(请在此添加描述)
     * @author: jinqing.rao
     * @date: 2025/4/8 15:11
     * @since JDK 1.8
     */
    private TradeRecordDetailInfoVO hwTransToDetailInfoVO(TradeRecordDetailInfoDTO t,
                                                          boolean supportUpload, FundBasicInfoDTO fundBasicInfoDTO,
                                                          List<HkBankCardInfoDTO> hkBankAcctLogoList,
                                                          String hkCustNo) {
        TradeRecordDetailInfoVO tradeRecordDetailInfoVO = new TradeRecordDetailInfoVO();
        tradeRecordDetailInfoVO.setFundName(t.getFundName());
        tradeRecordDetailInfoVO.setFundCode(t.getFundCode());
        tradeRecordDetailInfoVO.setMBusiCode(t.getMiddleBusiCode());
        tradeRecordDetailInfoVO.setMBusiCodeDesc(HkFundMBusiCodeEnum.getDescByMCode(t.getMiddleBusiCode()));

        // 构建页面展示金额和单位
        OrderNumAndUnitDTO orderNumAndUnitDTO = buildOrderNumAndUnitDTO(t, fundBasicInfoDTO);
        Pair<String, String> orderNumAndUnit = getOrderNumAndUnit(orderNumAndUnitDTO);

        tradeRecordDetailInfoVO.setNum(orderNumAndUnit.getKey());
        tradeRecordDetailInfoVO.setUnit(orderNumAndUnit.getValue());
        tradeRecordDetailInfoVO.setTradeStatus(t.getTradeStatus());
        String orderTradeStatusDesc = getOrderTradeStatus(t.getOrderStatus(), t.getPayStatus());
        tradeRecordDetailInfoVO.setTradeStatus(orderTradeStatusDesc);
        tradeRecordDetailInfoVO.setTradeStatusColor(t.getTradeStatusColor());
        tradeRecordDetailInfoVO.setCurrencyDesc(CurrencyEnum.getDescription(t.getCurrency()));
        // 是否分次Call
        boolean getGradationCallBoolean = StringUtils.equals(YesNoEnum.YES.getCode(), fundBasicInfoDTO.getGradationCall());
        tradeRecordDetailInfoVO.setGradationCall(getGradationCallBoolean ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        // 认缴金额
        tradeRecordDetailInfoVO.setSubAmt(NumberUtils.bigDecimalToString(t.getSubAmt(), 2, RoundingMode.DOWN));
        // 关联订单号
        tradeRecordDetailInfoVO.setRelationOrderList(t.getRelationOrderList());

        // 设置银行卡信息
        initBankInfo(t, tradeRecordDetailInfoVO);
        tradeRecordDetailInfoVO.setAppVol(defaultShowFormat(NumberUtils.formatToThousandths(t.getAppVol(), fundBasicInfoDTO.getVolPrecision(), RoundingMode.DOWN)));
        tradeRecordDetailInfoVO.setAppAmt(defaultShowFormat(NumberUtils.formatToThousandths(t.getAppAmt(), 2, RoundingMode.DOWN)));
        tradeRecordDetailInfoVO.setAckAmt(defaultShowFormat(NumberUtils.formatToThousandths(t.getAckAmt(), 2, RoundingMode.DOWN)));
        tradeRecordDetailInfoVO.setAckVol(defaultShowFormat(NumberUtils.formatToThousandths(t.getAckVol(), fundBasicInfoDTO.getVolPrecision(), RoundingMode.DOWN)));
        List<String> estimateFeeOrderStatus = Arrays.asList(HKFundOrderStatusEnum.APPLY_SUCCESS.getCode(), HKFundOrderStatusEnum.CONFIRM_FAIL.getCode(),
                HKFundOrderStatusEnum.SELF_CANCEL.getCode(), HKFundOrderStatusEnum.FORCE_CANCEL.getCode());
        // 订单状态=“确认成功/部分确认”，取确认手续费，即订单【手续费】字段
        if (HKFundOrderStatusEnum.CONFIRM_SUCCESS.getCode().equals(t.getOrderStatus()) || HKFundOrderStatusEnum.PART_CONFIRM.getCode().equals(t.getOrderStatus())) {
            tradeRecordDetailInfoVO.setFee(defaultShowFormat(NumberUtils.formatToThousandths(t.getFee(), 2, RoundingMode.DOWN)));
        }
        // // 订单状态=“申请成功/确认失败/自行撤销/强制撤销”，取订单【预估手续费】
        else if (estimateFeeOrderStatus.contains(t.getOrderStatus())) {
            tradeRecordDetailInfoVO.setFee(defaultShowFormat(NumberUtils.formatToThousandths(t.getEstimateFee(), 2, RoundingMode.DOWN)));
            // 申请成功数据单独判断费用
            if (HKFundOrderStatusEnum.APPLY_SUCCESS.getCode().equals(t.getOrderStatus()) && CollectionUtils.isNotEmpty(t.getTradeRecordMultiDetialInfoDTOList())) {
                BigDecimal fee = getFee(t.getTradeRecordMultiDetialInfoDTOList());
                tradeRecordDetailInfoVO.setFee(defaultShowFormat(NumberUtils.formatToThousandths(fee, 2, RoundingMode.DOWN)));
            }
        }


        tradeRecordDetailInfoVO.setPayStatus(t.getPayStatus());
        tradeRecordDetailInfoVO.setOrderStatus(t.getOrderStatus());
        tradeRecordDetailInfoVO.setAppTm(t.getAppTm());
        tradeRecordDetailInfoVO.setAckDt(t.getAckDt());
        tradeRecordDetailInfoVO.setAppDt(t.getAppDt());

        String disDate = DateUtils.formatDateStr(t.getAppDt() + t.getAppTm(), DateUtils.YYYY_MM_DD_HH_MM, DateUtils.YYYYMMDDHHMMSS);
        tradeRecordDetailInfoVO.setAppDisDt(disDate);
        Integer jzws = null == fundBasicInfoDTO.getJzws() ? Integer.valueOf("4") : fundBasicInfoDTO.getJzws();
        tradeRecordDetailInfoVO.setNav(defaultShowFormat(NumberUtils.formatToThousandths(t.getNav(), jzws, RoundingMode.DOWN)));
        tradeRecordDetailInfoVO.setTradeDt(t.getTaTradeDt());
        tradeRecordDetailInfoVO.setOpenDt(t.getOpenDt());
        tradeRecordDetailInfoVO.setTradeRecordMultiDetialInfoVOList(buildMltiDetailInfoVoList(t.getTradeRecordMultiDetialInfoDTOList(), fundBasicInfoDTO));

        if (t.getTransferFundCode() != null) {
            // 获取基金基本信息
            FundBasicInfoDTO transFundBasicDto = fundBasicInfoOuterService.queryFundBasicInfo(t.getTransferFundCode());
            tradeRecordDetailInfoVO.setTransferAckAmt(defaultShowFormat(NumberUtils.formatToThousandths(t.getTransferAckAmt(), 2, RoundingMode.DOWN)));
            tradeRecordDetailInfoVO.setTransferAckVol(defaultShowFormat(NumberUtils.formatToThousandths(t.getTransferAckVol(), transFundBasicDto.getVolPrecision(), RoundingMode.DOWN)));
            tradeRecordDetailInfoVO.setTransferFundName(t.getTransferFundName());
            tradeRecordDetailInfoVO.setTransferFundCode(t.getTransferFundCode());
            tradeRecordDetailInfoVO.setTransferCurrency(t.getTransferCurrency());
            tradeRecordDetailInfoVO.setTransferCurrencyDesc(CurrencyEnum.getDescription(t.getTransferCurrency()));
            Integer transferJzws = null == transFundBasicDto.getJzws() ? Integer.valueOf("4") : transFundBasicDto.getJzws();
            tradeRecordDetailInfoVO.setTransferNav(defaultShowFormat(NumberUtils.formatToThousandths(t.getTransferNav(), transferJzws, RoundingMode.DOWN)));
            FundBasicInfoDTO fundBasicInfo = fundBasicInfoOuterService.queryFundBasicInfo(t.getFundCode());
            Integer intoJzws = null == fundBasicInfo.getJzws() ? Integer.valueOf("4") : fundBasicInfo.getJzws();
            tradeRecordDetailInfoVO.setNav(defaultShowFormat(NumberUtils.formatToThousandths(t.getNav(), intoJzws, RoundingMode.DOWN)));
            tradeRecordDetailInfoVO.setAckVol(defaultShowFormat(NumberUtils.formatToThousandths(t.getAckVol(), fundBasicInfo.getVolPrecision(), RoundingMode.DOWN)));
        }

        // 设置打款凭证状态
        setPaymentVoucherStatus(t.getPayVoucherStatus(), supportUpload, tradeRecordDetailInfoVO);
        // 设置支付方式
        setPaymentType(t, tradeRecordDetailInfoVO);
        //设置赎回方式
        setRedeemDirection(t, tradeRecordDetailInfoVO);
        //设置资金募集状态
        setFundRaisingStatus(t, tradeRecordDetailInfoVO);

        // 是否展示修改回款方式入口
        tradeRecordDetailInfoVO.setShowModifyRedeemDirection(canShowModifyRedeemDirection(t.getMiddleBusiCode(), t.getOrderStatus(), t.getAckStatus()) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());

        if(null != t.getDealNo()){
            // 待回款订单提醒
            QueryInTransitTradeCapitalRequestDTO requestDTO = QueryInTransitTradeCapitalRequestDTO.builder()
                    .hkCustNo(hkCustNo)
                    .dealNo(t.getDealNo().toString()).build();

            List<TradeRecordInfoDTO> tradeRecordInfoDTOS = queryInTransitTradeCapitalOuterService.queryInTransitTradeCapitalOrderList(requestDTO);
            tradeRecordDetailInfoVO.setWaitRefundOrder(CollectionUtils.isNotEmpty(tradeRecordInfoDTOS) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        }

        // 赋值银行卡信息
        setHkBankCardInfo(hkBankAcctLogoList, tradeRecordDetailInfoVO);
        return tradeRecordDetailInfoVO;
    }

    /**
     * @description: 是否展示修改回款方式入口
     * @param middleBusiCode 中台业务码
     * @param orderStatus 订单状态
     * @param ackStatus 确认状态
     * @return boolean
     * @author: jinqing.rao
     * @date: 2025/7/2 16:50
     * @since JDK 1.8
     */
    private static boolean canShowModifyRedeemDirection(String middleBusiCode,String orderStatus,String ackStatus) {
        // 中台业务码 为 1124(赎回)，119B(交易过户赎回)
        boolean containsRedeemBusiCode = Arrays.asList(BusinessCodeEnum.REDEEM.getMCode(), BusinessCodeEnum._119B.getMCode()).contains(middleBusiCode);
        // 订单状态申请成功
        boolean applySuccessStatus = HKFundOrderStatusEnum.APPLY_SUCCESS.getCode().equals(orderStatus);
        // 【确认状态】=“未确认”、“确认中”
        boolean confirmStatus = AckStatusEnum.UN_CONFIRM.getCode().equals(ackStatus) || AckStatusEnum.CONFIRM_ING.getCode().equals(ackStatus);

        return containsRedeemBusiCode && applySuccessStatus && confirmStatus;
    }

    /**
     * @description: 设置银行卡信息
     * @param hkBankAcctLogoList	
     * @param tradeRecordDetailInfoVO
     * @return void
     * @author: jinqing.rao
     * @date: 2025/7/2 16:41
     * @since JDK 1.8
     */
    private static void setHkBankCardInfo(List<HkBankCardInfoDTO> hkBankAcctLogoList, TradeRecordDetailInfoVO tradeRecordDetailInfoVO) {
        if(CollectionUtils.isNotEmpty(hkBankAcctLogoList)){
            List<TradeRecordDetailInfoVO.FundBankCardVO> bankInfoVOS = hkBankAcctLogoList.stream().map(m -> {
                TradeRecordDetailInfoVO.FundBankCardVO bankInfoVO = new TradeRecordDetailInfoVO.FundBankCardVO();
                bankInfoVO.setBankName(StringUtils.isBlank(m.getBankChineseName()) ? m.getBankName() : m.getBankChineseName());
                bankInfoVO.setBankLogoUrl(m.getBankLogoUrl());
                bankInfoVO.setCpAcctNo(m.getHkCpAcctNo());
                bankInfoVO.setBankAcctMask(m.getBankAcctMask());
                return bankInfoVO;
            }).collect(Collectors.toList());
            tradeRecordDetailInfoVO.setBankCardList(bankInfoVOS);
        }
    }


    /**
     * @param t
     * @param volPreview
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.hktraderecord.OrderNumAndUnitDTO
     * @description: 构建交易列表金额和单位信息
     * @author: jinqing.rao
     * @date: 2025/5/27 19:55
     * @since JDK 1.8
     */
    private OrderNumAndUnitDTO buildOrderNumAndUnitDTO(TradeRecordInfoDTO t, Integer volPreview) {
        OrderNumAndUnitDTO orderNumAndUnitDTO = new OrderNumAndUnitDTO();
        orderNumAndUnitDTO.setTradeType(t.getTradeType());
        orderNumAndUnitDTO.setAppAmt(t.getAppAmount());
        orderNumAndUnitDTO.setCurrency(t.getCurrency());
        orderNumAndUnitDTO.setRedeemType(t.getRedeemType());
        orderNumAndUnitDTO.setAppVol(t.getAppVol());
        orderNumAndUnitDTO.setUnit(t.getUnit());
        orderNumAndUnitDTO.setNum(t.getNum());
        orderNumAndUnitDTO.setFundDivMode(t.getFundDivMode());
        orderNumAndUnitDTO.setAckAmt(t.getAckAmt());
        orderNumAndUnitDTO.setAckVol(t.getAckVol());
        orderNumAndUnitDTO.setVolPrecision(volPreview);
        orderNumAndUnitDTO.setNetAppAmt(t.getNetAppAmt());
        orderNumAndUnitDTO.setMiddleBusiCode(t.getMiddleBusiCode());
        return orderNumAndUnitDTO;
    }

    private static OrderNumAndUnitDTO buildOrderNumAndUnitDTO(TradeRecordDetailInfoDTO t, FundBasicInfoDTO fundBasicInfoDTO) {
        OrderNumAndUnitDTO orderNumAndUnitDTO = new OrderNumAndUnitDTO();
        orderNumAndUnitDTO.setTradeType(t.getTradeType());
        orderNumAndUnitDTO.setAppAmt(t.getAppAmt());
        orderNumAndUnitDTO.setCurrency(t.getCurrency());
        orderNumAndUnitDTO.setRedeemType(t.getRedeemType());
        orderNumAndUnitDTO.setAppVol(t.getAppVol());
        orderNumAndUnitDTO.setUnit(t.getUnit());
        orderNumAndUnitDTO.setNum(t.getNum());
        orderNumAndUnitDTO.setFundDivMode(t.getFundDivMode());
        orderNumAndUnitDTO.setAckAmt(t.getAckAmt());
        orderNumAndUnitDTO.setAckVol(t.getAckVol());
        orderNumAndUnitDTO.setVolPrecision(fundBasicInfoDTO.getVolPrecision());
        orderNumAndUnitDTO.setNetAppAmt(t.getNetAppAmt());
        orderNumAndUnitDTO.setMiddleBusiCode(t.getMiddleBusiCode());
        return orderNumAndUnitDTO;
    }

    /**
     * @param tradeRecordMultiDetialInfoDTOList
     * @return java.lang.String
     * @description:(获取明细里面的费用汇总)
     * @author: xufanchao
     * @date: 2025/1/9 14:22
     * @since JDK 1.8
     */
    private BigDecimal getFee(List<TradeRecordMultiDetialInfoDTO> tradeRecordMultiDetialInfoDTOList) {
        BigDecimal fee = new BigDecimal(0);
        for (TradeRecordMultiDetialInfoDTO t : tradeRecordMultiDetialInfoDTOList) {
            if (null != t.getRedeemFee()) {
                fee = fee.add(t.getRedeemFee());
            }
        }
        if (fee.compareTo(BigDecimal.ZERO) > 0) {
            return fee;
        } else {
            return null;
        }
    }

    private List<TradeRecordMultiDetialInfoVO> buildMltiDetailInfoVoList(List<TradeRecordMultiDetialInfoDTO> tradeRecordMultiDetialInfoDTOList, FundBasicInfoDTO fundBasicInfoDTO) {
        List<TradeRecordMultiDetialInfoVO> tradeRecordMultiDetialInfoVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tradeRecordMultiDetialInfoDTOList)) {
            for (TradeRecordMultiDetialInfoDTO t : tradeRecordMultiDetialInfoDTOList) {
                TradeRecordMultiDetialInfoVO tradeRecordMultiDetialInfoVO = new TradeRecordMultiDetialInfoVO();
                tradeRecordMultiDetialInfoVO.setSeriesNo(t.getSeriesNo());
                tradeRecordMultiDetialInfoVO.setRegDate(DateUtils.formatDateStr(t.getRegDate()));
                tradeRecordMultiDetialInfoVO.setTradeDate(DateUtils.formatDateStr(t.getTradeDate()));
                tradeRecordMultiDetialInfoVO.setConfirmDate(DateUtils.formatDateStr(t.getConfirmDate()));
                tradeRecordMultiDetialInfoVO.setConfirmAmt(defaultShowFormat(NumberUtils.formatToThousandths(t.getConfirmAmt(), 2, RoundingMode.DOWN)));
                tradeRecordMultiDetialInfoVO.setConfirmVol(defaultShowFormat(NumberUtils.formatToThousandths(t.getConfirmVol(), fundBasicInfoDTO.getVolPrecision(), RoundingMode.DOWN)));
                tradeRecordMultiDetialInfoVO.setRedeemFee(defaultShowFormat(NumberUtils.formatToThousandths(t.getRedeemFee(), 2, RoundingMode.DOWN)));
                Integer jzws = null == fundBasicInfoDTO.getJzws() ? Integer.valueOf("4") : fundBasicInfoDTO.getJzws();
                tradeRecordMultiDetialInfoVO.setConfirmNav(defaultShowFormat(NumberUtils.formatToThousandths(t.getConfirmNav(), jzws, RoundingMode.DOWN)));
                tradeRecordMultiDetialInfoVO.setCurrency(t.getCurrency());
                tradeRecordMultiDetialInfoVO.setCurrencyDesc(CurrencyEnum.getDescription(t.getCurrency()));
                tradeRecordMultiDetialInfoVOList.add(tradeRecordMultiDetialInfoVO);
            }
        }
        return tradeRecordMultiDetialInfoVOList;
    }

    private void initBankInfo(TradeRecordDetailInfoDTO t, TradeRecordDetailInfoVO tradeRecordDetailInfoVO) {
        // 没值直接返回，前端需要展示默认样式
        if (StringUtils.isBlank(t.getBankAcctMask())) {
            return;
        }
        List<BankCardVO> bankCardVOS = new ArrayList<>();
        BankCardVO bankCardVO = new BankCardVO();
        bankCardVO.setBankName(StringUtils.isBlank(t.getBankChineseName()) ? t.getBankEnName() : t.getBankChineseName());
        // 历史原因 资金账号需要转掩码
        bankCardVO.setBankAcctNo(t.getBankAcctMask());
        bankCardVOS.add(bankCardVO);
        tradeRecordDetailInfoVO.setMutiCardList(bankCardVOS);
    }

    private void setPaymentVoucherStatus(String payVoucherStatus, boolean supportUpload, TradeRecordDetailInfoVO tradeRecordDetailInfoVO) {
        tradeRecordDetailInfoVO.setSupportUpload(supportUpload ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        // 默认未上传
        tradeRecordDetailInfoVO.setUploadStatus(HKFundPayVoucherStateEnum.NOT_UPLOAD.getCode());
        if (YesNoEnum.YES.getCode().equals(tradeRecordDetailInfoVO.getSupportUpload())) {
            tradeRecordDetailInfoVO.setUploadStatus(StringUtils.isBlank(payVoucherStatus) ? HKFundPayVoucherStateEnum.NOT_UPLOAD.getCode() : payVoucherStatus);
        }
    }

    /**
     * @param t
     * @param tradeRecordDetailInfoVO
     * @return void
     * @description: 设置支付类型
     * @author: jinqing.rao
     * @date: 2025/2/18 9:34
     * @since JDK 1.8
     */
    private void setPaymentType(TradeRecordDetailInfoDTO t, TradeRecordDetailInfoVO tradeRecordDetailInfoVO) {
        String paymentDescString = HkFundPayMethodEnum.getPaymentEnumListByValue(t.getPaymentType())
                .stream()
                .map(HkFundPayMethodEnum::getDesc)
                .collect(Collectors.joining(","));
        tradeRecordDetailInfoVO.setPaymentType(paymentDescString);
    }

    /**
     * @param t
     * @param tradeRecordDetailInfoVO
     * @return void
     * @description: 设置赎回方式
     * @author: jinqing.rao
     * @date: 2025/2/18 9:35
     * @since JDK 1.8
     */
    private void setRedeemDirection(TradeRecordDetailInfoDTO t, TradeRecordDetailInfoVO tradeRecordDetailInfoVO) {
        String redeemDescs = HKRedeemDirectionEnum.getRedeemDirectionEnumByValue(t.getRedeemDirection())
                .stream()
                .map(HKRedeemDirectionEnum::getDisPlay)
                .collect(Collectors.joining(","));

        String key = HKRedeemDirectionEnum.getRedeemDirectionEnumByValue(t.getRedeemDirection())
                .stream()
                .map(HKRedeemDirectionEnum::getKey)
                .collect(Collectors.joining(","));
        tradeRecordDetailInfoVO.setRedeemDirection(redeemDescs);
        tradeRecordDetailInfoVO.setRedeemDirectionValue(key);
        tradeRecordDetailInfoVO.setCpAcctNo(t.getCpAcctNo());
        boolean applyOrderStatus = HKFundOrderStatusEnum.APPLY_SUCCESS.getCode().equals(t.getOrderStatus());
        tradeRecordDetailInfoVO.setSupportRepeal(applyOrderStatus ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
    }

    /**
     * @param t
     * @param tradeRecordDetailInfoVO
     * @return void
     * @description: 资金募集状态 0--隐藏 1--展示
     * @author: jinqing.rao
     * @date: 2025/2/18 9:35
     * @since JDK 1.8
     */
    private void setFundRaisingStatus(TradeRecordDetailInfoDTO t, TradeRecordDetailInfoVO tradeRecordDetailInfoVO) {
        HKFundTradeStatusEnum statusEnumByOrder = getHKFundOrderStatusEnumByOrder(t.getOrderStatus(), t.getPayStatus());
        Boolean checkResult = HkFundPayMethodEnum.checkPayMethodForAssign(t.getPaymentType(), HkFundPayMethodEnum.OVERSEASPIGGY);
        if (!checkResult && null != statusEnumByOrder && HKFundTradeStatusEnum.PAYING.getCode().equals(statusEnumByOrder.getCode())) {
            tradeRecordDetailInfoVO.setFundRaisingStatus(YesNoEnum.YES.getCode());
        } else {
            tradeRecordDetailInfoVO.setFundRaisingStatus(YesNoEnum.NO.getCode());
        }
    }

    private String defaultShowFormat(String value) {
        if (StringUtils.isBlank(value)) {
            return "--";
        }
        return value;
    }

    /**
     * @param mutiCardList
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.dtmsorder.BankCardVO>
     * @description:(获取银行逆袭)
     * @author: xufanchao
     * @date: 2024/3/8 10:48
     * @since JDK 1.8
     */
    private List<BankCardVO> getMutiCardList(List<BankCardDTO> mutiCardList) {
        List<BankCardVO> bankCardVOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(mutiCardList)) {
            return bankCardVOS;
        }
        mutiCardList.forEach(it -> {
            BankCardVO bankCardVO = new BankCardVO();
            bankCardVO.setBankName(it.getBankName());
            bankCardVO.setBankAcctNo(it.getBankAcctNo());
            bankCardVOS.add(bankCardVO);
        });
        return bankCardVOS;
    }

    /**
     * @param preBookId
     * @return java.lang.String
     * @description:(获取在中台的订单号)
     * @author: xufanchao
     * @date: 2024/3/6 17:28
     * @since JDK 1.8
     */
    @Deprecated
    public String getAppserialNo(String preBookId) {
        // 需求下架,CRM接口删除 接口返回空数据
        return null;
    }

    public TradeRecordInfoListVO queryTradeRecordListByRelationOrderList(RelationOrderListRequest request) {

        // 获取基金列表信息
        List<TradeRecordInfoDTO> tradeRecordInfoDTOS = hkTradeOuterService.queryHwFundRecordListByRelationOrderList(request.getHkCustNo(), request.getRelationOrderList());
        if (CollectionUtils.isEmpty(tradeRecordInfoDTOS)) {
            return new TradeRecordInfoListVO();
        }
        // 获取基金CodeList
        List<String> fundCodes = tradeRecordInfoDTOS.stream().map(TradeRecordInfoDTO::getFundCode).collect(Collectors.toList());
        // 获取基金编码和volPrecision的Map, 用于获取基金产品金额/份额的 小数位的精度处理
        Map<String, Integer> volPreviewMap = fundBasicInfoOuterService.queryFundVolPreviewMap(fundCodes);
        return transToTradeRecordInfoListVO(tradeRecordInfoDTOS, volPreviewMap);
    }

    /**
     * @description: 修改订单的回款方向方式
     * @param request
     * @return void
     * @author: jinqing.rao
     * @date: 2025/7/2 16:44
     * @since JDK 1.8
     */
    public void changeRedeemDirection(ChangeRedeemDirectionRequest request) {
        // 验证交易密码
        hkCustInfoOuterService.validateHkTradePassword(request.getHkCustNo(), request.getTxPassword());
        hkFundOuterService.changeRedeemDirection(request.getHkCustNo(),
                request.getDealNo(),
                request.getRedeemDirection(),
                request.getCpAcctNo()
        );
    }

    /**
     * @description: 查询待回款订单
     * @param request	请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TradeRecordInfoListVO
     * @author: jinqing.rao
     * @date: 2025/7/14 20:39
     * @since JDK 1.8
     */
    public TradeRecordInfoListVO queryWaitRefundOrderList(QueryWaitRefundListRequest request) {
        // 查询待回款订单
        QueryInTransitTradeCapitalRequestDTO requestDTO = QueryInTransitTradeCapitalRequestDTO.builder().hkCustNo(request.getHkCustNo()).build();
        List<TradeRecordInfoDTO> tradeRecordInfoDTOS = queryInTransitTradeCapitalOuterService.queryInTransitTradeCapitalOrderList(requestDTO);
        // 获取基金CodeList
        List<String> fundCodes = tradeRecordInfoDTOS.stream().map(TradeRecordInfoDTO::getFundCode).collect(Collectors.toList());
        // 获取基金编码和volPrecision的Map, 用于获取基金产品金额/份额的 小数位的精度处理
        Map<String, Integer> volPreviewMap = fundBasicInfoOuterService.queryFundVolPreviewMap(fundCodes);
        return transToTradeRecordInfoListVO(tradeRecordInfoDTOS, volPreviewMap);

    }
}