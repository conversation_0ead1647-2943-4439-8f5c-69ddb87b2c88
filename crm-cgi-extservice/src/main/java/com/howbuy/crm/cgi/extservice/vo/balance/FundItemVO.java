package com.howbuy.crm.cgi.extservice.vo.balance;

/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 产品详细信息实体类
 * @author: 陈杰文
 * @date: 2025-06-17 14:34:43
 * @since JDK 1.8
 */
@Setter
@Getter
public class FundItemVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 参考市值
     */
    private String currencyMarketValue;

    /**
     * 参考净值
     */
    private String nav;

    /**
     * 净值日期
     */
    private String navDate;

    /**
     * 持仓份额
     */
    private String totalBalanceVol;

    /**
     * 当前收益（当前币种）
     */
    private String currentAssetCurrency;

    /**
     * 费后当前收益（当前币种）
     */
    private String currentAssetCurrencyExFee;

    /**
     * 币种描述
     */
    private String currencyDesc;

    /**
     * 当前收益率
     */
    private String yieldRate;

    /**
     * 当前收益率(不含费)
     */
    private String yieldRateExFee;

    /**
     * 分红标志
     */
    private String navDivFlag;

    /**
     * 费后持仓市值
     */
    private String currencyMarketValueExFee;

    /**
     * 累计应收管理费
     */
    private String receivManageFee;

    /**
     * 累计应收业绩报酬
     */
    private String receivPreformFee;

    /**
     * 净购买金额(投资成本)(当前币种)
     */
    private String currencyNetBuyAmount;

    /**
     * 费后累计总回款(当前币种)
     */
    private String currencyTotalCollectionExFee;

    /**
     * 平衡因子
     */
    private String balanceFactor;

    /**
     * 收益计算状态
     */
    private String incomeCalStat;

    /**
     * 清算标识
     */
    private String crisisFlag;

    /**
     * 平衡因子转换完成
     */
    private String convertFinish;

    /**
     * 平衡因子日期
     */
    private String balanceFactorDate;
} 