/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.material;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.asset.client.domain.assetreport.AssetPdfReportFileDTO;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.extservice.convert.portrait.PortraitMaterialConvert;
import com.howbuy.crm.cgi.extservice.request.portrait.*;
import com.howbuy.crm.cgi.extservice.vo.portrait.material.*;
import com.howbuy.crm.cgi.manager.domain.crmcore.ConsCustBaseInfoDTO;
import com.howbuy.crm.cgi.manager.domain.portrait.material.AddMetarialSendRecordDTO;
import com.howbuy.crm.cgi.manager.outerservice.asset.assetreport.assetpdf.AssetPdfReportOuterService;
import com.howbuy.crm.cgi.manager.outerservice.cms.CmsProductReportOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmcore.QueryConscustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmwechat.WechatCommonOuterService;
import com.howbuy.crm.cgi.manager.outerservice.portrait.material.PortraitMaterialOuterService;
import com.howbuy.crm.cgi.manager.outerservice.portrait.material.QueryMaterialDirectoryOuterService;
import com.howbuy.crm.cgi.manager.outerservice.portrait.material.QueryMaterialLibraryRecommendListOuterService;
import com.howbuy.crm.portrait.client.domain.dto.material.MaterialSendRecordDTO;
import com.howbuy.crm.portrait.client.domain.response.material.QueryMaterialLibraryRecommendListResponse;
import com.howbuy.crm.portrait.client.enums.MaterialSendTypeEnum;
import com.howbuy.crm.wechat.client.enums.WechatAppEnum;
import com.howbuy.crm.wechat.client.enums.WechatUploadTypeEnum;
import com.howbuy.member.dto.userportrait.report.CompanyReportDTO;
import crm.howbuy.base.utils.Base64Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 客户画像素材公共服务
 * @Date 2024/9/6 15:11
 */
@Slf4j
@Service("portraitMaterialService")
public class PortraitMaterialService {

    @Resource
    private PortraitMaterialOuterService portraitMaterialOuterService;

    @Resource
    private AssetPdfReportOuterService assetPdfReportOuterService;

    @Resource
    private QueryConscustInfoOuterService queryConscustInfoOuterService;

    @Resource
    private CmsProductReportOuterService cmsProductReportOuterService;

    @Resource
    private WechatCommonOuterService wechatCommonOuterService;

    @Resource
    private QueryMaterialLibraryRecommendListOuterService queryMaterialLibraryRecommendListOuterService;

    @Resource
    private QueryMaterialDirectoryOuterService queryMaterialDirectoryOuterService;

    /**
     * 根据基金搜索客户画像报告
     *
     * @param request req
     * @return PortraitMetarialReportVO
     */
    public PortraitMetarialReportVO searchReport(PortraitMetarialSearchReportRequest request) {
        PortraitMetarialReportVO vo = new PortraitMetarialReportVO();
        vo.setDataList(new ArrayList<>());
        List<CompanyReportDTO> companyReportDTOS = portraitMaterialOuterService.searchReport(request.getFundCode());
        if (CollectionUtils.isEmpty(companyReportDTOS)) {
            return vo;
        }

        String materialType =MaterialSendTypeEnum.POSITION_REPORT.getCode();
        //注意：持仓报告Id
        List<String> idList = companyReportDTOS.stream()
                .map(CompanyReportDTO::getId)
                .map(String::valueOf)
                .distinct()
                .collect(Collectors.toList());
        // 查询素材内容信息获取发送次数
        List<MaterialSendRecordDTO> materialList = portraitMaterialOuterService.getMaterialList(request.getHboneNo(), request.getConscode(), materialType,idList);
        //NOTICE : 注意： 此处使用 持仓报告Id 作为key，
        Map<String, Integer> sendNumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(materialList)) {
            sendNumMap = materialList.stream().collect(Collectors.toMap(MaterialSendRecordDTO::getMaterialId, MaterialSendRecordDTO::getSendNum, (v1, v2) -> v1));
        }

        for (CompanyReportDTO reportDTO : companyReportDTOS) {
            PortraitMetarialReportVO.MetarialReport metarialReport = new PortraitMetarialReportVO.MetarialReport();
            metarialReport.setMaterialId(String.valueOf(reportDTO.getId()));
            metarialReport.setMaterialSendType(MaterialSendTypeEnum.POSITION_REPORT.getCode());
            metarialReport.setRedirectUrl(reportDTO.getRedirectUrl());
            metarialReport.setReportDate(reportDTO.getReportDate());
            metarialReport.setReportType(reportDTO.getReportType());
            metarialReport.setReportTypeName(reportDTO.getReportTypeName());
            metarialReport.setTitle(reportDTO.getTitle());
            metarialReport.setReportSource(reportDTO.getReportSource());
            Integer sendNum = sendNumMap.getOrDefault(String.valueOf(reportDTO.getId()), 0);
            metarialReport.setSendNum(String.valueOf(sendNum));
            vo.getDataList().add(metarialReport);
        }
        vo.setTotal(String.valueOf(companyReportDTOS.size()));
        return vo;
    }


    /**
     * 新增素材发送记录
     *
     * @param request req
     * @return AddMetarialSendRecordVO
     */
    public AddMetarialSendRecordVO addSendRecord(AddMetarialSendRecordRequest request) {
        AddMetarialSendRecordVO vo = new AddMetarialSendRecordVO();
        AddMetarialSendRecordDTO addDto = new AddMetarialSendRecordDTO();
        addDto.setHboneNo(request.getHboneNo());
        addDto.setConscode(request.getConscode());
        addDto.setContentId(request.getContentId());
        addDto.setMaterialId(request.getMaterialId());
        addDto.setMaterialType(request.getMaterialType());
        String result = portraitMaterialOuterService.addSendRecord(addDto);
        vo.setResult(result);
        return vo;
    }

    /**
     * 查询资配报告文件内容信息
     *
     * @param request req
     * @return PortraitMetarialAssetInfoVO
     */
    public PortraitMetarialAssetInfoVO getAssetPdfInfo(PortraitMetarialAssetInfoRequest request) {
        PortraitMetarialAssetInfoVO vo = new PortraitMetarialAssetInfoVO();
        ConsCustBaseInfoDTO conscustInfo = queryConscustInfoOuterService.getConscustInfoByHboneNo(request.getHboneNo());
        if (null == conscustInfo) {
            log.error("根据一账通号查询客户信息为空！ 一账通号={}", request.getHboneNo());
            return vo;
        }

        AssetPdfReportFileDTO reportFileDTO = assetPdfReportOuterService.queryAssetPdfReportFile(conscustInfo.getConscustno(), request.getAssetId());
        if (null == reportFileDTO) {
            log.error("根据客户号和报告ID查询报告文件为空！ 客户号={}, 报告ID={}", conscustInfo.getConscustno(), request.getAssetId());
            return vo;
        }

        vo.setId(reportFileDTO.getId());
        vo.setFileName(reportFileDTO.getFileName());
        vo.setFilebase64(Base64Utils.encode(reportFileDTO.getFileBytes()));
        return vo;
    }

    /**
     * 上传报告到企微
     *
     * @param request req
     * @return PortraitMetarialAssetInfoVO
     */
    public SendMetarialVO uploadReport2Wechat(SendMetarialRequest request) {
        SendMetarialVO vo = new SendMetarialVO();

        ConsCustBaseInfoDTO conscustInfo = queryConscustInfoOuterService.getConscustInfoByHboneNo(request.getHboneNo());
        if (null == conscustInfo) {
            log.error("根据一账通号查询客户信息为空！ 一账通号={}", request.getHboneNo());
            return vo;
        }

        byte[] fileBytes = null;
        if (Constants.PORTRAIT_UPLOAD_REPORT_TYPE_ASSET.equals(request.getReportType())) {
            // 资配报告上传
            AssetPdfReportFileDTO reportFileDTO = assetPdfReportOuterService.queryAssetPdfReportFile(conscustInfo.getConscustno(), request.getAssetId());
            if (null == reportFileDTO) {
                log.error("根据客户号和报告ID查询报告文件为空！ 客户号={}, 报告ID={}", conscustInfo.getConscustno(), request.getAssetId());
                return vo;
            }

            fileBytes = reportFileDTO.getFileBytes();
            log.info("上传资配报告文件结果! 文件名={}, 文件大小={}", reportFileDTO.getFileName(), fileBytes.length);
        } else if (Constants.PORTRAIT_UPLOAD_REPORT_TYPE_PRODUCT.equals(request.getReportType())) {
            // 产品报告上传
            String reportUrl = request.getReportUrl();
            fileBytes = cmsProductReportOuterService.getReportBytesByReportUrl(reportUrl);
            log.info("上传产品报告文件结果! 文件URL={}, 文件大小={}", reportUrl, fileBytes.length);

        }

        if (fileBytes == null) {
            log.error("文件为空！ 一账通号={}, 文件名={}", request.getHboneNo(), request.getName());
            return vo;
        }

        String mediaId = wechatCommonOuterService.uploadMediaFile(fileBytes, request.getName(),
                WechatUploadTypeEnum.FILE.getCode(), WechatAppEnum.WEALTH_PORTRAIT.getKey());
        log.info("上传文件到企微结果! 一账通号={}, 文件名={}, 文件ID={}", request.getHboneNo(), request.getName(), mediaId);
        vo.setMediaId(mediaId);
        return vo;
    }

    /**
     * 获取素材库推荐列表
     * @description 获取素材库推荐列表信息
     * @param request 素材库推荐列表请求对象
     * @return PortraitMaterialPoolVO 素材库推荐列表响应对象
     * <AUTHOR>
     * @date 2024-03-19 15:30:00
     */
    public PortraitMaterialPoolVO getMaterialPool(PortraitMaterialPoolRequest request) {
        log.info("获取素材库推荐列表-请求参数：{}", JSON.toJSONString(request));

        // 调用外部服务获取素材库推荐列表
        QueryMaterialLibraryRecommendListResponse response = queryMaterialLibraryRecommendListOuterService.queryMaterialLibraryRecommendList(
                request.getConscode(),
                request.getHboneNo(),
                request.getTab(),
                request.getPage(),
                request.getSize()
        );

        log.info("获取素材库推荐列表-响应结果：{}", JSON.toJSONString(response));

        // 使用转换类构建响应对象
        return PortraitMaterialConvert.convertToVO(response);
    }

}
