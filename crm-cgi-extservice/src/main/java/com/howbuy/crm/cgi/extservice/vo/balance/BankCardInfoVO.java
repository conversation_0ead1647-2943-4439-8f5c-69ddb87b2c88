package com.howbuy.crm.cgi.extservice.vo.balance;

/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 银行卡信息实体类
 * @author: 陈杰文
 * @date: 2025-06-17 17:46:35
 * @since JDK 1.8
 */
@Setter
@Getter
public class BankCardInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 香港资金账号
     */
    private String hkCpAcctNo;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 银行名称（银行名称优先级为：取到银行简称，否则取银行全称）
     */
    private String bankName;

    /**
     * 银行号
     */
    private String bankRegionCode;

    /**
     * 银行账户名称
     */
    private String bankAcctName;

    /**
     * 摘要-银行账号
     */
    private String bankAcctDigest;

    /**
     * 掩码-银行账号
     */
    private String bankAcctMask;
} 