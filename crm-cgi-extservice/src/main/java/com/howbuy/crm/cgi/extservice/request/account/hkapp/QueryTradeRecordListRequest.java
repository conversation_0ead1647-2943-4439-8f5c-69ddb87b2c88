/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account.hkapp;

import com.howbuy.crm.cgi.common.base.PageRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (查询交易记录的列表请求参数)
 * <AUTHOR>
 * @date 2024/2/22 18:46
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryTradeRecordListRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = 4259399264616137701L;
    /**
     * 海外中台数据源 在途订单状态传 1 申请成功
     */
    private String tradeStatus;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金类别列表
     * 1-公募、2-私募 3. 储蓄罐 9-其他(这个搜索添加在迭代 3.0中移除了)
     */
    private List<String> fundCategoryList;

    /**
     * 业务类型列表
     * 1-买入、2-卖出、9-其他
     */
    private List<String> busiTypeList;

    /**
     * 交易状态列表
     * 1-付款中、2-确认中、3-交易成功、4-交易失败、5-已撤单、9-其他
     */
    private List<String> tradeStatusList;

    /**
     * 持仓状态
     * 1-持有、2-已清仓
     */
    private List<String> holdStatus;

    /**
     * 时间范围枚举
     * 1-近一个月、2-近半年、3-近一年
     */
    private String timePeriod;

    /**
     * 订单开始时间
     * 格式：YYYYMMdd
     */
    private String orderStartDt;

    /**
     * 订单结束时间
     * 格式：YYYYMMdd
     */
    private String orderEndDt;

    /**
     * 基金交易账号
     */
    private String fundTxCode;

    /**
     * 包含储蓄罐 1 是 0 否
     */
    private String includePiggy;
}