package com.howbuy.crm.cgi.extservice.vo.balance;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 查询全委基金资产响应类
 * @author: 陈杰文
 * @date: 2025-06-17 15:00:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFullFundAssetVO extends Body implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否显示资产 [资产小眼睛] 0:显示, 1: 隐藏
     */
    private String showAsset;

    /**
     * 全委总资产
     */
    private String fullFundAsset;

    /**
     * 全委基金持仓收益
     */
    private String fullFundIncome;

    /**
     * 全委基金持仓持仓总资产
     */
    private String fullHoldFundAsset;

    /**
     * 存在全委基金阳光私募产品 0-不存在、1-存在
     */
    private String existsFundEquityFund;

    /**
     * 全委基金持仓收益计算状态 0-计算中;1-计算成功
     */
    private String fullFundIncomeCalStatus;

    /**
     * 存在全委基金股权基金 0-不存在、1-存在
     */
    private String existsFullFundEquityFund;

    /**
     * 全委基金股权总回款
     */
    private String fullFundTotalEquityRecovery;

    /**
     * 全委基金在途交易金额
     */
    private String fullFundInTransitTradeAmt;

    /**
     * 全委基金在途交易笔数
     */
    private String fullFundInTransitTradeCount;

    /**
     * 全委基金在途交易订单号 在途交易笔数 == 1 时的交易单号
     */
    private String fullFundInTransitTradeDealNo;

    /**
     * 全委基金在途交易资金笔数
     */
    private String fullFundInTransitTradeCapitalCount;

    /**
     * 全委基金在途交易资金订单号 在途交易资金笔数 == 1 时的交易单号
     */
    private String fullFundInTransitTradeCapitalDealNo;

    /**
     * 全委现金余额总资产
     */
    private String fullCashBalanceAsset;

    /**
     * 全委可用余额
     */
    private String fullAvailableBalance;

    /**
     * 全委冻结余额
     */
    private String fullFreezeBalance;

    /**
     * 基金持仓详情列表
     */
    private List<AssetFundDetailVO> assetFundDetailList;

    /**
     * 全委基金交易账号列表
     */
    private List<FundTxAcctVO> fundTxAcctList;
} 