/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ContractFileVO;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggySignContractRecordVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 合同信息新版本VO
 * @author: system
 * @date: 2025/7/3
 * @since JDK 1.8
 */
@Setter
@Getter
public class ContractInfoNewVO extends AccountBaseVO implements Serializable {


    private static final long serialVersionUID = -5489632920623883818L;

    /**
     * 开户文件
     */
    private OpenContractFileVO openContractFileVO;
    /**
     * 产品合同文件列表
     */
    private List<ContractFileVO> contractFileList;

    /**
     * 储蓄罐开通文件列表
     */
    private List<PiggySignContractInfo> openCustPiggyFileList;

    /**
     * 储蓄罐更换文件列表
     */
    private List<PiggySignContractInfo> changeCustPiggyFileList;


    @Setter
    @Getter
    public static class OpenContractFileVO{
        /**
         * 开户文件列表
         */
        private List<OpenFileInfo> openFileList;

        /**
         * 签约时间
         */
        private String signDate;

        /**
         * 类型名称
         */
        private String typeName;

        /**
         * 邮箱掩码
         */
        private String emailMask;
    }

    @Setter
    @Getter
    public static class PiggySignContractInfo implements Serializable {


        private static final long serialVersionUID = -1199456305046136680L;
        /**
         * 海外储蓄罐签约文件
         */
        private List<PiggySignContractRecord> contractList;

        /**
         * 签约时间
         */
        private String signDate;

        /**
         * 类型名称
         */
        private String typeName;

    }


    @Setter
    @Getter
    public static class PiggySignContractRecord implements Serializable {
        private static final long serialVersionUID = -5989725841833977763L;

        /**
         * 文件url
         */
        private String url;

        /**
         * 文件名称
         */
        private String fileName;

        /**
         * 文件类型
         */
        private String fileType;

        /**
         * 文件地址访问类型类型 (1: 静态文件 2: 文件流下载)
         */
        private String fileUrlType;

    }

    @Setter
    @Getter
    public static class OpenFileInfo implements Serializable{

        private static final long serialVersionUID = -2973926382849287675L;
        /**
         * 文件名称
         */
        private String filaName;

        /**
         * 文件URL
         */
        private String fileUrl;

        /**
         * 文件类型
         */
        private String fileType;

        /**
         * 文件类型名称
         */
        private String fileTypeName;

        /**
         * 文件地址类型,1 ： 静态地址链接,可以直接访问  2：相对路径需要通过文件下载接口，获取文件流下载
         */
        private String fileUrlType;

        /**
         * 文件上传时间
         */
        private String upDt;

    }
}
