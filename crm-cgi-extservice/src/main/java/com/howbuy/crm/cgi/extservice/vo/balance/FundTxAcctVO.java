package com.howbuy.crm.cgi.extservice.vo.balance;

/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 基金交易账号信息实体类
 * @author: 陈杰文
 * @date: 2025-06-17 15:00:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class FundTxAcctVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全委基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 全委基金交易账号名称
     */
    private String fundTxAcctName;
} 