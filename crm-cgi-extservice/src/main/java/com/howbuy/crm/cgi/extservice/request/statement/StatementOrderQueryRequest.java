package com.howbuy.crm.cgi.extservice.request.statement;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: 日结订单查询请求类
 * @author: 陈杰文
 * @date: 2025-06-17 15:15:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class StatementOrderQueryRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单类型 1-日结单、2-月结单、3-全委-日结单、4-全委-月结单
     */
    @NotBlank(message = "订单类型不能为空")
    private String statementType;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 当前页
     */
    @NotBlank(message = "当前页不能为空")
    private String page;

    /**
     * 每页条数
     */
    @NotBlank(message = "每页条数不能为空")
    private String size;
} 