package com.howbuy.crm.cgi.extservice.vo.statement;

/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 结单文件信息实体类
 * @author: 陈杰文
 * @date: 2025-06-17 15:15:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class StatementFileVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件名称(好买香港${结单类型}-${结单时间}，日期格式：YYYYMMDD，举例“好买香港日结单-20250428”)
     */
    private String fileName;

    /**
     * 文件预览地址
     */
    private String previewUrl;

    /**
     * 文件业务类型
     */
    private String fileBizType;
} 