package com.howbuy.crm.cgi.extservice.factory.balance;

import com.howbuy.crm.cgi.extservice.common.annotation.FundTypeSupport;
import com.howbuy.crm.cgi.extservice.common.enums.balance.HkCustBalanceDisPalyCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.utils.balance.BalanceUtils;
import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.BalancePortfolioDetail;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.AcctBalanceDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.BalanceBeanDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryBalanceDTO;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@FundTypeSupport(HkCustBalanceDisPalyCategoryEnum.NON_FULL_FUND_BALANCE)
public class NonFullPortfolioStrategy extends AbstractBalancePortfolioStrategy implements BalancePortfolioStrategy<BalancePortfolioDetail> {

    @Override
    public BalancePortfolioDetail calculate(BalanceContent content) {
        BalancePortfolioDetail.BalancePortfolioDetailBuilder builder = BalancePortfolioDetail.builder();
        if (CollectionUtils.isEmpty(content.getFundTxAcctNoDTOList())) {
            log.info("没有非全委的基金交易账号,非全委的基金策略不执行，返回 空数据 , hkCustNo :{} ", content.getHkCustNo());
            return builder.build();
        }
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = content.getFundTxAcctNoDTOList().stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.NON_FULL.getCode().equals(m.getFundTxAccType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hkFundTxAcctDTOS)) {
            log.error("用户有基金交易账号,没有非全委的基金交易账号,非全委的基金策略不执行，返回 空数据 , hkCustNo :{} ", content.getHkCustNo());
            return builder.build();
        }

        List<String> nonFullFundTxAcctNoList = BalanceUtils.extractFieldList(hkFundTxAcctDTOS, HkFundTxAcctDTO::getFundTxAcctNo);

        // 获取海外储蓄罐基金列表信息
        List<FundBasicInfoDTO> fundBasicInfoDTOList = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList();
        List<String> fundCodes = CollectionUtils.isEmpty(fundBasicInfoDTOList) ? new ArrayList<>() :
                fundBasicInfoDTOList.stream()
                        .map(FundBasicInfoDTO::getFundCode)
                        .distinct()
                        .collect(Collectors.toList());

        QueryBalanceDTO queryBalanceDTO = new QueryBalanceDTO();
        queryBalanceDTO.setHkCustNo(content.getHkCustNo());
        // 排除海外储蓄罐产品
        queryBalanceDTO.setExcludeFundCodeList(fundCodes);
        queryBalanceDTO.setFundTxAcctNoList(nonFullFundTxAcctNoList);
        queryBalanceDTO.setDisPlayCurrency(content.getDisPlayCurrencyCode());

        AcctBalanceDTO acctBalanceDTO = queryBalanceOuterService.queryHkCustBalance(queryBalanceDTO);
        List<BalanceBeanDTO> balanceList = acctBalanceDTO.getBalanceList();

        // 计算总收益
        BigDecimal cxgTotalIncome = BalanceUtils.sumBigDecimal(balanceList, BalanceBeanDTO::getDisCurCurrentAssetCurrency);

        // 收益状态
        String incomeCalStat = BalanceUtils.calculateIncomeCalStat(balanceList);

        return builder
                .totalAsset(acctBalanceDTO.getDisPlayCurrencyTotalMarketValue())
                .totalIncome(cxgTotalIncome)
                .incomeStatus(incomeCalStat)
                .inTransitTradeAmt(acctBalanceDTO.getTotalUnconfirmedAmt())
                .totalCashCollection(acctBalanceDTO.getTotalCashCollection())
                .itemList(balanceList)
                .build();
    }

    @Override
    public Class<BalancePortfolioDetail> getSupportedType() {
        return BalancePortfolioDetail.class;
    }
}