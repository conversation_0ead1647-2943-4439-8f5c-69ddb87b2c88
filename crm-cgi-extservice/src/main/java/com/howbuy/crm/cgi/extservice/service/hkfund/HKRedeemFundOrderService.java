/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.hkfund;

import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.cgi.common.enums.*;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.CommonRequestUtil;
import com.howbuy.crm.cgi.common.utils.WebUtil;
import com.howbuy.crm.cgi.extservice.common.enums.InvstTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.FundBusiTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HKFundCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HKFundTradeModeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.common.utils.NumberUtils;
import com.howbuy.crm.cgi.extservice.convert.fund.HKRedeemFundConvert;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKRedeemFundBaseRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKRedeemFundOrderRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKRedeemFundSubmitRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.QueryFundCanSellRequest;
import com.howbuy.crm.cgi.extservice.validator.hkfund.BindBankCardValidator;
import com.howbuy.crm.cgi.extservice.validator.hkfund.HkCustStatusValidator;
import com.howbuy.crm.cgi.extservice.validator.hkfund.HkFundValidator;
import com.howbuy.crm.cgi.extservice.validator.hkfund.HkOpenAcctStatusValidator;
import com.howbuy.crm.cgi.extservice.vo.hkfund.*;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.CustContractSignDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.DigitalSignSignDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.HKPurchaseFundOrderInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.QueryBuyOrSellReqDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkRedeemFundSubmitInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.RedeemPageInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundCanTradeDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.response.fund.FundFeeRateResponseDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.response.fund.FundLimitResponseDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkBankCardInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.enums.TradeButtonShowEnum;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.CustContractSignOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.JudgeBuyOrSellOuterService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 海外基金赎回接口
 * @date 2024/5/9 16:57
 * @since JDK 1.8
 */
@Service
public class HKRedeemFundOrderService extends AbstractFundOrderService {

    @Resource
    private CustContractSignOuterService custContractSignOuterService;

    @Resource
    private JudgeBuyOrSellOuterService judgeBuyOrSellOuterService;

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKFundVerificationVO
     * @description: 海外基金赎回校验接口
     * @author: jinqing.rao
     * @date: 2024/5/9 16:59
     * @since JDK 1.8
     */
    public HKFundVerificationVO redeemFundVerification(HKRedeemFundBaseRequest request) {
        //获取基金产品基本信息
        FundBasicInfoDTO fundBasicInfoDTO = getFundBasicInfoDTO(request.getFundCode());
        //获取用户的账户信息
        HkCustInfoDTO hkCustInfo = getHkCustInfoDTO(request.getHkCustNo());
        //添加校验器
        List<HkFundValidator> validators = addHkFundValidators(hkCustInfo, fundBasicInfoDTO);
        //执行校验器
        HKFundVerificationVO verificationVO = verification(validators);
        // 结果不为空,返回具体的状态status,前端根据具体的状态跳转对应的页面
        if (null != verificationVO) {
            return verificationVO;
        }
        HKFundVerificationVO purchaseFundVerificationVO = new HKFundVerificationVO();
        purchaseFundVerificationVO.setBuyVerfiyState(HkFundVerificationStatusEnum.NORMAL.getCode());
        return purchaseFundVerificationVO;
    }

    @Override
    public List<HkFundValidator> addHkFundValidators(HkCustInfoDTO hkCustInfo, FundBasicInfoDTO fundBasicInfoDTO) {
        return new ArrayList<HkFundValidator>() {
            private static final long serialVersionUID = -5640545335979781797L;

            {
                //客户状态过滤器 以及交易是否激活
                add(new HkCustStatusValidator(hkCustInfo));
                //校验用户的开户信息
                add(new HkOpenAcctStatusValidator(hkCustInfo, hkCustInfoOuterService));
                //是否绑定银行卡
                add(new BindBankCardValidator(hkCustInfo, hkBankCardInfoOuterService));
            }
        };
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKRedeemFundPageInfoVO
     * @description: 赎回页面的信息查询接口
     * @author: jinqing.rao
     * @date: 2024/5/9 17:32
     * @since JDK 1.8
     */
    public HKRedeemFundPageInfoVO queryRedeemPageInfo(HKRedeemFundBaseRequest request) {
        //获取基金的基本信息
        FundBasicInfoDTO fundBasicInfoDTO = getFundBasicInfoDTO(request.getFundCode());
        //获取用户的客户信息
        HkCustInfoDTO hkCustInfo = getHkCustInfoDTO(request.getHkCustNo());

        //调用海外中台接口获取赎回页面信息,储蓄罐和普通基金接口做了隔离,主要考虑储蓄罐后续有其他的特定逻辑
        RedeemPageInfoDTO redeemPageInfoDTO = null;
        if (YesNoEnum.YES.getCode().equals(fundBasicInfoDTO.getIsPiggyFund())) {
            redeemPageInfoDTO = querySellInfoOuterService.queryFundSellPageInfo(request.getHkCustNo(), request.getFundCode());
        } else {
            redeemPageInfoDTO = hkFundOuterService.queryRedeemPageInfo(request.getHkCustNo(), request.getFundCode());
        }

        // 查询基金交易限额配置
        List<FundLimitResponseDTO> fundLimitDTO = getFundLimitDTO(request.getFundCode(), InvstTypeEnum.PERS.getKey(), FundBusiTypeEnum.BUSI_TYPE_024.getCode());
        // 获取基金交易费率配置
        List<FundFeeRateResponseDTO> fundFeeRateDTO = queryFundFeeRateInfo(request.getFundCode(), InvstTypeEnum.PERS.getKey(), FundBusiTypeEnum.BUSI_TYPE_024.getCode(), "2");
        //获取海外用户绑定的银行卡信息
        List<HkBankCardInfoDTO> hkBankAcctLogoList = getHkBankAcctLogoList(request.getHkCustNo());
        return HKRedeemFundConvert.toHkRedeemFundPageInfoVO(hkCustInfo, redeemPageInfoDTO, fundBasicInfoDTO, fundLimitDTO, fundFeeRateDTO, hkBankAcctLogoList);
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKRedeemFundOrderSubmitVO
     * @description: 赎回申请提交接口
     * @author: jinqing.rao
     * @date: 2024/5/10 14:33
     * @since JDK 1.8
     */
    public HKRedeemFundOrderSubmitVO redeemOrderSubmit(HKRedeemFundSubmitRequest request) {
        // 查询基金基础信息
        FundBasicInfoDTO fundBasicInfoDTO = getFundBasicInfoDTO(request.getFundCode());
        //手机验证码或者邮箱校验
        if (!(fundBasicInfoDTO != null && HKFundCategoryEnum.PUBLIC_FUND.getCode().equals(fundBasicInfoDTO.getFundCategory()))) {
            //手机验证码或者邮箱校验或者语音验证码校验
            validVerifyCodeByType(request.getVerifyCodeType(), request.getVerifyCode(), request.getHkCustNo(),
                    VerifyCodeTypeEnum.HK_FUND_REDEEM_VERIFY_CODE, VerifyCodeTypeEnum.HK_FUND_REDEEM_EMAIL_VERIFY_CODE, VerifyCodeTypeEnum.SELL_VOICE_CODE);

        }
        //校验交易密码,签署合同
        hkCustInfoOuterService.validateHkTradePassword(request.getHkCustNo(), request.getTxPassword());
        //签署合同,获取订单号
        DigitalSignSignDTO digitalSignSignDTO = HKRedeemFundConvert.toRedeemFundContractSignDTO(request.getHkCustNo(), request.getFundCode(),
                YesNoEnum.YES.getCode(), HKFundTradeModeEnum.HK_REDEEM_FUND.getCode(), request.getContractList());
        CustContractSignDTO responseDTO = custContractSignOuterService.fundSign(digitalSignSignDTO);
        if (null == responseDTO || StringUtils.isBlank(responseDTO.getDealNo())) {
            throw new BusinessException(ExceptionCodeEnum.HK_PURCHASE_FUND_SIGN_CONTRACT_FAIL);
        }
        //提交赎回的数据
        HkRedeemFundSubmitInfoDTO hkRedeemFundSubmitInfoDTO = HKRedeemFundConvert.toHkRedeemFundSubmitInfoDTO(request, responseDTO.getDealNo());
        if (YesNoEnum.YES.getCode().equals(fundBasicInfoDTO.getIsPiggyFund())) {
            hkFundOuterService.redeemPiggyOrderSubmit(hkRedeemFundSubmitInfoDTO);
        } else {
            hkFundOuterService.redeemOrderSubmit(hkRedeemFundSubmitInfoDTO);
        }

        //返回订单号
        HKRedeemFundOrderSubmitVO hkRedeemFundOrderSubmitVO = new HKRedeemFundOrderSubmitVO();
        hkRedeemFundOrderSubmitVO.setOrderNo(responseDTO.getDealNo());
        return hkRedeemFundOrderSubmitVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKRedeemFundOrderInfoVO
     * @description: 申请接口查询接口
     * @author: jinqing.rao
     * @date: 2024/5/10 15:21
     * @since JDK 1.8
     */
    public HKRedeemFundOrderInfoVO redeemApplyResult(HKRedeemFundOrderRequest request) {
        //获取订单信息
        HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO = hkFundOuterService.queryFundOrderInfo(request.getHkCustNo(), request.getOrderNo());
        //获取基金信息
        FundBasicInfoDTO fundBasicInfoDTO = queryFundBasicInfoOuterService.queryFundBasicInfo(hkPurchaseFundOrderInfoDTO.getFundCode());
        // 份额单位
        Integer volPrecision = fundBasicInfoDTO.getVolPrecision();
        HKRedeemFundOrderInfoVO hkRedeemFundOrderInfoVO = new HKRedeemFundOrderInfoVO();
        hkRedeemFundOrderInfoVO.setSupportPrebook(hkPurchaseFundOrderInfoDTO.getSupportPrebookFlag());
        hkRedeemFundOrderInfoVO.setCurrencyCode(hkPurchaseFundOrderInfoDTO.getCurrency());
        hkRedeemFundOrderInfoVO.setCurrencyDesc(CurrencyEnum.getDescription(hkPurchaseFundOrderInfoDTO.getCurrency()));
        hkRedeemFundOrderInfoVO.setRedeemMethod(hkPurchaseFundOrderInfoDTO.getRedeemMethod());
        hkRedeemFundOrderInfoVO.setRedeemAmt(NumberUtils.bigDecimalToString(hkPurchaseFundOrderInfoDTO.getAppAmt(), 2, RoundingMode.DOWN));
        hkRedeemFundOrderInfoVO.setRedeemVol(NumberUtils.bigDecimalToString(hkPurchaseFundOrderInfoDTO.getAppVol(), volPrecision, RoundingMode.DOWN));
        return hkRedeemFundOrderInfoVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.QueryFundCanSellVO
     * @description: 查询基金是否可卖出
     * @author: jinqing.rao
     * @date: 2024/12/21 14:33
     * @since JDK 1.8
     */
    public QueryFundCanSellVO queryCanSell(QueryFundCanSellRequest request) {
        QueryFundCanSellVO response = new QueryFundCanSellVO();

        // 构建查询参数
        QueryBuyOrSellReqDTO queryDTO = buildQueryDTO(request.getFundCode(), request.getHkCustNo());

        // 调用判断是否可卖出的服务
        FundCanTradeDTO fundCanTradeDTO = judgeBuyOrSellOuterService.queryCanSell(queryDTO);

        // 设置返回结果
        if (TradeButtonShowEnum.CAN_TRADE.getCode().equals(fundCanTradeDTO.getTradeStatus())) {
            response.setIsCanSell(YesNoEnum.YES.getCode());
        } else {
            response.setIsCanSell(YesNoEnum.NO.getCode());
            response.setNotCanSellCode(fundCanTradeDTO.getTradeCode());
            response.setNotCanSellMsg(fundCanTradeDTO.getTradeMsg());
        }

        return response;
    }

    /**
     * @param fundCode 基金代码
     * @param hkCustNo 香港客户号
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.QueryBuyOrSellReqDTO
     * @description: 构建查询参数
     * @author: jinqing.rao
     * @date: 2024/12/21 14:35
     * @since JDK 1.8
     */
    private QueryBuyOrSellReqDTO buildQueryDTO(String fundCode, String hkCustNo) {
        QueryBuyOrSellReqDTO queryBuyOrSellReqDTO = new QueryBuyOrSellReqDTO();

        queryBuyOrSellReqDTO.setAppTm(DateUtil.date2String(new Date(), DateUtil.StR_PATTERN_HHMMSS));
        queryBuyOrSellReqDTO.setAppDt(DateUtil.date2String(new Date(), DateUtil.SHORT_DATEPATTERN));
        queryBuyOrSellReqDTO.setFundCode(fundCode);
        queryBuyOrSellReqDTO.setTradeChannel(TxChannelEnum.HK_APP.getCode());
        queryBuyOrSellReqDTO.setIpAddress(WebUtil.getCustIP(CommonRequestUtil.getHttpRequest()));
        queryBuyOrSellReqDTO.setHkCustNo(hkCustNo);

        return queryBuyOrSellReqDTO;
    }
}
