package com.howbuy.crm.cgi.extservice.vo.statement;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 结单邮箱信息实体类
 * @author: 陈杰文
 * @date: 2025-06-17 15:15:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class StatementEmailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 邮箱地址掩码
     */
    private String emailMask;

    /**
     * 邮箱地址摘要
     */
    private String emailDigest;
} 