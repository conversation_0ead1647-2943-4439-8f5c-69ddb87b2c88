/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkaccount;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.account.DepositRequest;
import com.howbuy.crm.cgi.extservice.request.piggy.DepositsCertificateRequest;
import com.howbuy.crm.cgi.extservice.service.hkaccount.CustInfoService;
import com.howbuy.crm.cgi.extservice.service.hkaccount.HkCommonService;
import com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct.OpenAcctService;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.DepositsBankListVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.DepositsCertificateVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.HbHkBankAcctInfoVOList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/11/29 20:30
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/deposits")
public class HkDepositsController {

    @Autowired
    private HkCommonService hkCommonService;

    @Autowired
    private CustInfoService custInfoService;

    @Autowired
    private OpenAcctService openAcctService;

    /**
     * @api {POST} /ext/hkaccount/deposits/getdepositinfolist getDepositInfoList()
     * @apiVersion 1.0.0
     * @apiGroup HkDepositsController
     * @apiName getDepositInfo()
     * @apiDescription 好买香港公司银行账户信息获取
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.hbHkBankAcctInfoVOList 好买香港银行账户信息
     * @apiSuccess (响应结果) {String} data.hbHkBankAcctInfoVOList.bankAcctName 账户名称
     * @apiSuccess (响应结果) {String} data.hbHkBankAcctInfoVOList.bankName 银行名称
     * @apiSuccess (响应结果) {String} data.hbHkBankAcctInfoVOList.bankAddress 银行地址
     * @apiSuccess (响应结果) {String} data.hbHkBankAcctInfoVOList.defaultLogo 默认logo
     * @apiSuccess (响应结果) {Array} data.hbHkBankAcctInfoVOList.bankAcctList 银行账号列表
     * @apiSuccess (响应结果) {String} data.hbHkBankAcctInfoVOList.swiftCode 国际汇款识别码
     * @apiSuccess (响应结果) {String} data.hbHkBankAcctInfoVOList.bankCode 银行代码
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Wu","data":{"hbHkBankAcctInfoVOList":[{"bankAcctList":["5pmUFgqUM"],"bankCode":"6A8DHx7s","bankAcctName":"s","swiftCode":"Xcrrhz16gg","bankName":"2utn4dc","bankAddress":"jruB"}]},"description":"YiYLd8r","timestampServer":"OQ"}
     */
    @PostMapping("/getdepositinfolist")
    @ResponseBody
    public CgiResponse<HbHkBankAcctInfoVOList> getDepositInfoList() {
        return CgiResponse.ok(hkCommonService.gethbHkBankAcctInfo());
    }


    /**
     * @api {POST} /ext/hkaccount/deposits/getdepositsbanklist getDepositsBankList()
     * @apiVersion 1.0.0
     * @apiGroup HkDepositsController
     * @apiName getDepositsBankList()
     * @apiDescription 查询入金银行卡列表接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.depositBankVOList 银行卡列表数据
     * @apiSuccess (响应结果) {String} data.depositBankVOList.bankAcctMask 银行卡号掩码
     * @apiSuccess (响应结果) {String} data.depositBankVOList.bankAcctDigest 银行卡号摘要
     * @apiSuccess (响应结果) {String} data.depositBankVOList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} data.depositBankVOList.bankLogoUrl 银行logoUrl
     * @apiSuccess (响应结果) {String} data.depositBankVOList.swiftCode swift编码
     * @apiSuccess (响应结果) {String} data.depositBankVOList.bankName 银行名称
     * @apiSuccess (响应结果) {String} data.depositBankVOList.bankCode 银行代码
     * @apiSuccess (响应结果) {Array} data.depositBankVOList.currencyVOList 币种数据列表
     * @apiSuccess (响应结果) {Number} data.depositBankVOList.index 银行卡的展示顺序，按照绑定时间倒排
     * @apiSuccess (响应结果) {String} data.defaultLogo 好买银行的配图
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Lo","data":{"depositBankVOList":[{"bankLogoUrl":"NBQ","bankCode":"Gd762","bankAcctMask":"xR3bt","currencyVOList":[],"swiftCode":"43","index":8893,"cpAcctNo":"74","bankName":"bncH1cQS","bankAcctDigest":"xO"}],"defaultLogo":"mUdZSU85B"},"description":"eOlWsZSk","timestampServer":"suTYsIA"}
     */
    @PostMapping("/getdepositsbanklist")
    @ResponseBody
    public CgiResponse<DepositsBankListVO> getDepositsBankList() {
        return CgiResponse.ok(custInfoService.getDepositsBankList());
    }

    /**
     * @api {POST} /ext/hkaccount/deposits/deposits deposits()
     * @apiVersion 1.0.0
     * @apiGroup HkDepositsController
     * @apiName deposits()
     * @apiDescription 入金提交/修改接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} cpAcctNo 资金账号
     * @apiParam (请求体) {String} remitCurCode 汇款币种
     * @apiParam (请求体) {String} amount 金额
     * @apiParam (请求体) {String} remark 备注
     * @apiParam (请求体) {Array} remitImageUrls 汇款图片URL列表
     * @apiParam (请求体) {String} remitImageUrls.remitImageUrl 汇款图片地址
     * @apiParam (请求体) {String} remitImageUrls.remitImagethumbnailUrl 汇款缩略图地址
     * @apiParam (请求体) {String} remitImageUrls.fileType 文件类型 jpg,png,bmp
     * @apiParamExample 请求体示例
     * {"amount":"vio","remitImageUrls":[{"remitImageUrl":"PL3HpnO","remitImagethumbnailUrl":"hc","fileType":"hX"}],"cpAcctNo":"c4ybZO1s","remark":"FmXyl0t33l","remitCurCode":"n"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"gXNA1LE","description":"qgrw","timestampServer":"Su1LBXpa"}
     */
    @PostMapping("/deposits")
    @ResponseBody
    public CgiResponse<Body> deposits(@RequestBody DepositRequest depositRequest) {
        try {
            openAcctService.deposit(depositRequest);
            return CgiResponse.ok(new Body());
        } catch (Exception e) {
            return CgiResponse.error(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), ExceptionCodeEnum.SYSTEM_ERROR.getDescription(), new Body());
        }
    }


    /**
     * @api {POST} /ext/hkaccount/deposits/getdepositscertificate getDepositsCertificate()
     * @apiVersion 1.0.0
     * @apiGroup HkDepositsController
     * @apiName getDepositsCertificate()
     * @apiDescription 查看入金打款凭证接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} voucherNo 打款凭证订单号
     * @apiParamExample 请求体示例
     * {"voucherNo":"7XplLVe7"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.bankAcctMask 银行卡号掩码
     * @apiSuccess (响应结果) {String} data.bankAcctDigest 银行卡号摘要
     * @apiSuccess (响应结果) {String} data.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} data.bankLogoUrl 银行logoUrl
     * @apiSuccess (响应结果) {String} data.swiftCode swift编码
     * @apiSuccess (响应结果) {String} data.bankName 银行名称
     * @apiSuccess (响应结果) {String} data.bankCode 银行名称
     * @apiSuccess (响应结果) {String} data.remitCurCode 汇款币种      人民币 CNY 156      欧元 EUR 978      港元（港币） HKD 344      日元 JPY JPY      美元 USD 840      英镑 GBP 826      台币 TWD 901      越南盾 VND 704
     * @apiSuccess (响应结果) {Number} data.amount 金额 后端返回数据精度为8位
     * @apiSuccess (响应结果) {String} data.remark 备注
     * @apiSuccess (响应结果) {Array} data.remitImageList 汇款凭证列表
     * @apiSuccess (响应结果) {String} data.remitImageList.exampleFileFormatType 文件类型
     * @apiSuccess (响应结果) {String} data.remitImageList.localFilePath 前端的本地地址
     * @apiSuccess (响应结果) {String} data.remitImageList.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.remitImageList.url 图片地址
     * @apiSuccess (响应结果) {Array} data.checkResult 审核原因
     * @apiSuccess (响应结果) {String} data.checkResult.fileType 字段类型描述
     * @apiSuccess (响应结果) {String} data.checkResult.fileName 字段名称
     * @apiSuccess (响应结果) {String} data.checkResult.reason 错误原因
     * @apiSuccess (响应结果) {String} data.voucherType 打款凭证类型
     * @apiSuccess (响应结果) {String} data.relationOrderNo 关联订单号
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"4J70BqDah","data":{"bankLogoUrl":"oJY78auy","bankCode":"cNYDoVhN","amount":2530.************,"bankAcctMask":"lMaatCl6n","voucherType":"BFV","swiftCode":"O2ljO","bankName":"yNe4qLx","remark":"ei8c","remitCurCode":"vf","relationOrderNo":"wVt","checkResult":[{"reason":"wxkNe","fileName":"zTE","fileType":"8C"}],"bankAcctDigest":"f2YIM9","remitImageList":[{"exampleFileFormatType":"EI","localFilePath":"A","url":"HzHs7","thumbnailUrl":"6SHV9o4"}],"cpAcctNo":"xlY6K"},"description":"t7ph","timestampServer":"WeTT"}
     */
    @PostMapping("/getdepositscertificate")
    @ResponseBody
    public CgiResponse<DepositsCertificateVO> getDepositsCertificate(@RequestBody DepositsCertificateRequest request) {

        return CgiResponse.ok(openAcctService.getDepositsCertificate(request));
    }
}