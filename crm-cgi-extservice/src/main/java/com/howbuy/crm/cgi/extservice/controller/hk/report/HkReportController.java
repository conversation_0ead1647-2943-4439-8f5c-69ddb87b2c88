package com.howbuy.crm.cgi.extservice.controller.hk.report;

/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.report.ReportAuthenticationRequest;
import com.howbuy.crm.cgi.extservice.service.report.HkReportService;
import com.howbuy.crm.cgi.extservice.vo.report.ReportAuthenticationVO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;

/**
 * @description: 香港产品报告控制器
 * @author: 陈杰文
 * @date: 2025-06-19 15:07:57
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hk/report")
@Slf4j
public class HkReportController {

    @Resource
    private HkReportService hkReportService;

    /**
     * @api {POST} /ext/hk/report/authentication authentication()
     * @apiVersion 1.0.0
     * @apiGroup HkReportController
     * @apiName authentication()
     * @apiDescription 产品报告预览页身份鉴权接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} reportId 产品报告id
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456789","reportId":"RPT123456"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.isViewed 报告是否能看，0-否，1-是
     * @apiSuccess (响应结果) {String} data.redirectUrl 报告url，isViewed为1，则返回报告url，否则返回空
     * @apiSuccess (响应结果) {String} data.reportType 报告类型：1:产品单页,2:认购指南,3:产品合同,4:通知公告,5:运作报告,6:调研报告,7:会议纪要,8:信息披露,9:分配公告,10:其他材料,11:推介材料,12:FAQ,13:项目进展,14:风险揭示书
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"成功","data":{"isViewed":"1","redirectUrl":"https://example.com/report/123","reportType":"1"},"timestampServer":"1640995200000"}
     */
    @PostMapping("/authentication")
    public CgiResponse<ReportAuthenticationVO> authentication(@RequestBody ReportAuthenticationRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        
        // 调用服务层进行鉴权
        ReportAuthenticationVO result = hkReportService.reportAuthentication(request);
        
        return CgiResponse.ok(result);
    }
} 