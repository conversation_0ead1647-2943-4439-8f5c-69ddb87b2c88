package com.howbuy.crm.cgi.extservice.service.hkaccount;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.dict.NationalityDict;
import com.howbuy.crm.cgi.common.enums.CurrencyEnum;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.TxChannelEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.EmailBizScenarioEnum;
import com.howbuy.crm.cgi.extservice.common.enums.HkIdTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.*;
import com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggySignStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherTypeEnum;
import com.howbuy.crm.cgi.extservice.common.utils.AppEncUtils;
import com.howbuy.crm.cgi.extservice.common.utils.ExceptionUtil;
import com.howbuy.crm.cgi.extservice.common.utils.ParamsValidator;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.convert.hkopen.OpenAcctConvert;
import com.howbuy.crm.cgi.extservice.request.account.*;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAcctAppPersonalCenterRequest;
import com.howbuy.crm.cgi.extservice.service.hkfund.HwOverseaReportService;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.*;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkAcctAppPersonalCenterVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HwOverseaReportListVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.*;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkChangeInvestorQualsResultDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAccOrderInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctDepositInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctRiskCalculateResultDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.PiggyAgreementSignDetailDTO;
import com.howbuy.crm.cgi.manager.outerservice.cms.CmsOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkBankCardInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkChangeInvestorOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkPiggyBankOuterService;
import com.howbuy.dtms.common.enums.CustTxPasswdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.Collator;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 客户信息服务
 * @date 2023/6/6 10:33
 * @since JDK 1.8
 */
@Slf4j
@Service("custInfoService")
public class CustInfoService {

    @Autowired
    private HkCustInfoOuterService hkCustInfoOuterService;
    @Autowired
    private HkBankCardInfoOuterService hkBankCardInfoOuterService;
    @Autowired
    private CmsOuterService cmsOuterService;
    @Autowired
    private HkCommonService hkCommonService;

    @Resource
    private HkChangeInvestorOuterService hkChangeInvestorOuterService;

    @Resource
    private HkPiggyBankOuterService hkPiggyBankOuterService;

    @Resource
    private HwOverseaReportService hwOverseaReportService;

    @Value("${applet.open.cms.bank.logo.key}")
    private String hkOpenAccCmsBankLogoKey;

    @Value("${COMMON_AREA}")
    private String commonArea;

    @Value("${DEFAULT_BANK_SWIFT_CODE}")
    private String defaultSwiftCode;


    /**
     * @description: 查询个人中心信息
     * @param: []
     * @return: com.howbuy.crm.cgi.extservice.vo.account.CustLessInfoVO
     * @author: shaoyang.li
     * @date: 2023/6/6 11:06
     * @since JDK 1.8
     */
    public PersonalCenterInfoVO getPersonalCenterInfo() {
        //获取香港客户号
        String hkCustNo = getHkCustNo();
        //获取香港客户信息
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
        //获取客户的银行卡信息
        List<HkBankCardInfoDTO> hkBankCardInfoDTOList = hkBankCardInfoOuterService.getHkBankAcctList(hkCustNo);
        //版本控制,首次版本控制,判断非空即可,小程序审核通过后可以删除该判断
        HkOpenCustomerStatusEnum hkOpenCustomerStatusEnum = null;
        HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO = null;
        String hboneBindStatus = YesNoEnum.NO.getCode();
            //获取一账通的状态
        HkCustInfoDTO hkCustInfoByBindHboneNo = hkCustInfoOuterService.getHkCustInfoByBindHboneNo(hkCustNo);
        if (null != hkCustInfoByBindHboneNo) {
            hboneBindStatus = hkCustInfoByBindHboneNo.getHboneBindStatus();
        }
        //采用责任链模式,获取用户对应的客户状态,hkOpenAccOrderInfoDTO,hkOpenDepositInfoDTO会在具体的枚举中赋值,然后透传到下个枚举
        //获取开户订单信息
        hkOpenAccOrderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(hkCustNo);
            //获取用户的入金信息
        List<String> txChannelEnums = Arrays.asList(TxChannelEnum.WAP.getCode(), TxChannelEnum.HK_APP.getCode(), TxChannelEnum.H5.getCode());
        HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO = hkCustInfoOuterService.queryHkOpenDepositInfo(hkCustNo, PayVoucherTypeEnum.OPEN_ACCOUNT_CONFIRM.getCode(), txChannelEnums);
        hkOpenCustomerStatusEnum = HkOpenCustomerStatusEnumHandler.HIDE_ACCOUNT_DEPOSIT_AREA_HANDLER.handleRequest(hkCustNo, hkCustInfoDTO, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
        return OpenAcctConvert.toPersonalCenterInfoVO(hkCustInfoDTO, hkOpenCustomerStatusEnum, hkBankCardInfoDTOList, hkOpenAccOrderInfoDTO, hboneBindStatus);
    }

    /**
     * @description: 查询个人简要信息
     * @param: []
     * @return: PersonalSimpleInfoVO
     * @author: jiewen.chen
     * @date: 2024/8/6 16:20
     * @since JDK 1.8
     */
    public PersonalSimpleInfoVO getPersonalSimpleInfo() {
        //获取香港客户号
        String hkCustNo = getHkCustNo();
        //获取香港客户信息
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
        return OpenAcctConvert.toPersonalSimpleInfoVO(hkCustInfoDTO);
    }


    /**
     * @param request 请求参数实体类
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkAcctAppPersonalCenterVO
     * @description: 海外APP个人中心查询接口
     * @author: jinqing.rao
     * @date: 2024/2/23 11:12
     * @since JDK 1.8
     */
    public HkAcctAppPersonalCenterVO getAppPersonalCenterInfo(HkAcctAppPersonalCenterRequest request) {
        String hkCustNo = request.getHkCustNo();
        //获取香港客户信息
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfo(hkCustNo);

        //获取客户的银行卡信息
        List<HkBankCardInfoDTO> hkBankCardInfoDTOList = hkBankCardInfoOuterService.getHkBankAcctList(hkCustNo);

        //获取一账通的状态
        String hboneBindStatus = YesNoEnum.NO.getCode();
        HkCustInfoDTO hkCustInfoByBindHboneNo = hkCustInfoOuterService.getHkCustInfoByBindHboneNo(hkCustNo);
        if (null != hkCustInfoByBindHboneNo) {
            hboneBindStatus = hkCustInfoByBindHboneNo.getHboneBindStatus();
        }

        // 获取是否具有衍生品知识资格
        String derivativeKnowledge = YesNoEnum.NO.getCode();
        HkOpenAcctRiskCalculateResultDTO hkOpenAcctRiskCalculateResultDTO = hkCustInfoOuterService.queryHkOpenAccKycInfoInfo(hkCustNo);
        if (null != hkOpenAcctRiskCalculateResultDTO) {
            derivativeKnowledge = hkOpenAcctRiskCalculateResultDTO.getDerivativeKnowledge();
        }


        //获取专业投资者是否存在上传资料
        String investorAuditStatus = getInvestorAuditStatus(request);

        //采用责任链模式,获取用户对应的客户状态,hkOpenAccOrderInfoDTO,hkOpenDepositInfoDTO会在具体的枚举中赋值,然后透传到下个枚举
        HkOpenCustomerStatusEnum hkOpenCustomerStatusEnum = null;
        HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO = null;
        //获取开户订单信息
        hkOpenAccOrderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(hkCustNo);
        //获取用户的入金信息
        List<String> txChannelEnums = Arrays.asList(TxChannelEnum.WAP.getCode(), TxChannelEnum.HK_APP.getCode(), TxChannelEnum.H5.getCode());
        HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO = hkCustInfoOuterService.queryHkOpenDepositInfo(hkCustNo,PayVoucherTypeEnum.OPEN_ACCOUNT_CONFIRM.getCode(),txChannelEnums);
        hkOpenCustomerStatusEnum = HkOpenAppCustomerStatusEnumHandler.HIDE_ACCOUNT_DEPOSIT_AREA_HANDLER.handleRequest(hkCustNo, hkCustInfoDTO, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);

        // 获取海外储蓄罐协议签署状态
        String piggySignStatus = YesNoEnum.NO.getCode();
        PiggyAgreementSignDetailDTO hkCustPiggyAgreement = hkPiggyBankOuterService.getHkCustPiggyAgreement(hkCustNo);
        if(PiggySignStatusEnum.SIGNED.getCode().equals(hkCustPiggyAgreement.getAgreementState())){
            piggySignStatus = YesNoEnum.YES.getCode();
        }
        //获取证件类型描述
        String idTypeDesc = getIdTypeDesc(hkCustInfoDTO);
        HkAcctAppPersonalCenterVO appPersonalCenterInfoVO = OpenAcctConvert.toAppPersonalCenterInfoVO(hkCustInfoDTO, hkOpenCustomerStatusEnum, hkBankCardInfoDTOList,
                hkOpenAccOrderInfoDTO, hboneBindStatus, idTypeDesc, investorAuditStatus, piggySignStatus, derivativeKnowledge);
        // 查询海外资产报告信息
        buildhwOverseaReportVO(hkCustNo, appPersonalCenterInfoVO,hkCustInfoDTO);

        return appPersonalCenterInfoVO;
    }

    /**
     * @description: 设置海外报告信息
     * @param hkCustNo	
     * @param appPersonalCenterInfoVO
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkAcctAppPersonalCenterVO
     * @author: jinqing.rao
     * @date: 2025/4/27 10:03
     * @since JDK 1.8
     */
    private HkAcctAppPersonalCenterVO buildhwOverseaReportVO(String hkCustNo, HkAcctAppPersonalCenterVO appPersonalCenterInfoVO,HkCustInfoDTO hkCustInfoDTO) {
        HwOverseaReportListVO hwOverseaReportListVO = hwOverseaReportService.queryHwOverseaReportForAppCenter(hkCustNo,hkCustInfoDTO.getInvestorQualification());
        List<HwOverseaReportListVO.HwOverseaReportVO> overseaReportList = hwOverseaReportListVO.getOverseaReportList();
        if(CollectionUtils.isEmpty(overseaReportList)){
            appPersonalCenterInfoVO.setHasOverseasReport(YesNoEnum.NO.getCode());
            return appPersonalCenterInfoVO;
        }
        // 设置有海外标识
        appPersonalCenterInfoVO.setHasOverseasReport(YesNoEnum.YES.getCode());
        // 获取最新时间，APP 实现未读小红点功能
        overseaReportList.sort(Comparator.comparing(
                (HwOverseaReportListVO.HwOverseaReportVO vo) -> Optional.ofNullable(vo.getUpdateTime()).orElse(vo.getCreateTime()),
                Comparator.nullsLast(Comparator.naturalOrder())
        ).reversed());
        LocalDateTime latestReportDate = overseaReportList.get(0).getUpdateTime();
        if(null == latestReportDate){
            latestReportDate = overseaReportList.get(0).getCreateTime();
        }
        String formattedDate = DateUtils.formatToString(latestReportDate, DateUtils.YYYYMMDDHHMMSS);
        appPersonalCenterInfoVO.setOverseasReportLatestDt(formattedDate);
        return appPersonalCenterInfoVO;
    }

    /**
     * @description:(请在此添加描述)
     * @param hkCustInfoDTO	
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/4/14 19:02
     * @since JDK 1.8
     */
    private String getIdTypeDesc(HkCustInfoDTO hkCustInfoDTO) {
        String idTypeDesc = null;
        if (hkCustInfoDTO.getIdSignAreaCode() != null) {
            idTypeDesc  = getIdTypeDescByTypeAndArea(hkCustInfoDTO.getIdType(), hkCustInfoDTO.getIdSignAreaCode());
        } else {
            idTypeDesc = getIdTypeDesc(hkCustInfoDTO.getIdType());
        }
        return idTypeDesc;
    }

    /**
     * @description: 获取资产审核状态
     * @param request
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/4/14 19:03
     * @since JDK 1.8
     */
    private String getInvestorAuditStatus(HkAcctAppPersonalCenterRequest request) {
        String investorAuditStatus  = YesNoEnum.NO.getCode();
        HkChangeInvestorQualsResultDTO hkChangeInvestorQualsResultDTO = hkChangeInvestorOuterService.getInvestAuditDetail(request.getHkCustNo());
        // 若不存在审核中的材料，即无关联材料，或材料【审核状态】=审核通过/作废/审核不通过
        if(HkOpenAcctOrderStatusEnum.PENDING_REVIEW.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())
                || HkOpenAcctOrderStatusEnum.PENDING_RECHECK.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())||
                HkOpenAcctOrderStatusEnum.REJECTED_TO_CUSTOMER.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())
        || HkOpenAcctOrderStatusEnum.REJECTED_TO_INITIAL.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())){
            investorAuditStatus = YesNoEnum.YES.getCode();
        }
        return investorAuditStatus;
    }
    /**
     * @description: 查询客户实名信息
     * @param: []
     * @return: com.howbuy.crm.cgi.extservice.vo.account.CustRealNameInfoVO
     * @author: shaoyang.li
     * @date: 2023/6/6 11:05
     * @since JDK 1.8
     */
    public CustRealNameInfoVO getCustRealNameInfoNewWay() {
        String hkCustNo = getHkCustNo();
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getRealHkCustInfo(hkCustNo);
        CustRealNameInfoVO custRealNameInfoVO = fillCustRealNameInfoVO(hkCustInfoDTO);
        if (StringUtils.isNotEmpty(custRealNameInfoVO.getNationality())) {
            custRealNameInfoVO.setNationalityDesc(NationalityDict.getValue(custRealNameInfoVO.getNationality()));
        }
        transAddressByCityListVO(hkCustInfoDTO, custRealNameInfoVO);
        return custRealNameInfoVO;

    }

    /**
     * @param hkCustInfoDTO
     * @param custRealNameInfoVO
     * @return void
     * @description:(根据类型转换对应数据)
     * @author: xufanchao
     * @date: 2023/12/25 23:20
     * @since JDK 1.8
     */
    private void transAddressByCityListVO(HkCustInfoDTO hkCustInfoDTO, CustRealNameInfoVO custRealNameInfoVO) {
        // 设置职业以及年收入
        if (Objects.nonNull(hkCustInfoDTO.getEmplStatus())) {
            custRealNameInfoVO.setEmplStatus(hkCustInfoDTO.getEmplStatus());
            custRealNameInfoVO.setEmplStatusDesc(null == (HkOpenAcctEmploymentTypeEnum.getHkOpenAcctEmploymentTypeEnumByCode(hkCustInfoDTO.getEmplStatus())) ? "--" : HkOpenAcctEmploymentTypeEnum.getHkOpenAcctEmploymentTypeEnumByCode(hkCustInfoDTO.getEmplStatus()).getDesc());
        }
        if (Objects.nonNull(hkCustInfoDTO.getIncLevel())) {
            custRealNameInfoVO.setIncLevel(hkCustInfoDTO.getIncLevel());
            custRealNameInfoVO.setIncLevelDesc(null == (HkOpenAcctIncLevelEnum.getHkOpenAcctIncLevelEnumByCode(hkCustInfoDTO.getIncLevel())) ? "--" : HkOpenAcctIncLevelEnum.getHkOpenAcctIncLevelEnumByCode(hkCustInfoDTO.getIncLevel()).getDesc());
        }
        setResidenceDesc(hkCustInfoDTO, custRealNameInfoVO);
        setMailingDesc(hkCustInfoDTO, custRealNameInfoVO);
    }

    /**
     * @param hkCustInfoDTO
     * @param custRealNameInfoVO
     * @return void
     * @description:(获取通讯地址)
     * @author: xufanchao
     * @date: 2024/1/14 14:25
     * @since JDK 1.8
     */
    private void setMailingDesc(HkCustInfoDTO hkCustInfoDTO, CustRealNameInfoVO custRealNameInfoVO) {
        if (hkCustInfoDTO.getMailingCountryCode() != null && "CN".contains(hkCustInfoDTO.getMailingCountryCode())) {
            String mailingCountryCode = hkCustInfoDTO.getMailingProvCode();
            String mailingCityCode = hkCustInfoDTO.getMailingCityCode();
            String mailingCountyCode = hkCustInfoDTO.getMailingCountyCode();
            ProvinceCityCoutryVO cityListVO = hkCommonService.getCityListVO();
            //  省市区列表中 省份的dm = residenceProvCode  的对象数据
            cityListVO.getCityListVO().stream().filter(provinceListVO -> provinceListVO.getDm().equals(mailingCountryCode)).findFirst().ifPresent(provinceListVO -> {
                // 找到省份后,再用lamber表达式从 市列表中 市的dm = residenceCityCode  的对象数据
                provinceListVO.getDataList().stream().filter(cityListVO1 -> cityListVO1.getDm().equals(mailingCityCode)).findFirst().ifPresent(provinceListVO1 -> {
                    if (StringUtils.isNotEmpty(mailingCountyCode)) {
                        // 找到市后,再用lamber表达式从 区列表中 区的dm = residenceCountyCode  的对象数据
                        provinceListVO1.getDataList().stream().filter(countyListVO -> countyListVO.getDm().equals(mailingCountyCode)).findFirst().ifPresent(provinceListVO2 -> {
                            custRealNameInfoVO.setMailingAddressDesc(provinceListVO.getMc() + " " + provinceListVO1.getMc() + " " + provinceListVO2.getMc() + hkCustInfoDTO.getMailingCnAddrMask());
                        });
                    }
                });
            });
        } else if (hkCustInfoDTO.getResidenceCountryCode() != null && "TW,HK,MO".contains(hkCustInfoDTO.getResidenceCountryCode())) {
            custRealNameInfoVO.setMailingAddressDesc(NationalityDict.getValue(hkCustInfoDTO.getMailingCountryCode()) + " " + hkCustInfoDTO.getResidenceCnAddrMask());
        } else if (hkCustInfoDTO.getMailingCountryCode() != null && hkCustInfoDTO.getMailingProvCode() != null && hkCustInfoDTO.getMailingCityCode() != null) {
            custRealNameInfoVO.setMailingAddressDesc(hkCustInfoDTO.getMailingCnAddrMask() + " " + hkCustInfoDTO.getMailingCityCode() + " " + hkCustInfoDTO.getMailingProvCode());
        } else if (StringUtils.isNotEmpty(hkCustInfoDTO.getMailingCnAddrMask())) {
            custRealNameInfoVO.setMailingAddressDesc(hkCustInfoDTO.getMailingCnAddrMask());
        } else {
            custRealNameInfoVO.setMailingAddressDesc("--");
        }
    }

    /**
     * @param hkCustInfoDTO
     * @param custRealNameInfoVO
     * @return void
     * @description:(获取拼接居住地址)
     * @author: xufanchao
     * @date: 2024/1/14 14:24
     * @since JDK 1.8
     */
    private void setResidenceDesc(HkCustInfoDTO hkCustInfoDTO, CustRealNameInfoVO custRealNameInfoVO) {
        if (hkCustInfoDTO.getResidenceCountryCode() != null && "CN".contains(hkCustInfoDTO.getResidenceCountryCode())) {
            String residenceProvCode = hkCustInfoDTO.getResidenceProvCode();
            String residenceCityCode = hkCustInfoDTO.getResidenceCityCode();
            String residenceCountyCode = hkCustInfoDTO.getResidenceCountyCode();
            ProvinceCityCoutryVO cityListVO = hkCommonService.getCityListVO();
            // 省市区列表中 省份的dm = residenceProvCode  的对象数据
            cityListVO.getCityListVO().stream().filter(provinceListVO -> provinceListVO.getDm().equals(residenceProvCode)).findFirst().ifPresent(provinceListVO -> {
                // 找到省份后,再用lamber表达式从 市列表中 市的dm = residenceCityCode  的对象数据
                provinceListVO.getDataList().stream().filter(cityListVO1 -> cityListVO1.getDm().equals(residenceCityCode)).findFirst().ifPresent(provinceListVO1 -> {
                    if (StringUtils.isNotEmpty(residenceCountyCode)) {
                        // 找到市后,再用lamber表达式从 区列表中 区的dm = residenceCountyCode  的对象数据
                        provinceListVO1.getDataList().stream().filter(countyListVO -> countyListVO.getDm().equals(residenceCountyCode)).findFirst().ifPresent(provinceListVO2 -> {
                            custRealNameInfoVO.setResidentialAddressDesc(provinceListVO.getMc() + " " + provinceListVO1.getMc() + " " + provinceListVO2.getMc() + hkCustInfoDTO.getResidenceCnAddrMask());
                        });
                    }
                });
            });
        } else if (hkCustInfoDTO.getResidenceCountryCode() != null && "TW,HK,MO".contains(hkCustInfoDTO.getResidenceCountryCode())) {
            custRealNameInfoVO.setResidentialAddressDesc(NationalityDict.getValue(hkCustInfoDTO.getResidenceCountryCode()) + " " + hkCustInfoDTO.getResidenceCnAddrMask());
        } else if (hkCustInfoDTO.getResidenceCountryCode() != null && hkCustInfoDTO.getResidenceProvCode() != null && hkCustInfoDTO.getResidenceCityCode() != null) {
            custRealNameInfoVO.setResidentialAddressDesc(hkCustInfoDTO.getResidenceCnAddrMask() + " " + hkCustInfoDTO.getResidenceCityCode() + " " + hkCustInfoDTO.getResidenceProvCode());
        } else if (StringUtils.isNotEmpty(hkCustInfoDTO.getResidenceCnAddrMask())) {
            custRealNameInfoVO.setResidentialAddressDesc(hkCustInfoDTO.getResidenceCnAddrMask());
        } else {
            custRealNameInfoVO.setResidentialAddressDesc("--");
        }
    }

    /**
     * @description: 填充CustRealNameInfoVO
     * @param: [hkCustInfoDTO]
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.CustRealNameInfoVO
     * @author: shaoyang.li
     * @date: 2023/6/7 17:35
     * @since JDK 1.8
     */
    private CustRealNameInfoVO fillCustRealNameInfoVO(HkCustInfoDTO hkCustInfoDTO) {
        CustRealNameInfoVO custRealNameInfoVO = new CustRealNameInfoVO();
        custRealNameInfoVO.setHkCustNo(hkCustInfoDTO.getHkCustNo());
        custRealNameInfoVO.setCustName(hkCustInfoDTO.getCustName());
        custRealNameInfoVO.setInvstType(hkCustInfoDTO.getInvstType());
        custRealNameInfoVO.setIdType(hkCustInfoDTO.getIdType());
        custRealNameInfoVO.setIdTypeDesc(getIdTypeDesc(hkCustInfoDTO.getIdType()));
        custRealNameInfoVO.setIdNoDigest(hkCustInfoDTO.getIdNoDigest());
        custRealNameInfoVO.setIdNoMask(hkCustInfoDTO.getIdNoMask());
        custRealNameInfoVO.setIdValidityEnd(DateUtils.formatDateStr(hkCustInfoDTO.getIdValidityEnd()));
        custRealNameInfoVO.setIdAlwaysValidFlag(hkCustInfoDTO.getIdAlwaysValidFlag());
        custRealNameInfoVO.setIdImageUploadStatus(hkCustInfoDTO.getIdImageUploadStatus());
        custRealNameInfoVO.setNationality(hkCustInfoDTO.getNationality());
        custRealNameInfoVO.setBirthday(DateUtils.formatDateStr(hkCustInfoDTO.getBirthday()));
        custRealNameInfoVO.setGender(hkCustInfoDTO.getGender());
        custRealNameInfoVO.setCustEnName(hkCustInfoDTO.getCustEnName());
        return custRealNameInfoVO;
    }

    /**
     * @description: 获取银行卡列表
     * @param: []
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.BankCardListVO
     * @author: shaoyang.li
     * @date: 2023/6/7 17:33
     * @since JDK 1.8
     */
    public BankCardListVO getBankCardList() {
        String hkCustNo = getHkCustNo();
        List<HkBankCardInfoDTO> hkBankCardInfoDTOList = hkBankCardInfoOuterService.getHkBankAcctList(hkCustNo);
        //获取CMS配置的银行卡图标
        Map<String, String> cmsConfigInfo = cmsOuterService.getCmsConfigInfo(hkOpenAccCmsBankLogoKey);
        //按银行名称排序：英文>中文，A>Z，中文拼音a>b
        List<BankCardInfoVO> bankCardInfoVOList = hkBankCardInfoDTOList.stream()
                .map(m -> {
                    return fillBankCardInfoVO(m, cmsConfigInfo);
                })
                .sorted(Comparator.comparing(BankCardInfoVO::getBankName, Collator.getInstance(Locale.CHINA)))
                .collect(Collectors.toList());

        BankCardListVO bankCardListVO = new BankCardListVO();
        bankCardListVO.setBankCardInfoVOList(bankCardInfoVOList);
        return bankCardListVO;
    }

    /**
     * @description: 获取客户信息
     * @param
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/4/14 19:03
     * @since JDK 1.8
     */
    private static String getHkCustNo() {
        return RequestUtil.getParameter(Constants.HKCUSTNO);
    }

    /**
     * @description: 填充BankCardInfoVO
     * @param: [hkBankCardInfoDTO]
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.BankCardInfoVO
     * @author: shaoyang.li
     * @date: 2023/6/7 17:37
     * @since JDK 1.8
     */
    private BankCardInfoVO fillBankCardInfoVO(HkBankCardInfoDTO hkBankCardInfoDTO, Map<String, String> cmsConfigInfo) {
        BankCardInfoVO bankCardInfoVO = new BankCardInfoVO();
        bankCardInfoVO.setHkCpAcctNo(hkBankCardInfoDTO.getHkCpAcctNo());
        bankCardInfoVO.setBankCode(hkBankCardInfoDTO.getBankCode());
        if (StringUtils.isNotEmpty(hkBankCardInfoDTO.getBankChineseName())) {
            bankCardInfoVO.setBankName(hkBankCardInfoDTO.getBankChineseName());
        } else {
            bankCardInfoVO.setBankName(hkBankCardInfoDTO.getBankName());
        }
        bankCardInfoVO.setBankRegionCode(hkBankCardInfoDTO.getBankRegionCode());
        bankCardInfoVO.setBankAcctName(hkBankCardInfoDTO.getBankAcctName());
        bankCardInfoVO.setBankAcctDigest(hkBankCardInfoDTO.getBankAcctDigest());
        bankCardInfoVO.setBankAcctMask(hkBankCardInfoDTO.getBankAcctMask());
        bankCardInfoVO.setBankAcctStatus(hkBankCardInfoDTO.getBankAcctStatus());
        bankCardInfoVO.setBankLogoUrl(cmsConfigInfo.get(hkBankCardInfoDTO.getSwiftCode()));
        return bankCardInfoVO;
    }


    /**
     * @description: 查询银行卡明文
     * @param: [request]
     * @return: com.howbuy.crm.cgi.extservice.vo.account.BankCardPlaintextVO
     * @author: shaoyang.li
     * @date: 2023/6/6 16:19
     * @since JDK 1.8
     */
    public BankCardPlaintextVO getBankCardPlaintext(BankCardPlaintextRequest request) {
        String hkCustNo = getHkCustNo();
        hkCustInfoOuterService.validateHkTradePassword(hkCustNo, request.getTradePassword());
        BankCardPlaintextVO bankCardPlaintextVO = new BankCardPlaintextVO();
        BankCardPlaintextDTO bankCardPlaintextDTO = hkBankCardInfoOuterService.getHkBankCardPlaintext(hkCustNo, request.getHkCpAcctNo());
        bankCardPlaintextVO.setHkCpAcctNo(bankCardPlaintextDTO.getHkCpAcctNo());
        bankCardPlaintextVO.setBankAcct(bankCardPlaintextDTO.getBankAcct());
        return bankCardPlaintextVO;
    }

    /**
     * @description: 根据香港客户号查询香港客户信息
     * @param: [hkCustNo]
     * @return: com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO
     * @author: shaoyang.li
     * @date: 2023/6/9 10:25
     * @since JDK 1.8
     */
    public HkCustInfoDTO getHkCustInfo(String hkCustNo) {
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
        ExceptionUtil.validateHkCustInfoDTO(hkCustInfoDTO, ExceptionCodeEnum.ACCT_ERROR);
        return hkCustInfoDTO;
    }

    /**
     * @description: 根据手机号查询香港客户信息
     * @param: [areaCode, mobileDigest]
     * @return: com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO
     * @author: shaoyang.li
     * @date: 2023/6/9 10:26
     * @since JDK 1.8
     */
    public HkCustInfoDTO getHkCustInfoByMobile(String areaCode, String mobileDigest) {
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfoByMobile(areaCode, mobileDigest);
        return hkCustInfoDTO;
    }

    /**
     * @param areaCode
     * @param mobileDigest
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO
     * @description:(根据手机号查询香港客户信息(去掉未开户校验))
     * @author: xufanchao
     * @date: 2023/12/22 14:19
     * @since JDK 1.8
     */
    public HkCustInfoDTO getnewHkCustInfoByMobile(String areaCode, String mobileDigest) {
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfoByMobile(areaCode, mobileDigest);
        return hkCustInfoDTO;
    }

    /**
     * @description: 根据证件号码查询香港客户信息
     * @param: [idType, idNoDigest]
     * @return: com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO
     * @author: shaoyang.li
     * @date: 2023/6/9 10:26
     * @since JDK 1.8
     */
    public HkCustInfoDTO getHkCustInfoByIdNo(String idType, String idNoDigest) {
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfoByIdNo(idType, idNoDigest);
        ExceptionUtil.validateHkCustInfoDTO(hkCustInfoDTO, ExceptionCodeEnum.IDNO_UN_OPEN_ACCT);
        return hkCustInfoDTO;
    }

    /**
     * @description: 根据证件号码查询香港客户信息
     * @param: [idType, idNoDigest]
     * @return: com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO
     * @author: shaoyang.li
     * @date: 2023/6/9 10:26
     * @since JDK 1.8
     */
    public HkCustInfoDTO validateInNoExist(String idType, String idNoDigest) {
        String loginHkCustNo = getHkCustNo();
        //HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfoByIdNo(idType, idNoDigest);
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfo(loginHkCustNo);
        if (StringUtils.isBlank(hkCustInfoDTO.getIdNoDigest())) {
            throw new BusinessException(ExceptionCodeEnum.IDNO_UN_OPEN_ACCT);
        }
        if (idType.equals(hkCustInfoDTO.getIdType()) && !idNoDigest.equals(hkCustInfoDTO.getIdNoDigest())) {
            throw new BusinessException(ExceptionCodeEnum.ID_NO_DIFF.getCode(), "证件号与您预留的不匹配,请重新输入");
        }
        //ExceptionUtil.validateHkCustInfoDTO(hkCustInfoDTO, ExceptionCodeEnum.IDNO_UN_OPEN_ACCT);
        return hkCustInfoDTO;
    }

    /**
     * @param idType     证件类型
     * @param idNoDigest 证件摘要
     * @param areaCode   地区码
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO
     * @description:(根据证件号码查询香港客户信息)
     * @author: xufanchao
     * @date: 2023/12/25 19:17
     * @since JDK 1.8
     */
    public HkCustInfoDTO getHkCustInfoByIdNoandType(String idType, String idNoDigest, String areaCode) {
        return hkCustInfoOuterService.getHkCustInfoByIdNoandType(idType, idNoDigest, areaCode);
    }

    /**
     * @param idNoDigest
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoCounterDTO>
     * @description:(根据证件号查询香港客户信息)
     * @author: xufanchao
     * @date: 2023/12/25 16:49
     * @since JDK 1.8
     */
    public List<HkCustInfoCounterDTO> listHkCustInfoByIdNo(String idNoDigest) {
        return hkCustInfoOuterService.queryCustInfoForCounter(idNoDigest);
    }


    /**
     * @description: 根据邮箱查询香港客户信息
     * @param: [emailDigest]
     * @return: com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO
     * @author: shaoyang.li
     * @date: 2023/6/9 10:26
     * @since JDK 1.8
     */
    public HkCustInfoDTO getHkCustInfoByEmail(String emailDigest) {
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfoByEmail(emailDigest);
        return hkCustInfoDTO;
    }

    /**
     * @description: 根据香港客户号获取手机号明文
     * @param: [hkCustNo]
     * @return: java.lang.String
     * @author: shaoyang.li
     * @date: 2023/6/7 19:23
     * @since JDK 1.8
     */
    public String getMobilePlaintext(String hkCustNo) {
        return hkCustInfoOuterService.getMobile(hkCustNo);
    }

    /**
     * @description: 根据香港客户号获取客户信息明文
     * @param: [hkCustNo]
     * @return: com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoPlaintextDTO
     * @author: shaoyang.li
     * @date: 2023/6/7 19:54
     * @since JDK 1.8
     */
    public HkCustInfoPlaintextDTO getCustInfoPlaintext(String hkCustNo) {
        return hkCustInfoOuterService.getCustInfoPlaintext(hkCustNo);
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.CustLessInfoVO
     * @description:(查询客户信息)
     * @author: shaoyang.li
     * @date: 2023/8/31 13:43
     * @since JDK 1.8
     */
    public CustInfoVO getCustInfo() {
        String hkCustNo = getHkCustNo();
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
        return transCustInfo(hkCustInfo);
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.DepositsBankListVO
     * @description:()
     * @author: xufanchao
     * @date: 2023/12/15 13:26
     * @since JDK 1.8
     */
    public DepositsBankListVO getDepositsBankList() {
        DepositsBankListVO depositsBankListVO = new DepositsBankListVO();
        String loginHkCustNo = getHkCustNo();
        List<HkBankCardInfoDTO> hkBankAcctList = hkBankCardInfoOuterService.getHkBankAcctList(loginHkCustNo);
        Map<String, String> cmsConfigInfo = cmsOuterService.getCmsConfigInfo(hkOpenAccCmsBankLogoKey);
        if (cmsConfigInfo != null) {
            depositsBankListVO.setDefaultLogo(cmsConfigInfo.get(defaultSwiftCode));
        }
        // 根据 hkBankAcctList 里面的绑卡时间进行倒排
        Collections.sort(hkBankAcctList, Comparator.comparing(HkBankCardInfoDTO::getBankBindTime).reversed());
        List<DepositsBankVO> depositBankVOList = new ArrayList<>();
        for (int i = 0; i < hkBankAcctList.size(); i++) {
            DepositsBankVO depositsBankVO = new DepositsBankVO();
            // 根据 香港客户号和资金账户获取银行卡数据，并且转成前端需要的格式
            //BankCardPlaintextDTO hkBankCardPlaintext = hkBankCardInfoOuterService.getHkBankCardPlaintext(loginHkCustNo, hkBankAcctList.get(i).getHkCpAcctNo());
            //String bankAcct = hkBankCardPlaintext.getBankAcct();
            depositsBankVO.setBankAcctMask(hkBankAcctList.get(i).getBankAcctMask());
            depositsBankVO.setBankAcctDigest(hkBankAcctList.get(i).getBankAcctDigest());
            depositsBankVO.setCpAcctNo(hkBankAcctList.get(i).getHkCpAcctNo());
            String url = cmsConfigInfo.get(hkBankAcctList.get(i).getSwiftCode());
            if(StringUtils.isBlank(url)){
                //获取默认的Logo
                url = cmsConfigInfo.get("MRYHLOGO");
            }
            depositsBankVO.setBankLogoUrl(url);
            if (!StringUtils.isBlank(hkBankAcctList.get(i).getBankChineseName())) {
                depositsBankVO.setBankName(hkBankAcctList.get(i).getBankChineseName());
            } else {
                depositsBankVO.setBankName(hkBankAcctList.get(i).getBankName());
            }
            depositsBankVO.setSwiftCode(hkBankAcctList.get(i).getSwiftCode());
            depositsBankVO.setIndex(i);
            List<CurrencyVO> currencyVOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(hkBankAcctList.get(i).getCurrencyCodes())) {
                for (String currencyCode : hkBankAcctList.get(i).getCurrencyCodes()) {
                    CurrencyVO currencyVO = new CurrencyVO();
                    currencyVO.setCurrencyCode(currencyCode);
                    currencyVO.setCurrencyDesc(CurrencyEnum.getDescription(currencyCode) + CurrencyEnum.getEnDescription(currencyCode));
                    currencyVOList.add(currencyVO);
                }
            }
            depositsBankVO.setCurrencyVOList(currencyVOList);
            depositBankVOList.add(depositsBankVO);
        }
        depositsBankListVO.setDepositBankVOList(depositBankVOList);
        return depositsBankListVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.CustInfoNotLoginVO
     * @description:(未登录查客户信息)
     * @author: shaoyang.li
     * @date: 2023/8/31 10:42
     * @since JDK 1.8
     */
    public CustInfoNotLoginVO getCustInfoNotLogin(CustInfoNotLoginRequest request) {
        ParamsValidator.validateParams(request, "mobile#email");
        HkCustInfoDTO hkCustInfo = null;
        CustInfoNotLoginVO notLoginVO = new CustInfoNotLoginVO();

        if (StringUtils.isNotEmpty(request.getMobile())) {
            ParamsValidator.validateParams(request, "mobile", "mobileAreaCode");
            hkCustInfo = hkCustInfoOuterService.getHkCustInfoByMobile(request.getMobileAreaCode(), DigestUtil.digest(request.getMobile()));
            return fillNotLoginVO(hkCustInfo);
        }
        //邮箱
        if (StringUtils.isNotEmpty(request.getEmail())) {
            hkCustInfo = hkCustInfoOuterService.getHkCustInfoByEmail(DigestUtil.digest(request.getEmail()));
            return fillNotLoginVO(hkCustInfo);
        }
        return notLoginVO;
    }

    /**
     * @param hkCustInfo
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.CustInfoNotLoginVO
     * @description:(fillNotLoginVO)
     * @author: shaoyang.li
     * @date: 2023/8/31 10:42
     * @since JDK 1.8
     */
    private CustInfoNotLoginVO fillNotLoginVO(HkCustInfoDTO hkCustInfo) {
        CustInfoNotLoginVO custInfoNotLoginVO = new CustInfoNotLoginVO();
        custInfoNotLoginVO.setHkCustNo(hkCustInfo.getHkCustNo());
        custInfoNotLoginVO.setHboneNo(hkCustInfo.getHboneNo());
        custInfoNotLoginVO.setInvstType(hkCustInfo.getInvstType());
        custInfoNotLoginVO.setIdType(hkCustInfo.getIdType());
        custInfoNotLoginVO.setIdTypeDesc(getIdTypeDesc(hkCustInfo.getIdType()));
        custInfoNotLoginVO.setIdNoDigest(hkCustInfo.getIdNoDigest());
        custInfoNotLoginVO.setIdNoMask(hkCustInfo.getIdNoMask());
        custInfoNotLoginVO.setCustName(hkCustInfo.getCustName());
        custInfoNotLoginVO.setMobileMask(hkCustInfo.getMobileMask());
        custInfoNotLoginVO.setMobileDigest(hkCustInfo.getMobileDigest());
        custInfoNotLoginVO.setMobileVerifyStatus(hkCustInfo.getMobileVerifyStatus());
        custInfoNotLoginVO.setEmailMask(hkCustInfo.getEmailMask());
        custInfoNotLoginVO.setEmailDigest(hkCustInfo.getEmailDigest());
        custInfoNotLoginVO.setEmailVerifyStatus(hkCustInfo.getEmailVerifyStatus());
        custInfoNotLoginVO.setBirthday(hkCustInfo.getBirthday());
        custInfoNotLoginVO.setReturnCode(hkCustInfo.getReturnCode());
        custInfoNotLoginVO.setDescription(hkCustInfo.getDescription());
        return custInfoNotLoginVO;
    }

    /**
     * @param hkCustInfo
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.CustLessInfoVO
     * @description:(转换客户信息)
     * @author: shuai.zhang
     * @date: 2023/6/9 13:42
     * @since JDK 1.8
     */
    private CustInfoVO transCustInfo(HkCustInfoDTO hkCustInfo) {
        CustInfoVO custInfoVO = new CustInfoVO();
        custInfoVO.setHkCustNo(hkCustInfo.getHkCustNo());
        custInfoVO.setHboneNo(hkCustInfo.getHboneNo());
        custInfoVO.setHkCustNoCipher(AppEncUtils.encrypt(hkCustInfo.getHkCustNo()));
        custInfoVO.setHboneNoCipher(AppEncUtils.encryptHboneNo(hkCustInfo.getHboneNo()));
        custInfoVO.setInvstType(hkCustInfo.getInvstType());
        custInfoVO.setIdType(hkCustInfo.getIdType());
        if (hkCustInfo.getIdSignAreaCode() != null) {
            custInfoVO.setIdTypeDesc(getIdTypeDescByTypeAndArea(hkCustInfo.getIdType(), hkCustInfo.getIdSignAreaCode()));
        } else {
            custInfoVO.setIdTypeDesc(getIdTypeDesc(hkCustInfo.getIdType()));
        }
        custInfoVO.setMobileAreaCode(hkCustInfo.getMobileAreaCode());
        custInfoVO.setIdNoDigest(hkCustInfo.getIdNoDigest());
        custInfoVO.setIdNoMask(hkCustInfo.getIdNoMask());
        custInfoVO.setCustName(hkCustInfo.getCustName());
        custInfoVO.setMobileMask(hkCustInfo.getMobileMask());
        custInfoVO.setMobileDigest(hkCustInfo.getMobileDigest());
        custInfoVO.setMobileVerifyStatus(hkCustInfo.getMobileVerifyStatus());
        custInfoVO.setEmailMask(hkCustInfo.getEmailMask());
        custInfoVO.setEmailDigest(hkCustInfo.getEmailDigest());
        custInfoVO.setEmailVerifyStatus(hkCustInfo.getEmailVerifyStatus());
        custInfoVO.setCustLoginPasswdType(hkCustInfo.getCustLoginPasswdType());
        custInfoVO.setCustTxPasswdType(hkCustInfo.getCustTxPasswdType());
        custInfoVO.setIdValidityEnd(DateUtils.formatDateStr(hkCustInfo.getIdValidityEnd()));
        custInfoVO.setIdAlwaysValidFlag(hkCustInfo.getIdAlwaysValidFlag());
        custInfoVO.setIdImageUploadStatus(hkCustInfo.getIdImageUploadStatus());
        custInfoVO.setNationality(hkCustInfo.getNationality());
        custInfoVO.setBirthday(DateUtils.formatDateStr(hkCustInfo.getBirthday()));
        custInfoVO.setGender(hkCustInfo.getGender());
        custInfoVO.setRiskToleranceLevel(hkCustInfo.getRiskToleranceLevel());
        custInfoVO.setCustState(hkCustInfo.getCustState());
        custInfoVO.setReturnCode(hkCustInfo.getReturnCode());
        custInfoVO.setDescription(hkCustInfo.getDescription());
        return custInfoVO;
    }

    /**
     * @description: 校验客户信息
     * @param: [request]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/13 10:51
     * @since JDK 1.8
     */
    public void validateCustInfo(ValidateCustInfoRequest request) {
        ParamsValidator.validateParams(request, "mobile#email#idNo");
        if (StringUtils.isNotEmpty(request.getMobile())) {
            ParamsValidator.validateParams(request, "mobile", "areaCode");
        }
        if (StringUtils.isNotEmpty(request.getIdNo())) {
            ParamsValidator.validateParams(request, "idNo", "idType");
        }
        String hkCustNo = LoginService.getLoginHkCustNoNoException();
        if (StringUtils.isNotEmpty(hkCustNo)) {
            HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
            validateMatch(request, hkCustInfoDTO);
        } else {
            validateExist(request);
        }
    }

    /**
     * @description: 已登录-校验是否匹配
     * @param: [request, hkCustInfoDTO]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/13 10:52
     * @since JDK 1.8
     */
    private void validateMatch(ValidateCustInfoRequest request, HkCustInfoDTO hkCustInfoDTO) {
        ExceptionUtil.validateHkCustInfoDTO(hkCustInfoDTO, ExceptionCodeEnum.ACCT_ERROR);

        if (StringUtils.isNotEmpty(request.getIdNo()) && !Objects.equals(DigestUtil.digest(request.getIdNo()), hkCustInfoDTO.getIdNoDigest())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.ID_NO_DIFF);
        }
        if (StringUtils.isNotEmpty(request.getMobile()) && !Objects.equals(DigestUtil.digest(request.getMobile()), hkCustInfoDTO.getMobileDigest())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.MOBILE_DIFF);
        }
        if (StringUtils.isNotEmpty(request.getEmail()) && !Objects.equals(DigestUtil.digest(request.getEmail()), hkCustInfoDTO.getEmailDigest())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.EMAIL_DIFF);
        }
    }

    /**
     * @description: 未登录-校验是否存在
     * @param: [request]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/13 10:52
     * @since JDK 1.8
     */
    private void validateExist(ValidateCustInfoRequest request) {
        if (StringUtils.isNotEmpty(request.getIdNo())) {
            validateInNoExist(request.getIdType(), DigestUtil.digest(request.getIdNo()));
        }
        if (StringUtils.isNotEmpty(request.getMobile())) {
            getHkCustInfoByMobile(request.getAreaCode(), DigestUtil.digest(request.getMobile()));
        }
        if (StringUtils.isNotEmpty(request.getEmail())) {
            getHkCustInfoByEmail(DigestUtil.digest(request.getEmail()));
        }
    }

    /**
     * @param idType
     * @return java.lang.String
     * @description:(根据证件类型获取证件类型描述)
     * @author: shaoyang.li
     * @date: 2023/8/2 14:37
     * @since JDK 1.8
     */
    private String getIdTypeDesc(String idType) {
        //idType为空返回null，没有对应枚举也返回null
        return StringUtils.isEmpty(idType) ? null : HkIdTypeEnum.getDesc(idType);
    }


    /**
     * @param idType
     * @return java.lang.String
     * @description:(获取证件类型描述)
     * @author: xufanchao
     * @date: 2024/1/8 15:51
     * @since JDK 1.8
     */
    private String getIdTypeDescByTypeAndArea(String idType, String areaCode) {
        //idType为空返回null，没有对应枚举也返回null
        IdTypeRequest idTypeRequest = new IdTypeRequest();
        idTypeRequest.setIdType(idType);
        idTypeRequest.setIdAreaCode(areaCode);
        IdTypeDescVO idTypeDesc = hkCommonService.getIdTypeDesc(idTypeRequest);
        String desc = idTypeDesc.getIdTypeDesc().replace("上传", "");
        return desc;
    }

    /**
     * @description: 更新客户昵称
     * @param: [request]
     * @return: void
     * @author: 陈杰文
     * @date: 2025-06-17 11:11:28
     * @since JDK 1.8
     */
    public void updateNickname(com.howbuy.crm.cgi.extservice.request.account.NicknameUpdateRequest request) {
        log.info("更新客户昵称，香港客户号：{}，昵称：{}", request.getHkCustNo(), request.getNickname());
        
        // 调用外部服务更新昵称
        hkCustInfoOuterService.updateNickname(request.getHkCustNo(), request.getNickname());
        
        log.info("客户昵称更新成功，香港客户号：{}", request.getHkCustNo());
    }

    /**
     * @description: 检查邮箱修改业务场景
     * @param: [request]
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.EmailUpdateCheckVO
     * @author: 陈杰文
     * @date: 2025-07-08 10:11:28
     * @since JDK 1.8
     */
    public EmailUpdateCheckVO checkEmailUpdate(EmailUpdateCheckRequest request) {
        log.info("检查邮箱修改业务场景，香港客户号：{}", request.getHkCustNo());
        String hkCustNo = request.getHkCustNo();
        
        // 获取用户开户绑定的邮箱
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
        String accountBindEmail = hkCustInfoDTO.getEmailDigest();
        
        // 获取用户的结单邮箱列表
        List<String> statementEmailList = hkCustInfoDTO.getStatementEmailMaskList();

        // 邮箱业务场景判断
        String emailBizScenario = this.determineEmailBizScenario(statementEmailList, accountBindEmail);
        
        EmailUpdateCheckVO result = new EmailUpdateCheckVO();
        result.setEmailBizScenario(emailBizScenario);
        log.info("邮箱修改业务场景检查完成，香港客户号：{}，业务场景：{}", hkCustNo, emailBizScenario);
        return result;
    }

    /**
     * @description: 判断邮箱业务场景
     * @param: [statementEmailList, accountBindEmail]
     * @return: java.lang.String
     * @author: 陈杰文
     * @date: 2025-07-08 10:25:55
     * @since JDK 1.8
     */
    private String determineEmailBizScenario(List<String> statementEmailList, String accountBindEmail) {
        // 用户没有维护过结单邮箱
        if (CollectionUtils.isEmpty(statementEmailList)) {
            return EmailBizScenarioEnum.NO_EMAIL_MAINTAINED.getCode();
        }
        
        // 用户有维护【一个】结单邮箱
        if (statementEmailList.size() == 1) {
            return EmailBizScenarioEnum.SINGLE_EMAIL_MAINTAINED.getCode();
        }
        
        // 用户有维护【多个】结单邮箱，检查是否包含开户绑定邮箱
        boolean hasMatchedEmail = StringUtils.isNotBlank(accountBindEmail) 
                && statementEmailList.contains(accountBindEmail);
        
        return hasMatchedEmail 
                ? EmailBizScenarioEnum.MULTIPLE_EMAIL_WITH_MATCHED.getCode()
                : EmailBizScenarioEnum.MULTIPLE_EMAIL_WITHOUT_MATCHED.getCode();
    }

    /**
     * @description: 查询客户状态
     * @param: [request]
     * @return: com.howbuy.crm.cgi.extservice.vo.hkaccount.QueryCustStatusVO
     * @author: 陈杰文
     * @date: 2025-06-17 11:11:28
     * @since JDK 1.8
     */
    public QueryCustStatusVO queryCustStatus(QueryCustStatusRequest request) {
        log.info("查询客户状态，香港客户号：{}", request.getHkCustNo());
        // 构建响应对象
        QueryCustStatusVO result = new QueryCustStatusVO();

        String hkCustNo = request.getHkCustNo();
        // 获取香港客户信息
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfo(hkCustNo);

        // 获取客户的银行卡信息
        List<HkBankCardInfoDTO> hkBankCardInfoDTOList = hkBankCardInfoOuterService.getHkBankAcctList(hkCustNo);
        
        // 获取开户订单信息
        HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(hkCustNo);
        
        // 获取用户的入金信息
        List<String> txChannelEnums = Arrays.asList(TxChannelEnum.WAP.getCode(), TxChannelEnum.HK_APP.getCode(), TxChannelEnum.H5.getCode());
        HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO = hkCustInfoOuterService.queryHkOpenDepositInfo(hkCustNo, PayVoucherTypeEnum.OPEN_ACCOUNT_CONFIRM.getCode(), txChannelEnums);
        
        // 采用责任链模式，获取用户对应的客户状态
        HkOpenCustomerStatusEnum hkOpenCustomerStatusEnum = HkOpenAppCustomerStatusEnumHandler.HIDE_ACCOUNT_DEPOSIT_AREA_HANDLER.handleRequest(hkCustNo, hkCustInfoDTO, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);

        // 设置客户状态 0-正常 1-注销 2-休眠 3-注册 4-开户申请成功
        result.setCustState(hkCustInfoDTO.getCustState());
        
        // 设置开户流程状态
        if (null != hkOpenCustomerStatusEnum) {
            result.setOpenDepositsStatus(Integer.valueOf(hkOpenCustomerStatusEnum.getCode()).toString());
        }
        
        // 设置开户流程标识
        if (hkOpenAccOrderInfoDTO != null) {
            result.setOpenAcctStepFlag(hkOpenAccOrderInfoDTO.getOpenAcctStep());
        }
        
        // 设置登录是否激活
        result.setLoginActivate(Constants.ONE.equals(hkCustInfoDTO.getCustLoginPasswdType()) ? YesNoEnum.NO.getCode() : YesNoEnum.YES.getCode());
        
        // 设置交易是否激活 (交易密码=2-未设置 or 交易密码=1-重置状态 or 交易密码为空)时未激活
        boolean transactionResult = CustTxPasswdTypeEnum.UNSETTLED.getCode().equals(hkCustInfoDTO.getCustTxPasswdType())
                || CustTxPasswdTypeEnum.RESET.getCode().equals(hkCustInfoDTO.getCustTxPasswdType())
                || StringUtils.isBlank(hkCustInfoDTO.getCustTxPasswdType());
        result.setTransactionActivation(transactionResult ? YesNoEnum.NO.getCode() : YesNoEnum.YES.getCode());
        
        // 设置绑卡数量
        result.setBindCardCount(CollectionUtils.isEmpty(hkBankCardInfoDTOList) ? Constants.ZERO : String.valueOf(hkBankCardInfoDTOList.size()));
        
        log.info("客户状态查询完成，香港客户号：{}，客户状态：{}", hkCustNo, result.getCustState());
        
        return result;
    }
}
