package com.howbuy.crm.cgi.extservice.vo.balance;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 查询储蓄罐资产响应类
 * @author: jinqing.rao
 * @date: 2025-06-23 14:00:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryPiggyAssetVO extends Body implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否显示资产 [资产小眼睛]
     */
    private String showAsset;

    /**
     * 储蓄罐资产
     */
    private String piggyAsset;

    /**
     * 储蓄罐收益
     */
    private String piggyIncome;

    /**
     * 储蓄罐收益计算状态
     */
    private String piggyIncomeCalStatus;

    /**
     * 储蓄罐签约状态 0-未签约1-已签约2-已暂停
     */
    private String piggySignStatus;

    /**
     * 签署的储蓄罐基金
     */
    private String piggySignFundCode;

    /**
     * 储蓄罐在途交易金额
     */
    private String piggyInTransitTradeAmt;

    /**
     * 储蓄罐在途交易笔数
     */
    private String piggyInTransitTradeCount;

    /**
     * 储蓄罐在途交易订单号
     */
    private String piggyInTransitTradeDealNo;

    /**
     * 储蓄罐在途交易资金笔数
     */
    private String piggyInTransitTradeCapitalCount;

    /**
     * 储蓄罐在途交易资金订单号
     */
    private String piggyInTransitTradeCapitalDealNo;

    /**
     * 储蓄罐持仓列表
     */
    private List<PiggyAssetFundListVO> piggyAssetFundListVO;
} 