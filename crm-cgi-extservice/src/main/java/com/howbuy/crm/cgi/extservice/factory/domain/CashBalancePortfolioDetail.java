package com.howbuy.crm.cgi.extservice.factory.domain;

import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.CurrencyBalanceDetailDTO;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;


@Getter
@Builder
public class CashBalancePortfolioDetail implements PortfolioDetail{
    /**
     * 总资产
     */
    private BigDecimal totalAsset;      // 总资产

    /**
     * 全委总现金资产
     */
    private BigDecimal totalFullAsset;

    /**
     * 全委总冻结金额
     */
    private BigDecimal fullTotalFrozenAmt;

    /**
     * 全委换汇后总可用余额
     */
    private BigDecimal fullTotalAvailableBalance;

    /**
     * 非全委总现金资产
     */
    private BigDecimal totalNonFullAsset;

    /**
     * 非全委总冻结金额
     */
    private BigDecimal nonFullTotalFrozenAmt;

    /**
     * 非全委换汇后总可用余额
     */
    private BigDecimal nonFullTotalAvailableBalance;

    /**
     * 全委基金交易账号币种余额明细列表
     */
    private List<CurrencyBalanceDetailDTO> fullCurrencyBalanceList;

    /**
     * 非全委基金交易账号币种余额明细列表
     */
    private List<CurrencyBalanceDetailDTO> nonFullCurrencyBalanceList;
}