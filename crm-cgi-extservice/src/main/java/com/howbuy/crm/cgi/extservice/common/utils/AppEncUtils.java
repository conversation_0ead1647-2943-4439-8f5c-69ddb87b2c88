/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils;

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import crm.howbuy.base.utils.DesUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: APP加解密工具类
 * <AUTHOR>
 * @date 2024/5/14 17:17
 * @since JDK 1.8
 */
@Slf4j
public class AppEncUtils {

    /**
     * @description: 香港客户号解密
     * @param value
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/5/26 14:20
     * @since JDK 1.8
     */
    public static String decrypt(String value) {
        if(StringUtils.isBlank(value)){
            return null;
        }
       return DesUtil.decrypt(value, Constants.HK_APP_CUST_NO_KEY);
    }

    /**
     * @description: 香港客户号加密
     * @param value
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/5/26 14:20
     * @since JDK 1.8
     */
    public static String encrypt(String value) {
        if(StringUtils.isBlank(value)){
            return null;
        }
        return DesUtil.encrypt(value, Constants.HK_APP_CUST_NO_KEY);
    }

    /**
     * @description: 一账通解密
     * @param hboneNo
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/5/26 14:18
     * @since JDK 1.8
     */
    public static String decryptHboneNo(String hboneNo) {
        try {
            return DesUtil.decrypt(hboneNo, Constants.HBONENO_DES_KEY);
        } catch (Exception e) {
            log.info("AppEncUtils>>>decryptHboneNo>>>解密失败 hboneNo:{}", hboneNo);
            throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "解密失败");
        }
    }

    /**
     * @description: 一账通加密
     * @param hboneNo
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/5/26 14:19
     * @since JDK 1.8
     */
    public static String encryptHboneNo(String hboneNo) {
        if (StringUtils.isBlank(hboneNo)) {
            return null;
        }
        try {
            return DesUtil.encrypt(hboneNo, Constants.HBONENO_DES_KEY);
        } catch (Exception e) {
            log.info("getEncryptHboneNo>>加密一账通号失败,hboneNo:{}", hboneNo);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }
}
