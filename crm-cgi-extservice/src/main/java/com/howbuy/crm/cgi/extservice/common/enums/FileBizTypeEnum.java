package com.howbuy.crm.cgi.extservice.common.enums;

import com.howbuy.crm.cgi.extservice.common.constant.ExternalConstant;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: 文件的业务类型
 *  Code 指具体的业务编码
 *  FileDir 指具体的文件目录
 *  StoreConfig 指具体的存储配置
 *  Desc 描述
 * @author: jinqing.rao
 * @date: 2024/5/11 9:20
 * @since JDK 1.8
 */
public enum FileBizTypeEnum {

    /**
     * 打款凭证
     */
    PAYMENT_VOUCHER_SAVE("11", "paymentVoucher", ExternalConstant.PAY_VOUCHER_TEMP_STORE_CONFIG, "打款凭证临时存储"),

//    PAYMENT_VOUCHER_READ("12", "", ExternalConstant.PAY_VOUCHER_DOC_STORE_CONFIG, "打款凭证正式存储"),

    /**
     * 基金申购/赎回合同存储配置,在海外中台dtms-order生成写入,CGI层负者读取展示
     */
    FUND_CONTRACT_FILE("13", "", ExternalConstant.FUND_CONTRACT_FILE_STORE_CONFIG, "基金申购/赎回合同存储配置"),


    /**
     * 交易打款凭证存储在CRM的数据  只有读取，没有写入
     */
    CRM_PAYMENT_VOUCHER_FILE("14", "", null, "交易打款凭证存储在CRM的数据"),
    ;

    /**
     * 文件类型编码
     */
    private final String code;

    /**
     * 文件目录
     */
    private final String fileDir;

    /**
     * 文件存储配置
     */
    private final String storeConfig;

    /**
     * 描述
     */
    private final String desc;

    private FileBizTypeEnum(String code, String fileDir, String storeConfig, String desc) {
        this.code = code;
        this.fileDir = fileDir;
        this.storeConfig = storeConfig;
        this.desc = desc;
    }

    public static FileBizTypeEnum getFileBizTypeEnumByCode(String code) {
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (FileBizTypeEnum fileBizTypeEnum : FileBizTypeEnum.values()) {
            if (fileBizTypeEnum.getCode().equals(code)) {
                return fileBizTypeEnum;
            }
        }
        return null;
    }

    /**
     *
     * 通过Code获取对应的文件目录
     */
    public static FileBizTypeEnum getFileDirByCode(String code) {
        for (FileBizTypeEnum fileBizTypeEnum : FileBizTypeEnum.values()) {
            if (fileBizTypeEnum.getCode().equals(code)) {
                return fileBizTypeEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }
    public String getFileDir() {
        return fileDir;
    }
    public String getDesc() {
        return desc;
    }

    public String getStoreConfig() {
        return storeConfig;
    }
}
