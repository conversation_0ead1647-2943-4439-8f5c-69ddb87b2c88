/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.extservice.vo.dtmsorder.BankCardVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HkPurchasePageInfoVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/2/22 18:51
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class TradeRecordDetailInfoVO extends AccountBaseVO implements Serializable {
    private static final long serialVersionUID = -7631215307877174484L;
    /** 基金名称 */
    private String fundName;

    /** 基金代码 */
    private String fundCode;

    /** 业务类型代码 */
    private String mBusiCode;

    /**
     * 业务类型描述
     */
    private String mBusiCodeDesc;

    /**
     * 金额/份额的 值
     */
    @Deprecated
    private String num;

    /**
     * 单位
     */
    @Deprecated
    private String unit;

    /**
     * 交易状态
     */
    private String tradeStatus;

    /**
     * 交易状态文字颜色 (0-默认、1-橘色、2-红色)   (ff8800 、ff8800、f14a51、)
     */
    private String tradeStatusColor;

    /** 申请时间 */
    private String appDt;

    /** 申请时间,前端展示 时间格式 yyyy-MM-dd HH:mm */
    private String appDisDt;

    /** 赎回去向 (赎回,分红,强赎的资金去向 0-银行卡;1-储蓄罐) */
    private String redeemDirection;


    /**
     *  资金账号  赎回去向 是银行卡的时候 有值
     */
    private String cpAcctNo;

    /**
     * 赎回去向 状态值 1 : 本人银行卡  2 ： 现金余额 3 ： 储蓄罐
     */
    private String redeemDirectionValue;
    /**
     * 赎回方式 0:按金额 1: 按份额
     */
    private String redeemType;


    /** 买入方式列表 */
    private String paymentType;

    /** 关联银行卡列表 */
    private List<BankCardVO> mutiCardList;

    /** 申请份额 */
    private String appVol;

    /** 申请金额 */
    private String appAmt;

    /** 确认金额 */
    private String ackAmt;

    /** 确认份额 */
    private String ackVol;

    /** 费用 (根据业务类型字段显示交易手续费（赎回费/业绩报酬扣减）) */
    private String fee;

    /** 净值 (根据业务类型字段显示 成交净值/确认净值) */
    private String nav;



    /**
     * 付款状态
     */
    private String payStatus;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 确认日期
     */
    private String ackDt;

    /**
     * 资金募集状态 0--隐藏 1--展示
     */
    private String fundRaisingStatus;


    /**
     * 开放日期
     */
    private String openDt;

    /**
     * 交易日期
     */
    private String tradeDt;


    /**
     * 是否展示上传凭证入口, 1 : 是 0 ： 否
     */
    private String supportUpload;

    /**
     * 是否上传打款凭证 审核结果 0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
     */
    private String uploadStatus;


    /**
     * 是否支持撤单, 1 : 是 0 ： 否
     */
    private String supportRepeal;

    /**
     * 当前币种描述
     */
    private String currencyDesc;

    /**
     * 是否有详情数据
     */
    private String hasDetail;

    /**
     * 转入产品名称
     */
    private String transferProductName;

    /**
     * 转入确认份额
     */
    private String transferAckVol;

    /**
     * 转入确认金额
     */
    private String transferAckAmt;

    /**
     * 转入确认净值
     */
    private String transferNav;

    /**
     * 转入基金名称
     */
    private String transferFundName;

    /**
     * 转入基金代码
     */
    private String transferFundCode;

    /**
     * 转出币种
     */
    private String transferCurrency;

    /**
     * 转出币种描述
     */
    private String transferCurrencyDesc;


    /**
     * 订单明细详情对象
     */
    private List<TradeRecordMultiDetialInfoVO> tradeRecordMultiDetialInfoVOList;

    /**
     * 关联订单号列表
     */
    private List<String> relationOrderList;

    /**
     * 是否分次Call
     * 1-是、0-否
     */
    private String gradationCall;

    /**
     * 认缴金额
     */
    private String subAmt;


    /**
     * 是否展示修改回款方式入口	 1 是  0 否
     */
    private String showModifyRedeemDirection;

    /**
     * 待回款订单 1 是  0 否
     */
    private String waitRefundOrder;

    /**
     * 是否储蓄罐 1 是 0 否
     */
    private String piggyProduct;

    /**
     * 储蓄罐生效状态 1 未生效  2 已生效  3 已过期
     */
    private String piggyEffectiveStatus;


    /**
     * 用户银行卡列表
     */
    /**
     *  银行卡列表
     */
    private List<FundBankCardVO> bankCardList;


    @Setter
    @Getter
    public static class FundBankCardVO implements Serializable {
        private static final long serialVersionUID = 6945899198946903226L;

        /**
         *  资金账号
         */
        private String cpAcctNo;

        /**
         * 银行卡号掩码
         */
        private String bankAcctMask;

        /**
         * bankName
         */
        private String bankName;

        /**
         * 银行logo
         */
        private String bankLogoUrl;

    }
}