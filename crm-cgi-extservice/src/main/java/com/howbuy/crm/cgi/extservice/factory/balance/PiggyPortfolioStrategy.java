package com.howbuy.crm.cgi.extservice.factory.balance;

import com.howbuy.crm.cgi.extservice.common.annotation.FundTypeSupport;
import com.howbuy.crm.cgi.extservice.common.enums.balance.HkCustBalanceDisPalyCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.utils.balance.BalanceUtils;
import com.howbuy.crm.cgi.extservice.factory.content.BalanceContent;
import com.howbuy.crm.cgi.extservice.factory.domain.PiggyBalancePortfolio;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.AcctBalanceDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance.QueryBalanceDTO;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@FundTypeSupport(HkCustBalanceDisPalyCategoryEnum.NON_FULL_PIGGY)
public class PiggyPortfolioStrategy extends AbstractBalancePortfolioStrategy implements BalancePortfolioStrategy<PiggyBalancePortfolio> {

    @Override
    public Class<PiggyBalancePortfolio> getSupportedType() {
        return PiggyBalancePortfolio.class;
    }
    @Override
    public PiggyBalancePortfolio calculate(BalanceContent content) {

        if(CollectionUtils.isEmpty(content.getFundTxAcctNoDTOList())){
            log.info("没有非全委的基金交易账号,非全委的储蓄罐策略不执行，返回 空数据 , hkCustNo :{} ",content.getHkCustNo());
           return PiggyBalancePortfolio.builder().build();
        }
        List<HkFundTxAcctDTO> hkFundTxAcctDTOS = content.getFundTxAcctNoDTOList().stream()
                .filter(m -> StringUtils.isNotBlank(m.getFundTxAccType()) && FundTxAcctTypeEnum.NON_FULL.getCode().equals(m.getFundTxAccType()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(hkFundTxAcctDTOS)){
            log.error("用户有基金交易账号,没有非全委的基金交易账号,非全委的储蓄罐策略不执行，返回 空数据 , hkCustNo :{} ",content.getHkCustNo());
        }

        List<String> nonFullFundTxAcctNoList = BalanceUtils.extractFieldList(hkFundTxAcctDTOS, HkFundTxAcctDTO::getFundTxAcctNo);

        // 获取储蓄罐基金
        List<FundBasicInfoDTO> fundBasicInfoDTOList = queryFundBasicInfoOuterService.queryPiggyFundBasicInfoList();
        List<String> piggyFundCodeList = BalanceUtils.extractFieldList(fundBasicInfoDTOList, FundBasicInfoDTO::getFundCode);
        // 查询持仓信息接口,基金交易账号类型、是否储蓄罐统计基金、储蓄罐、全委三个维度的资产、收益、收益计算状态
        QueryBalanceDTO queryBalanceDTO = new QueryBalanceDTO();
        queryBalanceDTO.setHkCustNo(content.getHkCustNo());
        queryBalanceDTO.setFundTxAcctNoList(nonFullFundTxAcctNoList);
        queryBalanceDTO.setFundCodeList(piggyFundCodeList);
        queryBalanceDTO.setDisPlayCurrency(content.getDisPlayCurrencyCode());
        queryBalanceDTO.setDisCodeList(getDisCodeList());
        // 查询非全委的储蓄罐数据
        AcctBalanceDTO acctBalanceDTO = queryBalanceOuterService.queryHkCustBalance(queryBalanceDTO);
        // 封装数据
        return getPiggyBalancePortfolio(acctBalanceDTO);
    }
}