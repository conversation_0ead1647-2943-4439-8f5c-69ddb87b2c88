/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account.hkapp;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/2 14:03
 * @since JDK 1.8
 */
@Setter
@Getter
public class ChangeRedeemDirectionRequest implements Serializable {

    private static final long serialVersionUID = -2699118042209343168L;

    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    @NotBlank(message = "订单号不能为空")
    private String dealNo;

    /**
     * 赎回方向 1-回银行卡|电汇、2-留账好买香港账户、3-回海外储蓄罐、4-支票
     */
    @NotBlank(message = "赎回方向不能为空")
    private String redeemDirection;

    /**
     * 资金账号  1-回银行卡|电汇时必须
     */
    private String cpAcctNo;

    /**
     * 交易密码
     */
    @NotBlank(message = "交易密码不能为空")
    private String txPassword;
}
