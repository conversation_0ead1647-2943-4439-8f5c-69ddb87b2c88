package com.howbuy.crm.cgi.extservice.request.papersign;

/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: 提交纸质签名请求类
 * @author: 陈杰文
 * @date: 2025-06-17 17:02:15
 * @since JDK 1.8
 */
@Setter
@Getter
public class SubmitPaperSignRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 文件名称
     */
    @NotBlank(message = "文件名称不能为空")
    private String fileName;

    /**
     * 缩略图地址
     */
    @NotBlank(message = "缩略图地址不能为空")
    private String thumbnailUrl;

    /**
     * 文件类型
     */
    @NotBlank(message = "文件类型不能为空")
    private String fileFormatType;
} 