/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkaccount;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.ParamsException;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.request.account.AdvertisementRequest;
import com.howbuy.crm.cgi.extservice.request.account.IdTypeRequest;
import com.howbuy.crm.cgi.extservice.request.account.OpenTranslateRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.*;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.HkAssetStatusRequest;
import com.howbuy.crm.cgi.extservice.request.dtmsproduct.QueryProductContractRequest;
import com.howbuy.crm.cgi.extservice.service.hkaccount.HkCommonService;
import com.howbuy.crm.cgi.extservice.validator.hkopen.HkOpenAcctValidator;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.*;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.*;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: (个人中心相关接口)
 * @date 2023/11/29 10:13
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/common")
public class HkCommonController {

    @Autowired
    private HkCommonService hkCommonService;

    /**
     * @api {POST} /ext/hkaccount/common/getnotice getNotice()
     * @apiVersion 1.0.0
     * @apiGroup HkInformationController
     * @apiName 获取公告信息接口
     * @apiDescription 获取公告信息接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.productId 产品ID
     * @apiSuccess (响应结果) {String} data.position 位置
     * @apiSuccess (响应结果) {String} data.important 重要程度 1: 一次性关闭 2: 重复显示 3: 不可关闭
     * @apiSuccess (响应结果) {String} data.seq 顺序
     * @apiSuccess (响应结果) {String} data.desc 说明
     * @apiSuccess (响应结果) {String} data.link 链接
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"5T","data":{"important":"IanvCkbPd","productId":"qyvuOF","link":"M1mgaeau","position":"TJvED","seq":"1NtI","desc":"InyD"},"description":"d","timestampServer":"ZsTA"}
     */
    @PostMapping("/getnotice")
    @ResponseBody
    public CgiResponse<ExchangeAnnounVO> getNotice() {
        ExchangeAnnounVO exchangeAnnounVO = hkCommonService.getExchangeAnnoun();
        return CgiResponse.ok(exchangeAnnounVO);
    }

    /**
     * @api {POST} /ext/hkaccount/common/queryproductreport queryproductreport()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName queryproductreport()
     * @apiDescription 查询产品报告接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParamExample 请求体示例
     * {"fundCode":"QywcYZPOf"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.productReportList 产品报告列表
     * @apiSuccess (响应结果) {String} data.productReportList.date 报告日期 (yyyy-MM-dd)
     * @apiSuccess (响应结果) {String} data.productReportList.title 报告名称
     * @apiSuccess (响应结果) {String} data.productReportList.url 报告链接
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"GlM","data":{"productReportList":[{"date":"ViHJh9u6","title":"knn","url":"NSMtJ8JG64"}]},"description":"f","timestampServer":"lTMJe6t"}
     */
    @PostMapping("/queryproductreport")
    @ResponseBody
    public CgiResponse<ProductReportListVO> queryproductreport(@RequestBody QueryProductContractRequest queryProductContractRequest) {
        return CgiResponse.appOk(hkCommonService.getProductReportList(queryProductContractRequest));
    }


    /**
     * @api {POST} /ext/hkaccount/common/queryderivativepdf queryDerivativePdf()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName queryDerivativePdf()
     * @apiDescription 查询cms配置的pdf地址
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.pdfUrl pdf预览地址
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"l9hwx6b","data":{"pdfUrl":"PHzwD"},"description":"PJTB3b","timestampServer":"BEXtbh"}
     */
    @PostMapping("/queryderivativepdf")
    @ResponseBody
    public CgiResponse<CmsPdfVO> queryDerivativePdf() {
        return CgiResponse.appOk(hkCommonService.queryDerivativePdf());
    }


    /**
     * @api {POST} /ext/hkaccount/common/app/getnotice getAppAnnouncementNotice()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName getAppAnnouncementNotice()
     * @apiDescription App获取公告信息接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} position 位置
     * @apiParamExample 请求体示例
     * {"position":"z5sXmFtm6s"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.announcementList
     * @apiSuccess (响应结果) {String} data.announcementList.productId 产品ID
     * @apiSuccess (响应结果) {String} data.announcementList.position 位置
     * @apiSuccess (响应结果) {String} data.announcementList.important 重要程度 1: 一次性关闭 2: 重复显示 3: 不可关闭
     * @apiSuccess (响应结果) {String} data.announcementList.seq 顺序
     * @apiSuccess (响应结果) {String} data.announcementList.desc 说明
     * @apiSuccess (响应结果) {String} data.announcementList.link 链接
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"K0LEnP4URo","data":{"announcementList":[{"important":"wpic2XilF","productId":"pc","link":"fkXqvv9Sv","position":"V9bW","seq":"TnwzU04N","desc":"XpCNwgB1"}]},"description":"n","timestampServer":"SoY5lS"}
     */
    @PostMapping("/app/getnotice")
    public CgiResponse<HkAppAnnouncementVO> getAppAnnouncementNotice(@RequestBody HkAppAnnouncementRequest request) {
        HkAppAnnouncementVO hkAppAnnouncementVO = hkCommonService.getAppAnnouncementNotice(request);
        return CgiResponse.appOk(hkAppAnnouncementVO);
    }


    /**
     * @api {POST} /ext/hkaccount/common/app/queryexamlist queryExamList()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName queryExamList()
     * @apiDescription 查询风测问卷列表
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.examId 问卷ID
     * @apiSuccess (响应结果) {Array} data.baseQuestions 基本信息问题列表
     * @apiSuccess (响应结果) {String} data.baseQuestions.questionId 题目id
     * @apiSuccess (响应结果) {String} data.baseQuestions.question 题目
     * @apiSuccess (响应结果) {String} data.baseQuestions.multipleOption 多选标识 1是 0否
     * @apiSuccess (响应结果) {String} data.baseQuestions.questionClassify 题目类别  暂定 A\B\C\D 基本信息、资产情况、投资经验、投资计划
     * @apiSuccess (响应结果) {String} data.baseQuestions.classifyName 题目类别名称
     * @apiSuccess (响应结果) {Array} data.baseQuestions.historyAnswer 历史缓存答案
     * @apiSuccess (响应结果) {Number} data.baseQuestions.sortNum 排序
     * @apiSuccess (响应结果) {Array} data.baseQuestions.options 选项列表
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"4","data":{"assetsSituationQuestions":[{"multipleOption":"Cq1","questionId":"WMJ0HjWq","question":"6FCIdF8M","options":[],"questionClassify":"McUA","sortNum":5057,"historyAnswer":["eRQyexr"],"classifyName":"5"}],"investmentPlanQuestions":[{"multipleOption":"46XHBDi","questionId":"mNUfBX","question":"xZBLfZTEjM","options":[],"questionClassify":"c6KDQF6rsq","sortNum":1768,"historyAnswer":["G2iW"],"classifyName":"xwE95K"}],"examId":"bTlw9Hp","examType":"tkllvqE","baseQuestions":[{"multipleOption":"SGTk9JAU8Y","questionId":"ldL915ZU","question":"LWko92","options":[],"questionClassify":"ZTVuIQZ","sortNum":3853,"historyAnswer":["W9wUGQpu"],"classifyName":"j"}],"investmentExperienceQuestions":[{"multipleOption":"xdMKzKPx","questionId":"V7v7","question":"qD","options":[],"questionClassify":"4ijOqJeK5","sortNum":5111,"historyAnswer":["IMwl1j"],"classifyName":"L6"}],"age":"iz3"},"description":"g9Wa","timestampServer":"v3"}
     */
    @PostMapping("/app/queryexamlist")
    @ResponseBody
    public CgiResponse<RiskExamListVO> queryExamList() {
        return CgiResponse.appOk(hkCommonService.queryExamList());
    }


    /**
     * @api {POST} /ext/hkaccount/common/app/calculatederanswer calculateDerivativeAnswer()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName calculateDerivativeAnswer()
     * @apiDescription 问卷结果的试算接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {Object} answerMap 答案列表
     * @apiParam (请求体) {String} examId 问卷ID
     * @apiParamExample 请求体示例
     * {"examId":"laKkE","answerMap":{}}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Number} data.derivativeKnowledgeScore 计算得到的分数
     * @apiSuccess (响应结果) {String} data.derivativeKnowledge 衍生金融工具知识 0-无 1-有
     * @apiSuccess (响应结果) {Array} data.answerVOList 试算结果列表接口
     * @apiSuccess (响应结果) {String} data.answerVOList.optionId 题目ID
     * @apiSuccess (响应结果) {Number} data.answerVOList.score 分数
     * @apiSuccess (响应结果) {String} data.answerVOList.option 历史选项
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"07GomWMI","data":{"derivativeKnowledgeScore":1313.252555718214,"derivativeKnowledge":"l","answerVOList":[{"score":"yUD6e","optionId":"WuFZq6k7Xe","option":"v8qzTi5"}]},"description":"37Tr88KIe","timestampServer":"X48gDuK"}
     */
    @PostMapping("/app/calculatederanswer")
    public CgiResponse<CalculateDerivativeAnswerVO> calculateDerivativeAnswer(@RequestBody HkCalculateDerAnswerRequest request) {
        // 1.先试算
        CalculateDerivativeAnswerVO calculateDerivativeAnswerVO = hkCommonService.calculateDerivativeAnswer(request);
        // 2.提交结果
        hkCommonService.submitAnswer(request);

        return CgiResponse.appOk(calculateDerivativeAnswerVO);
    }



    /**
     * @api {POST} /ext/hkaccount/common/app/advertising getAppAdvertising()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName getAppAdvertising()
     * @apiDescription App广告位查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号 加密
     * @apiParam (请求体) {String} position 广告的信息位置 APP个人中心广告位 ： 1
     * @apiParam (请求体) {String} length 图片的长度
     * @apiParam (请求体) {String} width 图片的宽度
     * @apiParamExample 请求体示例
     * {"hkCustNo":"hjo1eBmMPY","length":"Zeo","width":"wm","position":"EM5Q"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.advertisings 广告信息列表
     * @apiSuccess (响应结果) {String} data.advertisings.adTitle 标题
     * @apiSuccess (响应结果) {String} data.advertisings.adOrder 排序
     * @apiSuccess (响应结果) {String} data.advertisings.adImg 图片地址
     * @apiSuccess (响应结果) {String} data.advertisings.onClick 广告URL
     * @apiSuccess (响应结果) {String} data.advertisings.verifyAccount 用于判断是否校验开户，枚举包括 0-无需开户/1-需开户；
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"m6","data":{"advertisings":[{"onClick":"Sj","adOrder":"l5NZ6L1LnO","verifyAccount":"2JFWCiiz","adImg":"mw1P536Wo","adTitle":"VxRflU"}]},"description":"h","timestampServer":"o4sNL"}
     */
    @PostMapping("/app/advertising")
    public CgiResponse<HkAcctAdvertisVO> getAppAdvertising(@RequestBody HkAcctAppAdvertisRequest request) {
        HkAcctAdvertisVO hkAcctAdvertisVO = hkCommonService.getAppAdvertising(request);
        return CgiResponse.appOk(hkAcctAdvertisVO);
    }

    /**
     * @api {POST} /ext/hkaccount/common/getadvertisement 获取广告接口
     * @apiVersion 1.0.0
     * @apiGroup HkInformationController
     * @apiName 获取广告接口
     * @apiDescription 获取广告接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} appVersion 版本ID
     * @apiParam (请求体) {String} systemType 系统类型
     * @apiParamExample 请求体示例
     * {"appVserionId":"parEHI9","systemType":"YiV7AFW"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.list 广告的list对象
     * @apiSuccess (响应结果) {String} data.list.id 广告ID 唯一标识
     * @apiSuccess (响应结果) {String} data.list.adTitle 标题
     * @apiSuccess (响应结果) {String} data.list.onClick 广告url
     * @apiSuccess (响应结果) {String} data.list.cId 手机系统类型
     * @apiSuccess (响应结果) {String} data.list.imgWidth 广告位宽度
     * @apiSuccess (响应结果) {String} data.list.imgHeight 广告位高度
     * @apiSuccess (响应结果) {String} data.list.adImg 广告图片地址
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"ad","data":{"list":[{"onClick":"ZmIWC","imgWidth":"vcGpl","adImg":"fdU","adTitle":"cMJa","imgHeight":"pQWRLVZhd","id":"dy","cId":"cb4"}]},"description":"Agq50i","timestampServer":"uoM2"}
     */
    @PostMapping("/getadvertisement")
    @ResponseBody
    public CgiResponse<AdMobileListVO> getAdvertisement(@RequestBody AdvertisementRequest request) {
        return CgiResponse.ok(hkCommonService.transAdMobileDtoToVO(hkCommonService.getAdMobileList(request)));
    }


    /**
     * @api {POST} /ext/hkaccount/common/getemailsuffixlist getEmailSuffixList()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName 获取邮箱后缀列表接口
     * @apiDescription 获取邮箱后缀列表接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.emailSuffixList 邮箱后缀列表
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"xKC7","data":{"emailSuffixList":["id9PX1Rxt"]},"description":"n","timestampServer":"v"}
     */
    @PostMapping("/getemailsuffixlist")
    @ResponseBody
    public CgiResponse<EmailSuffixListVO> getEmailSuffixList() {
        EmailSuffixListVO emailSuffix = hkCommonService.getEmailSuffix();
        //海外App成功返回 0000
        if(RequestUtil.isHkAppLogin()){
            return CgiResponse.appOk(emailSuffix);
        }
        return CgiResponse.ok(emailSuffix);
    }

    /**
     * @api {POST} /ext/hkaccount/common/saveassetstatus saveAssetStatus()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName saveAssetStatus()
     * @apiDescription 保存资产显示状态接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} showAsset
     * @apiParamExample 请求参数示例
     * showAsset=RU
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"mJlSDvXUUj","description":"CJ","timestampServer":"x4o"}
     */
    @PostMapping("/saveassetstatus")
    @ResponseBody
    public CgiResponse<Body> saveAssetStatus(@RequestBody HkAssetStatusRequest request) {
        hkCommonService.saveAssetStatus(request.getShowAsset());
        if (Objects.isNull(request.getType())) {
            return CgiResponse.appOk(null);
        } else {
            return CgiResponse.ok(null);
        }
    }


    /**
     * @api {POST} /ext/hkaccount/common/getsamplefile getSampleFile()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName getSampleFile()
     * @apiDescription 获取示例文件地址
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.chinaOpenAccountSample 中国大陆居民开户材料示例
     * @apiSuccess (响应结果) {String} data.foreignOpenAccountSample 非中国大陆居民开户材料示例
     * @apiSuccess (响应结果) {String} data.employerAgreementSample 雇主书面同意书的示例
     * @apiSuccess (响应结果) {String} data.assetProofSample 资产证明示例
     * @apiSuccess (响应结果) {String} data.investorNoticeSample 个人专业投资者通知示例
     * @apiSuccess (响应结果) {String} data.investorDeclarationSample 个人专业投资者声明示例
     * @apiSuccess (响应结果) {String} data.currentAddressProofSample 现居地址、通讯地址证明示例
     * @apiSuccess (响应结果) {String} data.jointAccountSupplementSample 联名账户补充材料示例
     * @apiSuccess (响应结果) {String} data.bankCardPhotoSample 银行卡照片示例
     * @apiSuccess (响应结果) {String} data.paymentVoucherSample 打款凭证示例
     * @apiSuccess (响应结果) {String} data.termsAndConditions 条款及细则
     * @apiSuccess (响应结果) {String} data.complesProductStatement 复杂产品警告声明
     * @apiSuccess (响应结果) {String} data.personInformationStatement 个人资料收集声明
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"47AhRuzaE","data":{"investorNoticeSample":"CQt2","currentAddressProofSample":"SSpmpRN","assetProofSample":"jM","jointAccountSupplementSample":"z","paymentVoucherSample":"uAXUDgQ","complesProductStatement":"wNbwnC","employerAgreementSample":"vKG","bankCardPhotoSample":"vYD4HQ","chinaOpenAccountSample":"giGa","foreignOpenAccountSample":"ILc9An","investorDeclarationSample":"pzHB","termsAndConditions":"a"},"description":"Y","timestampServer":"w"}
     */
    @PostMapping("/getsamplefile")
    @ResponseBody
    public CgiResponse<SampleFileVO> getSampleFile() {
        return CgiResponse.ok(hkCommonService.getSampleFileVO());
    }


    /**
     * @api {POST} /ext/hkaccount/common/getidtypelist getIdTypeList()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName 地区证件类型列表接口
     * @apiDescription 地区证件类型列表接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} countryCode 国家代码
     * @apiParamExample 请求参数示例
     * countryCode=52
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.idTypeVOList 证件列表
     * @apiSuccess (响应结果) {String} data.idTypeVOList.idType 证件类型
     * @apiSuccess (响应结果) {String} data.idTypeVOList.idTypeDesc 证件类型描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Ew3v","data":{"idTypeVOList":[{"idType":"NmseVo","idTypeDesc":"Kv1rTL"}]},"description":"KJsixNgx","timestampServer":"x9pyfG"}
     */
    @PostMapping("/getidtypelist")
    @ResponseBody
    public CgiResponse<IdTypeListVO> getIdTypeList(@RequestBody IdTypeRequest idTypeRequest) {
        IdTypeListVO idTypeListVO = new IdTypeListVO();
        idTypeListVO.setIdTypeVOList(hkCommonService.getIdTypeVOList(idTypeRequest.getCountryCode(),idTypeRequest.getBizCode()));
        //海外App的返回 0000成功状态
        if(RequestUtil.isHkAppLogin()){
            return CgiResponse.appOk(idTypeListVO);
        }
        return CgiResponse.ok(idTypeListVO);
    }

    /**
     * @api {POST} /ext/hkaccount/common/getcountry getCountry()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName 国家列表接口
     * @apiDescription 国家列表接口
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.indexList 索引列表
     * @apiSuccess (响应结果) {Array} data.countryVOList 国家列表
     * @apiSuccess (响应结果) {String} data.countryVOList.index 索引
     * @apiSuccess (响应结果) {String} data.countryVOList.countryCode 国家代码
     * @apiSuccess (响应结果) {String} data.countryVOList.chineseName 国家中文名称
     * @apiSuccess (响应结果) {String} data.countryVOList.englishName 国家的英文名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"53nVSx3","data":{"countryVOList":[{"countryCode":"vHMEtnJF","chineseName":"OAH","index":"qggqa"}],"indexList":["NAYWKF"]},"description":"x28CMY","timestampServer":"QJLqB3C"}
     */
    @PostMapping("/getcountry")
    @ResponseBody
    public CgiResponse<CountryListVO> getCountry() {
        CountryListVO countryList = hkCommonService.getCountryList();
        if(RequestUtil.isHkAppLogin()){
            return CgiResponse.appOk(countryList);
        }
        return CgiResponse.ok(countryList);
    }

    /**
     * @api {POST} /ext/hkaccount/common/getidtypedesc 获取证件类型文案
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName 获取证件类型文案
     * @apiDescription 获取证件类型文案
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} idAreaCode 证件地区码
     * @apiParam (请求体) {String} idType 证件类型
     * @apiParamExample 请求体示例
     * {"idType":"8","idAreaCode":"nkFsNtxt5Z"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.idTypeDesc 证件类型描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"t","data":{"idTypeDesc":"TCL1hIVWoK"},"description":"OJ","timestampServer":"RMNIgi0NK"}
     */
    @PostMapping("/getidtypedesc")
    @ResponseBody
    public CgiResponse<IdTypeDescVO> getIdTypeDesc(@RequestBody IdTypeRequest idTypeRequest) {
        return CgiResponse.ok(hkCommonService.getIdTypeDesc(idTypeRequest));
    }

    /**
     * @api {POST} /ext/hkaccount/common/translatetopinyin translateToPinYin()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName translateToPinYin()
     * @apiDescription 中文转拼音接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {Array} openTranslateInfoList 请求参数
     * @apiParam (请求体) {String} openTranslateInfoList.uuidKey 中文字符串对应的唯一key,前端默认生成,方便获取翻译后的拼音
     * @apiParam (请求体) {String} openTranslateInfoList.chinese 需要翻译的中文字符串
     * @apiParamExample 请求体示例
     * {"openTranslateInfoList":[{"chinese":"M2vtq","uuidKey":"V4gaXjn"}]}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.translateList 翻译后信息
     * @apiSuccess (响应结果) {String} data.translateList.uuidKey 中文字符串对应的唯一key,前端默认生成,方便获取翻译后的拼音
     * @apiSuccess (响应结果) {String} data.translateList.pinyin 翻译后的拼音
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"xWeua","data":{"translateList":[{"uuidKey":"ROMtTk6","pinyin":"NcQ7RK"}]},"description":"G","timestampServer":"fw"}
     */
    @PostMapping("translatetopinyin")
    public CgiResponse<OpenTranslatePinYinVO> translateToPinYin(@RequestBody OpenTranslateRequest request) {
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(hkCommonService.translateToPinYin(request.getOpenTranslateInfoList()));
    }


    /**
     * @api {POST} /ext/hkaccount/common/translatetoenglish 中文转英文接口
     * @apiVersion 1.0.0
     * @apiGroup CommonController
     * @apiName translateToEnglish()
     * @apiDescription 中文转英文接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {Array} chineseList 中文参数
     * @apiParamExample 请求体示例
     * {"chineseList":["cRIrn"]}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.translateList 翻译后信息
     * @apiSuccess (响应结果) {String} timestampServer 耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"qrrxZ","data":{"translateList":["bvwdxEogi"]},"description":"b1IoxUbdz","timestampServer":"HscTR1PMv"}
     */
    @PostMapping("translatetoenglish")
    public CgiResponse<OpenTranslateForeignVO> translateToEnglish(@RequestBody OpenTranslateRequest request) {
        return CgiResponse.ok(hkCommonService.translateToEnglish(request));
    }


    /**
     * @api {GET} /ext/hkaccount/common/splitname 姓名拆分接口
     * @apiVersion 1.0.0
     * @apiGroup CommonController
     * @apiName splitName()
     * @apiDescription 姓名拆分接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} custName
     * @apiParamExample 请求参数示例
     * custName=cfTBvst5
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.familyName 姓
     * @apiSuccess (响应结果) {String} data.firstName 名
     * @apiSuccess (响应结果) {String} timestampServer 业务耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"SJ2Y4kCP","data":{"firstName":"U","familyName":"r"},"description":"59W9Tkd","timestampServer":"VFd"}
     */
    @GetMapping("splitname")
    public CgiResponse<SplitNameVO> splitName(@RequestParam("custName") String custName) {
        BasicDataTypeValidator.validatorStringType(custName);
        return CgiResponse.ok(hkCommonService.splitName(custName));
    }

    /**
     * @api {POST} /ext/hkaccount/common/getcitylist getCityList()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName getCityList()
     * @apiDescription 获取省市区列表接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"gTu4V9Y2be","data":{"cityVOList":[{"mc":"SCN0","szm":"aLGP","dataList":[],"dm":"pXqtQYNI"}]},"description":"DJjZY","timestampServer":"PC6E"}
     */
    @PostMapping("/getcitylist")
    public CgiResponse<ProvinceCityCoutryVO> getCityList() {
        return CgiResponse.ok(hkCommonService.getCityListVO());
    }


    /**
     * @api {GET} /ext/hkaccount/common/querybizenumbytype queryBizEnumByType()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName queryBizEnumByType()
     * @apiDescription 通过业务类型查询业务枚举信息
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} bizType 1：职业信息页业务性质枚举
     * @apiParamExample 请求参数示例
     * bizType=hHYXyv3
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.bizEnumInfoList 枚举参数
     * @apiSuccess (响应结果) {String} data.bizEnumInfoList.bizCode 业务编码
     * @apiSuccess (响应结果) {String} data.bizEnumInfoList.bizDesc 业务描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"4nX9mzmwq3","data":{"bizEnumInfoList":[{"bizDesc":"WiC4x6c","bizCode":"RBlvW9Gp29"}]},"description":"46GVseq3","timestampServer":"zDHC"}
     */
    @GetMapping("/querybizenumbytype")
    public CgiResponse<HkOpenAcctBizEnumVO> queryBizEnumByType(@RequestParam("bizType") String bizType) {
        BasicDataTypeValidator.validatorStringType(bizType);
        return CgiResponse.ok(hkCommonService.queryBizEnumByType(bizType));
    }

    /**
     * @api {GET} /ext/hkaccount/common/downloadPdf downloadPdf()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName downloadPdf()
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} msg
     * @apiParamExample 请求参数示例
     * msg=dyNdIOx
     * @apiSuccess (响应结果) {Object} response
     *
     */
    @GetMapping("downloadPdf")
    public void downloadPdf(@RequestParam("relativeUrl") String relativeUrl, HttpServletResponse response) {
        hkCommonService.downloadPdf(relativeUrl,response);
    }


    /**
     * @api {GET} /ext/hkaccount/common/downloadFile downloadFile()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName downloadFile()
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} relativeUrl
     * @apiParamExample 请求参数示例
     * relativeUrl=e0IqmHLiml
     * @apiSuccess (响应结果) {Object} response
     */
    @GetMapping("downloadFile")
    public void downloadFile(@RequestParam("relativeUrl") String relativeUrl, HttpServletResponse response) {
        hkCommonService.downloadFile(relativeUrl,response);
    }


    /**
     * @api {POST} /ext/hkaccount/common/checkhkappupdate checkHkAppUpdate()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName checkHkAppUpdate()
     * @apiDescription 检查APP是否更新接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} version 版本号
     * @apiParam (请求体) {String} parPhoneModel 主机型
     * @apiParam (请求体) {String} subPhoneModel 子机型
     * @apiParam (请求体) {String} productId 产品ID
     * @apiParam (请求体) {String} channelId 渠道ID
     * @apiParamExample 请求体示例
     * {"productId":"GLOcghaxW","hkCustNo":"2Laom","parPhoneModel":"BSNyE","subPhoneModel":"kjKv7","version":"dJ","channelId":"KEqeQZcBL"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.fileSize 文件大小(单位字节)
     * @apiSuccess (响应结果) {String} data.updateUrl 更新地址
     * @apiSuccess (响应结果) {String} data.updateDesc 更新描述
     * @apiSuccess (响应结果) {String} data.versionNum 版本号
     * @apiSuccess (响应结果) {String} data.versionNeedUpdate 版本是否需要更新(0:通知更新 1:强制更新 2:不通知 3:维护通知 4:检测更新)
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"VGYv2Cbbx","data":{"versionNeedUpdate":"08uHl7dyd","fileSize":"wToWt5UzmX","versionNum":"86JkUJv7K","updateUrl":"d2Qp","updateDesc":"tE"},"description":"hALbU","timestampServer":"XwY5YT0j"}
     */
    @PostMapping("checkhkappupdate")
    public CgiResponse<HkAppUpdateVO> checkHkAppUpdate(@RequestBody HkAppUpdateRequest request) {
        HkOpenAcctValidator.validator(request);
        return CgiResponse.appOk(hkCommonService.checkHkAppUpdate(request));
    }


    /**
     * @api {POST} /ext/hkaccount/common/appuploadfile uploadFile()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName uploadFile()
     * @apiDescription webdav方式上传文件流
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} hkCustNo 香港客户号
     * @apiParam (请求参数) {Array} file 文件流
     * @apiParam (请求参数) {String} imageType 文件类型 不同的类型存储的文件目录不一样
     * @apiParamExample 请求参数示例
     * file=&hkCustNo=Yuhgm&imageType=VaeLg
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.openImageVOList 开户文件上传接口返回值
     * @apiSuccess (响应结果) {String} data.openImageVOList.url 图片地址
     * @apiSuccess (响应结果) {String} data.openImageVOList.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.openImageVOList.exampleFileFormatType 文件类型
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"ysB","data":{"openImageVOList":[{"exampleFileFormatType":"LEoXDvO","url":"hrjN0yPi2","thumbnailUrl":"G0mNI"}]},"description":"TzWlvkmZ","timestampServer":"Z"}
     */
    @PostMapping("appuploadfile")
    public CgiResponse<HkOpenImageVO> uploadFile(HkAppUploadFileRequest request) {
        if(null == request.getFile() || request.getFile().length == 0){
            throw new ParamsException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(),"文件信息流不能为空");
        }
        return CgiResponse.appOk(hkCommonService.uploadFile(request));
    }


    /**
     * @api {POST} /ext/hkaccount/common/remittance/bank queryRemittanceBankInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName queryRemittanceBankInfo()
     * @apiDescription 查询汇款银行账号信息
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"J65fOz97y"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.transferBankInfos 转账银行信息列表
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferBankAcctName 转账账户名称
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferBankName 转账银行名称
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferBankAddress 转账银行地址
     * @apiSuccess (响应结果) {Array} data.transferBankInfos.transferBankAccts 转账银行账号列表
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferSwiftCode 国际汇款识别码
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferBankCode 银行代码
     * @apiSuccess (响应结果) {String} data.transferBankInfos.bankLogoUrl 银行logo
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"NOHw8Pvd5","data":{"transferBankInfos":{"bankLogoUrl":"1F","transferBankCode":"sfyrDl0Sd","transferBankAccts":[],"transferBankName":"FR","transferBankAcctName":"Au4s","transferBankAddress":"my","transferSwiftCode":"41hW"}},"description":"Cc5Z2z","timestampServer":"Y7kyk"}
     */
    @PostMapping("remittance/bank")
    public CgiResponse<HkRemittanceBankVO> queryRemittanceBankInfo(@RequestBody HkRemittanceBankRequest  request) {
        HkOpenAcctValidator.validator(request);
        return CgiResponse.appOk(hkCommonService.queryRemittanceBankInfo(request));
    }

    /**
     * @api {POST} /ext/hkaccount/common/fund/search searchFund()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName searchFund()
     * @apiDescription 交易列表页模糊搜索基金
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} searchValue 搜索关键词(支持基金编码和基金名称模糊查询)
     * @apiParamExample 请求体示例
     * {
     *   "hkCustNo": "1",
     *   "bizType": "1",
     *   "searchValue": "000001"
     * }
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.fundList 基金列表
     * @apiSuccess (响应结果) {String} data.fundList.fundCode 基金编码
     * @apiSuccess (响应结果) {String} data.fundList.fundName 基金名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "000000",
     *   "description": "成功",
     *   "data": {
     *     "fundList": [
     *       {
     *         "fundCode": "000001",
     *         "fundName": "华夏成长混合",
     *         "fundNameEn":"英文名称"
     *       }
     *     ]
     *   },
     *   "timestampServer": "**************"
     * }
     */
    @PostMapping("/fund/search")
    public CgiResponse<FundSearchVO> queryFundBySearchValue(@RequestBody FundSearchRequest request) {
        HkOpenAcctValidator.validator(request);
        return CgiResponse.ok(hkCommonService.queryFundBySearchValue(request));
    }


    /**
     * @api {POST} /ext/hkaccount/common/fundtxcode/list queryTxFundCodeList()
     * @apiVersion 1.0.0
     * @apiGroup HkCommonController
     * @apiName queryTxFundCodeList()
     * @apiDescription 全委账号列表查询
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"XI"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.fundTxCodeList 账号列表
     * @apiSuccess (响应结果) {String} data.fundTxCodeList.fundName 账户名称
     * @apiSuccess (响应结果) {String} data.fundTxCodeList.fundTxCodeType 基金交易账号 0-非全委 1-全委
     * @apiSuccess (响应结果) {String} data.fundTxCodeList.fundTxCode 基金交易账号
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"1YcqdcU0","data":{"fundTxCodeList":[{"fundTxCodeType":"7xwbV","fundTxCode":"a","fundName":"bGzjS"}]},"description":"L","timestampServer":"o9mugx"}
     */
    @PostMapping("/fundtxcode/list")
    public CgiResponse<QueryFundTxAcctNoListVO> queryFundTxAcctNoList(@RequestBody FundTxCodeListRequest request) {
        HkOpenAcctValidator.validator(request);
        return CgiResponse.ok(hkCommonService.queryFundTxAcctNoList(request));
    }
}