/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.piggy;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.piggy.*;
import com.howbuy.crm.cgi.extservice.service.piggy.PiggyDepositService;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherDetailVO;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherDuplicateCheckVO;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherInitPageVO;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyDepositVoucherRecordListVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 海外储蓄罐存入
 * <AUTHOR>
 * @date 2024/7/16 13:16
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/piggy/deposit/")
public class PiggyDepositController {


    @Resource
    private PiggyDepositService piggyDepositService;

    /**
     * @api {POST} /ext/piggy/deposit/payvoucher/queryinitinfo queryPiggyDepositVoucherInitPageInfo()
     * @apiVersion 1.0.0
     * @apiGroup PiggyDepositController
     * @apiName queryPiggyDepositVoucherInitPageInfo()
     * @apiDescription 海外储蓄罐存入-打款凭证页面初始化接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} size 每页大小
     * @apiParamExample 请求体示例
     * {"size":6096,"hkCustNo":"LTbkUTp","page":4400}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.signPiggyStatus 是否签约储蓄罐 0-未签署 1-签署
     * @apiSuccess (响应结果) {String} data.piggyCurrency 储蓄罐币种
     * @apiSuccess (响应结果) {String} data.minAppAmt 储蓄罐起购金额
     * @apiSuccess (响应结果) {String} data.fundAddr 基金简称
     * @apiSuccess (响应结果) {String} data.fundEnName 基金英文名称
     * @apiSuccess (响应结果) {String} data.openStartDt 开放开始日期 yyyymmdd
     * @apiSuccess (响应结果) {String} data.openEndDt 开放结束日期 yyyymmdd
     * @apiSuccess (响应结果) {String} data.tradeDt 交易日期  yyyymmdd
     * @apiSuccess (响应结果) {String} data.currentDt 当前日期 yyyymmdd
     * @apiSuccess (响应结果) {String} data.supportPrebook 是否支持预约
     * @apiSuccess (响应结果) {String} data.payEndDt 打款截止日期 yyyyMMdd
     * @apiSuccess (响应结果) {String} data.payEndTm 打款截止时间 HHmmss
     * @apiSuccess (响应结果) {String} data.fundCode 基金Code
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"NsME","data":{"supportPrebook":"Qd","signPiggyStatus":"ikZWlJ1myq","openStartDt":"7gc9hLkZwJ","minAppAmt":"hA5ro","piggyCurrency":"erxUkh8Xkw","fundAddr":"rGYNGsj","fundEnName":"AHl7d4","tradeDt":"oLohnffr","fundCode":"iHV","payEndTm":"d3nzr","payEndDt":"nY5rlVFC6E","openEndDt":"DJLhIAsVn","currentDt":"d"},"description":"5RjVyoo","timestampServer":"RC"}
     */
    @PostMapping("payvoucher/queryinitinfo")
    public CgiResponse<PiggyDepositVoucherInitPageVO> queryPiggyDepositVoucherInitPageInfo(@RequestBody PiggyBaseParamRequest request) {
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(piggyDepositService.queryPiggyDepositVoucherInitPageInfo(request));
    }


    /**
     * @api {POST} /ext/piggy/deposit/payvoucher/submit submitPiggyDepositVoucher()
     * @apiVersion 1.0.0
     * @apiGroup PiggyDepositController
     * @apiName submitPiggyDepositVoucher()
     * @apiDescription 海外储蓄罐存入-打款凭证页面提交接口
     * @apiParam (请求体) {String} voucherNo 打款凭证号,新增不传值,编辑传值
     * @apiParam (请求体) {String} duplicatePaymentCheck 校验重复提交 1 是 0 否  ,首次需要提交传 1 ，二次确认后不用，传0
     * @apiParam (请求体) {String} cpAcctNo 香港资金账号
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} bankAcctMask 汇款账户掩码
     * @apiParam (请求体) {String} bankName 银行名称
     * @apiParam (请求体) {String} swiftCode 银行swiftCode码值
     * @apiParam (请求体) {String} remitCurrency 汇款账户币种
     * @apiParam (请求体) {String} remitAmt 汇款金额
     * @apiParam (请求体) {String} remark 备注
     * @apiParam (请求体) {String} hbSceneId 防重标识不能为空
     * @apiParam (请求体) {String} agreeCurrencyExchange 是否同意换汇 0-否 1-是
     * @apiParam (请求体) {Array} payVoucherFiles 汇款账户掩码
     * @apiParam (请求体) {String} payVoucherFiles.fileName 文件名称
     * @apiParam (请求体) {String} payVoucherFiles.thumbnailUrl 文件路径
     * @apiParam (请求体) {String} payVoucherFiles.fileFormatType 文件的格式类型
     * @apiParamExample 请求体示例
     * {"remitCurrency":"09","agreeCurrencyExchange":"UEK","bankAcctMask":"iCsIFjhyS","hkCustNo":"AP","swiftCode":"Ul5TozDs","payVoucherFiles":[{"fileName":"s","fileFormatType":"KI","thumbnailUrl":"1"}],"bankName":"TKCTMPXOWR","remark":"U5Ncp1i8W","voucherNo":"Tjt","hbSceneId":"2G7fbJjpQw","cpAcctNo":"477Xp6","remitAmt":"lUIquAo","duplicatePaymentCheck":"tkv9"}
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"hmcuMM","description":"dkAVdx","timestampServer":"mI3"}
     */
    @PostMapping("payvoucher/submit")
    public CgiResponse<Body> submitPiggyDepositVoucher(@RequestBody PiggyDepositVoucherSubmitRequest request) {
        BasicDataTypeValidator.validator(request);
        piggyDepositService.submitPiggyDepositVoucher(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /ext/piggy/deposit/payvoucher/duplicate/check piggyDepositVoucherDuplicateCheck()
     * @apiVersion 1.0.0
     * @apiGroup PiggyDepositController
     * @apiName piggyDepositVoucherDuplicateCheck()
     * @apiDescription 打款凭证重复Check校验
     * @apiParam (请求体) {String} cpAcctNo 香港资金账号
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} bankAcctMask 汇款账户掩码
     * @apiParam (请求体) {String} bankName 银行名称
     * @apiParam (请求体) {String} swiftCode 银行swiftCode码值
     * @apiParam (请求体) {String} remitCurrency 汇款账户币种
     * @apiParam (请求体) {String} remitAmt 汇款金额
     * @apiParamExample 请求体示例
     * {"remitCurrency":"b1xMM7iCl","bankAcctMask":"fySp5HfW","hkCustNo":"RPF","swiftCode":"zoGdHSYs","cpAcctNo":"4MNOggEM","bankName":"HWhorf2","remitAmt":"mwL2vy"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.duplicateNum 重提打款凭证个数
     * @apiSuccess (响应结果) {Array} data.duplicateVoucherTypeList 重复类型
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"aM2zU","data":{"duplicateVoucherTypeList":["Tc"],"duplicateNum":"MVGBga"},"description":"2","timestampServer":"r"}
     */
    @PostMapping("payvoucher/duplicate/check")
    public CgiResponse<PiggyDepositVoucherDuplicateCheckVO> piggyDepositVoucherDuplicateCheck(@RequestBody PiggyDepositVoucherDuplicateCheckRequest request) {
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(piggyDepositService.piggyDepositVoucherDuplicateCheck(request));
    }



    /**
     * @api {POST} /ext/piggy/deposit/payvoucher/querypayrecorddetail queryPiggyDepositVoucherRecordDetail()
     * @apiVersion 1.0.0
     * @apiGroup PiggyDepositController
     * @apiName queryPiggyDepositVoucherRecordDetail()
     * @apiDescription 海外储蓄罐存入-打款凭证页面详情接口
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} voucherNo 打款凭证订单号
     * @apiParamExample 请求体示例
     * {"voucherNo":"KhkWwz7KI","hkCustNo":"ta"}
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.bankAcctMask 银行卡号掩码
     * @apiSuccess (响应结果) {String} data.bankLogoUrl 银行图标的url
     * @apiSuccess (响应结果) {String} data.bankName 银行中文名称(没有获取英文名称)
     * @apiSuccess (响应结果) {String} data.remitCurrency 汇款币种代码
     * @apiSuccess (响应结果) {String} data.remitCurrencyDesc 汇款币种描述
     * @apiSuccess (响应结果) {String} data.remitAmt 汇款金额
     * @apiSuccess (响应结果) {String} data.remark 备注
     * @apiSuccess (响应结果) {String} data.payVoucherState 打款凭证状态
     * @apiSuccess (响应结果) {String} data.swiftCode 银行swiftCode
     * @apiSuccess (响应结果) {String} data.cpAcctNo 汇款资金账号
     * @apiSuccess (响应结果) {Array} data.payVoucherImages 打款凭证图片列表
     * @apiSuccess (响应结果) {String} data.payVoucherImages.id 文件ID
     * @apiSuccess (响应结果) {String} data.payVoucherImages.url 图片地址
     * @apiSuccess (响应结果) {String} data.payVoucherImages.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.payVoucherImages.fileName 文件名称
     * @apiSuccess (响应结果) {String} data.payVoucherImages.exampleFileFormatType 文件类型
     * @apiSuccess (响应结果) {Array} data.checkResult 审核原因
     * @apiSuccess (响应结果) {String} data.checkResult.fileType 字段类型描述
     * @apiSuccess (响应结果) {String} data.checkResult.fileName 字段名称
     * @apiSuccess (响应结果) {String} data.checkResult.reason 错误原因
     * @apiSuccess (响应结果) {String} data.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.whetherSignPiggy 是否签约储蓄罐  (埋点需要)
     * @apiSuccess (响应结果) {String} data.duplicatePayment 是否重复打款凭证 1 是  0 否
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"1JEv","data":{"bankLogoUrl":"HjTFG7QY","remitCurrency":"cooen","remitCurrencyDesc":"vfcbTI","bankAcctMask":"08J","swiftCode":"CnHBs","bankName":"NFem2","remark":"tDDOZMlaH","whetherSignPiggy":"olu","checkResult":[{"reason":"cWmj5sxhj","fileName":"rpmOv","fileType":"8M"}],"fundCode":"1cFIxt","payVoucherImages":[{"fileName":"SQj05JgcMW","id":"ZzpJ","exampleFileFormatType":"fu7N1","url":"HHLejH2SY","thumbnailUrl":"93jvqZL73U"}],"cpAcctNo":"tT0kSPaNjK","remitAmt":"BwTnJwO","duplicatePayment":"zm","payVoucherState":"WPo"},"description":"6otW9DWTq3","timestampServer":"wV1YXog9M8"}
     */
    @PostMapping("payvoucher/querypayrecorddetail")
    public CgiResponse<PiggyDepositVoucherDetailVO> queryPiggyDepositVoucherRecordDetail(@RequestBody PiggyDepositVoucherDetailRequest request) {
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok( piggyDepositService.queryPiggyDepositVoucherRecordDetail(request));
    }

    /**
     * @api {POST} /ext/piggy/deposit/payvoucher/list queryPiggyDepositVoucherRecordList()
     * @apiVersion 1.0.0
     * @apiGroup PiggyDepositController
     * @apiName queryPiggyDepositVoucherRecordList()
     * @apiDescription 打款凭证列表接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {Array} payVoucherType 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
     * @apiParam (请求体) {String} voucherDisStatus 已入账 ：1、已提交 ： 2、入账失败 ：3、重复凭证 ： 4
     * @apiParam (请求体) {String} timePeriod 1 近一个月   2 近半年  3 近一年
     * @apiParam (请求体) {String} voucherStartDt 时间格式  YYYYMMdd   开始时间和结束时间都是必填的
     * @apiParam (请求体) {String} voucherEndDt 时间格式  YYYYMMdd   开始时间和结束时间都是必填的
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} size 每页大小
     * @apiParamExample 请求体示例
     * {"size":492,"voucherEndDt":"V0dLf","hkCustNo":"97y","voucherDisStatus":"iwf5XlOVI","timePeriod":"RpgHN","voucherStartDt":"AlZ6Z9SjJ","page":9448,"payVoucherType":["Pu2NNWkcS"]}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.total 总条数
     * @apiSuccess (响应结果) {Array} data.piggyDepositVoucherRecordList 打款凭证列表页
     * @apiSuccess (响应结果) {String} data.piggyDepositVoucherRecordList.appDate 申请日期 yyyyMMdd
     * @apiSuccess (响应结果) {String} data.piggyDepositVoucherRecordList.appTime 申请时间 HHmmss
     * @apiSuccess (响应结果) {String} data.piggyDepositVoucherRecordList.remittanceAmount 汇款金额
     * @apiSuccess (响应结果) {String} data.piggyDepositVoucherRecordList.remittanceCurrency 汇款币种
     * @apiSuccess (响应结果) {String} data.piggyDepositVoucherRecordList.actualReceiptAmount 实际到账金额
     * @apiSuccess (响应结果) {String} data.piggyDepositVoucherRecordList.actualReceiptCurrency 实际到账币种
     * @apiSuccess (响应结果) {String} data.piggyDepositVoucherRecordList.voucherNo 打款凭证订单号
     * @apiSuccess (响应结果) {String} data.piggyDepositVoucherRecordList.payVoucherState 打款凭证状态 已入账 ：1、已提交 ： 2、入账失败 ：3、重复凭证 ： 4
     * @apiSuccess (响应结果) {String} data.piggyDepositVoucherRecordList.payVoucherStateDesc 前端状态的名称展示字段   已入账 ：1、已提交 ： 2、入账失败 ：3、重复凭证 ： 4
     * @apiSuccess (响应结果) {String} data.piggyDepositVoucherRecordList.voucherType 打款凭证类型: 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
     * @apiSuccess (响应结果) {Array} data.piggyDepositVoucherRecordList.rejectReason 审核不通过原因
     * @apiSuccess (响应结果) {String} data.piggyDepositVoucherRecordList.canDeleted 是否展示删除按钮  1 是 0 否
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"ZktyGVG9Wi","data":{"total":"U","piggyDepositVoucherRecordList":[{"remittanceAmount":"3hPXhx","voucherNo":"NO4ow","canDeleted":"CACmR","appTime":"x3lsf","actualReceiptAmount":"uR","rejectReason":["yKKRDidYgR"],"payVoucherStateDesc":"vJva9lLYv","voucherType":"1v","appDate":"856xF","remittanceCurrency":"IMVJjFwT","actualReceiptCurrency":"oyqLUVERR7","payVoucherState":"q"}]},"description":"m","timestampServer":"F8x"}
     */
    @PostMapping("payvoucher/list")
    public CgiResponse<PiggyDepositVoucherRecordListVO> queryPiggyDepositVoucherRecordList(@RequestBody PiggyDepositVoucherRecordListRequest request) {
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(piggyDepositService.queryPiggyDepositVoucherRecordList(request));
    }

    /**
     * @api {POST} /ext/piggy/deposit/payvoucher/bankverify verifyPiggyDepositBank()
     * @apiVersion 1.0.0
     * @apiGroup PiggyDepositController
     * @apiName verifyPiggyDepositBank()
     * @apiDescription 银行卡有效性校验
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} cpAcctNo 资金账号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"KatW","cpAcctNo":"UdF4a7"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Eq552P","description":"I8q","timestampServer":"c"}
     */
    @PostMapping("payvoucher/bankverify")
    public CgiResponse<Body> verifyPiggyDepositBank(@RequestBody PiggyDepositBankVerifyRequest request) {
        BasicDataTypeValidator.validator(request);
        piggyDepositService.verifyPiggyDepositBank(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /ext/piggy/deposit/payvoucher/deleted deletedDepositVoucher()
     * @apiVersion 1.0.0
     * @apiGroup PiggyDepositController
     * @apiName deletedDepositVoucher()
     * @apiDescription 打款凭证删除接口
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} voucherNo 打款凭证号
     * @apiParamExample 请求体示例
     * {"voucherNo":"hq","hkCustNo":"JJ0nkfGG6h"}
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"TCPZ7FTYE","description":"diDyDH9fj","timestampServer":"qyDY"}
     */
    @PostMapping("payvoucher/deleted")
    public CgiResponse<Body> deletedDepositVoucher(@RequestBody PiggyDepositVoucherDeletedRequest request) {
        piggyDepositService.deletedDepositVoucher(request);
        return CgiResponse.ok(new Body());
    }

}
