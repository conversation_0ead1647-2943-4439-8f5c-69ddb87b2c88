/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkaccount;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.ParamsException;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.common.utils.AppEncUtils;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.*;
import com.howbuy.crm.cgi.extservice.service.hkaccount.HkTradeRecordService;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TradeDealInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TradeRecordDetailInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.TradeRecordInfoListVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: (查询交易记录控制类)
 * <AUTHOR>
 * @date 2024/2/22 18:08
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/traderecord")
public class TradeRecordController {


    @Autowired
    private HkTradeRecordService hkTradeRecordService;


    /**
     * @api {POST} /ext/hkaccount/traderecord/querylist queryTradeRecordList()
     * @apiVersion 1.0.0
     * @apiGroup TradeRecordController
     * @apiName queryTradeRecordList()
     * @apiDescription 查询交易记录的列表接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} tradeStatus 交易状态 在途 '4|5|6|10' 全部 传空           海外中台数据源 在途订单状态传 1 申请成功
     * @apiParam (请求体) {String} tradeStatus 交易状态 在途 '4|5|6|10' 全部 传空           海外中台数据源 在途订单状态传 1 申请成功
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} size 每页大小
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {Array} fundCategoryList 基金类别 1-公募、2-私募、9-其他
     * @apiParam (请求体) {Array} busiTypeList 业务类型列表 1-买入、2-卖出、9-其他
     * @apiParam (请求体) {Array} tradeStatusList 交易状态列表 1-付款中、2-确认中、3-交易成功、4-交易失败、5-已撤单、9-其他
     * @apiParam (请求体) {Array} holdStatus 持仓状态 1-持有、2-已清仓
     * @apiParam (请求体) {String} timePeriod 时间范围枚举 1-近一个月、2-近半年、3-近一年
     * @apiParam (请求体) {String} orderStartDt 订单开始时间 格式：YYYYMMdd
     * @apiParam (请求体) {String} orderEndDt 订单结束时间 格式：YYYYMMdd
     * @apiParam (请求体) {String} fundTxCode 基金交易账号
     * @apiParamExample 请求体示例
     * {
     *   "hkCustNo": "HK123456",
     *   "fundCode": "000001",
     *   "fundCategoryList": ["1", "2"],
     *   "busiTypeList": ["1", "2"],
     *   "tradeStatusList": ["1", "2", "3"],
     *   "holdStatus": ["1"],
     *   "timePeriod": "1",
     *   "orderStartDt": "20240301",
     *   "orderEndDt": "20240306",
     *   "fundTxCode": "FT123456"
     * }
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.tradeRecordInfoList 交易记录列表
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.dealNo 订单号
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeType 交易类型
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeTypeDesc 交易类型描述
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeDirection 交易方向 ("+", "-")
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeStatus 交易状态 (1-交易成功、2-交易失败、3-部分成功、4-确认中、5-付款中、6-等待付款、7-平衡中、8-已撤单、9-强制撤单)
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeStatusColor 交易状态文字颜色 (ff8800 、ff8800、f14a51、)
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.appDate 月份时间
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.productName 产品名称
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.appTime 预计确认时间
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.num 数量 金额/份额 的单位值数据
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.unit 单位 (当前记录类型的单位展示 份额/金额/比例/克数)
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.typeIcon 交易类型展示 枚举值 0，1，2，3 【style.tb_gray, style.tb_red, style.tb_green, style.tb_blue】
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.specialTag 私募标签
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.currency 币种
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.transferProductName 转入产品名称
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.canGoToDetail 是否可以跳转到详情页 0-否、1-是
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "000000",
     *   "description": "成功",
     *   "data": {
     *     "tradeRecordInfoList": [
     *       {
     *         "dealNo": "************",
     *         "tradeType": "022",
     *         "tradeTypeDesc": "申购",
     *         "tradeDirection": "+",
     *         "tradeStatus": "1",
     *         "tradeStatusColor": "ff8800",
     *         "appDate": "2024-03-06",
     *         "productName": "测试基金",
     *         "appTime": "2024-03-06 10:00:00",
     *         "num": "1000.00",
     *         "unit": "元",
     *         "typeIcon": "1",
     *         "specialTag": "私募",
     *         "currency": "CNY",
     *         "transferProductName": "",
     *         "canGoToDetail": "1"
     *       }
     *     ]
     *   },
     *   "timestampServer": "**************"
     * }
     */
    // 资产详情中的交易记录查询列表
    @PostMapping("/querylist")
    public CgiResponse<TradeRecordInfoListVO> queryTradeRecordList(@RequestBody QueryTradeRecordListRequest request) {
        return CgiResponse.appOk(hkTradeRecordService.queryTradeRecordList(request));
    }

    /**
     * @api {POST} /ext/hkaccount/traderecord/querylistfortradelist queryTradeRecordListForTradeList()
     * @apiVersion 1.0.0
     * @apiGroup TradeRecordController
     * @apiName queryTradeRecordListForTradeList()
     * @apiDescription APP交易记录列表查询接口
     * @apiParam (请求体) {String} tradeStatus 海外中台数据源 在途订单状态传 1 申请成功
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {Array} fundCategoryList 基金类别列表      1-公募、2-私募、9-其他
     * @apiParam (请求体) {Array} busiTypeList 业务类型列表      1-买入、2-卖出、9-其他
     * @apiParam (请求体) {Array} tradeStatusList 交易状态列表      1-付款中、2-确认中、3-交易成功、4-交易失败、5-已撤单、9-其他
     * @apiParam (请求体) {Array} holdStatus 持仓状态      1-持有、2-已清仓
     * @apiParam (请求体) {String} timePeriod 时间范围枚举      1-近一个月、2-近半年、3-近一年
     * @apiParam (请求体) {String} orderStartDt 订单开始时间      格式：YYYYMMdd
     * @apiParam (请求体) {String} orderEndDt 订单结束时间      格式：YYYYMMdd
     * @apiParam (请求体) {String} fundTxCode 基金交易账号
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} size 每页大小
     * @apiParamExample 请求体示例
     * {"fundCategoryList":["otrpYI"],"holdStatus":["cl"],"size":4874,"fundCode":"CYIztuOj","tradeStatus":"w4r","timePeriod":"jEdAr6g","orderEndDt":"LjGnsHzn","fundTxCode":"AH2KXU","page":5620,"busiTypeList":["wdIV"],"orderStartDt":"Qk","tradeStatusList":["eIq"]}
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.tradeRecordInfoList 交易记录列表
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.dealNo 订单号
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeType 交易类型
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeTypeDesc 交易类型描述
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeDirection 交易方向 ("+", "-")
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeStatus 交易状态 (1-交易成功、2-交易失败、3-部分成功、4-确认中、5-付款中、6-等待付款、7-平衡中、8-已撤单、9-强制撤单)
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeStatusColor 交易状态文字颜色 (ff8800 、ff8800、f14a51、)
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.appDate 月份时间
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.productName 产品名称
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.appTime 预计确认时间
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.num 数量 金额/份额 的单位值数据
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.unit 单位 (当前记录类型的单位展示 份额/金额/比例/克数)
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.typeIcon 交易类型展示 枚举值 0，1，2，3 【style.tb_gray, style.tb_red, style.tb_green, style.tb_blue】
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.specialTag 私募标签
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.currency 币种
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.transferProductName 转入产品名称
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.canGoToDetail 是否可以跳转到详情页 0-否、1-是
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Z","data":{"tradeRecordInfoList":[{"appTime":"NFd406aBR","specialTag":"TdXmvF","num":"XzFJSoZxLV","appDate":"Pf5X","canGoToDetail":"8RERPMYP","dealNo":"on","productName":"MGQKq","tradeDirection":"s5dyzBDVx","unit":"MK","tradeStatus":"kQzJS0rI","tradeStatusColor":"Eq","typeIcon":"Lhi","tradeTypeDesc":"T","currency":"qpaYgQ","tradeType":"wn","transferProductName":"c8S"}]},"description":"4aJoW9QJoL","timestampServer":"yGTQo2"}
     */
    // 交易记录列表查询列表，涉及到多维度的检索条件查询
    @PostMapping("/querylistfortradelist")
    public CgiResponse<TradeRecordInfoListVO> queryTradeRecordListForTradeList(@RequestBody QueryTradeRecordListRequest request) {
        // 时间范围枚举 和自定义时间不能同时传值
        if (StringUtils.isNotBlank(request.getTimePeriod()) && (StringUtils.isNotBlank(request.getOrderStartDt()) || StringUtils.isNotBlank(request.getOrderEndDt()))) {
           throw new ParamsException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "时间范围枚举 和自定义时间不能同时传值");
        }
        return CgiResponse.appOk(hkTradeRecordService.queryTradeRecordListForTradeList(request));
    }

    /**
     * @api {POST} /ext/hkaccount/traderecord/querywaitrefundorderlist queryWaitRefundOrderList()
     * @apiVersion 1.0.0
     * @apiGroup TradeRecordController
     * @apiName queryWaitRefundOrderList()
     * @apiDescription 查询待回款订单列表
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundTxCode 基金交易账号
     * @apiParam (请求体) {String} includePiggy 包含储蓄罐 1 是 0 否
     * @apiParamExample 请求体示例
     * {"hkCustNo":"Q1tJ","includePiggy":"Duh85hq","fundTxCode":"HbrZ5N7IO"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.tradeRecordInfoList 交易记录列表
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.dealNo 订单号
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeType 交易类型
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.subTradeType 子交易类型
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeTypeDesc 交易类型描述
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeDirection 交易方向 ("+", "-")
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeStatus 交易状态 (1-交易成功、2-交易失败、3-部分成功、4-确认中、5-付款中、6-等待付款、7-平衡中、8-已撤单、9-强制撤单)
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeStatusColor 交易状态文字颜色 (ff8800 、ff8800、f14a51、)
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.appDate 月份时间
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.productName 产品名称
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.appTime 预计确认时间
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.num 数量 金额/份额 的单位值数据
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.unit 单位 (当前记录类型的单位展示 份额/金额/比例/克数)
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.typeIcon 交易类型展示 枚举值 0，1，2，3 【style.tb_gray, style.tb_red, style.tb_green, style.tb_blue】
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.specialTag 私募标签
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.currency 币种
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.transferProductName 转入产品名称
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.canGoToDetail 是否可以跳转到详情页 0-否、1-是
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"FipYm","data":{"tradeRecordInfoList":[{"appTime":"sJBpdO9rD","specialTag":"K6jjw","num":"KB","appDate":"gMQ7qkOpOT","canGoToDetail":"jxZ","dealNo":"UcdqTA1JCL","productName":"p5arh","tradeDirection":"kOrm","unit":"19jilEF47l","tradeStatus":"OC9sl0j8","tradeStatusColor":"8wf5BlA","typeIcon":"91yvP","tradeTypeDesc":"fY","currency":"uY6806Hgp3","subTradeType":"pnJ8","tradeType":"AyHH5AV","transferProductName":"7eUL"}]},"description":"hRqZLfT","timestampServer":"YbjCM"}
     */
    @PostMapping("/querywaitrefundorderlist")
    public CgiResponse<TradeRecordInfoListVO> queryWaitRefundOrderList(@RequestBody QueryWaitRefundListRequest request) {

        return CgiResponse.appOk(hkTradeRecordService.queryWaitRefundOrderList(request));
    }


    /**
     * @api {POST} /ext/hkaccount/traderecord/querydetail queryTradeContractDetail()
     * @apiVersion 1.0.0
     * @apiGroup TradeRecordController
     * @apiName queryTradeContractDetail()
     * @apiDescription 查询交易记录的详情接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} contractNo 订单号
     * @apiParamExample 请求体示例
     * {"contractNo":"i"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.fundName 基金名称
     * @apiSuccess (响应结果) {String} data.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.mBusiCode 业务类型代码
     * @apiSuccess (响应结果) {String} data.mBusiCodeDesc 业务类型描述
     * @apiSuccess (响应结果) {String} data.tradeStatus 交易状态
     * @apiSuccess (响应结果) {String} data.tradeStatusColor 交易状态文字颜色 (0-默认、1-橘色、2-红色)   (ff8800 、ff8800、f14a51、)
     * @apiSuccess (响应结果) {String} data.appDt 申请时间
     * @apiSuccess (响应结果) {String} data.appDisDt 申请时间,前端展示 时间格式 yyyy-MM-dd HH:mm
     * @apiSuccess (响应结果) {String} data.redeemDirection 赎回去向 (赎回,分红,强赎的资金去向 0-银行卡;1-储蓄罐)
     * @apiSuccess (响应结果) {String} data.cpAcctNo 资金账号  赎回去向 是银行卡的时候 有值
     * @apiSuccess (响应结果) {String} data.redeemDirectionValue 赎回去向 状态值 1 : 本人银行卡  2 ： 现金余额 3 ： 储蓄罐
     * @apiSuccess (响应结果) {String} data.redeemType 赎回方式 0:按金额 1: 按份额
     * @apiSuccess (响应结果) {String} data.paymentType 买入方式列表
     * @apiSuccess (响应结果) {Array} data.mutiCardList 关联银行卡列表
     * @apiSuccess (响应结果) {String} data.mutiCardList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} data.mutiCardList.bankAcctNo 银行卡号      a）默认展示：银行名称“前四位”+“****”+“后四位”；      b）若客户存在多张银行卡首尾4位数字一致，则增加中间位校验，即将中间位不一致的第一位展示出来，      样式为：银行名称“前四位”+“**”+“第一个不一样的”+“**”+“后四位”
     * @apiSuccess (响应结果) {String} data.mutiCardList.bankLogoUrl 银行logo地址
     * @apiSuccess (响应结果) {String} data.mutiCardList.bankName 银行名称
     * @apiSuccess (响应结果) {String} data.mutiCardList.bankCode 银行编号
     * @apiSuccess (响应结果) {String} data.appVol 申请份额
     * @apiSuccess (响应结果) {String} data.appAmt 申请金额
     * @apiSuccess (响应结果) {String} data.ackAmt 确认金额
     * @apiSuccess (响应结果) {String} data.ackVol 确认份额
     * @apiSuccess (响应结果) {String} data.fee 费用 (根据业务类型字段显示交易手续费（赎回费/业绩报酬扣减）)
     * @apiSuccess (响应结果) {String} data.nav 净值 (根据业务类型字段显示 成交净值/确认净值)
     * @apiSuccess (响应结果) {String} data.payStatus 付款状态
     * @apiSuccess (响应结果) {String} data.orderStatus 订单状态
     * @apiSuccess (响应结果) {String} data.appTm 申请时间
     * @apiSuccess (响应结果) {String} data.ackDt 确认日期
     * @apiSuccess (响应结果) {String} data.fundRaisingStatus 资金募集状态 0--隐藏 1--展示
     * @apiSuccess (响应结果) {String} data.openDt 开放日期
     * @apiSuccess (响应结果) {String} data.tradeDt 交易日期
     * @apiSuccess (响应结果) {String} data.supportUpload 是否展示上传凭证入口, 1 : 是 0 ： 否
     * @apiSuccess (响应结果) {String} data.uploadStatus 是否上传打款凭证 审核结果 0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
     * @apiSuccess (响应结果) {String} data.supportRepeal 是否支持撤单, 1 : 是 0 ： 否
     * @apiSuccess (响应结果) {String} data.currencyDesc 当前币种描述
     * @apiSuccess (响应结果) {String} data.hasDetail 是否有详情数据
     * @apiSuccess (响应结果) {String} data.transferProductName 转入产品名称
     * @apiSuccess (响应结果) {String} data.transferAckVol 转入确认份额
     * @apiSuccess (响应结果) {String} data.transferAckAmt 转入确认金额
     * @apiSuccess (响应结果) {String} data.transferNav 转入确认净值
     * @apiSuccess (响应结果) {String} data.transferFundName 转入基金名称
     * @apiSuccess (响应结果) {String} data.transferFundCode 转入基金代码
     * @apiSuccess (响应结果) {String} data.transferCurrency 转出币种
     * @apiSuccess (响应结果) {String} data.transferCurrencyDesc 转出币种描述
     * @apiSuccess (响应结果) {Array} data.tradeRecordMultiDetialInfoVOList 订单明细详情对象
     * @apiSuccess (响应结果) {String} data.tradeRecordMultiDetialInfoVOList.seriesNo 系列号
     * @apiSuccess (响应结果) {String} data.tradeRecordMultiDetialInfoVOList.regDate 份额注册日期
     * @apiSuccess (响应结果) {String} data.tradeRecordMultiDetialInfoVOList.tradeDate 交易日期
     * @apiSuccess (响应结果) {String} data.tradeRecordMultiDetialInfoVOList.confirmDate 确认日期
     * @apiSuccess (响应结果) {String} data.tradeRecordMultiDetialInfoVOList.confirmAmt 确认金额
     * @apiSuccess (响应结果) {String} data.tradeRecordMultiDetialInfoVOList.confirmVol 确认份额
     * @apiSuccess (响应结果) {String} data.tradeRecordMultiDetialInfoVOList.redeemFee 赎回费
     * @apiSuccess (响应结果) {String} data.tradeRecordMultiDetialInfoVOList.confirmNav 确认净值
     * @apiSuccess (响应结果) {String} data.tradeRecordMultiDetialInfoVOList.currency 币种
     * @apiSuccess (响应结果) {String} data.tradeRecordMultiDetialInfoVOList.currencyDesc 币种描述
     * @apiSuccess (响应结果) {Array} data.relationOrderList 关联订单号列表
     * @apiSuccess (响应结果) {String} data.gradationCall 是否分次Call      1-是、0-否
     * @apiSuccess (响应结果) {String} data.subAmt 认缴金额
     * @apiSuccess (响应结果) {String} data.showModifyRedeemDirection 是否展示修改回款方式入口	 1 是  0 否
     * @apiSuccess (响应结果) {String} data.waitRefundOrder 待回款订单
     * @apiSuccess (响应结果) {String} data.piggyProduct 是否储蓄罐 1 是 0 否
     * @apiSuccess (响应结果) {String} data.piggyEffectiveStatus 储蓄罐生效状态 1 未生效  2 已生效  3 已过期
     * @apiSuccess (响应结果) {Array} data.bankCardList 银行卡列表
     * @apiSuccess (响应结果) {String} data.bankCardList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} data.bankCardList.bankAcctMask 银行卡号掩码
     * @apiSuccess (响应结果) {String} data.bankCardList.bankName bankName
     * @apiSuccess (响应结果) {String} data.bankCardList.bankLogoUrl 银行logo
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"0NfD7qQJHh","data":{"piggyProduct":"1fUrUw","bankCardList":[{"bankLogoUrl":"Yk8","bankAcctMask":"k","cpAcctNo":"ksMuDlnmAJ","bankName":"zJS0"}],"appAmt":"dD6Tly0xED","appDisDt":"p8Gy","fee":"p","uploadStatus":"K","mBusiCode":"FR","fundCode":"EfYfvav8","appVol":"1pj9zx0K5o","redeemDirectionValue":"QOwdYi","appDt":"o0","ackVol":"3gXf0vzu","currencyDesc":"tD","nav":"lVD3qidiV","relationOrderList":["GEO"],"subAmt":"T9Z","transferCurrency":"Qus2L7a","tradeStatus":"xCRrttSW","openDt":"26rPHhvW2","transferCurrencyDesc":"H82dk9qXBn","fundName":"Ayj0NH","gradationCall":"h2rQY22h","orderStatus":"g5T","fundRaisingStatus":"I9KNEt","waitRefundOrder":"cK","piggyEffectiveStatus":"BwC1Uup","transferAckVol":"oI","redeemDirection":"cCrCSdCtJ","paymentType":"Opoa4Gzi","mutiCardList":[{"bankLogoUrl":"RWbFqWVV","bankCode":"sqVjzYBL","bankAcctNo":"USgCWwny65","cpAcctNo":"zd","bankName":"4x0a"}],"hasDetail":"crl5","transferAckAmt":"Iyzm7sp4","transferFundName":"ZscbllKn","ackAmt":"k","tradeStatusColor":"3OOi","cpAcctNo":"vNM3U","supportUpload":"WGmOSW6","transferProductName":"tDDcFCrY","supportRepeal":"VyLZrdc","appTm":"OEWZ1","tradeRecordMultiDetialInfoVOList":[{"redeemFee":"TI","confirmDate":"vVC3km7ao","currencyDesc":"5N","seriesNo":"JRr","confirmVol":"41pc","confirmNav":"v9XSPPKbJ","regDate":"1a","currency":"eLPWsk","tradeDate":"89i7OMDcQ6","confirmAmt":"B74fT"}],"tradeDt":"0iUfVX","mBusiCodeDesc":"P","redeemType":"j8q","transferFundCode":"mpNCs","transferNav":"ddZA","ackDt":"hbJT1ww3u","payStatus":"fD","showModifyRedeemDirection":"uZkJhIQP"},"description":"5ixALt9U","timestampServer":"fx6A"}
     */
    @PostMapping("/querydetail")
    public CgiResponse<TradeRecordDetailInfoVO> queryTradeContractDetail(@RequestBody QueryTradeRecordDetailRequest request) {
        return CgiResponse.ok(hkTradeRecordService.queryTradeRecordDetail(request.getContractNo()));
    }


    /**
     * @api {POST} /ext/hkaccount/traderecord/getdealnobypreid getDealNoByPreId()
     * @apiVersion 1.0.0
     * @apiGroup TradeRecordController
     * @apiName getDealNoByPreId()
     * @apiDescription 根据预约单id查询交易记录合同号
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} contractNo 订单号
     * @apiParamExample 请求体示例
     * {"contractNo":"tYEw"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.dealNo 合同单号
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"YHp17qFwJ","data":{"dealNo":"XpR4Gif"},"description":"t","timestampServer":"mv9b"}
     */
    @PostMapping("/getdealnobypreid")
    public CgiResponse<TradeDealInfoVO> getDealNoByPreId(@RequestBody QueryTradeRecordDetailRequest request) {
        TradeDealInfoVO vo = new TradeDealInfoVO();
        vo.setDealNo(hkTradeRecordService.getAppserialNo(request.getContractNo()));
        return CgiResponse.ok(vo);
    }

    /**
     * @api {POST} /ext/hkaccount/traderecord/relation/order/list queryRelationOrderList()
     * @apiVersion 1.0.0
     * @apiGroup TradeRecordController
     * @apiName queryRelationOrderList()
     * @apiDescription 交易记录详情关联订单列表查询
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {Array} relationOrderList 关联订单号列表
     * @apiParamExample 请求体示例
     * {
     *   "hkCustNo": "HK123456",
     *   "relationOrderList": ["************", "************"]
     * }
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.tradeRecordInfoList 交易记录列表
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.dealNo 订单号
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeType 交易类型
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeTypeDesc 交易类型描述
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeDirection 交易方向 ("+", "-")
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeStatus 交易状态 (1-交易成功、2-交易失败、3-部分成功、4-确认中、5-付款中、6-等待付款、7-平衡中、8-已撤单、9-强制撤单)
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.tradeStatusColor 交易状态文字颜色 (ff8800 、ff8800、f14a51、)
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.appDate 月份时间
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.productName 产品名称
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.appTime 预计确认时间
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.num 数量 金额/份额 的单位值数据
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.unit 单位 (当前记录类型的单位展示 份额/金额/比例/克数)
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.typeIcon 交易类型展示 枚举值 0，1，2，3 【style.tb_gray, style.tb_red, style.tb_green, style.tb_blue】
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.specialTag 私募标签
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.currency 币种
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.transferProductName 转入产品名称
     * @apiSuccess (响应结果) {String} data.tradeRecordInfoList.canGoToDetail 是否可以跳转到详情页 0-否、1-是
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "000000",
     *   "description": "成功",
     *   "data": {
     *     "tradeRecordInfoList": [
     *       {
     *         "dealNo": "************",
     *         "tradeType": "022",
     *         "tradeTypeDesc": "申购",
     *         "tradeDirection": "+",
     *         "tradeStatus": "1",
     *         "tradeStatusColor": "ff8800",
     *         "appDate": "2024-03-06",
     *         "productName": "测试基金",
     *         "appTime": "2024-03-06 10:00:00",
     *         "num": "1000.00",
     *         "unit": "元",
     *         "typeIcon": "1",
     *         "specialTag": "私募",
     *         "currency": "CNY",
     *         "transferProductName": "",
     *         "canGoToDetail": "1"
     *       }
     *     ]
     *   },
     *   "timestampServer": "**************"
     * }
     */
    @PostMapping("/relation/order/list")
    public CgiResponse<TradeRecordInfoListVO> queryRelationOrderList(@RequestBody RelationOrderListRequest request) {
        return CgiResponse.appOk( hkTradeRecordService.queryTradeRecordListByRelationOrderList(request));
    }

    /**
     * @api {POST} /ext/hkaccount/traderecord/change/redeem/direction changeRedeemDirection()
     * @apiVersion 1.0.0
     * @apiGroup TradeRecordController
     * @apiName changeRedeemDirection()
     * @apiDescription 交易记录修改回款方向
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo
     * @apiParam (请求体) {String} dealNo
     * @apiParam (请求体) {String} redeemDirection 赎回方向 1-回银行卡|电汇、2-留账好买香港账户、3-回海外储蓄罐、4-支票
     * @apiParam (请求体) {String} cpAcctNo 资金账号  1-回银行卡|电汇时必须
     * @apiParamExample 请求体示例
     * {"hkCustNo":"VfCO","cpAcctNo":"nj3LDip","dealNo":"PoJeTZkK","redeemDirection":"pqGc4UOu"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"v74RRyu","description":"n5honN","timestampServer":"qHQaIiR"}
     */
    @PostMapping("/change/redeem/direction")
    public CgiResponse<Body> changeRedeemDirection(@RequestBody ChangeRedeemDirectionRequest request) {
        BasicDataTypeValidator.validator(request);
        hkTradeRecordService.changeRedeemDirection(request);
        return CgiResponse.ok(new Body());
    }

}