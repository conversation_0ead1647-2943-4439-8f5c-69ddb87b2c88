package com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.alibaba.fastjson.JSON;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.constants.MarkConstants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.TxChannelEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.common.utils.ImageUtils;
import com.howbuy.crm.cgi.extservice.common.constant.ExternalConstant;
import com.howbuy.crm.cgi.extservice.common.enums.FileDownLoadTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.contract.ContractFileBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctCheckFilePageEnum;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctOrderStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherTypeEnum;
import com.howbuy.crm.cgi.extservice.common.utils.ParamsValidator;
import com.howbuy.crm.cgi.extservice.common.utils.ReflectUtils;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.common.utils.hkopen.HkOpenImageUrlUtils;
import com.howbuy.crm.cgi.extservice.convert.hkopen.OpenAcctConvert;
import com.howbuy.crm.cgi.extservice.request.account.*;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAccIdentityOcrRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAppUploadFileRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.*;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.bankinfo.HkOpenAcctBankRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.declare.HkOpenAcctDeclareRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.idInfo.HkAcctOpenIdInfoRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.occupation.HkOpenAcctOccupationRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.personalInfo.HkOpenPersonalInfoRequest;
import com.howbuy.crm.cgi.extservice.request.piggy.DepositsCertificateRequest;
import com.howbuy.crm.cgi.extservice.validator.hkopen.HkOpenAcctValidator;
import com.howbuy.crm.cgi.extservice.vo.dtmsproduct.LicenseeDetailVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.*;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.ContractInfoNewVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.*;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.bankinfo.HkOpenAcctBankInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.declare.HkOpenAcctDeclareVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.idinfo.HkAcctOpenIdDetailVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.idinfo.HkOpenAcctIdInfoRespVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.investinfo.HkOpenAcctInvestVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.pdf.HkOpenAcctPdfVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.personalinfo.HkOpenPersonalInfoResponseVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.signnature.HkOpenAcctESignatureVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.FilePreviewReqDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.*;
import com.howbuy.crm.cgi.manager.enums.HkOpenAccountStepEnum;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkAccFileOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkopen.OpenAcctCatchService;
import com.howbuy.crm.cgi.manager.outerservice.ocr.IdentityCardOcrOuterService;
import com.howbuy.dfile.HFileService;
import com.howbuy.dtms.common.enums.HwPayVoucherFileSourceEnum;
import com.howbuy.paycommon.model.enums.IdTypeEnum;
import com.howbuy.paycommon.model.enums.RnAuthStateEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 海外开户服务
 * @date 2023/12/1 15:50
 * @since JDK 1.8
 */
@Service
public class OpenAcctService {

    private static final Logger log = LoggerFactory.getLogger(OpenAcctService.class);

    private static final String MOBILE = "mobile";

    private static final String EMAIL = "email";

    @Resource
    private IdentityCardOcrOuterService identityCardOcrOuterService;

    @Resource
    private HkCustInfoOuterService hkCustInfoOuterService;
    @Resource
    private OpenAcctCatchService openAcctCatchService;

    @Autowired
    private HkAccFileOuterService hkAccFileOuterService;

    @Value("${OPEN_FILE_TYPE_LIST}")
    private String openFileTypeList;

    @Value("${hk.id.image.file.type}")
    private String hkIdImageFileType;

    @Resource
    private OpenAcctIdInfoService openAcctIdInfoService;

    @Resource
    private OpenPersonalInfoService openPersonalInfoService;

    @Resource
    private OpenAcctOccupationService openAcctOccupationService;

    @Resource
    private OpenAcctDeclareInfoService openAcctDeclareInfoService;

    @Resource
    private OpenAcctInvestInfoService openAcctInvestInfoService;

    @Resource
    private OpenAcctBankInfoService openAcctBankinfoService;

    @Resource
    private OpenAcctSignatureService openAcctSignatureService;


    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.IdentityCardVO
     * @description: 查询身份证OCR详情
     * @author: jinqing.rao
     * @date: 2023/12/1 18:18
     * @since JDK 1.8
     */
    public IdentityCardVO queryIdentityCardOcrDetail(IdentityCardOcrRequest request) {
        return openAcctIdInfoService.queryIdentityCardOcrDetail(request);
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.BankInfoVO
     * @description: 获取银行卡列表
     * 1.调用香港账户中心的银行列表查询接口QueryHkSwiftCodeFacade，银行名称默认取银行中文名称，银行中文名称为空，则取银行英文名称。
     * 2.调用cms的银行logo配置接口
     * @author: jinqing.rao
     * @date: 2023/12/6 14:26
     * @since JDK 1.8
     */
    public BankInfoVO queryBankInfoList(String swiftOrBankName) {
        return openAcctBankinfoService.queryBankInfoList(swiftOrBankName);
    }

    /**
     * @param request 证件信息请求类
     * @return com.howbuy.crm.cgi.common.base.Body
     * @description: 证件信息-开户暂存接口
     * @author: jinqing.rao
     * @date: 2023/12/7 9:41
     * @since JDK 1.8
     */
    public CgiResponse<HkOpenAcctIdInfoRespVO> saveOpenIdInfo(HkAcctOpenIdInfoRequest request) {
        return openAcctIdInfoService.saveOpenIdInfo(request);
    }


    public HkOpenAcctIdInfoRespVO temporaryStorageOpenId(HkAcctOpenIdInfoRequest request) {
        return openAcctIdInfoService.temporaryStorageOpenId(request);
    }


    /**
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.idinfo.OpenIdVO
     * @description: 证件信息详情查询接口
     * @author: jinqing.rao
     * @date: 2023/12/7 11:04
     * @since JDK 1.8
     */
    public HkAcctOpenIdDetailVO queryOpenIdDetail() {
        return openAcctIdInfoService.queryOpenIdDetail();

    }

    /**
     * @return java.lang.String
     * @description: 获取香港客户证件号
     * @author: jinqing.rao
     * @date: 2023/12/7 11:05
     * @since JDK 1.8
     */
    private static String getHkCusNo() {
        String hkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        if (StringUtils.isBlank(hkCustNo)) {
            throw new BusinessException(ExceptionCodeEnum.ACCT_ERROR);
        }
        return hkCustNo;
    }

    /**
     * @param request
     * @return void
     * @description: 个人信息-开户暂存查询接口 线上开户-填写资料2
     * @author: jinqing.rao
     * @date: 2023/12/7 14:44
     * @since JDK 1.8
     */
    public CgiResponse<HkOpenPersonalInfoResponseVO> saveOpenPersonalInfo(HkOpenPersonalInfoRequest request) {
        return openPersonalInfoService.saveOpenPersonalInfo(request);
    }

    /**
     * @param request
     * @return void
     * @description: 暂存个人信息
     * @author: jinqing.rao
     * @date: 2024/1/4 10:42
     * @since JDK 1.8
     */
    public void temporaryStorageOpenPersonalInfo(HkOpenPersonalInfoRequest request) {
        openPersonalInfoService.temporaryStorageOpenPersonalInfo(request);
    }

    /**
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenPersonalInfoVO
     * @description: 个人信息-开户暂存查询接口 线上开户-填写资料2
     * @author: jinqing.rao
     * @date: 2023/12/7 15:59
     * @since JDK 1.8
     */
    public OpenPersonalInfoVO queryOpenPersonalInfoDetail() {
        return openPersonalInfoService.queryOpenPersonalInfoDetail();
    }


    /**
     * @param request 请求参数
     * @description: 保存职业信息
     * @author: jinqing.rao
     * @date: 2023/12/7 19:20
     * @since JDK 1.8
     */
    public void saveOpenOccupationInfo(HkOpenAcctOccupationRequest request) {
        openAcctOccupationService.saveOpenOccupationInfo(request);

    }

    public void temporaryStorageOpenOccupationInfo(HkOpenAcctOccupationRequest request) {
        openAcctOccupationService.temporaryStorageOpenOccupationInfo(request);

    }


    /**
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenOccupationVO
     * @description: 获取声明信息详情
     * @author: jinqing.rao
     * @date: 2023/12/7 20:00
     * @since JDK 1.8
     */
    public OpenOccupationVO queryOpenOccupationInfoDetail() {
        return openAcctOccupationService.queryOpenOccupationInfoDetail();
    }

    /**
     * @param request
     * @return void
     * @description: 声明信息保存
     * @author: jinqing.rao
     * @date: 2023/12/7 20:10
     * @since JDK 1.8
     */
    public void saveOpenDeclareInfo(HkOpenAcctDeclareRequest request) {
        openAcctDeclareInfoService.saveOpenDeclareInfo(request);

    }

    public void temporaryStorageOpenDeclareInfo(HkOpenAcctDeclareRequest request) {
        openAcctDeclareInfoService.temporaryStorageOpenDeclareInfo(request);
    }


    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.declare.OpenDeclareVO
     * @description: 获取声明信息详情
     * @author: jinqing.rao
     * @date: 2023/12/8 9:13
     * @since JDK 1.8
     */
    public HkOpenAcctDeclareVO queryOpenDeclareInfoDetail() {
        return openAcctDeclareInfoService.queryOpenDeclareInfoDetail();
    }

    /**
     * @param request 请求参数
     * @description: 投资经验保存接口
     * @author: jinqing.rao
     * @date: 2023/12/8 10:38
     * @since JDK 1.8
     */
    public void saveOpenInvestInfo(OpenInvestRequest request) {
        openAcctInvestInfoService.saveOpenInvestInfo(request);

    }

    public void temporaryStorageOpenInvestInfo(OpenInvestRequest request) {
        openAcctInvestInfoService.temporaryStorageOpenInvestInfo(request);
    }


    /**
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.investinfo.OpenInvestVO
     * @description: 投资经验查询
     * @author: jinqing.rao
     * @date: 2023/12/8 11:14
     * @since JDK 1.8
     */
    public HkOpenAcctInvestVO queryOpenInvestInfoDetail() {
        return openAcctInvestInfoService.queryOpenInvestInfoDetail();
    }

    /**
     * @param request 请求参数
     * @return void
     * @description: 银行卡保存接口
     * @author: jinqing.rao
     * @date: 2023/12/8 11:25
     * @since JDK 1.8
     */
    public void saveOpenBankInfo(HkOpenAcctBankRequest request) {
        openAcctBankinfoService.saveOpenBankInfo(request);

    }

    public void temporaryStorageOpenBankInfo(HkOpenAcctBankRequest request) {
        //保存缓存
        openAcctCatchService.saveHkOpenAcctBankInfo(getHkCusNo(), OpenAcctConvert.toHkOpenAcctBankDTO(request));
    }


    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.bankinfo.OpenBankVO
     * @description: 获取开户填写页，银行卡信息
     * @author: jinqing.rao
     * @date: 2023/12/8 13:37
     * @since JDK 1.8
     */
    public HkOpenAcctBankInfoVO queryOpenBankInfoDetail() {
        return openAcctBankinfoService.queryOpenBankInfoDetail();
    }

    /**
     * @param request 请求参数
     * @return void
     * @description: 电子签名保存接口
     * @author: jinqing.rao
     * @date: 2023/12/8 14:03
     * @since JDK 1.8
     */
    public void saveOpenSignature(ESignatureRequest request) {
        String hkCusNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        //获取开户订单信息,
        //查询账户中心获取用户的开户订单
        HkOpenAccOrderInfoDTO orderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(hkCusNo);
        //证件信息页数据
        HkOpenAcctIdInfoDTO accountIdInfoDTO = openAcctCatchService.getHkOpenAcctIdInfoDTO(hkCusNo);
        //客户个人信息
        HkOpenAcctCustInfoDTO acctCustInfoDTO = openAcctCatchService.getHkOpenAcctCustInfoDTO(hkCusNo);
        //职业信息
        HkOpenAcctOccupationDTO acctOccupationDTO = openAcctCatchService.getOpenOccupationInfoDetail(hkCusNo);
        //声明信息
        HkOpenAcctDeclarationInfoDTO acctDeclarationInfoDTO = openAcctCatchService.getHkOpenAcctDeclarationInfo(hkCusNo);
        //投资经验信息
        HkOpenAcctInvestExpDTO acctInvestExpDTO = openAcctCatchService.getHkOpenAcctInvestExpDTO(hkCusNo);
        //银行信息
        HkOpenAcctBankInfoDTO accountBankInfoDTO = openAcctCatchService.getHkOpenAcctBankInfoDTO(hkCusNo);
        //审核驳回客户的订单,因为用户可以直接跳到对应的错误页面,存在部分页面的缓存没有数据,在提交的时候重新赋值
        if (null != orderInfoDTO && HkOpenAcctOrderStatusEnum.REJECTED_TO_CUSTOMER.getCode().equals(orderInfoDTO.getAcctOrderStatus())) {
            reSubmitAcctOpenOrder(orderInfoDTO, accountIdInfoDTO, acctCustInfoDTO, acctOccupationDTO, acctDeclarationInfoDTO, acctInvestExpDTO, accountBankInfoDTO, request);
        } else {
            submitAcctOpenOrder(request, accountIdInfoDTO, acctCustInfoDTO, acctOccupationDTO, acctDeclarationInfoDTO, acctInvestExpDTO, accountBankInfoDTO);
        }
    }

    /**
     * @param request                签名页信息
     * @param accountIdInfoDTO       证件信息页信息
     * @param acctCustInfoDTO        客户信息页
     * @param acctOccupationDTO      职业信息页
     * @param acctDeclarationInfoDTO 声明信息页
     * @param acctInvestExpDTO       投资经验信息页
     * @param accountBankInfoDTO     银行信息页
     * @return void
     * @description: 提交开户的订单信息
     * @author: jinqing.rao
     * @date: 2024/3/26 10:16
     * @since JDK 1.8
     */
    private void submitAcctOpenOrder(ESignatureRequest request, HkOpenAcctIdInfoDTO accountIdInfoDTO, HkOpenAcctCustInfoDTO acctCustInfoDTO, HkOpenAcctOccupationDTO acctOccupationDTO, HkOpenAcctDeclarationInfoDTO acctDeclarationInfoDTO, HkOpenAcctInvestExpDTO acctInvestExpDTO, HkOpenAcctBankInfoDTO accountBankInfoDTO) {
        //检查证件信息和客户信息是否存在
        checkIdAndCustInfo(accountIdInfoDTO, acctCustInfoDTO);
        //检查职业信息和投资经验信息是否存在
        checkOccupationAndInvertInfo(acctOccupationDTO, acctDeclarationInfoDTO, acctInvestExpDTO);
        if (null == accountBankInfoDTO || !YesNoEnum.YES.getCode().equals(accountBankInfoDTO.getOpenSaveType())) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_BANK_INFO_NOT_EXIST);
        }
        openAcctSignatureService.saveOpenSignature(accountIdInfoDTO, acctCustInfoDTO, acctOccupationDTO, acctDeclarationInfoDTO, acctInvestExpDTO, accountBankInfoDTO, request);
    }

    private static void checkOccupationAndInvertInfo(HkOpenAcctOccupationDTO acctOccupationDTO, HkOpenAcctDeclarationInfoDTO acctDeclarationInfoDTO, HkOpenAcctInvestExpDTO acctInvestExpDTO) {
        if (null == acctOccupationDTO || !YesNoEnum.YES.getCode().equals(acctOccupationDTO.getOpenSaveType())) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_OCCUPATION_NOT_EXIST);
        }
        if (null == acctDeclarationInfoDTO || !YesNoEnum.YES.getCode().equals(acctDeclarationInfoDTO.getOpenSaveType())) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_DECLARATION_NOT_EXIST);
        }
        if (null == acctInvestExpDTO || !YesNoEnum.YES.getCode().equals(acctInvestExpDTO.getOpenSaveType())) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_INVEST_EXP_NOT_EXIST);
        }
    }

    private static void checkIdAndCustInfo(HkOpenAcctIdInfoDTO accountIdInfoDTO, HkOpenAcctCustInfoDTO acctCustInfoDTO) {
        if (null == accountIdInfoDTO || !YesNoEnum.YES.getCode().equals(accountIdInfoDTO.getOpenSaveType())) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_ID_NOT_EXIST);
        }
        if (null == acctCustInfoDTO || !YesNoEnum.YES.getCode().equals(acctCustInfoDTO.getOpenSaveType())) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_CUST_NOT_EXIST);
        }
    }

    /**
     * @param request                签名页信息
     * @param accountIdInfoDTO       证件信息页信息
     * @param acctCustInfoDTO        客户信息页
     * @param acctOccupationDTO      职业信息页
     * @param acctDeclarationInfoDTO 声明信息页
     * @param acctInvestExpDTO       投资经验信息页
     * @param accountBankInfoDTO     银行信息页
     * @return void
     * @description: 重新提交开户订单, 因为用户可以跳转到指定的
     * @author: jinqing.rao
     * @date: 2024/3/26 10:16
     * @since JDK 1.8
     */
    private void reSubmitAcctOpenOrder(HkOpenAccOrderInfoDTO orderInfoDTO, HkOpenAcctIdInfoDTO accountIdInfoDTO, HkOpenAcctCustInfoDTO acctCustInfoDTO,
                                       HkOpenAcctOccupationDTO acctOccupationDTO, HkOpenAcctDeclarationInfoDTO acctDeclarationInfoDTO,
                                       HkOpenAcctInvestExpDTO acctInvestExpDTO, HkOpenAcctBankInfoDTO accountBankInfoDTO, ESignatureRequest request) {
        if (null == accountIdInfoDTO) {
            accountIdInfoDTO = orderInfoDTO.getHkOpenAcctIdInfoDTO();
        }
        if (null == acctCustInfoDTO) {
            acctCustInfoDTO = orderInfoDTO.getAccountCustInfo();
        }
        if (null == acctOccupationDTO) {
            acctOccupationDTO = orderInfoDTO.getAccountOccupationDTO();
        }
        if (null == acctDeclarationInfoDTO) {
            acctDeclarationInfoDTO = orderInfoDTO.getAccountDeclarationInfoDTO();
        }
        if (null == acctInvestExpDTO) {
            acctInvestExpDTO = orderInfoDTO.getAccountInvestExpDTO();
        }
        if (null == accountBankInfoDTO) {
            accountBankInfoDTO = orderInfoDTO.getAccountBankInfoDTO();
        }
        openAcctSignatureService.saveOpenSignature(accountIdInfoDTO, acctCustInfoDTO, acctOccupationDTO, acctDeclarationInfoDTO, acctInvestExpDTO, accountBankInfoDTO, request);
    }

    /**
     * @param request 请求参数
     * @description: 风险信息保存接口
     * @author: jinqing.rao
     * @date: 2023/12/8 14:25
     * @since JDK 1.8
     */
    public void saveRiskDisclosure(RiskDisclosureRequest request) {
        openAcctSignatureService.saveRiskDisclosure(request);
    }

    /**
     * @param request
     * @return void
     * @description: 风险披露暂存接口
     * @author: jinqing.rao
     * @date: 2024/1/5 14:50
     * @since JDK 1.8
     */
    public void temporaryStorageRiskDisclosure(RiskDisclosureRequest request) {
        //保存缓存
        openAcctCatchService.saveHkOpenAcctRiskDisclosure(getHkCusNo(), new HkOpenAcctRiskDisclosureDTO(request.getAudioPlayStatus(), request.getRemainderTime()));
    }

    public HkOpenImageVO batchUploadIdPhoto(UploadIdPhotoRequest request) {
        HkOpenImageVO hkOpenImageVO = new HkOpenImageVO();
        List<ImageVO> openImageVOList = new ArrayList<>();
        String hkCusNo = getHkCusNo();
        if (CollectionUtils.isNotEmpty(request.getFileBase64List())) {
            request.getFileBase64List().forEach(f -> {
                String imageType = request.getImageType();
                ImageVO imageVO = getUploadImageUrl(imageType, hkCusNo, ImageUtils.base64ToBytes(f), request.getImageFormat(), request.getImageName());
                openImageVOList.add(imageVO);
            });
            hkOpenImageVO.setOpenImageVOList(openImageVOList);
            return hkOpenImageVO;
        }
        //单个上传文件
        String fileBase64 = request.getFileBase64();
        String imageType = request.getImageType();
        byte[] bytes = ImageUtils.base64ToBytes(fileBase64);
        ImageVO imageVO = getUploadImageUrl(imageType, hkCusNo, bytes, request.getImageFormat(), request.getImageName());
        openImageVOList.add(imageVO);
        hkOpenImageVO.setOpenImageVOList(openImageVOList);
        return hkOpenImageVO;
    }

    /**
     * @param imageType 照片的类型
     * @param hkCusNo   香港客户号
     * @param bytes     文件流
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO
     * @description: 上传图片并且获取图片对应的DFILE的文件地址和压缩图片的地址URL
     * @author: jinqing.rao
     * @date: 2023/12/20 15:04
     * @since JDK 1.8
     */
    private ImageVO getUploadImageUrl(String imageType, String hkCusNo, byte[] bytes, String imageFormatType, String imageName) {
        if (StringUtils.isNotBlank(imageName)) {
            //校验文件的原始名称，是否包含小数点,换行符,空格等特殊字符
            if (StringUtils.containsAny(imageName, MarkConstants.SEPARATOR_DOT, File.separator, MarkConstants.SEPARATOR_SPACE, MarkConstants.SEPARATOR_SLASH, "\\n", "\n")) {
                //throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_FILE_NAME_FORMAT_ERROR);
                //不拦截 替换默认名称,防止特殊字符 李期的需求
                imageName = "defaultFile";
            }
        }
        //路劲名称
        String fileUrl = HkOpenImageUrlUtils.getRelativePathUrl(imageType, hkCusNo);
        //基础文件名称 格式 原始名称+ 客户号+ 文件类型+时间戳
        String baseFileName = HkOpenImageUrlUtils.getBaseFileName(imageType, hkCusNo, imageName);
        //基础文件名称添加格式后缀
        String fileName = baseFileName + MarkConstants.SEPARATOR_DOT + imageFormatType;
        //上传
        try {
            HFileService.getInstance().write(ExternalConstant.PAY_VOUCHER_TEMP_STORE_CONFIG, fileUrl, fileName, bytes);
        } catch (Exception e) {
            log.info("batchUploadIdPhoto>>>上传文件失败,hkCustNo :{}", hkCusNo, e);
        }
        //生成压缩文件
        String thumbnailName = HkOpenImageUrlUtils.getThumbnailNameByBaseFileName(baseFileName);
        //压缩图片添加后缀
        String thumbnailFileName = thumbnailName + MarkConstants.SEPARATOR_DOT + imageFormatType;
        //上传压缩图
        try {
            InputStream inputStream = ImageUtils.compressImage(ImageUtils.byteToInputStream(bytes));
            HFileService.getInstance().write(ExternalConstant.PAY_VOUCHER_TEMP_STORE_CONFIG, fileUrl, thumbnailFileName, inputStream);
        } catch (Exception e) {
            log.error("batchUploadIdPhoto>>>上传压缩图失败,hkCustNo :{}", hkCusNo, e);
        }
        String realFileUrl = fileUrl + fileName;
        String thumbnailFileUrl = fileUrl + thumbnailFileName;
        return new ImageVO(realFileUrl, thumbnailFileUrl);
    }


    private ImageVO getUploadFileUrl(String imageType, String hkCusNo, byte[] bytes, String imageFormatType, String imageName) {
        if (StringUtils.isNotBlank(imageName)) {
            //校验文件的原始名称，是否包含小数点,换行符,空格等特殊字符
            if (StringUtils.containsAny(imageName, MarkConstants.SEPARATOR_DOT, File.separator, MarkConstants.SEPARATOR_SPACE, MarkConstants.SEPARATOR_SLASH, "\\n", "\n")) {
                //throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_FILE_NAME_FORMAT_ERROR);
                //不拦截 替换默认名称,防止特殊字符 李期的需求
                imageName = "defaultFile";
            }
        }
        //路劲名称
        String fileUrl = HkOpenImageUrlUtils.getRelativePathUrl(imageType, hkCusNo);
        //基础文件名称 格式 原始名称+ 客户号+ 文件类型+时间戳
        String baseFileName = HkOpenImageUrlUtils.getBaseFileName(imageType, hkCusNo, imageName);
        //基础文件名称添加格式后缀
        String fileName = baseFileName + MarkConstants.SEPARATOR_DOT + imageFormatType;
        //上传
        try {
            HFileService.getInstance().write(ExternalConstant.PAY_VOUCHER_TEMP_STORE_CONFIG, fileUrl, fileName, bytes);
        } catch (Exception e) {
            log.error("batchUploadIdPhoto>>>上传文件失败,hkCustNo :{}", hkCusNo, e);
        }
        //生成压缩文件
        String thumbnailName = HkOpenImageUrlUtils.getThumbnailNameByBaseFileName(baseFileName);
        //压缩图片添加后缀
        String thumbnailFileName = thumbnailName + MarkConstants.SEPARATOR_DOT + imageFormatType;
        //上传压缩图
        try {
            InputStream inputStream = ImageUtils.compressImage(ImageUtils.byteToInputStream(bytes));
            HFileService.getInstance().write(ExternalConstant.PAY_VOUCHER_TEMP_STORE_CONFIG, fileUrl, thumbnailFileName, inputStream);
        } catch (Exception e) {
            log.error("batchUploadIdPhoto>>>上传压缩图失败,hkCustNo :{}", hkCusNo, e);
        }
        String realFileUrl = fileUrl + fileName;
        String thumbnailFileUrl = fileUrl + thumbnailFileName;
        return new ImageVO(realFileUrl, thumbnailFileUrl);
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.signnature.ESignatureVO
     * @description: 电子签名查询接口
     * @author: jinqing.rao
     * @date: 2023/12/11 16:11
     * @since JDK 1.8
     */
    public HkOpenAcctESignatureVO queryOpenSignatureImages() {
        return openAcctSignatureService.queryOpenSignatureImages();

    }

    /**
     * @param request 请求参数
     * @return void
     * @description: 实名认证的接口
     * @author: jinqing.rao
     * @date: 2023/12/12 15:56
     * @since JDK 1.8
     */
    public void queryRealNameAuthenticationResult(RealNameAuthResultRequest request) {
        String status = identityCardOcrOuterService.queryRealNameAuthenticationResult(request.getCustName(), IdTypeEnum.IDCARD, request.getIdNo());
        if (!RnAuthStateEnum.RNAUTH_SUCESS.getValue().equals(status)) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_IDENTITY_AUTH_ERROR);
        }
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenCustFileVO
     * @description:(获取开户文件列表数据)
     * @author: xufanchao
     * @date: 2023/12/14 17:28
     * @since JDK 1.8
     */
    public OpenCustFileVO getOpenCustFileVO() {
        OpenCustFileVO openCustFileVO = new OpenCustFileVO();
        // 获取当前已登陆的香港客户号数据
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        // 根据 配置的文件类型 001 002 003 获取目前需要的开户文件信息
        List<String> typeList = Arrays.asList(openFileTypeList.split("\\s*,\\s*"));
        List<HkOpenFileDTO> openCustFile = hkAccFileOuterService.getOpenCustFile(loginHkCustNo, typeList);
        // 数据转换
        List<OpenFileVO> openFileVOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(openCustFile)) {
            openFileVOList = openCustFile.stream()
                    .map(openFileDTO -> {
                        FilePreviewReqDTO reqDTO = new FilePreviewReqDTO();
                        reqDTO.setFileName(openFileDTO.getFilaName());
                        reqDTO.setFilePath(openFileDTO.getFileUrl());
                        return new OpenFileVO(openFileDTO.getFilaName(), openFileDTO.getFileUrl(), openFileDTO.getFileTypeName(), openFileDTO.getFileType());
                    })
                    .collect(Collectors.toList());
        } else {
            openFileVOList = null;
        }
        openCustFileVO.setOpenFileVOList(openFileVOList);

        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(loginHkCustNo);
        openCustFileVO.setEmailMask(hkCustInfo.getEmailMask());
        openCustFileVO.setEbrokerID(hkCustInfo.getEbrokerId());
        return openCustFileVO;
    }

    /**
     * @description: 获取开户合同
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.ContractInfoNewVO.OpenContractFileVO
     * @author: jinqing.rao
     * @date: 2025/7/3 16:20
     * @since JDK 1.8
     */
    public ContractInfoNewVO.OpenContractFileVO getOpenContractFileVO() {
        ContractInfoNewVO.OpenContractFileVO openCustFileVO = new ContractInfoNewVO.OpenContractFileVO();
        // 获取当前已登陆的香港客户号数据
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        // 根据 配置的文件类型 001 002 003 获取目前需要的开户文件信息
        List<String> typeList = Arrays.asList(openFileTypeList.split("\\s*,\\s*"));
        List<HkOpenFileDTO> openCustFile = hkAccFileOuterService.getOpenCustFile(loginHkCustNo, typeList);
        if(CollectionUtils.isEmpty(openCustFile)){
            return openCustFileVO;
        }
        // 数据转换
        List<ContractInfoNewVO.OpenFileInfo>  openFileVOList = openCustFile.stream()
                .map(openFileDTO -> {
                    ContractInfoNewVO.OpenFileInfo openFileInfo = new ContractInfoNewVO.OpenFileInfo();
                    openFileInfo.setFilaName(openFileDTO.getFilaName());
                    openFileInfo.setFileUrl(openFileDTO.getFileUrl());
                    openFileInfo.setFileUrlType(FileDownLoadTypeEnum.STATIC_FILE_URL.getCode());
                    openFileInfo.setFileType(openFileDTO.getFileType());
                    openFileInfo.setFileTypeName(openFileDTO.getFileTypeName());
                    openFileInfo.setUpDt(openFileDTO.getUpDt());
                    return openFileInfo;
                })
                .collect(Collectors.toList());

        openCustFileVO.setOpenFileList(openFileVOList);

        String signDate = null;
        // 获取 List<OpenFileVO>  openFileVOList 中 UpDt 最大的时间
        Optional<ContractInfoNewVO.OpenFileInfo> latestFile = openFileVOList.stream()
                .max(Comparator.comparing(ContractInfoNewVO.OpenFileInfo::getUpDt));
        if(latestFile.isPresent()){
            signDate = DateUtils.formatDateStr(latestFile.get().getUpDt(),DateUtils.YYYY_MM_DD_HH_MM, DateUtils.YYYYMMDD);
        }
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(loginHkCustNo);
        openCustFileVO.setEmailMask(hkCustInfo.getEmailMask());
        openCustFileVO.setSignDate(signDate);
        openCustFileVO.setTypeName(ContractFileBizTypeEnum.OPEN_ACCOUNT_FILE.getDesc());
        return openCustFileVO;
    }


    /**
     * @param
     * @return java.lang.String
     * @description:(开户文件发送邮箱接口)
     * @author: xufanchao
     * @date: 2023/12/14 19:04
     * @since JDK 1.8
     */
    public String sendOpenFileToEmail() {
        // 获取当前登陆用户号
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        // 获取当前香港客户信息
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(loginHkCustNo);
        // 根据 配置的文件类型 001 002 003 获取目前需要的开户文件信息
        List<String> typeList = Arrays.asList(openFileTypeList.split("\\s*,\\s*"));
        hkAccFileOuterService.sendEmailWithFile(loginHkCustNo, typeList);
        return hkCustInfo.getEmailMask();
    }

    /**
     * @param request
     * @return void
     * @description:(调用入金接口)
     * @author: xufanchao
     * @date: 2023/12/15 16:06
     * @since JDK 1.8
     */
    public void deposit(DepositRequest request) {
        // 校验入参
        ParamsValidator.validateParams(request, "amount", "cpAcctNo", "remitCurCode");
        if (CollectionUtils.isEmpty(request.getRemitImageUrls())) {
            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR);
        }
        List<RemitImageVO> remitImageUrls = request.getRemitImageUrls();
        // 获取 remitImageUrl 地址数据
        List<String> remitImageUrlList = remitImageUrls.stream().map(RemitImageVO::getUrl).collect(Collectors.toList());
        List<String> txChannelEnums = Arrays.asList(TxChannelEnum.WAP.getCode(), TxChannelEnum.HK_APP.getCode(), TxChannelEnum.H5.getCode());
        hkAccFileOuterService.deposit(getHkCusNo(), request.getCpAcctNo(), request.getAmount(),
                request.getRemitCurCode(), request.getRemark(), remitImageUrlList, PayVoucherTypeEnum.OPEN_ACCOUNT_CONFIRM.getCode(),
                txChannelEnums, HwPayVoucherFileSourceEnum.DTMS_ORDER.getCode());
    }

    /**
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.DepositsCertificateVO
     * @description:(查看入金打款凭证接口)
     * @author: xufanchao
     * @date: 2023/12/18 13:06
     * @since JDK 1.8
     */
    public DepositsCertificateVO getDepositsCertificate(DepositsCertificateRequest request) {
        // 获取当前登录用户的香港客户号
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        // 这里是跌打版本兼容，前版本没有 请求参数的，默认查询开户的入金凭证，后版本新增了打款凭证列表需要支持打款凭证号查询
        if (StringUtils.isBlank(request.getVoucherNo())) {
            // 获取入金的凭证数据
            List<String> txChannelEnums = Arrays.asList(TxChannelEnum.WAP.getCode(), TxChannelEnum.HK_APP.getCode(), TxChannelEnum.H5.getCode());
            DepositsCertificateDTO depositsCertificateDTO = hkAccFileOuterService.getDepositsCertificate(loginHkCustNo, PayVoucherTypeEnum.OPEN_ACCOUNT_CONFIRM.getCode(), txChannelEnums);
            return depositTransToVO(depositsCertificateDTO);
        }

        DepositsCertificateDTO depositsCertificateDTO = hkAccFileOuterService.getDepositsCertificate(loginHkCustNo, request.getVoucherNo());
        return depositTransToVO(depositsCertificateDTO);
    }

    /**
     * @param depositsCertificateDTO
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.DepositsCertificateVO
     * @description:(dto 转为 vo)
     * @author: xufanchao
     * @date: 2023/12/18 14:24
     * @since JDK 1.8
     */
    private DepositsCertificateVO depositTransToVO(DepositsCertificateDTO depositsCertificateDTO) {
        DepositsCertificateVO depositsCertificateVO = new DepositsCertificateVO();
        //调用cms的银行logo配置接口
        depositsCertificateVO.setBankLogoUrl(depositsCertificateDTO.getBankLogoUrl());
        depositsCertificateVO.setBankName(depositsCertificateDTO.getBankName());
        depositsCertificateVO.setBankAcctMask(depositsCertificateDTO.getBankAcctMask());
        depositsCertificateVO.setBankAcctDigest(depositsCertificateDTO.getBankAcctDigest());
        depositsCertificateVO.setCpAcctNo(depositsCertificateDTO.getCpAcctNo());
        depositsCertificateVO.setSwiftCode(depositsCertificateDTO.getSwiftCode());
        depositsCertificateVO.setRemitCurCode(depositsCertificateDTO.getRemitCurCode());
        depositsCertificateVO.setAmount(depositsCertificateDTO.getAmount());
        depositsCertificateVO.setBankCode(depositsCertificateDTO.getBankCode());
        depositsCertificateVO.setBankName(depositsCertificateDTO.getBankName());
        depositsCertificateVO.setRemark(depositsCertificateDTO.getRemark());
        depositsCertificateVO.setVoucherType(depositsCertificateDTO.getVoucherType());
        depositsCertificateVO.setRelationOrderNo(depositsCertificateDTO.getTradeOrderNo());
        if (CollectionUtils.isNotEmpty(depositsCertificateDTO.getRemitImageList())) {
            List<RemitImageVO> remitImageVOS = new ArrayList<>();
            depositsCertificateDTO.getRemitImageList().forEach(it -> {
                RemitImageVO imageVO = new RemitImageVO();
                imageVO.setExampleFileFormatType(it.getExampleFileFormatType());
                imageVO.setThumbnailUrl(it.getThumbnailUrl());
                imageVO.setUrl(it.getUrl());
                remitImageVOS.add(imageVO);
            });
            depositsCertificateVO.setRemitImageList(remitImageVOS);
        }
        if (CollectionUtils.isNotEmpty(depositsCertificateDTO.getCheckResult())) {
            List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS = new ArrayList<>();
            depositsCertificateDTO.getCheckResult().forEach(it -> {
                HkOpenAcctCheckVO hkOpenAcctCheckVO = new HkOpenAcctCheckVO(it.getFileName(), it.getReason());
                hkOpenAcctCheckVOS.add(hkOpenAcctCheckVO);
            });
            depositsCertificateVO.setCheckResult(hkOpenAcctCheckVOS);
        }


        return depositsCertificateVO;
    }

    /**
     * @param request 地址信息类型
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctAddressInfoVO
     * @description: 根据地址的类型获取地址的详细信息
     * @author: jinqing.rao
     * @date: 2023/12/19 14:49
     * @since JDK 1.8
     */
    public HkOpenAcctAddressInfoVO queryAddressInfoByType(HkOpenAcctAddressRequest request) {
        return openPersonalInfoService.queryAddressInfoByType(request);
    }

    /**
     * @param request 请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctProCitySaveVO
     * @description: 海外小程序信息填写页, 地址保存
     * @author: jinqing.rao
     * @date: 2023/12/19 16:05
     * @since JDK 1.8
     */
    public HkOpenAcctProCitySaveVO saveAddressInfo(HkOpenAcctProCityRequest request) {
        return openPersonalInfoService.saveAddressInfo(request);
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckAccountVO
     * @description: 校验手机号和邮箱是否
     * @author: jinqing.rao
     * @date: 2023/12/20 10:53
     * @since JDK 1.8
     */
    public void checkMobileEmail(HkOpenAcctCheckAccountRequest request) {
        String hkCusNo = getHkCusNo();
        if (MOBILE.equals(request.getType())) {
            HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfoByMobile(request.getAreaCode(), DigestUtil.digest(request.getMobile()));
            HkOpenAcctValidator.validateHkCustInfoDTO(hkCusNo, hkCustInfoDTO, ExceptionCodeEnum.HK_OPEN_ACCOUNT_MOBILE_BIND);
        }
        if (EMAIL.equals(request.getType())) {
            HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfoByEmail(DigestUtil.digest(request.getEmail()));
            HkOpenAcctValidator.validateHkCustInfoDTO(hkCusNo, hkCustInfoDTO, ExceptionCodeEnum.HK_OPEN_ACCOUNT_EMAIL_BIND);
        }
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.dtmsproduct.LicenseeDetailVO
     * @description: 查询持牌人信息
     * @author: jinqing.rao
     * @date: 2023/12/21 15:40
     * @since JDK 1.8
     */
    public LicenseeDetailVO queryLicenseeInfoDetail() {
        return openAcctSignatureService.queryLicenseeInfoDetail();

    }

    /**
     * @param request 签名照片请求
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.signnature.ESignatureVO
     * @description: 电子签名验证
     * @author: jinqing.rao
     * @date: 2023/12/14 19:19
     * @since JDK 1.8
     */
    public ESignatureImageVO verificationElectronicSignature(HkOpenAccESignatureImageRequest request) {
        return openAcctSignatureService.verificationElectronicSignature(request);
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.HkOpenAcctSubmitResultVO
     * @description: 查询开户的结果
     * @author: jinqing.rao
     * @date: 2023/12/25 20:52
     * @since JDK 1.8
     */
    public HkOpenAcctSubmitResultVO querySubmitResult() {
        HkOpenAcctSubmitResultVO hkOpenAcctSubmitResultVO = new HkOpenAcctSubmitResultVO();
        String hkCusNo = getHkCusNo();
        HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(getHkCusNo());
        if (null == hkOpenAccOrderInfoDTO || null == hkOpenAccOrderInfoDTO.getDealNo()) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_ORDER_NOT_EXIST);
        }
        HkOpenAcctCheckInfoDTO accountCheckInfoDTO = hkOpenAccOrderInfoDTO.getAccountCheckInfoDTO();
        boolean equals = HkOpenAcctOrderStatusEnum.REJECTED_TO_CUSTOMER.getCode().equals(hkOpenAccOrderInfoDTO.getAcctOrderStatus());
        hkOpenAcctSubmitResultVO.setStatus(equals ? "0" : "1");
        List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS = ReflectUtils.extractFieldInfo(accountCheckInfoDTO);
        String minPage = "1";
        if (CollectionUtils.isNotEmpty(hkOpenAcctCheckVOS)) {
            //没有数据库,申请开户订单被驳回时，还需要将信息反写到缓存里面去
            initCheckAcctOpenOrderPassReason(hkOpenAcctCheckVOS, minPage, hkCusNo, hkOpenAcctSubmitResultVO);
        }
        //如果是驳回的状态,需要将订单信息反写到缓存中
        // 证件信息页
        openAcctIdInfoService.queryOpenIdDetail();
        return hkOpenAcctSubmitResultVO;
    }

    /**
     * @param hkOpenAcctCheckVOS
     * @param minPage
     * @param hkCusNo
     * @param hkOpenAcctSubmitResultVO
     * @return void
     * @description: 审核不通过时, 需要定位到错误的第一个页面, 并且初始化每个页面不同的问题原因
     * 1.需要展示全部的错误原因
     * 2.需要处理每个页面的错误原因
     * @author: jinqing.rao
     * @date: 2024/3/25 20:04
     * @since JDK 1.8
     */
    private void initCheckAcctOpenOrderPassReason(List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS, String minPage, String hkCusNo, HkOpenAcctSubmitResultVO hkOpenAcctSubmitResultVO) {
        //证件信息页的审核错误信息
        List<HkOpenAcctCheckVO> idNoPage = new ArrayList<>();
        //个人信息页的审核错误信息
        List<HkOpenAcctCheckVO> personalPage = new ArrayList<>();
        //职业信息审核字段集合
        List<HkOpenAcctCheckVO> occupationPage = new ArrayList<>();
        //声明信息页的审核错误信息
        List<HkOpenAcctCheckVO> declarationPage = new ArrayList<>();
        //投资经验页的审核错误信息
        List<HkOpenAcctCheckVO> investExpPage = new ArrayList<>();
        //银行信息页的审核错误信息
        List<HkOpenAcctCheckVO> bankInfoPage = new ArrayList<>();
        //电子签名页的审核错误信息
        List<HkOpenAcctCheckVO> signPage = new ArrayList<>();
        //定位错误信息的最小页
        minPage = initCheckInfo(hkOpenAcctCheckVOS, minPage, idNoPage, personalPage, occupationPage, declarationPage, investExpPage, bankInfoPage, signPage);
        openAcctCatchService.saveCheckReason(HkOpenAccountStepEnum.IDENTITY_INFORMATION_CHECK_RESULT, hkCusNo, JSON.toJSONString(idNoPage));
        openAcctCatchService.saveCheckReason(HkOpenAccountStepEnum.PERSONAL_INFORMATION_CHECK_RESULT, hkCusNo, JSON.toJSONString(personalPage));
        openAcctCatchService.saveCheckReason(HkOpenAccountStepEnum.PROFESSIONAL_INFORMATION_CHECK_RESULT, hkCusNo, JSON.toJSONString(occupationPage));
        openAcctCatchService.saveCheckReason(HkOpenAccountStepEnum.DECLARATION_INFORMATION_CHECK_RESULT, hkCusNo, JSON.toJSONString(declarationPage));
        openAcctCatchService.saveCheckReason(HkOpenAccountStepEnum.INVESTMENT_EXPERIENCE_RISK_CHECK_RESULT, hkCusNo, JSON.toJSONString(investExpPage));
        openAcctCatchService.saveCheckReason(HkOpenAccountStepEnum.BANK_CARD_CHECK_RESULT, hkCusNo, JSON.toJSONString(bankInfoPage));
        openAcctCatchService.saveCheckReason(HkOpenAccountStepEnum.ELECTRONIC_SIGNATURE_CHECK_RESULT, hkCusNo, JSON.toJSONString(signPage));
        List<HkOpenAcctCheckVO> allCheckResults = new ArrayList<>();
        allCheckResults.addAll(idNoPage);
        allCheckResults.addAll(personalPage);
        allCheckResults.addAll(occupationPage);
        allCheckResults.addAll(declarationPage);
        allCheckResults.addAll(investExpPage);
        allCheckResults.addAll(bankInfoPage);
        allCheckResults.addAll(signPage);
        hkOpenAcctSubmitResultVO.setCheckResult(allCheckResults);
        //App1.0.0版本,需要定位到最小的错误页面,这里兼容小程序 默认 第一页
        if (RequestUtil.isHkAppLogin()) {
            hkOpenAcctSubmitResultVO.setMinCheckPage(minPage);
        } else {
            hkOpenAcctSubmitResultVO.setMinCheckPage("1");
        }

    }

    private static String initCheckInfo(List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS, String minPage, List<HkOpenAcctCheckVO> idNoPage, List<HkOpenAcctCheckVO> personalPage, List<HkOpenAcctCheckVO> occupationPage, List<HkOpenAcctCheckVO> declarationPage, List<HkOpenAcctCheckVO> investExpPage, List<HkOpenAcctCheckVO> bankInfoPage, List<HkOpenAcctCheckVO> signPage) {
        List<String> pageList = new ArrayList<>();
        for (HkOpenAcctCheckVO hkOpenAcctCheckVO : hkOpenAcctCheckVOS) {
            HkOpenAcctCheckFilePageEnum acctCenterFileName = HkOpenAcctCheckFilePageEnum.getPageByAcctCenterFileName(hkOpenAcctCheckVO.getFileName());
            if (null != acctCenterFileName) {
                String page = acctCenterFileName.getPage();
                if (!pageList.contains(page)) {
                    pageList.add(page);
                }
                //将数据归类的具体的页面
                switchPage(idNoPage, personalPage, occupationPage, declarationPage, investExpPage, bankInfoPage, signPage, hkOpenAcctCheckVO, page, acctCenterFileName);
            }
        }
        if (CollectionUtils.isNotEmpty(pageList)) {
            Collections.sort(pageList);
            minPage = pageList.get(0);
        }
        return minPage;
    }

    /**
     * @param idNoPage
     * @param personalPage
     * @param occupationPage
     * @param declarationPage
     * @param investExpPage
     * @param bankInfoPage
     * @param signPage
     * @param hkOpenAcctCheckVO
     * @param page
     * @param acctCenterFileName
     * @return void
     * @description: 匹配到具体的页面
     * @author: jinqing.rao
     * @date: 2024/3/26 14:58
     * @since JDK 1.8
     */
    private static void switchPage(List<HkOpenAcctCheckVO> idNoPage, List<HkOpenAcctCheckVO> personalPage, List<HkOpenAcctCheckVO> occupationPage, List<HkOpenAcctCheckVO> declarationPage, List<HkOpenAcctCheckVO> investExpPage, List<HkOpenAcctCheckVO> bankInfoPage, List<HkOpenAcctCheckVO> signPage, HkOpenAcctCheckVO hkOpenAcctCheckVO, String page, HkOpenAcctCheckFilePageEnum acctCenterFileName) {
        switch (page) {
            case "1":
                //证件信息页
                idNoPage.add(new HkOpenAcctCheckVO(acctCenterFileName.getFileName(), hkOpenAcctCheckVO.getReason()));
                break;
            case "2":
                //个人信息页的审核错误信息
                personalPage.add(new HkOpenAcctCheckVO(acctCenterFileName.getFileName(), hkOpenAcctCheckVO.getReason()));
                break;
            case "3":
                //职业信息页面的审核错误信息
                occupationPage.add(new HkOpenAcctCheckVO(acctCenterFileName.getFileName(), hkOpenAcctCheckVO.getReason()));
                break;
            case "4":
                //声明信息页
                declarationPage.add(new HkOpenAcctCheckVO(acctCenterFileName.getFileName(), hkOpenAcctCheckVO.getReason()));
                break;
            case "5":
                //投资经验及目页
                investExpPage.add(new HkOpenAcctCheckVO(acctCenterFileName.getFileName(), hkOpenAcctCheckVO.getReason()));
                break;
            case "6":
                //银行卡页
                bankInfoPage.add(new HkOpenAcctCheckVO(acctCenterFileName.getFileName(), hkOpenAcctCheckVO.getReason()));
                break;
            case "8":
                //签名页
                signPage.add(new HkOpenAcctCheckVO(acctCenterFileName.getFileName(), hkOpenAcctCheckVO.getReason()));
                break;
            default:
                //其他页
        }
    }


    public HkOpenDownImageVO downLoadUploadImage(KhOpenAcctImageRequest hkOpenAcctImageRequest) {
        HkOpenDownImageVO hkOpenDownImageVO = new HkOpenDownImageVO();
        //根据URL,获取文件名,URL
        String relativeUrl = hkOpenAcctImageRequest.getRelativeUrl();
        String[] split = relativeUrl.split("/");
        if (split == null || split.length != 4) {
            throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_FILE_NOT_EXIST);
        }
        //证件类型 split[0] ,香港客户号 split[1],时间戳 split[2],文件名称 split[3]
        hkOpenDownImageVO.setImageName(split[3]);
        //获取文件格式
        String imageFormat = relativeUrl.substring(MarkConstants.SEPARATOR_DOT.length());
        //相对路径
        String relativePath = split[0] + MarkConstants.SEPARATOR_SLASH + split[1] + MarkConstants.SEPARATOR_SLASH + split[2];
        hkOpenDownImageVO.setImageFormat(imageFormat);
        try {
            //获取文件流
            byte[] bytes = HFileService.getInstance().read2Bytes(ExternalConstant.PAY_VOUCHER_TEMP_STORE_CONFIG, relativePath, split[3]);
            hkOpenDownImageVO.setImageBase(ImageUtils.bytesToBase64(bytes));
        } catch (Exception e) {
            throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_FILE_DOWNLOAD_ERROR);
        }
        return hkOpenDownImageVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.pdf.HkOpenAcctPdfVO
     * @description: 通过枚举类型获取对应的PDF文件
     * @author: jinqing.rao
     * @date: 2024/1/17 13:35
     * @since JDK 1.8
     */
    public HkOpenAcctPdfVO previewPdfByBizCode(HkOpenAcctPdfRequest request) {
        return openAcctSignatureService.previewPdfByBizCode(request);

    }

    /**
     * @param request 文件流
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.HkOpenImageVO
     * @description: 文件流上传接口
     * @author: jinqing.rao
     * @date: 2024/2/29 20:38
     * @since JDK 1.8
     */
    public HkOpenImageVO uploadFileByStream(HkAppUploadFileRequest request) {
        HkOpenImageVO hkOpenImageVO = new HkOpenImageVO();
        List<ImageVO> openImageVOList = new ArrayList<>();
        for (MultipartFile multipartFile : request.getFile()) {
            try {
                String contentType = multipartFile.getContentType();
                String originalFilename = multipartFile.getOriginalFilename();
                String name = originalFilename.substring(0, originalFilename.lastIndexOf("."));
                String type = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
                ImageVO imageVO = getUploadImageUrl(request.getImageType(), request.getHkCustNo(), multipartFile.getBytes(), type, name);
                openImageVOList.add(imageVO);
            } catch (IOException e) {
                throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_APP_FILE_UPLOAD_ERROR, e);
            }
        }
        hkOpenImageVO.setOpenImageVOList(openImageVOList);
        return hkOpenImageVO;
    }

    public HkOpenImageVO uploadFile(HkAppUploadFileRequest request) {
        HkOpenImageVO hkOpenImageVO = new HkOpenImageVO();
        List<ImageVO> openImageVOList = new ArrayList<>();
        for (MultipartFile multipartFile : request.getFile()) {
            try {
                String contentType = multipartFile.getContentType();
                String originalFilename = multipartFile.getOriginalFilename();
                String name = originalFilename.substring(0, originalFilename.lastIndexOf("."));
                String type = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
                ImageVO imageVO = getUploadFileUrl(request.getImageType(), request.getHkCustNo(), multipartFile.getBytes(), type, name);
                openImageVOList.add(imageVO);
            } catch (IOException e) {
                throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_APP_FILE_UPLOAD_ERROR, e);
            }
        }
        hkOpenImageVO.setOpenImageVOList(openImageVOList);
        return hkOpenImageVO;
    }

    /**
     * @param hkCustNo
     * @param bizCode
     * @param channelCode
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.pdf.HkOpenAcctPdfVO
     * @description: App中H5页面开户的PDF预览
     * @author: jinqing.rao
     * @date: 2024/3/7 16:17
     * @since JDK 1.8
     */
    public void previewPdfStreamByBizCode(String hkCustNo, String bizCode, String channelCode, HttpServletResponse response) {
        openAcctSignatureService.previewPdfStreamByBizCode(hkCustNo, bizCode, channelCode, response);
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.common.base.Body
     * @description: OCR文件流上传识别
     * @author: jinqing.rao
     * @date: 2024/3/8 16:23
     * @since JDK 1.8
     */
    public IdentityCardVO queryIdentityOcrDetailByFileStream(HkAccIdentityOcrRequest request) {
        MultipartFile[] file = request.getFile();
        MultipartFile frontFileStream = file[0];
        MultipartFile backFileStream = file[1];
        String frontContentType = frontFileStream.getContentType();
        String backContentType = backFileStream.getContentType();
        // 添加这行日志,是因为产线发现 一个haoxiaomai的文件后缀。
        log.info("queryIdentityOcrDetailByFileStream>>> 文件类型 frontContentType:{},backContentType{}", frontContentType, backContentType);
        frontContentType = StringUtils.isBlank(frontContentType) ? "jpg" : frontContentType.substring(frontContentType.lastIndexOf("image/") + 6);
        backContentType = StringUtils.isBlank(backContentType) ? "jpg" : backContentType.substring(backContentType.lastIndexOf("image/") + 6);
        // 产线兼容方案,前端类型某种请款下回传域名,临时处理 20240705
        frontContentType = hkIdImageFileType.contains(frontContentType) ? "jpg" : frontContentType;
        backContentType = hkIdImageFileType.contains(backContentType) ? "jpg" : backContentType;
        log.info("queryIdentityOcrDetailByFileStream>>> 替换后文件类型 frontContentType:{},backContentType{}", frontContentType, backContentType);
        IdentityCardVO identityCardVO = openAcctIdInfoService.queryIdentityOcrDetailByFileStream(frontFileStream, backFileStream, frontContentType, backContentType);
        log.info("queryIdentityOcrDetailByFileStream >>> 返回结果,identityCardVO :{}", JSON.toJSONString(identityCardVO));
        return identityCardVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctExpirationPageVO
     * @description: 查询开户的缓存失效的页面
     * @author: jinqing.rao
     * @date: 2024/8/26 18:32
     * @since JDK 1.8
     */
    public HkOpenAcctExpirationPageVO queryExpirePage(HkOpenAcctExpirationPageRequest request) {
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        if (StringUtils.isBlank(loginHkCustNo)) {
            throw new BusinessException(ExceptionCodeEnum.LOGIN_INFO_ERROR);
        }
        HkOpenAcctExpirationPageVO hkOpenAcctExpirationPageVO = new HkOpenAcctExpirationPageVO();
        hkOpenAcctExpirationPageVO.setExpirePage("0");
        //检索没有页面的数据是否缺失
        HkOpenAccountStepEnum hkOpenAccountStepEnum = openAcctCatchService.queryExpirePage(loginHkCustNo);
        if (null != hkOpenAccountStepEnum) {
            hkOpenAcctExpirationPageVO.setExpirePage(hkOpenAccountStepEnum.getNumberCode());
        }
        return hkOpenAcctExpirationPageVO;
    }

    /**
     * 检查缓存失效的页面枚举
     *
     * @param hkCustNo
     * @param instance
     * @param invalidStepEnum
     * @return
     */
    private static HkOpenAccountStepEnum getHkOpenAccountInvalidStepEnum(String hkCustNo, CacheService instance, HkOpenAccountStepEnum invalidStepEnum) {
        List<HkOpenAccountStepEnum> stepEnums = Arrays.asList(HkOpenAccountStepEnum.IDENTITY_INFORMATION,
                HkOpenAccountStepEnum.PERSONAL_INFORMATION,
                HkOpenAccountStepEnum.PROFESSIONAL_INFORMATION,
                HkOpenAccountStepEnum.DECLARATION_INFORMATION,
                HkOpenAccountStepEnum.INVESTMENT_EXPERIENCE,
                HkOpenAccountStepEnum.BANK_CARD);
        for (HkOpenAccountStepEnum acctStepEnum : stepEnums) {
            String key = acctStepEnum.getHkOpenAccountStepKey(hkCustNo);
            if (!instance.exists(key)) {
                if (null == invalidStepEnum) {
                    invalidStepEnum = acctStepEnum;
                    continue;
                }
                invalidStepEnum = Integer.parseInt(invalidStepEnum.getNumberCode()) < Integer.parseInt(acctStepEnum.getCode()) ? invalidStepEnum : acctStepEnum;
            }
        }
        return invalidStepEnum;
    }
}
