package com.howbuy.crm.cgi.extservice.vo.balance;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 非全委托现金资产查询响应类
 * @author: 陈杰文
 * @date: 2025-06-17 14:13:43
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryNotFullCashBalanceAssetVO extends Body implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否显示资产 [资产小眼睛]
     */
    private String showAsset;

    /**
     * 现金资产总资产
     */
    private String cashBalanceAsset;

    /**
     * 可用余额
     */
    private String availableBalance;

    /**
     * 冻结余额
     */
    private String freezeBalance;

    /**
     * 在途提取申请笔数
     */
    private String inTransitDrawMoneyAppCount;

    /**
     * 在途提取申请单号
     */
    private String inTransitDrawMoneyAppNo;

    /**
     * 在途提取资金笔数
     */
    private String inTransitDrawMoneyCapitalCount;

    /**
     * 在途提取资金单号
     */
    private String inTransitDrawMoneyCapitalNo;

    /**
     * 在途打款凭证笔数
     */
    private String inTransitPayVoucherCount;

    /**
     * 在途打款凭证单号
     */
    private String inTransitPayVoucherNo;

    /**
     * 现金资产明细列表
     */
    private List<CashBalanceDetailVO> cashBalanceDetailList;
} 