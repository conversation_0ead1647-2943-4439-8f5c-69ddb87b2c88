package com.howbuy.crm.cgi.extservice.common.enums.product;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description: 产品子业务类型枚举
 * @author: 陈杰文
 * @date: 2025-07-04 18:57:14
 * @since JDK 1.8
 */
public enum ProductSubBizTypeEnum {

    /**
     * 阳光私募
     */
    SUNSHINE_PRIVATE("1", "阳光私募", ProductBizTypeEnum.OVERSEAS_PRIVATE),
    
    /**
     * 私募股权
     */
    PRIVATE_EQUITY("2", "私募股权", ProductBizTypeEnum.OVERSEAS_PRIVATE),
    ;

    private final String code;
    private final String description;
    private final ProductBizTypeEnum parentType;

    /**
     * 根据编码获取枚举对象
     *
     * @param code 编码
     * @return 枚举对象
     */
    public static ProductSubBizTypeEnum getByCode(String code) {
        return Stream.of(values())
                .filter(type -> type.code.equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(String code) {
        ProductSubBizTypeEnum typeEnum = getByCode(code);
        return typeEnum == null ? null : typeEnum.getDescription();
    }

    /**
     * 根据父类型获取所有子类型
     *
     * @param parentType 父类型
     * @return 子类型列表
     */
    public static List<ProductSubBizTypeEnum> getByParentType(ProductBizTypeEnum parentType) {
        return Stream.of(values())
                .filter(type -> type.parentType == parentType)
                .collect(Collectors.toList());
    }

    /**
     * 根据父类型编码获取所有子类型
     *
     * @param parentCode 父类型编码
     * @return 子类型列表
     */
    public static List<ProductSubBizTypeEnum> getByParentCode(String parentCode) {
        ProductBizTypeEnum parentType = ProductBizTypeEnum.getByCode(parentCode);
        return parentType == null ? null : getByParentType(parentType);
    }

    /**
     * 获取编码
     *
     * @return 编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取父类型
     *
     * @return 父类型
     */
    public ProductBizTypeEnum getParentType() {
        return parentType;
    }

    /**
     * 获取父类型编码
     *
     * @return 父类型编码
     */
    public String getParentCode() {
        return parentType.getCode();
    }

    /**
     * 获取父类型描述
     *
     * @return 父类型描述
     */
    public String getParentDescription() {
        return parentType.getDescription();
    }

    ProductSubBizTypeEnum(String code, String description, ProductBizTypeEnum parentType) {
        this.code = code;
        this.description = description;
        this.parentType = parentType;
    }
} 