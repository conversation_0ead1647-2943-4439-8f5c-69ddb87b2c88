package com.howbuy.crm.cgi.extservice.request.agreement;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 补签协议列表请求参数
 * @author: hongdong.xie
 * @date: 2024-02-24 10:00:00
 */
@Setter
@Getter
public class SupplementalAgreementListRequest {
    
    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 渠道编码    储蓄罐持仓 = '2',   基金持仓 = '3',  全委服务 = '4',
     */
    private String channelCode;
} 