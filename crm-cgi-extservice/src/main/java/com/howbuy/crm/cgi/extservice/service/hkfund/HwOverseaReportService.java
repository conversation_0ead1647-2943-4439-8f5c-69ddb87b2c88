/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.hkfund;

import com.howbuy.crm.cgi.common.constants.OutReturnCodes;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.common.utils.FileUtils;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HwOverseaReportFileTypeEnum;
import com.howbuy.crm.cgi.extservice.request.hkfund.HwOverseaReportRequest;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HwOverseaReportListVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.QueryProductWealthAppointmentConfigDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.dubbo.QueryLatestProductWealthOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 海外资产报告服务
 * @date 2025/4/24 18:50
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HwOverseaReportService {

    // 财富配置报告
    private static final String WEALTH_ALLOCATION_REPORT = "好买香港海外市场观察";

    // 好买海外配置产品申赎日历
    private static final String APPOINTMENT_CALENDAR_SUMMARY = "好买海外配置产品申赎日历";

    @Resource
    private QueryLatestProductWealthOuterService queryLatestProductWealthOuterService;

    @Resource
    private HkCustInfoOuterService hkCustInfoOuterService;

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HwOverseaReportListVO
     * @description: 查询海外观察报告
     * @author: jinqing.rao
     * @date: 2025/4/25 10:15
     * @since JDK 1.8
     */
    public HwOverseaReportListVO queryHwOverseaReport(HwOverseaReportRequest request) {
        return queryHwOverseaReportListVO(request.getHkCustNo());
    }

    /**
     * @description: APP个人中心查询是否有海外资产报告接口
     * @param investorQualification
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HwOverseaReportListVO
     * @author: jinqing.rao
     * @date: 2025/4/27 9:41
     * @since JDK 1.8
     */
    public HwOverseaReportListVO queryHwOverseaReportForAppCenter(String hkCustNo,String investorQualification) {
        return getHwOverseaReportListVO(hkCustNo,investorQualification);
    }

    /**
     * @description: 查询海外资产报告列表
     * @param hkCustNo
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HwOverseaReportListVO
     * @author: jinqing.rao
     * @date: 2025/4/27 9:40
     * @since JDK 1.8
     */
    private HwOverseaReportListVO queryHwOverseaReportListVO(String hkCustNo) {


        // 查询用户的客户信息
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
        // 判断客户信息是否为空
        if (hkCustInfo == null || OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS.equals(hkCustInfo.getReturnCode())) {
           throw new BusinessException(ExceptionCodeEnum.HK_CUST_INFO_NOT_EXIST);
        }
        if(StringUtils.isBlank(hkCustInfo.getInvestorQualification())){
            return new HwOverseaReportListVO();
        }

        return getHwOverseaReportListVO(hkCustInfo.getHkCustNo(),hkCustInfo.getInvestorQualification());
    }

    /**
     * @description:(请在此添加描述)
     * @param hkCustNo	 香港客户号
     * @param investorQualification 客户类型
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HwOverseaReportListVO
     * @author: jinqing.rao
     * @date: 2025/5/16 16:34
     * @since JDK 1.8
     */
    private HwOverseaReportListVO getHwOverseaReportListVO(String hkCustNo,String investorQualification) {
        if(StringUtils.isBlank(investorQualification)){
            log.info("getHwOverseaReportListVO >>> 查询海外资产财富报告,客户客户投资者资质为空 hkCustNo : {}",hkCustNo);
            return new HwOverseaReportListVO();
        }
        try {
            HwOverseaReportListVO hwOverseaReportListVO = new HwOverseaReportListVO();
            // 查询产品财富配置信息
            QueryProductWealthAppointmentConfigDTO configDTO = queryLatestProductWealthOuterService
                    .queryLatestProductWealthAppointmentConfig(investorQualification);

            // 使用 Lambda 表达式创建报告列表
            List<HwOverseaReportListVO.HwOverseaReportVO> hwOverseaReportList = new ArrayList<>();

            // 添加财富分配报告
            addReportToList(hwOverseaReportList,
                    configDTO.getWealthAllocationReport(),
                    configDTO.getWealthAllocationReportUrl(),
                    WEALTH_ALLOCATION_REPORT,
                    configDTO.getCreateTime(),
                    configDTO.getUpdateTime());

            // 添加预约日历摘要
            addReportToList(hwOverseaReportList,
                    configDTO.getAppointmentCalendarSummary(),
                    configDTO.getAppointmentCalendarSummaryUrl(),
                    APPOINTMENT_CALENDAR_SUMMARY,
                    configDTO.getCreateTime(),
                    configDTO.getUpdateTime());

            if(CollectionUtils.isEmpty(hwOverseaReportList)){
                return new HwOverseaReportListVO();
            }
            // 过滤有效的报告并设置到返回对象中
            hwOverseaReportListVO.setOverseaReportList(
                    hwOverseaReportList.stream()
                            .filter(report -> StringUtils.isNotBlank(report.getFileType()))
                            .peek(report -> report.setFileName(Objects.requireNonNull(DateUtils.formatDateStr(configDTO.getYearMonth(), DateUtils.CHINESE_YYYYMMDD_MONTH, DateUtils.YYYY_MM))
                                    .concat(" ") // 空格
                                    .concat(report.getFileName())))
                            .collect(Collectors.toList()));

            return hwOverseaReportListVO;
        } catch (Exception e) {
            log.error("getHwOverseaReportListVO >>> 获取海外资产报告列表异常", e);
            return new HwOverseaReportListVO();
        }
    }

    /**
     * 添加报告到列表中
     * 
     * @param reportList 报告列表
     * @param fileName   文件名
     * @param fileUrl    文件URL
     */
    private void addReportToList(List<HwOverseaReportListVO.HwOverseaReportVO> reportList,
                                 String fileName,
                                 String fileUrl,
                                 String disName,
                                 LocalDateTime createTime,
                                 LocalDateTime updateTime) {
        if (StringUtils.isNotBlank(fileName) && StringUtils.isNotBlank(fileUrl)) {
            HwOverseaReportListVO.HwOverseaReportVO reportVO = new HwOverseaReportListVO.HwOverseaReportVO();
            // 文件名称
            reportVO.setFileName(disName);
            // 文件类型
            reportVO.setFileType(HwOverseaReportFileTypeEnum.getFileTypeByFileSuffix(
                    FileUtils.getFileExtension(fileName)));
            // 文件URL
            reportVO.setFileUrl(fileUrl);
            // 创建时间
            reportVO.setCreateTime(createTime);
            reportVO.setUpdateTime(updateTime);

            reportList.add(reportVO);
        }
    }
}
