package com.howbuy.crm.cgi.extservice.common.enums.refund;

public enum RefundApplyAuditStatusEnum {

    // 【0-正常、1-删除、2-未审核，3-审核拒绝、4-无需审核】
    NORMAL("0", "正常"),
    DELETED("1", "删除"),
    UNAUDITED("2", "未审核"),
    REFUSE("3", "审核拒绝"),
    NO_NEED_AUDIT("4", "无需审核");
    private final String code;
    private final String desc;

    RefundApplyAuditStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
