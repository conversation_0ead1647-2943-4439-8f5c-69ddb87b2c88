/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.common.base.PageRequest;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 问答回复请求对象
 *
 * <AUTHOR>
 * @date 2024-03-19 10:40:00
 */
@Getter
@Setter
public class QaReplyRequest extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 来源类型 0-1V1入口 1-工作台
     */
    private String source = "0";

    /**
     * 检索池类型 0-全池 1-推荐池
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "检索池类型", isRequired = true)
    private String searchPool;

    /**
     * 检索条件
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "检索条件", isRequired = true)
    private String searchCondition;

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 投顾编号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投顾编号", isRequired = true)
    private String conscode;

    /**
     * 一级回复内容
     */
    private String firstReply;

    /**
     * 二级回复内容
     */
    private String secondReply;
} 