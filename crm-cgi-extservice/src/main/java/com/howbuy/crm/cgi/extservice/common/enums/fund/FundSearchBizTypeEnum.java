package com.howbuy.crm.cgi.extservice.common.enums.fund;

public enum FundSearchBizTypeEnum {
    // 1 交易记录列表的基金模糊搜索  2.合同列表的基金模糊查询
    TRADE_RECORD("1","交易记录列表的基金模糊搜索"),
    CONTRACT("2","合同列表的基金模糊查询");

    private final String code;

    private final String desc;

    FundSearchBizTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FundSearchBizTypeEnum getByCode(String code) {
        for (FundSearchBizTypeEnum value : FundSearchBizTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
