package com.howbuy.crm.cgi.extservice.controller.hk.assetcertificate;

/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.assetcertificate.AssetCertificateSendRequest;
import com.howbuy.crm.cgi.extservice.service.assetcertificate.AssetCertificateService;
import com.howbuy.crm.cgi.extservice.vo.assetcertificate.AssetCertificateSendVO;
import com.howbuy.crm.cgi.extservice.vo.assetcertificate.AssetCertificateVerifyVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 资产证明控制器
 * @author: 陈杰文
 * @date: 2025-06-17 17:32:12
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hk/assetcertificate")
@Slf4j
public class AssetCertificateController {

    @Autowired
    private AssetCertificateService assetCertificateService;

    /**
     * @api {POST} /ext/hk/assetcertificate/send sendAssetCertificate()
     * @apiVersion 1.0.0
     * @apiGroup AssetCertificateController
     * @apiName sendAssetCertificate()
     * @apiDescription 资产证明发送接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号 必传
     * @apiParam (请求体) {String} idNo 证件号码 必传
     * @apiParam (请求体) {String} tradePassword 交易密码 必传
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","idNo":"123456789012345678","tradePassword":"123456"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱掩码
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"A","data":{"emailMask":"test***@example.com"},"description":"操作成功","timestampServer":"2025-06-17 17:32:12"}
     */
    @PostMapping("/send")
    public CgiResponse<AssetCertificateSendVO> sendAssetCertificate(@RequestBody AssetCertificateSendRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        
        // 调用服务层发送资产证明
        AssetCertificateSendVO result = assetCertificateService.sendAssetCertificate(request);
        
        return CgiResponse.ok(result);
    }

    /**
     * @api {POST} /ext/hk/assetcertificate/verify verifyAssetCertificate()
     * @apiVersion 1.0.0
     * @apiGroup AssetCertificateController
     * @apiName verifyAssetCertificate()
     * @apiDescription 资产证明身份验证接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号 必传
     * @apiParam (请求体) {String} idNo 证件号码 必传
     * @apiParam (请求体) {String} tradePassword 交易密码 必传
     * @apiParamExample 请求体示例
     * {"hkCustNo":"HK123456","idNo":"123456789012345678","tradePassword":"123456"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱掩码
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"A","data":{"emailMask":"test***@example.com"},"description":"操作成功","timestampServer":"2025-06-17 17:36:24"}
     */
    @PostMapping("/verify")
    public CgiResponse<AssetCertificateVerifyVO> verifyAssetCertificate(@RequestBody AssetCertificateSendRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        
        // 调用服务层验证资产证明身份
        AssetCertificateVerifyVO result = assetCertificateService.verifyAssetCertificate(request);
        
        return CgiResponse.ok(result);
    }
} 