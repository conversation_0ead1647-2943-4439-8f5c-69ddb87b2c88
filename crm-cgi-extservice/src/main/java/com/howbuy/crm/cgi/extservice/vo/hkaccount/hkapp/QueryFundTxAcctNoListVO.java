package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 全委账号列表查询响应对象
 * @author: jinqing.rao
 * @date: 2024/3/11 11:20
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundTxAcctNoListVO extends Body {

    private static final long serialVersionUID = 4423551455280300166L;
    /**
     * 账号列表
     */
    private List<FundTxAcctNoInfo> fundTxCodeList;

    @Setter
    @Getter
    public static class FundTxAcctNoInfo {
        /**
         * 账户名称
         */
        private String fundName;

        /**
         * 基金交易账号 0-非全委 1-全委
         */
        private String fundTxCodeType;

        /**
         * 基金交易账号
         */
        private String fundTxCode;
    }
} 