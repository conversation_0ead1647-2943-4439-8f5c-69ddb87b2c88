package com.howbuy.crm.cgi.extservice.vo.balance;

/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 现金提取记录实体类
 * @author: 陈杰文
 * @date: 2025-06-17 17:46:35
 * @since JDK 1.8
 */
@Setter
@Getter
public class CashDrawMoneyRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 现金提取Id
     */
    private String cashdRawMoneyId;

    /**
     * 申请日期 YYYYMMDD
     */
    private String appDt;

    /**
     * 申请时间 hhmmss
     */
    private String appTm;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 提取金额
     */
    private String drawmoneyAmount;

    /**
     * 香港资金账号
     */
    private String hkCpAcctNo;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行号
     */
    private String bankRegionCode;

    /**
     * 银行账户名称
     */
    private String bankAcctName;

    /**
     * 摘要-银行账号
     */
    private String bankAcctDigest;

    /**
     * 掩码-银行账号
     */
    private String bankAcctMask;

    /**
     * 提取状态 1-提取中 2-提取成功 3-提取失败
     */
    private String drawMoneyStatus;

    /**
     * 失败原因
     */
    private String failMsg;

    /**
     * 来源 待定枚举-TODO
     */
    private String source;

    /**
     * 修改时间 YYYYMMDDhhmmss
     */
    private String updateTime;

    /**
     * 转账时间 YYYYMMDDhhmmss
     */
    private String transferTime;
} 