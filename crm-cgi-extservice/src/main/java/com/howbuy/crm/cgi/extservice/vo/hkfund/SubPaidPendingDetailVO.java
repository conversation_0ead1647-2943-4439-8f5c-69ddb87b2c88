package com.howbuy.crm.cgi.extservice.vo.hkfund;

/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 购买页-基金信息查询
 * <AUTHOR>
 * @date 2024/4/9 14:37
 * @since JDK 1.8
 */
@Setter
@Getter
public class SubPaidPendingDetailVO extends Body implements Serializable {


    private static final long serialVersionUID = -3010835308400903282L;

    /**
     * 实缴订单信息
     */
    private PaidOrderVO  paidOrderVO;
    /**
     * 购买页基金信息
     */
    private FundInfoVO buyFundInfoVO;

    /**
     *  支付方式信息
     */
    private PayMethodInfoVO payMethodInfoVO;

    /**
     *  银行卡列表
     */
    private List<BuyFundBankCardVO> bankCardList;

    
    
    @Setter
    @Getter
    public static class PaidOrderVO implements Serializable {
        private static final long serialVersionUID = 5340411075226721659L;

        /**
         * 支付方式  1 银行卡  2 支票  3 银行卡
         */
        private String paymentType;

        /**
         * 打款截止日期
         */
        private String payEndDate;

        /**
         * 打款截止日期 hh:mm
         */
        private String payEndTime;

        /**
         * 基金名称
         */
        private String fundName;

        /**
         *  如果是银行卡支付，会返回资金账号
         */
        private String cpAcctNo;

        /**
         * 实缴金额
         */
        private String paidAmt;
    }

    @Setter
    @Getter
    public static class FundInfoVO implements Serializable {

        private static final long serialVersionUID = -3204781420812123623L;

        /**
         * 基金简称
         */
        private String fundShortName;

        /**
         *基金英文名称
         */
        private String fundEnName;

        /**
         * 1:是 0:否
         */
        private String supportPrebook;

        /**
         * 币种
         */
        private String currencyCode;

        /**
         * 币种描述
         */
        private String currencyDesc;
    }

    @Setter
    @Getter
     public static class PrebookBuyInfoVO implements Serializable {
         private static final long serialVersionUID = -5416567178557779695L;

         /**
          * 预约支付类型 1-电汇、2-支票、3-海外储蓄罐
          */
         private String prebookPayMethod;

         /**
          * 预约申请金额
          */
         private String prebookAppAmt;

         /**
          * 预约折扣率
          */
         private String prebookDiscountRate;

         /**
          * 预约单号
          */
         private String prebookDealNo;

     }
     @Setter
     @Getter
     public static class PayMethodInfoVO implements Serializable {
         private static final long serialVersionUID = -5416567178557779695L;

         /**
          *    否支持储蓄罐支付  0-不支持 1-支持
          */
         private String supportCxgPay;

         /**
          *    是否签约储蓄罐  0-未签署 1-签署
          */
         private String hasSignCxg;

         /**
          *  1 未生效  2 已生效  3 已过期
          */
         private String agreementValidStatus;

         /**
          *  储蓄罐下单结束时间  hhmmss
          */
         private String cxgOrderEndTime;

         /**
          *  当前时间的时分秒是否小于等于储蓄罐下单结束时间
          */
         private boolean beforeCxgOrderEndTime;

         /**
          *  是否支持电汇  0-不支持 1-支持
          */
         private List<FundCurrencyVO> cxgCurrencyVOList;
         
         @Setter
         @Getter
         public static class FundCurrencyVO implements Serializable {

             private static final long serialVersionUID = 7120832541755838488L;

             /**
              * 储蓄罐币种代码 币种 156-人民币、250-法郎、280-马克、344-港元、392-日元、826-英镑、840-美元、954欧元
              */
             private String cxgCurrencyCode;

             /**
              * 储蓄罐币种描述 币种 156-人民币、250-法郎、280-马克、344-港元、392-日元、826-英镑、840-美元、954欧元
              */
             private String cxgCurrencyDesc;
         }
     }

     @Setter
     @Getter
     public static class BuyFundBankCardVO implements Serializable {
         private static final long serialVersionUID = 6945899198946903226L;

         /**
          *  资金账号
          */
         private String cpAcctNo;

         /**
          * 银行卡号掩码
          */
         private String bankAcctMask;

         /**
          * bankName
          */
         private String bankName;

         /**
          * 银行logo
          */
         private String bankLogoUrl;

     }
}
