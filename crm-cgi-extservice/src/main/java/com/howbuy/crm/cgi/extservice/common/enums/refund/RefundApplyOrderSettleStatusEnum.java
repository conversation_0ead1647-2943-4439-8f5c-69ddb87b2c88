package com.howbuy.crm.cgi.extservice.common.enums.refund;

public enum RefundApplyOrderSettleStatusEnum {

    /** 0-无需清算、1-未清算、3-清算中、4-清算成功、5-清算失败 */
    UNAUDITED("0", "无需清算"),
    WAIT_CLEAR("1", "未清算"),
    CLEARING("3", "清算中"),
    CLEAR_SUCCESS("4", "清算成功"),
    CLEAR_FAIL("5", "清算失败"),
   ;
    private final String code;
    private final String desc;
    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
    RefundApplyOrderSettleStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
