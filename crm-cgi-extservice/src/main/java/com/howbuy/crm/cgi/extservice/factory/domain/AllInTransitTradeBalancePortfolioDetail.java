package com.howbuy.crm.cgi.extservice.factory.domain;

import lombok.Builder;
import lombok.Getter;


@Getter
@Builder
public class AllInTransitTradeBalancePortfolioDetail implements PortfolioDetail{

    /**
     * 在途交易笔数
     */
    private String inTransitTradeCount;

    /**
     * 在途交易订单号
     */
    private String inTransitTradeDealNo;

    /**
     * 在途交易资金笔数
     */
    private String inTransitTradeCapitalCount;

    /**
     * 在途交易资金订单号
     */
    private String inTransitTradeCapitalDealNo;

    /**
     * 在途打款凭证笔数
     */
    private String inTransitPayVoucherCount;

    /**
     * 在途打款凭证编号
     */
    private String inTransitPayVoucherNo;

    /**
     * 在途资金打款凭证上限
     */
    private String inTransitCashPayVoucherLimit;

    /**
     * 在途提款申请笔数
     * 类型：String
     */
    private String inTransitDrawMoneyAppCount;

    /**
     * 在途提款申请编号
     */
    private String inTransitDrawMoneyAppNo;

    /**
     * 在途提款资金笔数
     * 类型：String
     */
    private String inTransitDrawMoneyCapitalCount;

    /**
     * 在途提款资金编号
     */
    private String inTransitDrawMoneyCapitalNo;
}