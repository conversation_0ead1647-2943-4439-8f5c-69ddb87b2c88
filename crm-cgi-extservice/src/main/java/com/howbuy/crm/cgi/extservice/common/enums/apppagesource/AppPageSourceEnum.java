package com.howbuy.crm.cgi.extservice.common.enums.apppagesource;

public enum AppPageSourceEnum {

    //  储蓄罐持仓 = '2',   基金持仓 = '3',  全委服务 = '4',
    PIGGY_BALANCE("2","储蓄罐资产页跳转"),
    NON_FULL_FUND_BALANCE("3","非全委基金资产跳转页"),
    FULL_FUND_BALANCE("4","全委基金资产跳转页");
    private final String code;
    private final String desc;
    AppPageSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

    public static AppPageSourceEnum getByCode(String code) {
        for (AppPageSourceEnum value : AppPageSourceEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
