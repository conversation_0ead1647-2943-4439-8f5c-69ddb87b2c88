/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT Co., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.fund;

import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.NumberUtil;
import com.howbuy.crm.cgi.extservice.request.fund.TradeCalendarRequest;
import com.howbuy.crm.cgi.extservice.vo.fund.BuyCalendarVO;
import com.howbuy.crm.cgi.extservice.vo.fund.RedemptionCalendarVO;
import com.howbuy.crm.cgi.extservice.vo.fund.TradeCalendarVO;
import com.howbuy.crm.cgi.manager.domain.hkfund.BuyCalendarDTO;
import com.howbuy.crm.cgi.manager.domain.hkfund.RedemptionCalendarDTO;
import com.howbuy.crm.cgi.manager.domain.hkfund.TradeCalendarDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.QueryNearestOpenDtOuterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @description: 香港基金服务类
 * @author: 陈杰文
 * @date: 2025-06-25 11:28:45
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkFundService {

    @Autowired
    private QueryNearestOpenDtOuterService hkFundTradeCalendarOuterService;

    /**
     * @description: 基金申赎交易日历查询
     * @param request 请求参数
     * @return TradeCalendarVO
     * @author: 陈杰文
     * @date: 2025-06-25 11:28:45
     */
    public TradeCalendarVO queryTradeCalendar(TradeCalendarRequest request) {
        log.info("开始处理基金交易日历查询请求，香港客户号：{}，基金代码：{}", request.getHkCustNo(), request.getFundCode());
        
        // 申请日期、申请时间 获取当前日期时间 yyyyMMdd HHmmss
        String appDt = DateUtil.formatNowDate(DateUtil.SHORT_DATE_PATTERN);
        String appTm = DateUtil.formatNowDate(DateUtil.StR_PATTERN_HHMMSS);
        
        // 调用外部服务获取交易日历数据
        TradeCalendarDTO tradeCalendarDTO = hkFundTradeCalendarOuterService.queryNearestOpenDt(
            request.getHkCustNo(), request.getFundCode(), appDt, appTm);

        // 组装购买日历
        BuyCalendarVO buyCalendar = new BuyCalendarVO();
        BuyCalendarDTO buyCalendarDTO = tradeCalendarDTO.getBuyCalendar();
        if (buyCalendarDTO != null) {
            // 支持预约购买
            buyCalendar.setSupportPrebook(tradeCalendarDTO.getBuyCalendar().getSupportPrebook());
            // 预约购买截止日期时间 格式：yyyy-MM-dd HH:mm
            buyCalendar.setPrebookBuyEndDt(formatDateTime(buyCalendarDTO.getAdvanceEndDt(), buyCalendarDTO.getAdvanceEndTm()));
            // 预计打款截止日期时间 格式：yyyy-MM-dd HH:mm
            buyCalendar.setPrebookPayEndDt(formatDateTime(buyCalendarDTO.getPaymentDeadlineDt(), buyCalendarDTO.getPaymentDeadlineTime()));
            // 开放开始日期 格式：yyyy-MM-dd
            buyCalendar.setOpenStartDt(DateUtil.format(buyCalendarDTO.getOpenStartDt(), DateUtil.SHORT_DATEPATTERN, DateUtil.SAMPLE_DATEPATTERN));
            // 开放结束日期 格式：yyyy-MM-dd
            buyCalendar.setOpenEndDt(DateUtil.format(buyCalendarDTO.getOpenEndDt(), DateUtil.SHORT_DATEPATTERN, DateUtil.SAMPLE_DATEPATTERN));
            // 预计交易日期 格式：yyyy-MM-dd
            buyCalendar.setExpectTradeDt(DateUtil.format(buyCalendarDTO.getOpenDt(), DateUtil.SHORT_DATEPATTERN, DateUtil.SAMPLE_DATEPATTERN));
            // 下单结束时间 格式：HH:mm
            buyCalendar.setOrderEndTm(formatTime(buyCalendarDTO.getOrderEndTm()));
            // 打款结束时间
            buyCalendar.setPaymentEndTm(formatTime(buyCalendarDTO.getPaymentDeadlineTime()));
            // 预计上报日期
            buyCalendar.setPreSubmitTaDt(DateUtil.format(buyCalendarDTO.getPreSubmitTaDt(), DateUtil.SHORT_DATEPATTERN, DateUtil.SAMPLE_DATEPATTERN));
            // 预计上报时间
            buyCalendar.setPreSubmitTaTm(buyCalendarDTO.getPreSubmitTaTm());
        }
        
        // 组装赎回日历
        RedemptionCalendarVO redemptionCalendar = new RedemptionCalendarVO();
        RedemptionCalendarDTO redemptionCalendarDTO = tradeCalendarDTO.getRedemptionCalendar();
        if (tradeCalendarDTO.getRedemptionCalendar() != null) {
            redemptionCalendar.setSupportPrebook(tradeCalendarDTO.getRedemptionCalendar().getSupportPrebook());
            // 预约赎回截止日期时间 格式：yyyy-MM-dd HH:mm
            redemptionCalendar.setPrebookRedeemEndDt(formatDateTime(redemptionCalendarDTO.getAdvanceEndDt(), redemptionCalendarDTO.getAdvanceEndTm()));
            // 开放开始日期 格式：yyyy-MM-dd
            redemptionCalendar.setOpenStartDt(DateUtil.format(redemptionCalendarDTO.getOpenStartDt(), DateUtil.SHORT_DATEPATTERN, DateUtil.SAMPLE_DATEPATTERN));
            // 开放结束日期 格式：yyyy-MM-dd
            redemptionCalendar.setOpenEndDt(DateUtil.format(redemptionCalendarDTO.getOpenEndDt(), DateUtil.SHORT_DATEPATTERN, DateUtil.SAMPLE_DATEPATTERN));
            // 预计交易日期 格式：yyyy-MM-dd
            redemptionCalendar.setExpectTradeDt(DateUtil.format(redemptionCalendarDTO.getOpenDt(), DateUtil.SHORT_DATEPATTERN, DateUtil.SAMPLE_DATEPATTERN));
            // 下单结束时间 格式：HH:mm
            redemptionCalendar.setOrderEndTm(formatTime(redemptionCalendarDTO.getOrderEndTm()));
            // 查询可赎回份额
            redemptionCalendar.setAvailableVol(NumberUtil.format2(redemptionCalendarDTO.getAvailableVol()));
            // 预计上报日期
            redemptionCalendar.setPreSubmitTaDt(DateUtil.format(redemptionCalendarDTO.getPreSubmitTaDt(), DateUtil.SHORT_DATEPATTERN, DateUtil.SAMPLE_DATEPATTERN));
            // 预计上报时间
            redemptionCalendar.setPreSubmitTaTm(redemptionCalendarDTO.getPreSubmitTaTm());
        }
        
        // 组装返回结果
        TradeCalendarVO result = new TradeCalendarVO();
        result.setBuyCalendar(buyCalendar);
        result.setRedemptionCalendar(redemptionCalendar);
        
        log.info("基金交易日历查询处理完成，香港客户号：{}，基金代码：{}", request.getHkCustNo(), request.getFundCode());
        
        return result;
    }

    /**
     * @description: 格式化日期时间
     * @param date 日期 yyyyMMdd
     * @param time 时间 HHmmss
     * @return java.lang.String yyyy-MM-dd HH:mm
     * @author: 陈杰文
     * @date: 2025-06-25 15:17:03
     * @since JDK 1.8
     */
    private String formatDateTime(String date, String time) {
        if (Objects.isNull(date) || Objects.isNull(time)) {
            return null;
        }

        try {
            // 将yyyyMMdd格式转换为yyyy-MM-dd
            String formattedDate = DateUtil.format(date, DateUtil.SHORT_DATE_PATTERN, DateUtil.SAMPLE_DATEPATTERN);
            // 将HHmmss格式转换为HH:mm
            String formattedTime = formatTime(time);

            return formattedDate + " " + formattedTime;
        } catch (Exception e) {
            log.warn("格式化日期时间失败, date: {}, time: {}", date, time, e);
            return null;
        }
    }

    /**
     * @description: 格式化时间
     * @param time 时间 HHmmss
     * @return java.lang.String HH:mm
     * @author: 陈杰文
     * @date: 2025-06-25 15:17:03
     * @since JDK 1.8
     */
    private String formatTime(String time) {
        if (Objects.isNull(time)) {
            return null;
        }

        if (time.length() != 6) {
            return null;
        }
        return time.substring(0, 2) + ":" + time.substring(2, 4);
    }
} 