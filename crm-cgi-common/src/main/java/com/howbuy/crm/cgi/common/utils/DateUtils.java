/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.utils;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.format.DateTimeParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 日期工具类
 * @date 2023/3/15 13:23
 * @since JDK 1.8
 */
public class DateUtils {

    private static final Logger log = LoggerFactory.getLogger(DateUtils.class);

    private DateUtils() {
    }

    /**
     * yyyyMMdd
     */
    public static final String YYYYMMDD = "yyyyMMdd";

    /**
     * yyyy年MM月dd日
     */
    public static final String CHINESE_YYYYMMDD = "yyyy年MM月dd日";

    /**
     * yyyy年MM月
     */
    public static final String CHINESE_YYYYMMDD_MONTH = "yyyy年MM月";

    /**
     * yyyyMMdd
     */
    public static final String YYYYMD = "yyyy/M/d";

    /**
     * yyyyMM
     */
    public static final String YYYYMM = "yyyyMM";
    /**
     * yyyy/MM
     */
    public static final String YYM = "yyyy/MM";
    /**
     * yyyy/MM/dd
     */
    public static final String YYMD = "yyyy/MM/dd";

    public static final String DDMMYYYY = "dd/MM/yyyy";

    public static final String MMDDYYYY = "MM/dd/yyyy";
    /**
     * yyyy-MM-dd
     */
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    /**
     * yyyy-MM
     */
    public static final String YYYY_MM = "yyyy-MM";
    /**
     * yyyy
     */
    public static final String YYYY = "yyyy";
    /**
     * MMdd
     */
    public static final String MMdd = "MMdd";

    /**
     * MM-dd
     */
    public static final String MM_dd = "MM-dd";

    /**
     * MM-dd HHmmss
     */
    public static final String MM_dd_HHMMSS = "MM-dd HH:mm";
    /**
     * yyyyMMddHHmmss
     */
    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static final String YYYYMMDD_HHMMSS = "yyyyMMdd HHmmss";
    /**
     * yyyy-MM-dd HH:mm:ss
     */
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
    /**
     * yyyy-MM-dd HH:mm:ss.SSS
     */
    public static final String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";
    /**
     * HHmmss
     */
    public static final String HHMMSS = "HHmmss";

    public static final String HH_MM_SS = "HH:mm:ss";


    public static final String HH_MM = "HH:mm";

    public static String formatDate(String inputDateStr) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat outputFormat = new SimpleDateFormat(MM_dd_HHMMSS);

        try {
            Date date = inputFormat.parse(inputDateStr);
            return outputFormat.format(date);
        } catch (ParseException e) {
            System.out.println("Error parsing date: " + e.getMessage());
            return null;
        }
    }

    /**
     * @param dateStr
     * @return java.lang.String
     * @description:(日期字符串转格式化日或月)
     * @author: shuai.zhang
     * @date: 2023/6/19 13:43
     * @since JDK 1.8
     */
    public static String formatDateStrForDayOrMonth(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return "";
        }
        if (dateStr.length() == 6) {
            StringBuffer sb = new StringBuffer(dateStr.substring(0, 4));
            sb.append("-").append(dateStr.substring(4, 6));
            return sb.toString();
        }
        if (dateStr.length() == 8) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYYMMDD);
            LocalDate date = LocalDate.parse(dateStr, formatter);
            return date.format(DateTimeFormatter.ofPattern(YYYY_MM_DD));
        }
        return "";
    }

    /**
     * @description: 日期字符串转格式化
     * @param: [dateStr]
     * @return: java.lang.String
     * @author: shaoyang.li
     * @date: 2023/6/15 11:23
     * @since JDK 1.8
     */
    public static String formatDateStr(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYYMMDD);
        LocalDate date = LocalDate.parse(dateStr, formatter);
        return date.format(DateTimeFormatter.ofPattern(YYYY_MM_DD));
    }

    /**
     * @description:(日期字符串转格式化)
     * @param dateStr
     * @param format
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/11/8 18:21
     * @since JDK 1.8
     */
    public static String formatDateStr(String dateStr, String format) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYYMMDD);
        LocalDate date = LocalDate.parse(dateStr, formatter);
        return date.format(DateTimeFormatter.ofPattern(format));
    }

    /**
     * @description:(日期字符串转格式化)
     * @param dateStr
     * @param originalFormat
     * @param targetFormat
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/24 10:14
     * @since JDK 1.8
     */
    public static String formatDateTimeStr(String dateStr, String originalFormat, String targetFormat) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(originalFormat);
            LocalDateTime date = LocalDateTime.parse(dateStr, formatter);
            return date.format(DateTimeFormatter.ofPattern(targetFormat));
        } catch (Exception e) {
            System.out.println("Error parsing date: " + e.getMessage());
            return null;
        }
    }

    /**
     * @param date    LocalDate类型的日期
     * @param pattern 需要格式化的格式
     * @return java.lang.String
     * @description: 日期格式化
     * @author: hongdong.xie
     * @date: 2023/3/15 13:28
     * @since JDK 1.8
     */
    public static String formatToString(LocalDateTime date, String pattern) {
        if (date == null || StringUtils.isEmpty(pattern)) {
            return null;
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(pattern);
        return date.format(dtf);
    }

    /**
     * @description: 获取LocalDate
     * @param inputDate
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/8 18:01
     * @since JDK 1.8
     */
    public static LocalDate getLocalDate(String inputDate, String format) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(format);
        try {
            return LocalDate.parse(inputDate, inputFormatter);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @description:格式化日期
     * @param date
     * @param pattern
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/8 18:10
     * @since JDK 1.8
     */
    public static String formatToString(LocalDate date, String pattern) {
        if (date == null || StringUtils.isEmpty(pattern)) {
            return null;
        }
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(pattern);
        return date.format(dtf);
    }

    /**
     * @description:计算两个日期相差的天数
     * @param date1
     * @param date2
     * @return long
     * <AUTHOR>
     * @date 2024/10/9 19:26
     * @since JDK 1.8
     */
    public static long calculateDays(LocalDate date1, LocalDate date2) {
        long daysBetween = ChronoUnit.DAYS.between(date1, date2);
        return daysBetween;
    }

    /**
     * @description:数值天数转中文
     * @param days
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/9 19:33
     * @since JDK 1.8
     */
    public static String convertDaysToChinese(int days) {
        int years = days / 365;
        int remainingDays = days % 365;
        if (years > 0) {
            return years + "年" + remainingDays + "天";
        } else {
            return remainingDays + "天";
        }
    }

    /**
     * @param date    Date类型的日期
     * @param pattern 需要格式化的格式
     * @return java.lang.String
     * @description:(日期格式转换)
     * @author: xufanchao
     * @date: 2024/3/5 16:17
     * @since JDK 1.8
     */
    public static String formatToString(Date date, String pattern) {
        if (date != null && !StringUtils.isEmpty(pattern)) {
            SimpleDateFormat format = new SimpleDateFormat(pattern);
            return format.format(date);
        } else {
            return null;
        }
    }

    /**
     * @param strDate
     * @return java.util.Date
     * @description:将短时间格式字符串转换为时间 YYYYMMDD
     * @author: yu.zhang
     * @date: 2023/4/28 19:10
     * @since JDK 1.8
     */
    public static Date strToDate(String strDate) {
        SimpleDateFormat formatter = new SimpleDateFormat(YYYYMMDD);
        ParsePosition pos = new ParsePosition(0);
        return formatter.parse(strDate, pos);
    }

    /**
     * @param date
     * @param pattern
     * @return java.lang.String
     * @description:java.util.Date日期格式转换
     * @author: yu.zhang
     * @date: 2023/3/21 13:39
     * @since JDK 1.8
     */
    public static String dateFormatToString(Date date, String pattern) {
        if (date == null || StringUtils.isEmpty(pattern)) {
            return null;
        }
        LocalDateTime ldt = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        return formatToString(ldt, pattern);
    }

    /**
     * @param startDate
     * @param endDate
     * @return java.lang.String
     * @description:计算时间区间相差的天数
     * @author: yu.zhang
     * @date: 2023/4/19 17:17
     * @since JDK 1.8
     */
    public static String standardDeviation(String startDate, String endDate) {
        DateFormat dft = new SimpleDateFormat(YYYYMMDD);

        try {
            Date star = dft.parse(startDate);
            Date endDay = dft.parse(endDate);
            Long starTime = star.getTime();
            Long endTime = endDay.getTime();
            //时间戳相差的毫秒数
            Long num = endTime - starTime;
            return String.valueOf(num / 24 / 60 / 60 / 1000);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param startDateStr
     * @param format
     * @param endDate
     * @return java.lang.Long
     * @description:计算日期相差天数
     * <AUTHOR>
     * @date 2024/9/24 15:53
     * @since JDK 1.8
     */
    public static Long computeDateDifferDay(String startDateStr, String format, LocalDate endDate) {
        if (StringUtils.isBlank(startDateStr) || Objects.isNull(endDate)) {
            return null;
        }
        try {
            DateTimeFormatter YYYY_MM_DD_FORMAT = DateTimeFormatter.ofPattern(format);
            LocalDate appLastVisitedTime = LocalDate.parse(startDateStr, YYYY_MM_DD_FORMAT);
            // 计算时间差
            long daysBetween = ChronoUnit.DAYS.between(appLastVisitedTime, endDate);
            return daysBetween;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param format
     * @return java.lang.String
     * @description:(获取当前时间)
     * @author: shuai.zhang
     * @date: 2023/6/5 10:57
     * @since JDK 1.8
     */
    public static String getCurrentDate(String format) {
        return formatToString(LocalDateTime.now(), format);
    }

    /**
     * 获取当前时间的时分秒
     */
    public static String getCurrentTime(String timeFormat) {
        return formatToString(LocalDateTime.now(), timeFormat);
    }

    /**
     * getStrNextMonthByDate:获取指定增加月份的日期
     *
     * @param d     时间
     * @param month 增加月份
     * @return yyyyMMdd
     * <AUTHOR>
     * @date 2016-9-20 下午5:28:36
     */
    public static String getStrNextMonthByDate(Date d, int month) {
        Calendar cal = Calendar.getInstance();
        if (d != null) {
            cal.setTime(d);
        }
        cal.add(Calendar.MONTH, month);
        SimpleDateFormat sdf = new SimpleDateFormat(YYYYMMDD);
        return sdf.format(cal.getTime());
    }

    /**
     * @param targetDateStr 指定时间字符串
     * @return boolean 返回结果
     * @description: 比较当前日期是否在指定日期之后, 空白字符串返回false
     * @author: jinqing.rao
     * @date: 2023/12/4 16:47
     * @since JDK 1.8
     */
    public static boolean isBeforeOrEqualToCurrentDate(String targetDateStr) {
        try {
            if (StringUtils.isBlank(targetDateStr)) {
                throw new BusinessException(ExceptionCodeEnum.TIME_FORMAT_CONVERSION_ERROR);
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate targetDate = LocalDate.parse(targetDateStr, formatter);
            LocalDate currentDate = LocalDate.now();
            return targetDate.isBefore(currentDate);
        } catch (Exception e) {
            log.warn("DateUtils>>>beforeCurrentDate日期比较异常,paras:{}", targetDateStr, e);
            throw new BusinessException(ExceptionCodeEnum.TIME_FORMAT_CONVERSION_ERROR);
        }
    }

    /**
     * @param targetDateStr 目标时间字符串
     * @return boolean
     * @description: 目标时间是否小于当前时间
     * @author: jinqing.rao
     * @date: 2023/12/14 11:01
     * @since JDK 1.8
     */
    public static boolean isBeforeCurrentDate(String targetDateStr, String format) {
        if (StringUtils.isBlank(targetDateStr)) {
            return false;
        }
        if (StringUtils.isBlank(format)) {
            format = YYYYMMDD;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            LocalDate targetDate = LocalDate.parse(targetDateStr, formatter);
            LocalDate currentDate = LocalDate.now();
            return targetDate.isBefore(currentDate);
        } catch (Exception e) {
            log.warn("DateUtils>>>beforeCurrentDate日期比较异常,paras:{}", targetDateStr, e);
            throw new BusinessException(ExceptionCodeEnum.TIME_FORMAT_CONVERSION_ERROR);
        }
    }

    /**
     * @description: 在某日期之后
     * @param targetDateStr	
     * @param format
     * @return boolean
     * @author: jinqing.rao
     * @date: 2025/6/10 17:35
     * @since JDK 1.8
     */
    public static boolean isAfterCurrentDate(String targetDateStr, String format) {
        if (StringUtils.isBlank(targetDateStr)) {
            return false;
        }
        if (StringUtils.isBlank(format)) {
            format = YYYYMMDD;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            LocalDate targetDate = LocalDate.parse(targetDateStr, formatter);
            LocalDate currentDate = LocalDate.now();
            return targetDate.isAfter(currentDate);
        } catch (Exception e) {
            log.warn("DateUtils>>>isAfterCurrentDate日期比较异常,paras:{}", targetDateStr, e);
            throw new BusinessException(ExceptionCodeEnum.TIME_FORMAT_CONVERSION_ERROR);
        }
    }

    /**
     * @description: 在当前时间之后
     * @param targetDateStr	
     * @param format
     * @return boolean
     * @author: jinqing.rao
     * @date: 2025/6/10 17:34
     * @since JDK 1.8
     */
    public static boolean isAfterCurrentDateTime(String targetDateStr, String format) {
        if (StringUtils.isBlank(targetDateStr)) {
            return false;
        }
        if (StringUtils.isBlank(format)) {
            format = YYYYMMDD;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            LocalDateTime targetDate =  LocalDateTime.parse(targetDateStr, formatter);
            LocalDateTime currentDate =  LocalDateTime.now();
            return targetDate.isAfter(currentDate);
        } catch (Exception e) {
            log.warn("DateUtils>>>isAfterCurrentDate日期比较异常,paras:{}", targetDateStr, e);
            throw new BusinessException(ExceptionCodeEnum.TIME_FORMAT_CONVERSION_ERROR);
        }
    }

    public static String formatDateStr(String dateStr, String targetFormat, String currentFormat) {
        if (StringUtils.isAnyBlank(dateStr, targetFormat, currentFormat)) {
            return null;
        }
        try {
            return new SimpleDateFormat(targetFormat).format(new SimpleDateFormat(currentFormat).parse(dateStr));
        } catch (Exception e) {
            log.warn("formatDateStr>>>时间格式化错误,dateStr:{},targetFormat:{},currentFormat:{}", dateStr, targetFormat, currentFormat, e);
        }
        return null;
    }

    /**
     * 判断当前系统时间是否大于等于 dateStr1 且小于 dateStr2。
     *
     * @param dateStr1 时间字符串1，格式为 "yyyyMMdd"。
     * @param dateStr2 时间字符串2，格式为 "yyyyMMdd"。
     * @return 如果当前时间在 dateStr1 和 dateStr2 之间（包括 dateStr1 和 dateStr2）则返回 true，否则返回 false。
     */
    public static boolean currentDateInRangeLeftClose(String dateStr1, String dateStr2,String format) {
        if(StringUtils.isAnyBlank(dateStr1,dateStr2)){
            return false;
        }
        if(StringUtils.isBlank(format)){
            format = YYYYMMDD;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        LocalDate date1 = LocalDate.parse(dateStr1, formatter);
        LocalDate date2 = LocalDate.parse(dateStr2, formatter);
        LocalDate currentDate = LocalDate.now();

        return !currentDate.isBefore(date1) && currentDate.isBefore(date2);
    }


    /**
     * 将时间字符串从一格式转换成另一格式
     *
     * @param dateStr
     * @param formatFrom
     * @param formatTo
     * @return
     * <AUTHOR>
     */
    public static String format(String dateStr, String formatFrom, String formatTo) {
        try {
            if (dateStr.charAt(0) == '0') {
                return "";
            }
            Date date = string2Date(dateStr, formatFrom);
            return date2String(date, formatTo);
        } catch (Exception e) {
            return dateStr == null ? "" : dateStr;
        }
    }

    /**
     * 将String类型转换成Date类型
     *
     * @param date Date对象
     * @return
     * <AUTHOR>
     */
    public static Date string2Date(String date, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            return sdf.parse(date);
        } catch (Exception e) {
            return null;
        }
    }

    public static Date formatToDate(String dateStr, String pattern) {
        if (!StringUtils.isEmpty(pattern) && !StringUtils.isEmpty(dateStr)) {
            SimpleDateFormat format = new SimpleDateFormat(pattern);

            try {
                return format.parse(dateStr);
            } catch (ParseException var4) {
                throw new RuntimeException(var4);
            }
        } else {
            return null;
        }
    }

    /**
     * 将Date按某一格式转化成String
     *
     * @param date    Date对象
     * @param pattern 日期类型(见上面定义)
     * @return
     * <AUTHOR>
     */
    public static String date2String(Date date, String pattern) {
        pattern = pattern == null ? YYYY_MM_DD_HH_MM_SS : pattern;
        return new SimpleDateFormat(pattern).format(date);
    }

    /**
     * @param timeString1 当前时间
     * @return
     * @description: 时分秒比较, 输入的时分秒小于等于当前系统的时分秒
     * 不在当前系统时间之后
     * @author: jinqing.rao
     * @date: 2024/6/5 15:16
     * @since JDK 1.8
     */
    public static boolean notAfterTargetTimeHHMMSS(String timeString1) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HHmmss");
        // 将时间字符串解析为LocalTime对象
        LocalTime time1 = LocalTime.parse(timeString1, formatter);
        // 获取当前系统的时分秒
        LocalTime currentTime = LocalTime.now();
        return !currentTime.isAfter(time1);
    }
    /**
     * 根据当前时间和用户的生日字段birthDay计算用户的年龄
     */
    public static String calculateAgeByDate(String birthday) {
        if(StringUtils.isBlank(birthday)){
            return null;
        }
        // 将生日字符串转换为LocalDate
        LocalDate birthDate = LocalDate.parse(birthday,DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 计算两个日期之间的年份差
        Period period = Period.between(birthDate, currentDate);
        // 返回年龄
        return String.valueOf(period.getYears());
    }


    /**
     * 比较两个表示时分秒的字符串所代表的时间。
     *
     * @param time1Str 第一个时间点的字符串（格式：HH:mm:ss）
     * @param time2Str 第二个时间点的字符串（格式：HH:mm:ss）
     * @return 如果 time1 早于 time2 返回 true，否则返回 false
     */
    public static boolean isEarlier(String time1Str, String time2Str) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HHmmss");
        LocalTime time1 = parseTime(time1Str, formatter);
        LocalTime time2 = parseTime(time2Str, formatter);

        if (time1 == null || time2 == null) {
            throw new IllegalArgumentException("Invalid time format.");
        }

        return time1.isBefore(time2);
    }

    /**
     * 将字符串解析为 LocalTime 对象。
     *
     * @param timeStr 时间字符串
     * @param formatter 时间格式器
     * @return 解析后的时间对象
     */
    private static LocalTime parseTime(String timeStr, DateTimeFormatter formatter) {
        try {
            return LocalTime.parse(timeStr, formatter);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    /**
     * @description: 时间字符串转成时间
     * @return java.time.LocalDateTime
     * @author: jinqing.rao
     * @date: 2024/9/5 11:15
     * @since JDK 1.8
     */
    public static LocalDateTime strToDate(String appDate, String appTime, String pattern) {
        if(StringUtils.isBlank(pattern)){
            pattern = YYYYMMDDHHMMSS;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDateTime.parse(appDate +appTime, formatter);
    }

    /**
     * @description: 判断当前时间是否在目标时间之后
     * @param sourceDate 当前时间
     * @param targetDate	目标时间
     * @param dateFormat	时间格式
     * @return boolean
     * @author: jinqing.rao
     * @date: 2024/9/12 17:54
     * @since JDK 1.8
     */
    public static boolean sourceDateAfterTargetDate(String sourceDate, String targetDate, String dateFormat) {
        // 比较 日期是否在目标日期之前
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
        try {
            LocalDateTime date1 = LocalDateTime.parse(sourceDate, formatter);
            LocalDateTime date2 = LocalDateTime.parse(targetDate, formatter);
            return date1.isAfter(date2);
        } catch (DateTimeParseException e) {
            // 处理解析异常
            return false;
        }
    }

    /**
     * @description: (获取陪伴天数)
     * @param startDate
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2024/11/26 14:08
     * @since JDK 1.8
     */
    public static String getAccompanyDays(String startDate) {
        if (StringUtils.isEmpty(startDate)) {
            return "1";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYYMMDD);

        // 解析给定日期
        LocalDate givenDate = LocalDate.parse(startDate, formatter);

        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 计算两个日期之间的天数差
        long daysBetween = currentDate.toEpochDay() - givenDate.toEpochDay() + 1;

        return String.valueOf(daysBetween);
    }
    /**
     * @description: 获取近n个月前的日期
     * @param month
     * @return void
     * @author: jinqing.rao
     * @date: 2025/4/10 9:36
     * @since JDK 1.8
     */
    public static String getStrNextMonth(int month) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        // 获取近 month  月前的日期
        LocalDate oneMonthAgo = currentDate.minusMonths(month);
        return oneMonthAgo.format(formatter);
    }

    /**
     * 当前时间添加N天
     * @param days
     * @return
     */
    public static String addDaysToDate(int days,String pattern) {
        LocalDate currentDate = LocalDate.now();
        LocalDate futureDate = currentDate.plusDays(days);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return futureDate.format(formatter);
    }

    /**
     * 判断当前系统时间是否大于等于 dateStr1 且小于等于 dateStr2。
     *
     * @param dateStr1 时间字符串1，格式为 "yyyyMMdd"。
     * @param dateStr2 时间字符串2，格式为 "yyyyMMdd"。
     * @return 如果当前时间在 dateStr1 和 dateStr2 之间（包括 dateStr1 和 dateStr2）则返回 true，否则返回 false。
     */
    public static boolean isCurrentDateInRange(String dateStr1, String dateStr2) {
        if(StringUtils.isAnyBlank(dateStr1,dateStr2)){
            return false;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate date1 = LocalDate.parse(dateStr1, formatter);
        LocalDate date2 = LocalDate.parse(dateStr2, formatter);
        LocalDate currentDate = LocalDate.now();

        return !currentDate.isBefore(date1) && !currentDate.isAfter(date2);
    }

    /**
     * 判断给定的时间是否在[startDate, endDate]区间内（包含等于）
     *
     * @param targetDate 目标时间字符串
     * @param startDate  开始时间字符串
     * @param endDate    结束时间字符串
     * @param formatter  时间格式化器
     * @return true 如果目标时间在[startDate, endDate]范围内，否则返回 false
     */
    public static boolean isDateInRange(String targetDate, String startDate, String endDate, String formatter) {
        if (targetDate == null || startDate == null || endDate == null || formatter == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(formatter);
        LocalDate target = LocalDate.parse(targetDate, dateTimeFormatter);
        LocalDate start = LocalDate.parse(startDate, dateTimeFormatter);
        LocalDate end = LocalDate.parse(endDate, dateTimeFormatter);

        return !target.isBefore(start) && !target.isAfter(end);
    }
    /**
     * 比较两个日期的大小
     *
     * @param date1      第一个日期字符串
     * @param date2      第二个日期字符串
     * @param formatter  日期格式（如 "yyyyMMdd"）
     * @return int 返回值含义：
     *         正数表示 date1 > date2，
     *         负数表示 date1 < date2，
     *         0 表示两者相等
     */
    public static int compareDate(String date1, String date2, String formatter) {
        if (date1 == null || date2 == null || formatter == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(formatter);
        LocalDate d1 = LocalDate.parse(date1, dtf);
        LocalDate d2 = LocalDate.parse(date2, dtf);

        return d1.compareTo(d2);
    }

    /**
     * 根据输入的日期字符串（格式为 yyyyMMdd），如果不是当年，则按 yyyy-MM-dd 格式返回
     *
     * @param dateStr 输入的日期字符串，格式为 yyyyMMdd
     * @return 如果不是当年，返回格式为 "最新收益(yyyy-MM-dd)"；否则返回空字符串或按需修改
     */
    public static String formatIfNotCurrentYear(String dateStr) {
        if(StringUtils.isBlank(dateStr)){
            return null;
        }
        LocalDate date;
        try {
            // 将字符串解析为 LocalDate 对象
            date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format. Expected format: yyyyMMdd", e);
        }

        LocalDate now = LocalDate.now();

        // 判断是否为当年
        if (date.getYear() != now.getYear()) {
            return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        // 如果是当年，返回MM-dd 格式
        return  date.format(DateTimeFormatter.ofPattern("MM-dd")); //
    }
}