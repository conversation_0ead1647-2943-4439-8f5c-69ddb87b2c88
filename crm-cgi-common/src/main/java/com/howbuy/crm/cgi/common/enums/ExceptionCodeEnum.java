/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.enums;

/**
 * <AUTHOR>
 * @description: 异常返回码枚举, 异常码定义规范
 * 异常码一共7位，分别位：1位团队标识+2位系统码+4位错误码
 * @date 2023/5/17 18:13
 * @since JDK 1.8
 */
public enum ExceptionCodeEnum {
    /**
     * C010000：成功
     */
    SUCCESS("C010000", "成功"),
    /**
     * C019999：系统异常
     */
    SYSTEM_ERROR("C019999", "系统异常"),
    /**
     * C010001：请求超时，请重新登陆
     */
    SESSION_TIMEOUT("C010003", "请求超时，请重新登陆"),
    /**
     * C010002：请求参数错误
     */
    REQUEST_PARAM_ERROR("C010002", "请求参数错误"),
    /**
     * C010003：加密或签名信息错误
     */
    SECURITY_MSG_ERROR("C011003", "加密或签名信息错误"),
    /**
     * C010004：安全密钥已过期
     */
    SECURITY_KEY_HAS_EXPIRED("C011004", "安全密钥已过期"),
    /**
     * C011010：参数错误
     */
    PARAM_ERROR("C011010", "参数错误"),
    /**
     * C011011：手机号未开户
     */
    MOBILE_UB_OPEN_ACCT("C011011", "手机号未开户"),
    /**
     * C011012：账户异常
     */
    ACCT_ERROR("C011012", "账户异常"),
    /**
     * C011013：手机号未验证
     */
    MOBILE_UN_VERIFY("C011013", "手机号未验证"),
    /**
     * C011014：交易账号未激活
     */
    TX_ACCT_UN_ACTIVE("C011014", "交易账号未激活"),
    /**
     * C011015：预留证件号不一致
     */
    ID_NO_DIFF("C011015", "预留证件号不一致"),
    /**
     * C011016：手机号验证失败
     */
    MOBILE_VERIFY_FAIL("C011016", "手机号验证失败"),
    /**
     * C011017：邮箱未开户
     */
    EMAIL_UN_OPEN_ACCT("C011017", "邮箱未开户"),
    /**
     * C011018：交易账户激活失败
     */
    TX_ACCT_ACTIVE_FAIL("C011018", "交易账户激活失败"),
    /**
     * C011019：证件号未开户
     */
    IDNO_UN_OPEN_ACCT("C011019", "证件号未开户"),
    /**
     * C011020：登录密码未设置
     */
    LOGIN_PSWD_UNSET("C011020", "登录密码未设置"),
    /**
     * C011021：用户名或密码错误
     */
    LOGIN_NAME_OR_PSWD_ERROR("C011021", "账户/密码错误,请重新输入。"),
    /**
     * C011022：需登录激活
     */
    NEED_LOGIN_ACTIVE("C011022", "需登录激活"),
    /**
     * C011023：登录账号激活失败
     */
    LOGIN_ACTIVE_FAIL("C011023", "登录账号激活失败"),
    /**
     * C011024：验证码错误
     */
    VERIFYCODE_ERROR("C011024", "验证码错误,请重新输入"),
    /**
     * C011025：交易密码错误
     */
    TXPASSWORD_ERROR("C011025", "交易密码错误"),

    /**
     * C011026：预留手机号不一致
     */
    MOBILE_DIFF("C011026", "预留手机号不一致"),

    /**
     * C011027：预留邮箱不一致
     */
    EMAIL_DIFF("C011027", "预留邮箱不一致"),

    /**
     * C011028：登录账号锁定
     */
    LOGIN_ACCT_LOCKED("C011028", "账户/密码错误次数已达上限，请2小时后再试。如有疑问，请咨询客服"),

    /**
     * C011029：校验验证码错误
     */
    CHECK_VERIFY_CODE_ERROR("C011029", "验证码错误,请重新输入"),

    /**
     * C011030：原密码错误
     */
    OLD_PASSWORD_ERROR("C011030", "原密码错误"),
    /**
     * C011031：新旧密码一致
     */
    NEW_OLD_PASSWORD_EQUAL("C011031", "新旧密码一致"),
    /**
     * C011032：登录密码格式错误
     */
    LOGIN_PASSWORD_FORMAT_ERROR("C011032", "登录密码格式错误"),
    /**
     * C011033：交易密码格式错误
     */
    TRADE_PASSWORD_FORMAT_ERROR("C011033", "交易密码格式错误"),
    /**
     * C011034：操作频繁，请稍后再试!
     */
    USER_OPT_TOO_MORE_ERROR("C011034", "操作频繁，请稍后再试!"),
    /**
     * C011035：文件预览异常!
     */
    FILE_PREVIEW_ERROR("C011035", "文件预览异常!"),

    /**
     * C011036：文件上传超过最大限制异常!
     */
    FILE_UPLOAD_MAX_SIZE_ERROR("C011036", "文件上传超过最大限制异常!"),
    /**
     * C011038：验证码过期
     */
    VERIFY_CODE_TIME_OUT("C011038", "验证码已过期，请重新获取"),

    /**
     * C011039 账户未注册
     */
    ACCOUNT_NOT_EXIST("C011039", "账户未注册"),
    /**
     * C011040 请选择开户证件所属国家/地区及证件类型
     */
    HK_OPEN_ACCOUNT_IDENTITY_TYPE_NOT_EXIST("C011040", "请选择开户证件所属国家/地区及证件类型"),
    /**
     * C011041 账号类型不为大陆手机号
     */
    HK_OPEN_ACCOUNT_MOBILE_TYPE_ERROR("C011041", "账号类型不为大陆手机号"),

    /**
     * C011042: 手机号注册失败
     */
    MOBILE_REGISTER_FAIL("C011042", "手机号注册失败"),

    /**
     * C011043: 证件号码已开户
     */
    HK_OPEN_ACCOUNT_ID_STEP_ACCOUNT_ERROR("C011043", "您输入的证件号码已开户"),

    /**
     * C011043 绑定一账通号 证件号以及手机号校验
     */
    BIND_HBONE_ID_MOBILE_CHECK_ERROR("C011043", "您填写的一账通证件号，手机号与当前好买香港账号的信息均不一致，请核实信息后重新填写"),
    /**
     * C011043_1 补充 注：证件号和手机号至少有一个要一致。
     */
    BIND_HBONE_ID_MOBILE_CHECK_ERROR_1("C011043", "注：证件号和手机号至少有一个要一致。"),

    /**
     * C011044 绑定一账通  一账通信息校验
     */
    BIND_HBONE_CHECK_ERROR("C011044", "您填写的一账通实名信息有误，请核实一账通的真实姓名，证件号，绑定手机号后重新填写"),

    /**
     * C011045 绑定一账通  一账通是否绑定其他香港客户号校验
     */
    UNBIND_HBONE_ID_MOBILE_CHECK_ERROR("C011045", "该证件对应的好买财富一账通，已与其他好买香港账号关联，请先登录原香港账号解绑"),

    /**
     * C011045: 账户中心问卷题目信息不存在
     */
    HK_OPEN_ACCOUNT_RISK_QUESTION_ERROR("C011045", "账户中心问卷题目信息不存在"),

    /**
     * C011046:外部接口返回数据为空
     */
    OUTER_SERVICE_INTERFACE_DATA_NOT_EXIST("C011046", "外部接口返回数据为空"),

    /**
     * C011047:账户中心问卷答案信息不存在
     */
    HK_OPEN_ACCOUNT_("C011047", "账户中心问卷答案信息不存在"),

    /**
     * C011048:姓名与所填的证件信息不一致
     */
    HK_OPEN_ACCOUNT_IDENTITY_AUTH_ERROR("C011048", "姓名与所填的证件信息不一致"),


    /**
     * C011049:时间格式化异常
     */
    TIME_FORMAT_CONVERSION_ERROR("C011049", "时间格式化异常"),
    /**
     * C011050:一账通绑定关系不存在
     */
    HBONE_BIND_RELATION_NOT_EXIST("C011050","一账通绑定关系不存在"),
    /**
     * C011051: 不允许注销
     */
    NOT_ALLOW_SIGNOFF("C011051","您已开通交易账户，为确保您的账户安全，销户需进行身份验证，详情请咨询您的专属服务人员或拨打客服电话 "),
    /**
     * C011051: 不允许注销
     */
    NOT_ALLOW_SIGNOFF_1("C011051","您已提交开户审核资料，为确保您的账户安全，销户需进行身份验证，详情请咨询您的专属服务人员或拨打客户电话"),
    /**
     * C011052 手机号被已开户账号占用错误
     */
    MOBILE_HAS_BEEN_OCCUPIED("C011052", "已被其他香港账号占用"),
    /**
     * C011096 新旧手机号相同
     */
    NEW_OLD_MOBILE_EQUAL("C011096", "新旧手机号一致"),
    /**
     * C011053 手机号被未开户账号占用错误
     */
    MOBILE_HAS_BEEN_OCCUPIED_UN_OPEN_ACCT("C011053", "已被其他香港账号占用。您可以使用上述手机号登录小程序-注销登录账号-再登录当前账号-修改为上述手机号"),
    /**
     * C011095 新旧邮箱相同
     */
    NEW_OLD_EMAIL_EQUAL("C011095", "新旧邮箱一致"),
    /**
     * C011054 邮箱号被已开户账号占用错误
     */
    EMAIL_HAS_BEEN_OCCUPIED("C011054", "已被其他香港账号占用"),
    /**
     * C011055 邮箱号被未开户账号占用错误
     */
    EMAIL_HAS_BEEN_OCCUPIED_UN_OPEN_ACCT("C011055", "已被其他香港账号占用。您可以使用上述邮箱号登录小程序-注销登录账号-再登录当前账号-修改为上述邮箱号"),

    HK_OPEN_ACCOUNT_RESIDENCE_ADDRESS_NOT_EXIST("C011056", "现居住地址信息错误,请检查完善地址信息"),

    HK_OPEN_ACCOUNT_MOBILE_BIND("C011057", "手机号已被绑定"),
    HK_OPEN_ACCOUNT_EMAIL_BIND("C011058", "邮箱已被绑定"),
    HK_OPEN_ACCOUNT_IDENTITY_OCR_ERROR("C011059", "证件信息解析异常"),

    HK_OPEN_ACCOUNT_MAIL_ADDRESS_NOT_EXIST("C011060", "通讯地址信息错误,请检查完善地址信息"),
    HK_OPEN_ACCOUNT_BIRTH_ADDRESS_NOT_EXIST("C011061", "出生地址信息错误,请检查完善地址信息"),

    HK_OPEN_ACCOUNT_ID_NOT_EXIST("C011062", "开户证件信息页信息过期,请检查完善证件信息页"),

    HK_OPEN_ACCOUNT_CUST_NOT_EXIST("C011063", "开户个人信息页信息过期,请检查完善个人信息页"),
    HK_OPEN_ACCOUNT_OCCUPATION_NOT_EXIST("C011064", "开户职业信息页信息过期,请检查完善职业信息页"),
    HK_OPEN_ACCOUNT_DECLARATION_NOT_EXIST("C011065", "开户声明信息页信息过期,请检查完善声明信息页"),

    HK_OPEN_ACCOUNT_INVEST_EXP_NOT_EXIST("C011066", "开户投资信息页信息过期,请检查完善投资信息页"),

    HK_OPEN_ACCOUNT_BANK_INFO_NOT_EXIST("C011067", "开户银行信息页信息过期,请检查完善银行信息页"),

    HK_OPEN_ACCOUNT_E_SIGNATURE_IMAGE_NOT_EXIST("C011068", "签名照片解析异常,提交失败"),
    HK_OPEN_ACCOUNT_E_SIGNATURE_NAME_NOT_MATCH("C011069","提示系统检测到您的签名与预留证件信息不一致，请确认是否重新签名"),


    HK_OPEN_ACCOUNT_BANK_ACCT_REPEAT("C011070","银行账户号码重复"),
    /**
     * C011071 证件号码登录时，客户信息不存在
     */
    IDNO_LOGIN_NOT_EXIST("C011071", "登录失败，请检查所属国家/地区/证件类型/证件号是否正确，如有疑问请咨询您的专属服务人员"),
    /**
     * C011072 绑定一账通号姓名校验
     */
    BIND_HBONE_NAME_CHECK_ERROR("C011072", "您填写的一账通真实姓名与当前好买香港账号的信息不一致，请核实信息后重新填写"),
    /**
     * C011073 是大陆手机号，但是登录密码未设置
     */
    NO_HK_MOBILE_LOGIN_PSWD_UNSET("C011073", "该账号未设置登录密码，您可以使用验证码登录"),
    /**
     * C011074 不是大陆手机号，登录密码没设置
     */
    IS_HK_MOBILE_LOGIN_PSWD_UNSET("C011074", "该账号未设置登录密码，您可以点击“忘记密码”功能设置密码后再登录。"),

    HK_OPEN_ACCOUNT_RISK_INFO_ERROR("C011076", "风险测评信息错误,请重新完成风险测评"),
    HK_OPEN_ACCOUNT_ORDER_NOT_EXIST("C011077", "开户订单不存在,请稍后再试"),

    HK_OPEN_ACCOUNT_COMPANY_ADDRESS_NOT_EXIST("C011078", "现居住地址信息错误,请检查完善地址信息"),
    Hk_OPEN_ACCOUNT_FILE_NOT_EXIST("C011079", "开户文件路劲地址解析失败,请稍后再试"),
    Hk_OPEN_ACCOUNT_FILE_DOWNLOAD_ERROR("C011080", "获取文件失败,请稍后再试"),
    HK_OPEN_ACCOUNT_FILE_NAME_FORMAT_ERROR("C011081", "文件名称包含特殊字符"),
    HK_OPEN_ACCOUNT_MOBILE_NOT_EXIST("C011082", "暂未获取到用户绑定手的机号信息"),
    HK_OPEN_ACCOUNT_EMAIL_NOT_EXIST("C011083", "暂未获取到用户绑定的邮箱信息"),
    HK_OPEN_ACCOUNT_ID_IS_EXIST("C011084", "您输入的证件号码已开户"),

    HK_OPEN_ACCOUNT_GET_LICENSEE_INFO_ERROR("C011085", "获取持牌人信息错误,请稍后再试"),
    /**
     * C011086 提示手机号验证
     */
    HK_OPEN_ACCOUNT_MOBILE_VERIFY("C011086", "您当前正在进行绑定操作，请进行手机号验证码校验。香港开户手机号："),
    /**
     * C011087 提示进行邮箱验证
     */
    HK_OPEN_ACCOUNT_EMAIL_VERIFY("C011087", "您当前正在进行绑定操作，请进行邮箱验证码校验。香港开户邮箱："),
    /**
     * C011088 提示进行手机解绑操作
     */
    HK_OPEN_ACCOUNT_MOBILE_UNBIND("C011088", "您当前正在进行解绑操作，请进行手机号验证码校验。一账通手机号:"),
    /**
     * C011088 提示进行手机解绑操作
     */
    HK_OPEN_ACCOUNT_MOBILE_UNBIND_1("C011088", "您当前正在进行解绑操作，请进行手机号验证码校验。香港开户手机号："),
    /**
     * C011089 提示进行邮箱解绑操作
     */
    HK_OPEN_ACCOUNT_EMAIL_UNBIND("C011089", "您当前正在进行解绑操作，请进行邮箱验证码校验。香港开户邮箱："),
    /**
     * C011090 销户二次弹框
     */
    HK_OPEN_ACCOUNT_DELETION_CONFIRM("C011090", "请您再次确认是否销户，确认后立即生效！"),

    HK_OPEN_ACCOUNT_PDF_NOT_EXIST("C011091", "开户文件PDF不存在"),
    HK_OPEN_ACCOUNT_MOBILE_VERIFICATION_CODE_ERROR("C011092", "手机号验证信息已失效,请重新验证"),
    HK_OPEN_ACCOUNT_EMAIL_VERIFICATION_CODE_ERROR("C011093", "邮箱验证信息已失效,请重新验证"),

    HK_OPEN_ACCOUNT_BANK_ACCT_SWIFT_BANK_ERROR("C011094", "SwiftCode银行信息错误"),

    Hk_OPEN_ACCOUNT_APP_ADVERTISING_POSITION_ERROR("C011095", "App广告位位置信息错误"),
    /**
     * 一账通号或者好买交易账号不存在
     */
    HBONE_OR_HK_TRADE_ACCT_NOT_EXIST("C011096", "一账通号或者好买交易账号不存在"),

    Hk_OPEN_ACCOUNT_APP_FILE_UPLOAD_ERROR("C011097", "文件上传失败"),

    /**
     * 用户持仓数据为空
     */
    USER_BALANCE_IS_NULL("C011098", "用户持仓数据为空"),
    /**
     * 数据为空
     */
    DATA_IS_EMPTY("C011099", "数据为空"),
    /**
     * 数据过大
     */
    DATA_IS_LARGE("C011100", "数据过大"),
    /**
     * 数据异常
     */
    DATA_IS_ERROR("C011101", "数据异常"),

    /**
     * 基金产品不存在
     */
    HK_FUND_PRODUCT_NOT_EXIST("C011099", "基金产品不存在"),

    /**
     * 客户信息不存在
     */
    HK_CUST_INFO_NOT_EXIST("C011100", "客户信息不存在"),

    /**
     * 申购基金签署合同失败
     */
    HK_PURCHASE_FUND_SIGN_CONTRACT_FAIL("C011101", "申购基金签署合同失败"),

    /**
     * 申购基金打款凭证提交失败
     */
    HK_PURCHASE_FUND_PAY_VOUCHER_SUBMIT_FAIL("C011102", "打款凭证提交失败"),

    HK_BANK_INFO_QUERY_ERROR("C011103", "获取银行列表信息错误"),
    FUND_FILE_URL_ERROR("C011104", "文件上传路劲错误"),
    HFILE_FILE_UPLOAD_FILE_TYPE_ERROR("C011105", "文件存储配置错误"),

    HFILE_FILE_UPLOAD_FILE_GET_FILE_NAME_ERROR("C011106", "文件上传:获取文件名称错误"),

    /**
     * 海外APP手机号注册时
     */
    HW_MOBILE_REGISTER_HKCUSTNO_ERROR("C011107", "当前手机号已注册，不可重复注册。如有疑问，请咨询您的专属服务人员或客服。"),

    /**
     * 海外客户修改手机号,若好买已实名，则不允许线上修改香港客户绑定的手机号，本次修改无效
     */
    HW_CUSTOMER_REAL_NAME_EDIT_MOBILE_ERROR("C011108", "暂不支持线上修改手机号"),


    /**
     * 海外修改手机号,修改后的【新手机号】是否已经被其他实名一账通A占用
     */
    HW_CUSTOMER_MOBILE_BIND_OTHER_MOBILE_ERROR("C011109", "暂不支持线上修改手机号"),


    /**
     * 海外APP/小程序 一账通授权管理 功能下架
     */
    AUTHORIZATION_MANAGEMENT_ERROR("C011110", "功能暂不可用"),

    HW_REDEEM_FUND_ORDER_ERROR("C011111", "撤单失败,请稍后再试"),

    /**
     * 母子基金关系查询异常
     */
    HK_MOTHER_CHILD_FUND_QUERY_ERROR("C011112", "母子基金关系查询异常"),
    /**
     * 获取储蓄罐底层基金信息错误
     */
    PIGGY_FUND_BASIC_INFO_ERROR("C011113", "获取储蓄罐底层基金信息错误"),

    /**
     * 获取储蓄罐底层基金年龄限制信息错误
     */
    PIGGY_FUND_AGE_LIMIT_ERROR("C011114", "获取储蓄罐底层基金年龄限制信息错误"),

    HK_CUST_BIRTHDAY_ERROR("C011115", "客户生日信息不存在"),

    /**
     * 获取基金产品合同信息错误
     */
    GET_PRODUCT_CONTRACT_ERROR("C011116", "获取基金产品合同信息错误"),

    /**
     * 获取基金产品表单配置合同错误
     */
    GET_PRODUCT_FORM_DOC_CFG_ERROR("C011117", "获取基金产品表单配置合同错误"),

    /**
     * 已签署海外储蓄罐协议
     */
    ALREADY_SIGNED_PIGGY_AGREEMENT("C011119", "客户海外储蓄罐协议已签署，无需重复签署"),


    /**
     * 储蓄罐信息异常，请联系您的专属客服！
     */
    PIGGY_INFO_ERROR("C011120", "储蓄罐信息异常，请联系您的专属客服！"),
    FUND_VALIDATOR_TYPE_ERROR("C011121", "基金校验业务类型错误"),

    FUND_RISK_LEVEL_ERROR("C011122", "基金风险等级参数配置错误"),
    /**
     * 当前不存在已签约的储蓄罐基金,无法发起储蓄罐协议变更
     */
    PIGGY_SIGN_FUND_NOT_EXIST_ERROR("C011123", "当前没有已签约的储蓄罐基金产品"),
    FUND_PAYMENT_END_TM_ERROR("C011124", "基金的下单时间错误"),
    QUERY_WORD_DAY_ERROR("C011125", "查询工作日错误"),
    PIGGY_AGREED_ERROR("C011126", "是否同意协议变更状态错误"),

    /**
     * 查询海外订单信息错误
     */
    HW_QUERY_ORDER_INFO_ERROR("C011127", "查询海外订单信息错误"),

    /**
     * 风险合适性匹配校验不通过
     */
    RISK_ASSESSMENT_ERROR("C011128", "风险合适性匹配校验不通过"),

    /**
     * 密码修改
     */
    HK_ACCOUNT_PASSWORD_MODIFY("C011129", "密码修改,请重新登录"),

    /**
     * 密码重置
     */
    HK_ACCOUNT_PASSWORD_RESET("C011130", "密码重置,请重新登录"),

    /**
     * 登录密码错误
     */
    LOGIN_PASSWORD_ERROR("C011131", "登录密码错误"),


    /**
     * 用户分险等级错误
     */
    CUST_RISK_LEVEL_ERROR("C011132", "用户风险等级错误"),
    /**
     * 交易账号未激活
     */
    TRADE_ACCOUNT_NOT_ACTIVATED_ERROR("C011133", "交易账号未激活"),
    /**
     * 未绑定银行卡
     */
    NOT_BIND_BANK_CARD_ERROR("C011134", "未绑定银行卡"),
    /**
     * 客户风险等级不匹配
     */
    RISK_LEVEL_NOT_MATCH_ERROR("C011135", "客户风险等级不匹配"),

    /**
     * 客户没有衍生品经验
     */
    CUSTOMER_DERIVATIVE_EXPERIENCE_NO_ERROR("C011136", "客户没有衍生品经验"),
    /**
     * 客户非专业投资者
     */
    CUSTOMER_NOT_PROFESSIONAL_INVESTOR_ERROR("C011137", "客户非专业投资者"),
    /**
     * 客户资产证明过期
     */
    CUSTOMER_ASSET_PROOF_EXPIRED_ERROR("C011138", "客户资产证明有效期已过期"),

    /**
     * 用户年龄超过基金产品最大年龄限制
     */
    CUSTOMER_AGE_GREATER_THAN_65_LIMIT_ERROR("C011139", "用户年龄超过基金产品最大年龄限制"),
    /**
     * 海外储蓄罐产品存在在途交易
     */
    CUSTOMER_HAS_PIGGY_IN_TRANSIT_ERROR("C011140", "海外储蓄罐产品存在在途交易"),
    /**
     * 储蓄罐有持仓或者有在途交易,暂不支持储蓄罐关闭
     */
    CUSTOMER_HAS_POSITION_OR_IN_TRANSIT_ERROR("C011141", "储蓄罐有持仓或者有在途交易,暂不支持储蓄罐关闭"),


    /**
     * 资配报告文件下载异常
     */
    ASSET_REPORT_FILE_DOWNLOAD_ERROR("C011142", "文件下载异常"),

    /**
     * 客户画像-未绑定微信
     */
    PORTRAIT_NOT_WECHAT_BIND("C011160", "未绑定微信"),

    /**
     * 客户画像-投顾编号获取错误
     */
    PORTRAIT_CONSCODE_GET_ERROR("C011161", "客户画像目前仅支持理财师使用"),

    /**
     * 客户画像-投顾关系不匹配
     */
    PORTRAIT_CONSCUST_NOT_MATCH("C011162", "投顾关系不匹配"),

    /**
     * 客户画像-一账通号对应多个投顾关系
     */
    PORTRAIT_HBONENO_MANY_CONSCUST("C011163", "一账通号对应多个投顾关系"),

    /**
     * 客户画像-一账通号未绑定客户
     */
    PORTRAIT_HBONENO_NOBAND_CONSCUST("C011164", "一账通号未绑定客户"),

    /**
     * 语音验证码过期
     */
    VOICE_MSG_EXPIRED_ERROR("C011143", "语音验证码过期"),

    /**
     * 输入的语音验证码不对
     */
    VOICE_MSG_ERROR("C011144", "输入的语音验证码不对"),

    /**
     * 会话异常，请重新登陆
     */
    SESSION_ERROR("C011145", "会话异常，请重新登陆"),

    /**
     * 登录信息异常
     */
    LOGIN_INFO_ERROR("C011146", "登录信息异常"),

    /**
     * "C011147","检索条件失效"
     */
    QA_SEARCH_CONDITION_INVALID("C011147", "检索条件失效"),
    /**
     * 打款凭证不存在
     */
    HK_PAY_VOUCHER_NOT_EXIST("C011148", "打款凭证不存在"),

    /**
     * 打款凭证订单状态不支持删除
     */
    HK_PAY_VOUCHER_NOT_SUPPORT_DELETE("C011149","此条打款凭证订单不支持删除"),

    /**
     * 当前暂无生效的储蓄罐，如有疑问，可联系您的专属服务人员
     */
    NO_EFFECTIVE_PIGGY_ERROR("C011150", "当前暂无生效的储蓄罐，如有疑问，可联系您的专属服务人员"),

    /**
     * 基金检索类型错误
     */
    FUND_SEARCH_TYPE_ERROR("C011151", "基金检索类型错误"),

    /**
     * 合同文件业务类型不存在
     */
    CONTRACT_FILE_BUSINESS_TYPE_NOT_EXIST("C011152", "合同文件业务类型不存在"),

    /**
     * 查询退款订单异常
     */
    QUERY_HK_FIN_REFUND_ORDER_ERROR("C011153", "查询退款订单异常"),

    /**
     * 昵称敏感词校验不通过
     */
    NICKNAME_CHECK_FAIL("C011154", "昵称敏感词校验不通过"),

    ;

    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    ExceptionCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}