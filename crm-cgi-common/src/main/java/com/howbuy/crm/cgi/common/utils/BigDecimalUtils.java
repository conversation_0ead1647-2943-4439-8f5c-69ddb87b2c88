/**
 * Copyright (c) 2020, Shang<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.utils;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.constants.MarkConstants;
import lombok.extern.log4j.Log4j2;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 计算工具类（资产中心用，截位法，正负数截位后都不大于原数据）
 *
 * <AUTHOR>
 * @date 2020/2/25 10:45
 * @since JDK 1.8
 */
@Log4j2
public class BigDecimalUtils {

    /**
     * 中间结果精度
     */
    private static final int TEMPORARY_SCALE = 6;
    private static final int FOUR_SCALE = 4;

    private static final int TWO_SCALE = 2;
    /**
     * 最终结果精度
     */
    private static final int FINAL_SCALE = 6;
    //产线资产中心收益精度
    private static final int ON_LINE_SCALE = 5;
    /** 100，计算%使用  */
    public static final BigDecimal HUNDRED = new BigDecimal("100");
    public static final BigDecimal TEN = new BigDecimal("10");
    /**
     * 收益为0
     */
    public static final String ZERO_ASSET = "0.00";

    private static final Map<String, Field[]> fieldMap = new ConcurrentHashMap<String, Field[]>();

    /**
     * @description:(小数转为百分比)
     * @param bigDecimal
     * @return java.lang.String
     * @author: shuai.zhang
     * @date: 2023/6/6 18:39
     * @since JDK 1.8
     */
    public static String floatToPercent(BigDecimal bigDecimal) {
        StringBuffer sb = new StringBuffer();
        BigDecimal afterHundered = processFinalRound(multiply(bigDecimal,HUNDRED));
        // 费率显示修改 位数<=2 固定保留两位 >2 保留非零有效位
        if (null != bigDecimal && bigDecimal.scale() <= TWO_SCALE) {
            afterHundered = afterHundered.setScale(2, RoundingMode.HALF_UP);
            sb.append(afterHundered).append(MarkConstants.SEPARATOR_PERCENT);
        } else {
            sb.append(afterHundered.stripTrailingZeros().toPlainString()).append(MarkConstants.SEPARATOR_PERCENT);
        }
        return sb.toString();
    }
    /**
     * 处理为4位小数，常用于百分比
     *
     * @param bigDecimal
     * @return java.math.BigDecimal
     * @author: huaqiang.liu
     * @date: 2020/2/25 10:48
     * @since JDK 1.8
     */
    public static BigDecimal process4(BigDecimal bigDecimal) {
        return process(bigDecimal, 4);
    }

    /**
     * 处理最终结果精度，2位小数
     *
     * @param bigDecimal
     * @return java.math.BigDecimal
     * @author: huaqiang.liu
     * @date: 2020/2/25 10:48
     * @since JDK 1.8
     */
    public static BigDecimal processFinal(BigDecimal bigDecimal) {
        return process(bigDecimal, FINAL_SCALE);
    }
    /**
     * 处理最终结果精度，2位小数
     *
     * @param bigDecimal
     * @return java.math.BigDecimal
     * @author: huaqiang.liu
     * @date: 2020/2/25 10:48
     * @since JDK 1.8
     */
    public static BigDecimal processFinalRound(BigDecimal bigDecimal) {
        return processRound(bigDecimal, FINAL_SCALE);
    }

    /**
     * 处理数据精度并转换为string
     * @param data
     * @return java.lang.String
     * @author: huaqiang.liu
     * @date: 2020/6/8 17:45
     * @since JDK 1.8
     */
    public static String processFinalString(BigDecimal data) {
        return processFinal(data).toString();
    }

    /**
     * 处理计算中间变量精度，6位小数
     *
     * @param bigDecimal
     * @return java.math.BigDecimal
     * @author: huaqiang.liu
     * @date: 2020/2/25 10:48
     * @since JDK 1.8
     */
    public static BigDecimal processTemp(BigDecimal bigDecimal) {
        return process(bigDecimal, TEMPORARY_SCALE);
    }

    /**
     * @description: 处理精度，5位小数
     * @param bigDecimal
     * @return java.math.BigDecimal
     * @author: xing.zhang
     * @date: 2020/5/22 15:27
     * @since JDK 1.8
     */
    public static BigDecimal processToLine(BigDecimal bigDecimal) {
        return process(bigDecimal, ON_LINE_SCALE);
    }

    /**
     * 加法计算
     *
     * @param a
     * @param b
     * @return java.math.BigDecimal
     * @author: huaqiang.liu
     * @date: 2020/2/25 10:50
     * @since JDK 1.8
     */
    public static BigDecimal add(BigDecimal a, BigDecimal b) {
        if (a == null) {
            return processTemp(b);
        } else {
            return b == null ? processTemp(a) : processTemp(a.add(b));
        }
    }

    /**
     * 减法计算
     *
     * @param a
     * @param b
     * @return java.math.BigDecimal
     * @author: huaqiang.liu
     * @date: 2020/2/25 11:38
     * @since JDK 1.8
     */
    public static BigDecimal subtract(BigDecimal a, BigDecimal b) {
        if (a == null) {
            return processTemp(b);
        } else {
            return b == null ? processTemp(a) : processTemp(a.subtract(b));
        }
    }

    /**
     * 乘法计算
     *
     * @param a
     * @param b
     * @return java.math.BigDecimal
     * @author: huaqiang.liu
     * @date: 2020/2/25 11:38
     * @since JDK 1.8
     */
    public static BigDecimal multiply(BigDecimal a, BigDecimal b) {
        if (a == null) {
            return processTemp(b);
        } else {
            return b == null ? processTemp(a) : processTemp(a.multiply(b));
        }
    }

    /**
     * @param decimal
     * @return java.math.BigDecimal
     * @description:(处理中间结果小数位)
     * @author: xing.zhang
     * @date: 2020/3/26 16:35
     * @since JDK 1.8
     */
    public static BigDecimal procesTransition(BigDecimal decimal) {
        return process(decimal, TEMPORARY_SCALE);
    }

    /**
     * @param decimal
     * @return java.math.BigDecimal
     * @description:(处理最终结果小数位)
     * @author: xing.zhang
     * @date: 2020/3/26 16:35
     * @since JDK 1.8
     */
    public static BigDecimal procesFinal(BigDecimal decimal) {
        return process(decimal, FINAL_SCALE);
    }

    /**
     * @description:(千分位格式化)
     * @param decimal
     * @return java.lang.String
     * @author: shuai.zhang
     * @date: 2023/6/15 11:01
     * @since JDK 1.8
     */
    public static String formatTh(BigDecimal decimal) {
        if (Objects.isNull(decimal)) {
            return "0.00";
        }
        DecimalFormat df2 = new DecimalFormat("###,##0.00");
        return df2.format(decimal);
    }

    /**
     * @description: 转String
     * @param decimal
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/6/9 11:20
     * @since JDK 1.8
     */
    public static String formatToStr(BigDecimal decimal) {
        if(null == decimal){
            return null;
        }
        return decimal.toString();
    }
    /**
     * @description:(千分位格式化)
     * @param decimal
     * @return java.lang.String
     * @author: shuai.zhang
     * @date: 2023/6/15 11:01
     * @since JDK 1.8
     */
    public static String formatTh6(BigDecimal decimal) {
        DecimalFormat df2 = new DecimalFormat("###,##0.000000");
        return df2.format(decimal);
    }
    /**
     * @description:(千分位格式化)
     * @param decimal
     * @return java.lang.String
     * @author: shuai.zhang
     * @date: 2023/6/15 11:01
     * @since JDK 1.8
     */
    public static String formatTh4(BigDecimal decimal) {
        DecimalFormat df2 = new DecimalFormat("###,##0.0000");
        return df2.format(decimal);
    }
    /**
     * 时间格式化
     * @param decimal
     * @param scale
     * @param roundingMode
     * @return
     */
    public static String procesFinalString(BigDecimal decimal, int scale,RoundingMode roundingMode) {
        if(null == decimal){
            return null;
        }
        return decimal.setScale(scale, roundingMode).toString();
    }
    /**
     * @param decimal
     * @param scale
     * @return java.math.BigDecimal
     * @description:(截取指定位小数)
     * @author: xing.zhang
     * @date: 2020/3/26 16:31
     * @since JDK 1.8
     */
    public static BigDecimal processRound(BigDecimal decimal, int scale) {
        if (decimal == null) {
            return BigDecimal.ZERO;
        } else {
            if (decimal.compareTo(BigDecimal.ZERO) >= 0) {
                decimal = decimal.setScale(scale, RoundingMode.HALF_UP);
            } else {
                decimal = decimal.setScale(scale, RoundingMode.UP);
            }

            return decimal;
        }
    }
    /**
     * @param decimal
     * @param scale
     * @return java.math.BigDecimal
     * @description:(截取指定位小数)
     * @author: xing.zhang
     * @date: 2020/3/26 16:31
     * @since JDK 1.8
     */
    public static BigDecimal process(BigDecimal decimal, int scale) {
        if (decimal == null) {
            return BigDecimal.ZERO;
        } else {
            if (decimal.compareTo(BigDecimal.ZERO) >= 0) {
                decimal = decimal.setScale(scale, RoundingMode.DOWN);
            } else {
                decimal = decimal.setScale(scale, RoundingMode.UP);
            }

            return decimal;
        }
    }

    /**
     * @param oneBigDecimal
     * @param twoBigDecimals
     * @return java.math.BigDecimal
     * @description:(求和)
     * @author: xing.zhang
     * @date: 2020/1/17 16:00
     * @since JDK 1.8
     */
    public static BigDecimal add(BigDecimal oneBigDecimal, BigDecimal... twoBigDecimals) {
        if (oneBigDecimal == null) {
            oneBigDecimal = BigDecimal.ZERO;
        }
        BigDecimal sum = oneBigDecimal;
        for (BigDecimal twoBigDecimal : twoBigDecimals) {
            if (twoBigDecimal == null) {
                twoBigDecimal = BigDecimal.ZERO;
            }
            sum = sum.add(twoBigDecimal);
        }
        return sum;
    }

    /**
     * @param numOne
     * @param numTwo
     * @return boolean
     * @description:(判断两个数据是否相等)
     * @author: xing.zhang
     * @date: 2020/3/10 13:41
     * @since JDK 1.8
     */
    public static boolean equals(BigDecimal numOne, BigDecimal numTwo, String ignoreNum) {
        if (numOne == null) {
            numOne = BigDecimal.ZERO;
        }
        if (numTwo == null) {
            numTwo = BigDecimal.ZERO;
        }
        if (numOne.compareTo(processToLine(numTwo)) == 0) {
            return true;
        }
        BigDecimal div = subtract(numOne, numTwo).abs();
        if(div.compareTo(new BigDecimal(ignoreNum)) <= 0){
            return true;
        }
        return false;
    }
    /**
     * @description: BigDecimal小数位操作
     * @param numStr	数字
     * @param precision	 保留位数
     * @param roundingMode	小数位的保留类型
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/5/20 19:46
     * @since JDK 1.8
     */
    public static String bigDecimalToString(BigDecimal numStr, Integer precision,RoundingMode roundingMode){
        if(null == numStr){
            return null;
        }
        // 默认两位
        if(null == precision){
            precision = 2;
        }
        return numStr.setScale(precision, roundingMode).toString();
    }

    /**
     * @description: BigDecimal 转字符串
     * @param numStr
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/8/9 10:10
     * @since JDK 1.8
     */
    public static String bigDecimalToString(BigDecimal numStr){
        if(null == numStr){
            return null;
        }
        return numStr.toString();
    }
}