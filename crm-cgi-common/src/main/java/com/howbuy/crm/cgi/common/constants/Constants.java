package com.howbuy.crm.cgi.common.constants;

/**
 * @description: 常量类
 * <AUTHOR>
 * @date 2023/3/16 10:26
 * @since JDK 1.8
 */
public class Constants {

    /**
     * 常量 1
     */
    public static final String ONE = "1";
    /**
     * 常量0
     */
    public static final String ZERO = "0";

    /**
     * 常量 2
     */
    public static final String TWO = "2";

    /**
     * 常量3
     */
    public static final String THREE = "3";

    /**
     * 常量4
     */
    public static final String FOUR = "4";
    /**
     * 常量-1
     */
    public static final String MINUS_ONE = "-1";
    /**
     * APP资产查询线程池
     */
    public static final String APP_BALANCE_QUERY_POOL = "APP_BALANCE_QUERY_POOL";

    private Constants() {
    }

    /**
     * 日志调用链id
     */
    public static final String TRACE_ID = "traceId";
    /**
     * UUID
     */
    public static final String UUID = "uuid";
    /**
     * UTF-8编码
     */
    public static final String CHARSET = "UTF-8";
    /**
     * 请求参数中的时间戳
     */
    public static final String TIMESTAMP = "timestamp";
    /**
     * 请求顺序编码
     */
    public static final String ACCESS_SEQ = "access_seq";
    /**
     * 香港交易session
     */
    public static final String HK_LOGIN_SESSION_NAME = "hkTradeSession";
    /**
     * 画像session
     */
    public static final String PORTRAIT_LOGIN_SESSION_NAME = "portraitSession";
    /**
     * 渠道ID，分销机构号
     */
    public static final String CORPID = "corpId";
    /**
     * 网点号
     */
    public static final String COOPID = "coopId";
    /**
     * 香港客户号
     */
    public static final String HKCUSTNO = "hkCustNo";
    /**
     * 画像userId
     */
    public static final String PORTRAIT_USER_ID = "userId";
    /**
     * 一账通号
     */
    public static final String HBONENO = "hboneNo";
    /**
     * 客户号
     */
    public static final String CUST_NO = "custNo";

    /**
     * 追踪信息
     */
    public static final String XTRACES = "xtraces";

    /**
     * 追踪信息
     */
    public static final String HBTRACEID = "hbTraceId";
    /**
     * request请求时间点
     */
    public static final String SERVICE_REQUEST_COST = "requestcost";
    /**
     * response响应时间点
     */
    public static final String SERVICE_RESPONSE_COST = "responsecost";
    /**
     * 请求头中的客户端IP
     */
    public static final String X_FORWARDED_FOR = "x-forwarded-for";
    /**
     * 请求头中的客户端IP
     */
    public static final String PROXY_CLIENT_IP = "Proxy-Client-IP";
    /**
     * 请求头中的客户端IP
     */
    public static final String WL_PROXY_CLIENT_IP = "WL-Proxy-Client-IP";
    /**
     * 请求头中的客户端IP
     */
    public static final String HTTP_CLIENT_IP = "HTTP_CLIENT_IP";
    /**
     * 请求头中的客户端IP
     */
    public static final String HTTP_X_FORWARDED_FOR = "HTTP_X_FORWARDED_FOR";
    /**
     * 请求头中的客户端IP
     */
    public static final String X_REAL_IP = "X-Real-IP";
    /**
     * unknown关键字
     */
    public static final String UNKNOWN = "unknown";
    /**
     * 客户IP
     */
    public static final String CUST_IP = "CUST_IP";
    /**
     * asset成功码
     */
    public static final String ASSET_SUCCESS = "C020000";
    /**
     * 渠道号
     */
    public static final String TX_CHANNEL = "txChannel";
    /**
     * 申请日期
     */
    public static final String APP_DT = "appDt";
    /**
     * 申请时间
     */
    public static final String APP_TM = "appTm";

    /**
     * tokenId：app初装时生成
     */
    public static final String TOKEN_ID = "tokenId";
    /**
     * 唤醒Id：每次后台唤醒到前台时生成
     */
    public static final String AWAKEID = "awakeId";
    /**
     * 功能Id：点击重要功能时生成（e.g.购买、卖出）
     */
    public static final String FUNCID = "funcId";
    /**
     * 页面Id：每个页面加载时生成
     */
    public static final String PAGEID = "pageId";
    /**
     * 请求Id：每个请求发送时生成
     */
    public static final String REQID = "reqId";
    /**
     * 默认网点号
     */
    public static final String DEFAULT_OUTLET_CODE = "A20150420";
    /**
     * 默认客户IP
     */
    public static final String DEFAULT_CUST_IP = "127.0.0.1";

    /**
     * 默认手机号地区码 86
     */
    public static final String DEFAULT_MOBILE_AREA_CODE = "+86";

    /**
     * 身份证类型的值
     */
    public static final String ID_TYPE_VALUE = "0";

    /**
     * 通用bankCode
     */
    public static final String COMMON_BANK_CODE = "tongyong_1111";
    /**
     * JSESSIONID
     */
    public static final String JSESSIONID = "JSESSIONID";


    /**
     * 通用分页大小 1000
     */
    public static final int PAGE_SIZE = 1000;

    /**
     * H5 请求标识
     */
    public static final String H5_REQ = "h5req";
    /**
     * H5 请求标识
     */
    public static final String H5_REQ_VALUE = "1";

    /**
     *  加密消息参数名
     */
    public static final String ENC_MSG = "encMsg";

    /**
     *  加密消息签名参数名
     */
    public static final String SIGN_MSG = "signMsg";


    /**
     * 设备主机型
     */
    public static final String PAR_PHONE_MODEL = "parPhoneModel";

    /**
     * 设备子机型
     */
    public static final String SUB_PHONE_MODEL = "subPhoneModel";


    /**
     * 产品ID
     */
    public static final String PRODUCT_ID = "productId";

    /**
     * 产品ID值
     */
    public static final String PRODUCT_ID_VALUE = "*********";

    /**
     * 渠道ID
     */
    public static final String CHANNEL_ID = "channelId";

    /**
     * 请求头中的公共参数
     */
    public static final String X_COMMON_PARAMS = "X-common-params";

    /**
     * app版本号
     */
    public static final String VERSION = "version";


    public static final String DEVICE_ID = "deviceId";
    public static final String DEVICE_NAME = "deviceName";


    /**
     * 开发环境
     */
    public static final String DEV = "dev";

    /**
     * 测试环境
     */
    public static final String TEST = "test";

    public static final String WX_VERSION = "wxVersion";

    public static final String HK_APP_CUST_NO_KEY = "73450971626e43c8a7df93f9a7582856";

    /**
     * hboneNo的des加密key
     */
    public static final String HBONENO_DES_KEY = "1234567890__howbuy__abcdefghij";

    public static final String MRYH_LOGO = "MRYHLOGO";

    /**
     * 专业投资者 Code
     */
    public static final String INVESTOR_QUALIFICATION_PRO = "PRO";

    /**
     * 普通投资者
     */
    public static final String INVESTOR_QUALIFICATION_NORMAL = "NORMAL";

    /**
     * 测试环境验证码
     */
    public static final String TEST_VERIFY_CODE_111111 = "111111";
    /**
     * app退出登录时间点
     */
    public static final String APP_AUTOEXIT_TIME = "4";

    /**
     * 常量-0
     */
    public static final String CONSTANT_ZERO = "0";

    /**
     * 常量数字 1
     */
    public static final Integer NUMBER_ONE = 1;

    /**
     * 常量数字 0
     */
    public static final Integer NUMBER_ZERO = 0;

    /**
     * 常量数字10
     */
    public static final Integer NUMBER_TEN = 10;

    /**
     * APP版本 2.2.0
     */
    public static final String APP_VERSION = "2.2.0";

    /**
     * 删除授信安全码
     */
    public static final String BUSICODE_DEL_CREDIT_SECURITY_CODE = "126";

    /**
     * 特殊场景-discode
     */
    public static final String SPECIAL_DISCODE_ALL = "ALL";

    /**
     * 标签地址分隔符
     */
    public static final String LABEL_ADDRESS_SPLIT = "-";

    /**
     * 客户画像上传报告类型-资配报告-1
     */
    public static final String PORTRAIT_UPLOAD_REPORT_TYPE_ASSET = "1";

    /**
     * 客户画像上传报告类型-产品报告-2
     */
    public static final String PORTRAIT_UPLOAD_REPORT_TYPE_PRODUCT = "2";

    /**
     * 日期开始时间
     */
    public static final String DATE_START_TIME_STR = " 00:00:00";

    /**
     * 日期截止时间
     */
    public static final String DATE_END_TIME_STR = " 23:59:59";

    /**
     * 警告主题
     */
    public final static String ALERT_TOPIC_CRM_CGI_BUSINISE = "CRM_CGI_BUSINISE_ALTER_TOPIC";
}
