/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.utils;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/6/9 11:15
 * @since JDK 1.8
 */
public class NumberUtils {

    /**
     * @description: 数字转字符串
     * @author: jinqing.rao
     * @date: 2025/6/9 11:15
     * @since JDK 1.8
     */
    public static String integerToStr(Integer value) {
        if(null == value){
            return null;
        }
        return value.toString();
    }

}
